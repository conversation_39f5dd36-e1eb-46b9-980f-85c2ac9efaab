# 音频合成服务Docker镜像
FROM openjdk:17-jre-slim

# 安装FFmpeg
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 创建临时目录
RUN mkdir -p /tmp/audio-merge && \
    chmod 755 /tmp/audio-merge

# 复制应用文件
COPY target/agent-*.jar app.jar

# 设置资源限制
ENV JAVA_OPTS="-Xmx512m -Xms256m -XX:MaxDirectMemorySize=128m"

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
