version: '3.8'

services:
  audio-merge-service:
    build:
      context: ../../
      dockerfile: docker/audio-merge/Dockerfile
    container_name: audio-merge-service
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'      # 限制CPU使用
          memory: 1G       # 限制内存使用
        reservations:
          cpus: '0.5'      # 预留CPU
          memory: 256M     # 预留内存
    
    # 环境变量
    environment:
      - SPRING_PROFILES_ACTIVE=prod,audio-merge
      - JAVA_OPTS=-Xmx512m -Xms256m -XX:MaxDirectMemorySize=128m
      - AUDIO_MERGE_MAX_CONCURRENT_TASKS=2
      - AUDIO_MERGE_MAX_MEMORY_MB=512
      - AUDIO_MERGE_CPU_LIMIT=2
      - AUDIO_MERGE_TEMP_DIR=/tmp/audio-merge
    
    # 卷挂载
    volumes:
      - audio_merge_temp:/tmp/audio-merge
      - /etc/localtime:/etc/localtime:ro
    
    # 网络
    networks:
      - app-network
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  audio_merge_temp:
    driver: local

networks:
  app-network:
    driver: bridge
