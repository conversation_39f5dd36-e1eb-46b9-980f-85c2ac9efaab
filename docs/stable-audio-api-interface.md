# Stable Audio API 接口文档

## 概述

新增的 Stable Audio API 接口提供了高质量音频生成功能，基于 fal-ai/stable-audio 模型。相比传统音效生成，Stable Audio 支持更长的音频时长（最多47秒）、更丰富的参数控制，以及更高的音频质量。

## API 接口

### 生成 Stable Audio

**接口地址：** `POST /agent/sound-effects/generate-stable-audio`

**接口描述：** 调用 Stable Audio API 生成高质量音频，等待完成后返回音频下载地址

**请求参数：**

```json
{
  "prompt": "128 BPM tech house drum loop",
  "duration": 30,
  "steps": 100,
  "secondsStart": 0,
  "conversationId": "canvas_12345",
  "contentId": "shot_67890",
  "index": 1
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 范围 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| prompt | String | 是 | - | - | 音频生成提示词 |
| duration | Integer | 否 | 1-47 | 30 | 音频时长（秒） |
| steps | Integer | 否 | 1-1000 | 100 | 去噪步数，影响生成质量 |
| secondsStart | Integer | 否 | 0-47 | 0 | 音频开始时间（秒） |
| conversationId | String | 是 | - | - | 会话ID（画布code） |
| contentId | String | 否 | - | - | 内容ID（分镜code） |
| index | Integer | 否 | - | - | 音频索引 |

**响应示例：**

```json
{
  "success": true,
  "data": {
    "audioUrl": "https://oss.example.com/audio/stable_audio_12345.wav",
    "prompt": "128 BPM tech house drum loop",
    "duration": 30,
    "steps": 100,
    "secondsStart": 0,
    "fileName": "generated_stable_audio.wav",
    "fileSize": "4.2 MB",
    "contentType": "audio/wav",
    "status": "SUCCESS",
    "generationTime": 45000
  },
  "errorCode": null,
  "errorMessage": null
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| audioUrl | String | 音频文件下载地址 |
| prompt | String | 音频生成提示词 |
| duration | Integer | 音频时长（秒） |
| steps | Integer | 去噪步数 |
| secondsStart | Integer | 音频开始时间（秒） |
| fileName | String | 文件名 |
| fileSize | String | 格式化的文件大小 |
| contentType | String | MIME类型 |
| status | String | 生成状态（SUCCESS/FAILED） |
| generationTime | Long | 生成耗时（毫秒） |

## 与传统音效生成的对比

| 特性 | 传统音效生成 | Stable Audio |
|------|-------------|--------------|
| **API端点** | `/generate-audio` | `/generate-stable-audio` |
| **最大时长** | 30秒 | 47秒 |
| **音频质量** | 标准 | 高质量 |
| **参数控制** | 基础（提示词+时长） | 丰富（提示词+时长+步数+开始时间） |
| **生成时间** | 较快 | 较慢（高质量需要更多时间） |
| **适用场景** | 简单音效 | 复杂音乐、高质量音频 |
| **文件大小** | 较小 | 较大 |

## 使用示例

### 1. 基础音频生成

```bash
curl -X POST "https://api.example.com/agent/sound-effects/generate-stable-audio" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "prompt": "Gentle rain sounds with thunder",
    "duration": 30,
    "conversationId": "canvas_12345"
  }'
```

### 2. 高质量音乐生成

```bash
curl -X POST "https://api.example.com/agent/sound-effects/generate-stable-audio" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "prompt": "Upbeat electronic dance music with synthesizers",
    "duration": 45,
    "steps": 200,
    "conversationId": "canvas_12345",
    "contentId": "shot_67890",
    "index": 1
  }'
```

### 3. 音频片段生成

```bash
curl -X POST "https://api.example.com/agent/sound-effects/generate-stable-audio" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "prompt": "Classical piano melody",
    "duration": 20,
    "secondsStart": 10,
    "steps": 150,
    "conversationId": "canvas_12345"
  }'
```

## 错误处理

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| INVALID_REQUEST | 请求参数无效 | 请求参数不符合要求 |
| INVALID_RESULT | Stable Audio 生成失败，返回结果无效 | 生成的音频文件无效 |
| GENERATE_STABLE_AUDIO_FAILED | Stable Audio 生成失败 | 生成过程中发生错误 |

### 错误响应示例

```json
{
  "success": false,
  "data": null,
  "errorCode": "INVALID_REQUEST",
  "errorMessage": "请求参数无效"
}
```

## 最佳实践

### 1. 提示词优化

- **具体描述**：使用具体的音乐风格、乐器、情绪描述
- **技术参数**：包含BPM、调性等技术参数
- **情境描述**：描述音频的使用场景和氛围

**好的提示词示例：**
```
"128 BPM uplifting house music with piano chords and warm pads"
"Ambient forest sounds with birds chirping and gentle wind"
"Epic orchestral music with strings and brass, cinematic style"
```

### 2. 参数调优

- **时长选择**：根据实际需求选择合适的时长，避免不必要的长时间生成
- **步数设置**：
  - 50-100步：快速生成，适合预览
  - 100-200步：标准质量，适合大多数场景
  - 200-500步：高质量，适合最终产品
- **开始时间**：用于生成音频的特定片段

### 3. 性能优化

- **批量处理**：对于多个音频生成需求，考虑分批处理
- **缓存策略**：对于相同参数的请求，可以考虑缓存结果
- **超时设置**：设置合理的请求超时时间（建议5-10分钟）

### 4. 质量控制

- **结果验证**：检查返回的音频URL是否有效
- **文件检查**：验证生成的音频文件是否可以正常播放
- **重试机制**：对于失败的请求实现重试机制

## 集成指南

### 前端集成

```javascript
async function generateStableAudio(params) {
  try {
    const response = await fetch('/agent/sound-effects/generate-stable-audio', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(params)
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.data.audioUrl;
    } else {
      throw new Error(result.errorMessage);
    }
  } catch (error) {
    console.error('Stable Audio 生成失败:', error);
    throw error;
  }
}

// 使用示例
const audioUrl = await generateStableAudio({
  prompt: "Relaxing ambient music",
  duration: 30,
  conversationId: "canvas_12345"
});
```

### 后端集成

```java
@Autowired
private SoundEffectsService soundEffectsService;

public String generateStableAudio(String prompt, int duration, String conversationId) {
    try {
        StableAudioResult result = soundEffectsService.generateStableAudio(
            prompt, duration, conversationId, null, null);
        
        if (result.isValid()) {
            return result.getAudioUrl();
        } else {
            throw new RuntimeException("生成结果无效");
        }
    } catch (Exception e) {
        log.error("Stable Audio 生成失败", e);
        throw new RuntimeException("音频生成失败: " + e.getMessage());
    }
}
```

## 注意事项

1. **API限制**：遵守 fal.ai 的 API 使用限制和配额
2. **生成时间**：高质量音频生成需要较长时间，请设置合理的超时
3. **文件大小**：生成的音频文件可能较大，注意存储和带宽成本
4. **成本控制**：监控 API 使用量，合理控制生成成本
5. **权限验证**：确保只有授权用户可以调用音频生成接口
6. **资源管理**：及时清理不需要的音频文件，避免存储空间浪费
