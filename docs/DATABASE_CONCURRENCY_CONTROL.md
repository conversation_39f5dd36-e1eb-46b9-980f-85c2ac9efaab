# 数据库并发控制方案

## 概述

为了解决Redis信号量在图片生成功能中的稳定性问题，我们实现了基于数据库的并发控制方案。这个方案更加稳定可靠，避免了信号量泄漏的问题。

## 问题背景

原有的Redis信号量方案存在以下问题：
1. **信号量泄漏**：异常情况下信号量可能没有正确释放
2. **网络异常**：Redis连接异常时可能导致信号量状态不一致
3. **应用重启**：应用异常重启时，正在处理的任务信号量可能丢失
4. **复杂的修复逻辑**：需要定时任务来修复泄漏的信号量

## 新方案优势

### 1. 稳定性
- 基于数据库事务，保证数据一致性
- 不依赖外部缓存系统，减少故障点
- 应用重启后状态自动恢复

### 2. 简单性
- 逻辑简单清晰，易于理解和维护
- 不需要复杂的修复机制
- 配置灵活，支持动态调整

### 3. 可观测性
- 所有状态变更都有数据库记录
- 提供监控接口，实时查看系统状态
- 支持手动干预和管理

## 核心组件

### 1. DatabaseConcurrencyControlService
负责并发控制的核心服务：
- `tryAcquireTask()`: 尝试获取任务执行权限
- `releaseTask()`: 释放任务执行权限
- `cleanupTimeoutTasks()`: 清理超时任务
- `getStatus()`: 获取系统状态

### 2. StableImageGenerationService
稳定的图片生成服务：
- 使用数据库并发控制
- 自动重试机制
- 完善的异常处理

### 3. TaskTimeoutCleanupJob
定时任务，负责：
- 清理超时的处理中任务
- 系统状态监控
- 异常情况告警

### 4. SystemMonitorController
管理接口：
- 查看系统并发状态
- 手动清理超时任务
- 系统健康检查

## 配置说明

在 `application.yml` 中添加配置：

```yaml
app:
  concurrency:
    # 最大并发任务数
    max-concurrent-tasks: 5
    # 任务超时时间（分钟）
    task-timeout-minutes: 10
    # 最大重试次数
    max-retry-count: 3
    # 重试延迟基础时间（秒）
    retry-delay-seconds: 30
    # 是否启用数据库并发控制
    enable-database-concurrency-control: true
```

## 工作原理

### 1. 任务获取权限
```
1. 检查当前处理中任务数量
2. 如果未达到上限，使用数据库乐观锁将任务状态从PENDING更新为PROCESSING
3. 更新成功表示获取权限成功，失败表示权限获取失败
```

### 2. 任务处理
```
1. 获取权限成功后执行图片生成
2. 处理完成后将状态更新为COMPLETED或FAILED
3. 异常情况下也会正确释放权限
```

### 3. 超时清理
```
1. 定时任务每2分钟检查一次
2. 将超过超时时间的PROCESSING任务重置为PENDING
3. 这样可以自动恢复因异常中断的任务
```

## 监控接口

### 1. 获取系统状态
```
GET /api/system/monitor/concurrency-status
```

返回示例：
```json
{
  "success": true,
  "data": {
    "maxConcurrent": 5,
    "currentProcessing": 2,
    "available": 3,
    "pendingTasks": 10
  }
}
```

### 2. 手动清理超时任务
```
POST /api/system/monitor/cleanup-timeout-tasks
```

### 3. 系统健康检查
```
GET /api/system/monitor/health
```

## 部署步骤

### 1. 更新配置
在配置文件中启用数据库并发控制：
```yaml
app:
  concurrency:
    enable-database-concurrency-control: true
```

### 2. 配置定时任务
在XXL-JOB中配置以下任务：
- `taskTimeoutCleanup`: 每2分钟执行一次
- `systemStatusMonitor`: 每5分钟执行一次

### 3. 验证部署
1. 检查系统状态接口是否正常
2. 提交几个测试任务验证并发控制
3. 观察日志确认任务正常处理

## 性能对比

| 指标 | Redis信号量 | 数据库并发控制 |
|------|-------------|----------------|
| 稳定性 | 中等 | 高 |
| 复杂度 | 高 | 低 |
| 维护成本 | 高 | 低 |
| 性能 | 高 | 中等 |
| 可观测性 | 低 | 高 |

## 注意事项

1. **数据库性能**：并发控制依赖数据库，需要确保数据库性能足够
2. **事务隔离**：使用默认的事务隔离级别即可
3. **监控告警**：建议配置监控告警，及时发现异常情况
4. **备份方案**：如果数据库出现问题，可以临时切换回Redis信号量

## 故障排查

### 1. 任务堵塞
- 检查是否有长时间处理中的任务
- 手动执行超时清理
- 检查外部API是否正常

### 2. 并发数异常
- 查看系统状态接口
- 检查配置是否正确
- 查看数据库中任务状态分布

### 3. 性能问题
- 监控数据库性能
- 检查是否有慢查询
- 考虑优化数据库索引
