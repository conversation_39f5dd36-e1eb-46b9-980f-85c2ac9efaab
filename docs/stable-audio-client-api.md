# Stable Audio 客户端 API 文档

## 概述

`FalStableAudioClient` 是基于 fal-ai/stable-audio API 的音频生成客户端，使用 JDK 17 新特性实现。它提供了完整的音频生成功能，包括请求提交、状态查询、结果获取和一键生成等。

## 主要特性

- **JDK 17 新特性**：使用 record、switch 表达式、CompletableFuture 等现代 Java 特性
- **异步处理**：基于 CompletableFuture 的异步 API
- **智能轮询**：自动轮询生成状态直到完成
- **错误处理**：完善的错误处理和重试机制
- **类型安全**：使用 record 确保类型安全

## API 接口

### 1. 基础 API

#### 提交生成请求

```java
CompletableFuture<QueueStatus> submitRequest(StableAudioRequest request)
```

**示例：**
```java
StableAudioRequest request = StableAudioRequest.of("128 BPM tech house drum loop", 30);
CompletableFuture<QueueStatus> future = client.submitRequest(request);
```

#### 查询请求状态

```java
CompletableFuture<QueueStatus> getStatus(String requestId, boolean includeLogs)
```

**示例：**
```java
CompletableFuture<QueueStatus> statusFuture = client.getStatus("request-id", false);
```

#### 获取生成结果

```java
CompletableFuture<StableAudioResult> getResult(String requestId)
```

**示例：**
```java
CompletableFuture<StableAudioResult> resultFuture = client.getResult("request-id");
```

#### 取消请求

```java
CompletableFuture<Boolean> cancelRequest(String requestId)
```

### 2. 高级 API

#### 轮询等待完成

```java
CompletableFuture<StableAudioResult> waitForCompletion(String requestId, int maxWaitMinutes)
```

**示例：**
```java
// 等待最多10分钟
CompletableFuture<StableAudioResult> resultFuture = client.waitForCompletion("request-id", 10);
```

#### 一键生成（推荐）

```java
// 基础版本
CompletableFuture<StableAudioResult> generateAndWait(String prompt, int secondsTotal, int maxWaitMinutes)

// 带自定义步数
CompletableFuture<StableAudioResult> generateAndWait(String prompt, int secondsTotal, int steps, int maxWaitMinutes)
```

**示例：**
```java
// 生成30秒的音频，最多等待5分钟
CompletableFuture<StableAudioResult> future = client.generateAndWait(
    "Ambient forest sounds with birds chirping", 30, 5);

// 带自定义步数
CompletableFuture<StableAudioResult> future = client.generateAndWait(
    "Electronic dance music", 20, 150, 10);
```

## 数据模型

### StableAudioRequest

使用 JDK 17 的 record 特性定义：

```java
public record StableAudioRequest(
    String prompt,           // 音频生成提示词（必填）
    Integer steps,           // 去噪步数（1-1000，默认100）
    Integer secondsTotal,    // 音频总时长（0-47秒，必填）
    Integer secondsStart     // 音频开始时间（0-47秒，默认0）
) {
    // 便捷创建方法
    public static StableAudioRequest of(String prompt, int secondsTotal);
    public static StableAudioRequest of(String prompt, int secondsTotal, int steps);
    public static StableAudioRequest of(String prompt);
}
```

### StableAudioResult

```java
public record StableAudioResult(
    AudioFile audioFile
) {
    public record AudioFile(
        String url,          // 音频文件URL
        Long fileSize,       // 文件大小（字节）
        String fileName,     // 文件名
        String contentType,  // MIME类型
        String fileData      // 文件数据（二进制）
    );
}
```

## 使用示例

### 1. 基础使用

```java
@Autowired
private FalStableAudioClient stableAudioClient;

public void generateAudio() {
    // 创建请求
    StableAudioRequest request = StableAudioRequest.of("Relaxing piano music", 30);
    
    // 一键生成
    stableAudioClient.generateAndWait("Relaxing piano music", 30, 10)
        .thenAccept(result -> {
            if (result.isValid()) {
                String audioUrl = result.getAudioUrl();
                System.out.println("音频生成成功: " + audioUrl);
            }
        })
        .exceptionally(throwable -> {
            System.err.println("音频生成失败: " + throwable.getMessage());
            return null;
        });
}
```

### 2. 分步处理

```java
public void generateAudioStepByStep() {
    StableAudioRequest request = StableAudioRequest.of("Jazz saxophone solo", 25, 120);
    
    // 1. 提交请求
    stableAudioClient.submitRequest(request)
        .thenCompose(queueStatus -> {
            System.out.println("请求已提交: " + queueStatus.requestId());
            // 2. 等待完成
            return stableAudioClient.waitForCompletion(queueStatus.requestId(), 15);
        })
        .thenAccept(result -> {
            // 3. 处理结果
            System.out.println("生成完成: " + result.getDescription());
        })
        .exceptionally(throwable -> {
            System.err.println("处理失败: " + throwable.getMessage());
            return null;
        });
}
```

### 3. 同步使用

```java
public StableAudioResult generateAudioSync(String prompt, int duration) {
    try {
        // 使用 .get() 方法同步等待结果
        return stableAudioClient.generateAndWait(prompt, duration, 10).get();
    } catch (Exception e) {
        throw new RuntimeException("音频生成失败", e);
    }
}
```

## 在 SoundEffectsServiceImpl 中的集成

新的 `FalStableAudioClient` 已经集成到 `SoundEffectsServiceImpl` 中：

```java
@Autowired
private FalStableAudioClient falStableAudioClient;

/**
 * 使用 Stable Audio 生成音效
 */
public StableAudioResult generateStableAudio(String prompt, int duration, 
                                           String conversationId, String contentId, Integer index) {
    // 实现细节...
}

/**
 * 异步生成 Stable Audio
 */
public CompletableFuture<StableAudioResult> generateStableAudioAsync(String prompt, int duration) {
    return falStableAudioClient.generateAndWait(prompt, duration, DEFAULT_MAX_WAIT_MINUTES);
}
```

## 配置

### API 密钥配置

在 `application.yml` 中配置：

```yaml
fal:
  api:
    key: "your-fal-api-key-here"
```

或者通过环境变量：

```bash
export FAL_API_KEY="your-fal-api-key-here"
```

### 超时配置

客户端使用以下默认超时设置：

- **连接超时**: 30秒
- **读取超时**: 5分钟
- **写入超时**: 30秒

## 错误处理

### 常见错误类型

1. **网络错误**: 连接超时、网络不可达
2. **API错误**: 无效的API密钥、请求参数错误
3. **生成超时**: 超过最大等待时间
4. **结果无效**: 生成的音频文件无效

### 错误处理示例

```java
stableAudioClient.generateAndWait("Test audio", 30, 5)
    .whenComplete((result, throwable) -> {
        if (throwable != null) {
            if (throwable.getMessage().contains("超时")) {
                System.err.println("生成超时，请稍后重试");
            } else if (throwable.getMessage().contains("HTTP请求失败")) {
                System.err.println("API调用失败，请检查网络和API密钥");
            } else {
                System.err.println("未知错误: " + throwable.getMessage());
            }
        } else {
            System.out.println("生成成功: " + result.getAudioUrl());
        }
    });
```

## 性能优化建议

1. **批量处理**: 对于多个音频生成请求，使用 `CompletableFuture.allOf()` 并行处理
2. **合理设置超时**: 根据音频长度调整等待时间
3. **错误重试**: 实现指数退避的重试机制
4. **资源管理**: 及时处理完成的 CompletableFuture

## 与原有 FalSoundEffectsClient 的对比

| 特性 | FalSoundEffectsClient | FalStableAudioClient |
|------|----------------------|---------------------|
| API 端点 | cassetteai/sound-effects-generator | fal-ai/stable-audio |
| 音频质量 | 标准 | 高质量 |
| 生成时长 | 最多30秒 | 最多47秒 |
| 参数控制 | 基础 | 丰富（步数、开始时间等） |
| 适用场景 | 音效生成 | 音乐和复杂音频生成 |

## 注意事项

1. **API 限制**: 遵守 fal.ai 的 API 使用限制和配额
2. **文件大小**: 生成的音频文件可能较大，注意存储和传输
3. **生成时间**: 高质量音频生成需要更长时间
4. **成本控制**: 监控 API 使用量和成本
