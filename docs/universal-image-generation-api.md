# 通用图片生成API文档

## 概述

通用图片生成API是一个统一的接口，可以根据客户端传递的参数自动选择合适的模型和方法来生成图片。该接口支持多种模型类型和生成方式，并提供完整的任务状态跟踪功能。

## 功能特性

- **多模型支持**: 支持 `kontext-pro` 和 `kontext-max` 两种模型
- **多种生成方式**: 
  - 文本到图像 (Text-to-Image)
  - 图像到图像 (Image-to-Image) 
  - 多图像处理 (Multi-Image)
- **异步处理**: 支持异步生成，立即返回任务编码
- **状态跟踪**: 可通过任务编码查询生成状态和结果
- **批量查询**: 支持批量查询多个任务状态
- **用户资源管理**: 完整的用户资源记录和管理

## API接口

### 1. 生成图片

**接口地址**: `POST /api/v1/universal/image/generate`

**请求参数**:

```json
{
  "modelType": "kontext-pro",
  "prompt": "A beautiful sunset over the ocean",
  "imageUrls": ["https://example.com/image1.jpg"],
  "numImages": 1,
  "safetyTolerance": "2",
  "seed": 12345,
  "aspectRatio": "16:9",
  "outputFormat": "jpeg",
  "syncMode": false,
  "guidanceScale": 3.5
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| modelType | String | 是 | 模型类型，支持 `kontext-pro` 或 `kontext-max` |
| prompt | String | 是 | 生成提示词 |
| imageUrls | List<String> | 否 | 参考图片URL集合 |
| numImages | Integer | 否 | 生成图片数量(1-4)，默认1 |
| safetyTolerance | String | 否 | 安全等级(1-6)，默认2 |
| seed | Integer | 否 | 随机种子值 |
| aspectRatio | String | 否 | 图片尺寸比例，默认16:9 |
| outputFormat | String | 否 | 输出格式(jpeg/png)，默认jpeg |
| syncMode | Boolean | 否 | 同步模式，默认false |
| guidanceScale | Double | 否 | 引导比例(1-20)，默认3.5 |

**响应示例**:

```json
{
  "success": true,
  "data": {
    "code": "IMG-ABC12345",
    "status": "PENDING",
    "externalRequestId": "req_123456789",
    "message": "图片生成任务已提交，请使用资源编码查询生成状态"
  }
}
```

### 2. 查询资源状态

**接口地址**: `GET /api/v1/universal/image/status/{code}`

**路径参数**:
- `code`: 资源编码

**响应示例**:

```json
{
  "success": true,
  "data": {
    "code": "IMG-ABC12345",
    "resourceType": 1,
    "generationStatus": "SUCCESS",
    "resourceUrls": [
      "https://example.com/generated-image1.jpg",
      "https://example.com/generated-image2.jpg"
    ],
    "resourceSize": 1024000,
    "width": 1920,
    "height": 1080,
    "modelType": "kontext-pro",
    "prompt": "A beautiful sunset over the ocean",
    "createTime": "2025-01-17T10:30:00",
    "updateTime": "2025-01-17T10:35:00"
  }
}
```

### 3. 批量查询资源状态

**接口地址**: `POST /api/v1/universal/image/status/batch`

**请求参数**:

```json
["IMG-ABC12345", "IMG-DEF67890", "IMG-GHI11111"]
```

**响应示例**:

```json
{
  "success": true,
  "data": [
    {
      "code": "IMG-ABC12345",
      "resourceType": 1,
      "generationStatus": "SUCCESS",
      "resourceUrls": ["https://example.com/image1.jpg"]
    },
    {
      "code": "IMG-DEF67890",
      "resourceType": 1,
      "generationStatus": "PENDING",
      "resourceUrls": null
    }
  ]
}
```

### 4. 查询用户资源列表

**接口地址**: `GET /api/v1/universal/image/user/{userId}`

**路径参数**:
- `userId`: 用户ID

**查询参数**:
- `resourceType`: 资源类型(1-图片,2-视频,3-音频)，可选
- `limit`: 限制数量，默认20

**响应示例**:

```json
{
  "success": true,
  "data": [
    {
      "code": "IMG-ABC12345",
      "resourceType": 1,
      "generationStatus": "SUCCESS",
      "resourceUrls": ["https://example.com/image1.jpg"],
      "createTime": "2025-01-17T10:30:00"
    }
  ]
}
```

## 模型选择逻辑

系统会根据 `modelType` 和 `imageUrls` 参数自动选择合适的生成方法：

### kontext-pro 模型
- **无参考图片**: 使用文本到图像方法
- **单张参考图片**: 使用图像到图像方法
- **多张参考图片**: 不支持，返回错误

### kontext-max 模型
- **无参考图片**: 使用文本到图像方法
- **单张参考图片**: 使用图像到图像方法
- **多张参考图片**: 使用多图像处理方法

## 状态说明

### 生成状态
- `PENDING`: 生成中
- `SUCCESS`: 生成成功
- `FAILED`: 生成失败

### 资源类型
- `1`: 图片
- `2`: 视频
- `3`: 音频

## 使用示例

### 1. 文本生成图片

```bash
curl -X POST "http://localhost:8080/api/v1/universal/image/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "modelType": "kontext-pro",
    "prompt": "A beautiful sunset over the ocean",
    "numImages": 2,
    "aspectRatio": "16:9"
  }'
```

### 2. 图像到图像生成

```bash
curl -X POST "http://localhost:8080/api/v1/universal/image/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "modelType": "kontext-pro",
    "prompt": "Make it more colorful",
    "imageUrls": ["https://example.com/reference.jpg"],
    "guidanceScale": 7.5
  }'
```

### 3. 查询生成状态

```bash
curl -X GET "http://localhost:8080/api/v1/universal/image/status/IMG-ABC12345" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 定时任务

系统包含一个定时任务 `UserResourceStatusSyncJob`，每2分钟执行一次，用于：

1. 查询外部API的任务状态
2. 更新本地资源记录
3. 处理超时任务
4. 保存生成结果

## 数据库设计

### ai_user_resource 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| code | varchar(64) | 资源唯一编码 |
| user_id | varchar(64) | 用户ID |
| resource_type | tinyint | 资源类型 |
| generation_status | varchar(20) | 生成状态 |
| resource_urls | text | 资源URL集合(JSON) |
| resource_size | bigint | 资源大小(字节) |
| width | int | 图片/视频宽度 |
| height | int | 图片/视频高度 |
| duration | int | 视频/音频时长(秒) |
| model_type | varchar(50) | 使用的模型类型 |
| prompt | text | 生成提示词 |
| reference_images | text | 参考图片URL集合(JSON) |
| generation_params | text | 生成参数(JSON) |
| error_message | text | 错误信息 |
| external_request_id | varchar(100) | 外部API请求ID |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| del_flag | tinyint | 删除标记 |

## 错误处理

API会返回详细的错误信息，常见错误包括：

- `GENERATION_FAILED`: 图片生成任务提交失败
- `QUERY_FAILED`: 查询资源状态失败
- `BATCH_QUERY_FAILED`: 批量查询资源状态失败
- `USER_RESOURCES_QUERY_FAILED`: 查询用户资源列表失败

## 注意事项

1. 所有接口都需要用户认证
2. 资源编码具有全局唯一性
3. 生成任务有10分钟超时限制
4. 每次批量查询最多支持50个资源编码
5. 用户只能查询自己的资源
