# 图片生成重试逻辑实现

## 概述

将原有`processTaskWithRetry`方法中的完整重试逻辑集成到`StableImageGenerationService`的`executeImageGeneration`方法中，实现了更稳定和完善的错误处理机制。

## 核心功能

### 1. 双重重试机制

#### 普通异常重试
- **最大重试次数**: 3次
- **重试间隔**: 1秒
- **适用场景**: 网络异常、API调用失败、响应解析错误等

#### 特定错误码重试
- **最大重试次数**: 3次
- **特定错误码**: `50411`, `50511`, `50412`, `50512`, `50413`
- **处理方式**: 调用AI完成API优化prompt后重新提交任务

### 2. 重试逻辑流程

```
图片生成API调用
    ↓
检查响应结果
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   成功响应      │   特定错误码    │   其他异常      │
│                 │                 │                 │
│ 更新任务完成    │ 调用AI完成API   │ 普通重试机制    │
│                 │     ↓           │     ↓           │
│                 │ 更新prompt      │ 等待1秒         │
│                 │ 重新提交任务    │ 递归重试        │
│                 │                 │     ↓           │
│                 │                 │ 达到上限标记失败│
└─────────────────┴─────────────────┴─────────────────┘
```

## 代码实现

### 1. 主要方法结构

```java
// 入口方法
private boolean executeImageGeneration(AiImageTaskQueuePo task)

// 带重试的核心方法
private boolean executeImageGenerationWithRetry(AiImageTaskQueuePo task, int retryCount)

// 特定错误码处理
private boolean handleSpecificErrorCode(AiImageTaskQueuePo task, String errorCode, DoubaoImageGenerationRequest apiRequest)

// 重试信息解析和构建
private ErrorCodeRetryInfo parseErrorCodeRetryInfo(String errorReason)
private String buildErrorCodeRetryInfo(String errorCode, int retryCount)
```

### 2. 关键常量

```java
// 普通重试最大次数
private static final int MAX_RETRY_COUNT = 3;

// 特定错误码重试最大次数
private static final int MAX_ERROR_CODE_RETRY_COUNT = 3;

// 错误码重试信息格式
private static final String ERROR_CODE_RETRY_FORMAT = "AI_COMPLETION_RETRY";

// 需要特殊处理的错误码
private static final Set<String> SPECIFIC_ERROR_CODES = new HashSet<>(
    Arrays.asList("50411", "50511", "50412", "50512", "50413")
);
```

## 错误处理策略

### 1. 成功情况
- 响应不为空
- 错误码为空或null
- 图片URL不为空

**处理**: 更新任务状态为`COMPLETED`

### 2. 特定错误码情况
- 错误码在预定义集合中：`50411`, `50511`, `50412`, `50512`, `50413`
- 图片URL为空

**处理流程**:
1. 检查当前错误码重试次数
2. 如果未达到上限，调用AI完成API优化prompt
3. 更新任务的请求参数和重试信息
4. 将任务状态重置为`PENDING`，等待重新调度
5. 如果达到上限，标记任务为`FAILED`

### 3. 普通异常情况
- API调用抛出异常
- 响应为空
- 图片URL为空但错误码不在特定集合中

**处理流程**:
1. 等待1秒
2. 递归调用重试方法，增加重试计数
3. 如果达到最大重试次数，标记任务为`FAILED`

## 重试信息管理

### 1. ErrorCodeRetryInfo 数据结构
```java
public static class ErrorCodeRetryInfo {
    private String errorCode;      // 错误码
    private int retryCount;        // 重试次数
    private Date lastRetryTime;    // 最后重试时间
}
```

### 2. 重试信息存储格式
```
AI_COMPLETION_RETRY:{"errorCode":"50411","retryCount":2,"lastRetryTime":"2024-01-01T10:00:00Z"}
```

### 3. 重试信息解析
- 从任务的`errorReason`字段解析重试信息
- 支持JSON格式和简单文本格式的兼容
- 解析失败时返回null，按新任务处理

## AI完成API集成

### 1. 调用逻辑
```java
// 第一次调用
String aiResponse = completionApiClient.requestCompletion(prompt, userId, null);

// 如果失败，重试一次
if (aiResponse == null) {
    aiResponse = completionApiClient.requestCompletion(prompt, userId, null);
}
```

### 2. 成功处理
- 使用AI返回的优化prompt更新请求参数
- 重置任务状态为`PENDING`
- 更新重试信息
- 任务将被调度器重新处理

### 3. 失败处理
- 记录失败原因
- 增加重试计数
- 标记任务为`FAILED`

## 监控和日志

### 1. 关键日志点
```java
// 开始执行
log.info("开始执行图片生成任务: {}, 重试次数: {}", task.getId(), retryCount);

// 特定错误码处理
log.info("任务 {} 返回错误码 {}, 尝试调用AI完成API", task.getId(), errorCode);

// AI完成API成功
log.info("AI完成API调用成功，更新任务prompt。重试次数: {}", currentRetryCount + 1);

// 重试等待
log.info("等待1秒后进行第{}次重试", retryCount + 1);

// 最终失败
log.error("图片生成失败，重试{}次后仍然失败", MAX_RETRY_COUNT);
```

### 2. 监控指标
- 任务重试次数分布
- 特定错误码出现频率
- AI完成API调用成功率
- 最终失败任务的失败原因

## 测试覆盖

### 1. 单元测试场景
- ✅ 成功执行无重试
- ✅ 普通异常重试后成功
- ✅ 达到最大重试次数失败
- ✅ 特定错误码AI完成API成功
- ✅ 特定错误码AI完成API失败
- ✅ 特定错误码达到最大重试次数
- ✅ 空图片URL重试处理

### 2. 集成测试场景
- 真实API调用重试
- 数据库状态更新验证
- 并发场景下的重试处理

## 配置参数

### 1. 应用配置
```yaml
app:
  concurrency:
    max-retry-count: 3  # 普通重试最大次数
```

### 2. 可扩展配置
```yaml
app:
  retry:
    max-error-code-retry-count: 3  # 特定错误码重试次数
    retry-delay-seconds: 1         # 重试延迟时间
    specific-error-codes:          # 特定错误码列表
      - "50411"
      - "50511"
      - "50412"
      - "50512"
      - "50413"
```

## 优势总结

1. **完整性**: 包含了原有`processTaskWithRetry`的所有重试逻辑
2. **稳定性**: 双重重试机制，提高任务成功率
3. **智能性**: 特定错误码使用AI优化prompt
4. **可观测性**: 详细的日志记录和状态跟踪
5. **可测试性**: 完整的单元测试覆盖
6. **可维护性**: 清晰的方法结构和错误处理

## 部署注意事项

1. **依赖检查**: 确保`CompletionApiClient`正常工作
2. **配置验证**: 验证AI完成API的配置和密钥
3. **监控设置**: 设置重试相关的监控告警
4. **性能测试**: 验证重试机制对系统性能的影响

这个实现确保了图片生成任务在各种异常情况下都能得到适当的处理，大大提高了系统的稳定性和成功率。
