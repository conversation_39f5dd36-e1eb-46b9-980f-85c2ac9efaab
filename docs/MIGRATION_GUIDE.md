# 迁移指南：从MQ+Redis信号量到XXL-JOB+数据库

## 迁移概述

本指南将帮助你从现有的MQ+Redis信号量架构平滑迁移到XXL-JOB+数据库架构。

## 迁移前准备

### 1. 备份数据
```bash
# 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份配置文件
cp application.yml application.yml.backup
```

### 2. 环境检查
- 确认XXL-JOB调度中心正常运行
- 确认数据库连接正常
- 确认应用有足够的线程池资源

## 迁移步骤

### 第一阶段：准备新组件（不影响现有功能）

#### 1. 添加新的服务类
将以下文件添加到项目中：
- `DatabaseConcurrencyControlService.java`
- `StableImageGenerationService.java`
- `SimpleImageTaskService.java`
- `ImageTaskSchedulerJob.java`
- `TaskTimeoutCleanupJob.java`
- `SystemMonitorController.java`
- `ConcurrencyControlConfig.java`

#### 2. 添加数据库索引
```sql
-- 优化查询性能的索引
CREATE INDEX idx_task_status_position ON ai_image_task_queue(task_status, global_queue_position);
CREATE INDEX idx_task_status_retry ON ai_image_task_queue(task_status, retry_count, create_time);
CREATE INDEX idx_processing_timeout ON ai_image_task_queue(task_status, update_time);
```

#### 3. 更新配置文件
在application.yml中添加新配置：
```yaml
app:
  concurrency:
    max-concurrent-tasks: 5
    task-timeout-minutes: 10
    max-retry-count: 3
    retry-delay-seconds: 30
    # 先设置为false，保持现有架构
    enable-database-concurrency-control: false
    
    scheduler:
      main-scheduler-interval-seconds: 30
      high-priority-scheduler-interval-seconds: 10
      statistics-interval-minutes: 5
      max-tasks-per-schedule: 10
```

### 第二阶段：配置XXL-JOB任务（并行运行）

#### 1. 在XXL-JOB管理界面添加任务组
创建任务组：`image-task-group`

#### 2. 添加调度任务
| 任务名称 | JobHandler | Cron表达式 | 状态 |
|---------|-----------|-----------|------|
| imageTaskScheduler | imageTaskScheduler | 0/30 * * * * ? | 停止 |
| editImageTaskScheduler | editImageTaskScheduler | 0/20 * * * * ? | 停止 |
| highPriorityImageTaskScheduler | highPriorityImageTaskScheduler | 0/10 * * * * ? | 停止 |
| imageTaskStatistics | imageTaskStatistics | 0 */5 * * * ? | 启动 |
| taskTimeoutCleanup | taskTimeoutCleanup | 0 */2 * * * ? | 停止 |
| systemStatusMonitor | systemStatusMonitor | 0 */5 * * * ? | 启动 |

**注意**: 先只启动监控类任务，处理类任务暂时停止

#### 3. 验证新组件
```bash
# 检查监控接口是否正常
curl http://localhost:8080/api/system/monitor/concurrency-status
curl http://localhost:8080/api/system/monitor/health

# 查看XXL-JOB任务执行日志
# 确认imageTaskStatistics和systemStatusMonitor正常执行
```

### 第三阶段：灰度切换（小流量测试）

#### 1. 修改任务提交逻辑
在ImageTaskQueueServiceImpl中添加开关逻辑：
```java
@Value("${app.concurrency.enable-database-concurrency-control:false}")
private boolean enableDatabaseControl;

@Autowired
private SimpleImageTaskService simpleImageTaskService;

public AiImageTaskQueuePo queueGenerateTask(String sessionId, VolcengineImageRequest request, String taskType) {
    if (enableDatabaseControl) {
        // 使用新架构
        return simpleImageTaskService.submitImageTask(sessionId, request, taskType);
    } else {
        // 使用原有架构
        return originalQueueGenerateTask(sessionId, request, taskType);
    }
}
```

#### 2. 小流量测试
```yaml
# 更新配置，启用新架构
app:
  concurrency:
    enable-database-concurrency-control: true
```

#### 3. 启动XXL-JOB处理任务
在XXL-JOB管理界面启动：
- `taskTimeoutCleanup`
- `imageTaskScheduler`
- `editImageTaskScheduler`
- `highPriorityImageTaskScheduler`

#### 4. 监控测试结果
- 观察任务处理日志
- 检查任务成功率
- 监控系统性能
- 验证并发控制是否正常

### 第四阶段：全量切换

#### 1. 确认测试结果
- 新架构运行稳定
- 任务处理正常
- 性能满足要求
- 监控数据正常

#### 2. 停止MQ消费者
```java
// 在ImageGenerationTaskListener中添加开关
@Value("${app.concurrency.enable-database-concurrency-control:false}")
private boolean enableDatabaseControl;

@Override
public Action consume(Message message, ConsumeContext context) {
    if (enableDatabaseControl) {
        log.info("数据库并发控制已启用，跳过MQ消息处理");
        return Action.CommitMessage;
    }
    
    // 原有处理逻辑...
}
```

#### 3. 清理旧代码（可选）
在确认新架构稳定运行一段时间后，可以清理：
- MQ相关代码
- Redis信号量相关代码
- 旧的任务处理服务

### 第五阶段：优化和监控

#### 1. 性能优化
- 根据实际负载调整并发数
- 优化调度间隔
- 调整线程池参数

#### 2. 监控告警
设置关键指标监控：
- 待处理任务数量
- 任务处理成功率
- 系统并发状态
- 任务处理耗时

#### 3. 运维工具
- 定期执行数据库性能检查
- 监控XXL-JOB任务执行状态
- 设置异常情况告警

## 回滚方案

如果迁移过程中出现问题，可以快速回滚：

### 1. 紧急回滚
```yaml
# 立即关闭新架构
app:
  concurrency:
    enable-database-concurrency-control: false
```

### 2. 停止XXL-JOB任务
在XXL-JOB管理界面停止所有新添加的任务。

### 3. 重启MQ消费者
确保MQ消费者正常工作。

### 4. 验证回滚结果
- 检查任务是否正常处理
- 确认MQ消息消费正常
- 验证Redis信号量工作正常

## 验证清单

### 功能验证
- [ ] 任务提交正常
- [ ] 任务调度正常
- [ ] 任务处理正常
- [ ] 并发控制正常
- [ ] 重试机制正常
- [ ] 超时清理正常

### 性能验证
- [ ] 任务处理速度满足要求
- [ ] 系统资源使用正常
- [ ] 数据库性能正常
- [ ] 无内存泄漏

### 监控验证
- [ ] 监控接口正常
- [ ] XXL-JOB任务执行正常
- [ ] 日志输出正常
- [ ] 告警机制正常

## 常见问题

### Q1: 迁移后任务处理变慢了？
A: 检查调度间隔设置，可以适当缩短间隔时间。

### Q2: 出现任务重复处理？
A: 检查数据库事务隔离级别，确保并发控制正常。

### Q3: XXL-JOB任务执行失败？
A: 检查应用日志，确认JobHandler注册正常。

### Q4: 监控接口返回异常？
A: 检查数据库连接，确认相关服务正常启动。

## 联系支持

如果在迁移过程中遇到问题，请：
1. 查看应用日志
2. 检查XXL-JOB执行日志
3. 确认数据库状态
4. 联系技术支持团队
