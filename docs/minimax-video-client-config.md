# MiniMax视频生成客户端配置文档

## 配置参数

在 `application.yml` 或 `application.properties` 中添加以下配置：

### YAML配置示例

```yaml
# MiniMax视频生成API配置
minimax:
  api:
    # API密钥（必填）
    key: "your-minimax-api-key-here"
    
    # API接口地址（可选，默认为官方地址）
    url: "https://api.minimaxi.com/v1/video_generation"
    
    # 状态查询接口地址（可选，默认为官方地址）
    status-url: "https://api.minimaxi.com/v1/video_generation/status"
    
    # 请求超时时间（秒，可选，默认60秒）
    timeout: 60
```

### Properties配置示例

```properties
# MiniMax视频生成API配置
minimax.api.key=your-minimax-api-key-here
minimax.api.url=https://api.minimaxi.com/v1/video_generation
minimax.api.status-url=https://api.minimaxi.com/v1/video_generation/status
minimax.api.timeout=60
```

## 支持的模型

| 模型名称 | 描述 | 支持功能 |
|---------|------|---------|
| MiniMax-Hailuo-02 | 最新的海螺模型 | 文生视频、图生视频、运镜控制 |
| T2V-01-Director | 导演版文生视频 | 文生视频、运镜控制 |
| I2V-01-Director | 导演版图生视频 | 图生视频、运镜控制 |
| S2V-01 | 主体参考视频生成 | 主体参考生成 |
| I2V-01 | 图生视频基础版 | 图生视频 |
| I2V-01-live | 图生视频实时版 | 图生视频 |
| T2V-01 | 文生视频基础版 | 文生视频 |

## 分辨率和时长支持

### 01系列模型
- **分辨率**: 720P（固定）
- **时长**: 6秒（固定）

### 02系列模型（MiniMax-Hailuo-02）
- **分辨率**: 768P、1080P
- **时长**: 
  - 768P: 6秒、10秒
  - 1080P: 6秒

## 运镜控制指令

### 基础运镜指令

| 类型 | 指令 | 描述 |
|------|------|------|
| 左右移 | `[左移]` | 镜头向左移动 |
| 左右移 | `[右移]` | 镜头向右移动 |
| 左右摇 | `[左摇]` | 镜头向左摇摆 |
| 左右摇 | `[右摇]` | 镜头向右摇摆 |
| 推拉 | `[推进]` | 镜头推近 |
| 推拉 | `[拉远]` | 镜头拉远 |
| 升降 | `[上升]` | 镜头上升 |
| 升降 | `[下降]` | 镜头下降 |
| 上下摇 | `[上摇]` | 镜头向上摇摆 |
| 上下摇 | `[下摇]` | 镜头向下摇摆 |
| 变焦 | `[变焦推近]` | 变焦推近 |
| 变焦 | `[变焦拉远]` | 变焦拉远 |
| 晃动 | `[晃动]` | 镜头晃动 |
| 跟随 | `[跟随]` | 跟随拍摄 |
| 固定 | `[固定]` | 固定镜头 |

### 运镜使用方式

1. **单一运镜**: `[左摇]`
2. **组合运镜**: `[左摇,右移]` - 同时生效
3. **先后运镜**: `描述1[左摇]，描述2[右移]` - 先后生效

## 回调配置

### 回调URL验证

当设置回调URL时，MiniMax会先发送验证请求：

```json
{
  "challenge": "1a3cs1j-601a-118y-ch52-o48911eabc3u"
}
```

需要在3秒内返回相同的challenge值：

```json
{
  "challenge": "1a3cs1j-601a-118y-ch52-o48911eabc3u"
}
```

### 回调通知格式

任务状态变化时会收到回调通知：

```json
{
  "task_id": "115334141465231360",
  "status": "success",
  "file_id": "205258526306433",
  "video_url": "https://example.com/video.mp4",
  "base_resp": {
    "status_code": 0,
    "status_msg": "success"
  }
}
```

## 错误码说明

| 状态码 | 描述 | 解决方案 |
|--------|------|----------|
| 0 | 请求成功 | - |
| 1002 | 触发限流 | 稍后重试 |
| 1004 | 账号鉴权失败 | 检查API-Key |
| 1008 | 账号余额不足 | 充值账户 |
| 1026 | 内容涉及敏感信息 | 调整提示词 |
| 2013 | 参数异常 | 检查请求参数 |
| 2049 | 无效的API Key | 检查API Key |

## 使用建议

### 1. 提示词优化
- 启用 `prompt_optimizer` 可以自动优化提示词
- 关闭优化器时需要提供更精细的描述

### 2. 运镜控制
- 建议组合运镜指令不超过3个
- 运镜指令与自然语言描述可同时使用

### 3. 图片要求
- 格式：JPG/JPEG/PNG/WebP
- 长宽比：大于2:5、小于5:2
- 短边像素：大于300px
- 文件大小：不大于20MB

### 4. 性能优化
- 使用异步方法避免阻塞
- 合理设置轮询间隔和超时时间
- 批量请求时控制并发数量

### 5. 错误处理
- 实现重试机制处理网络异常
- 记录详细日志便于问题排查
- 设置合理的超时时间
