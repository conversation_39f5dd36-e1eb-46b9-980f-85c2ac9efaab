# VideoGenerationQueueService 集成 MinimaxFalClient 修改说明

## 修改概述

本次修改将 `VideoGenerationQueueServiceImpl` 中的 `submitMiniMaxVideoTask` 方法进行了重构，对于图像到视频生成的场景，使用新的 `MinimaxFalClient` 替代原来的 `MiniMaxVideoApiClient`。

## 修改背景

- **原有实现**：使用 `MiniMaxVideoApiClient` 调用 MiniMax 原生 API
- **新的实现**：对于图像到视频场景，使用 `MinimaxFalClient` 调用 FAL AI 的 MiniMax Hailuo-02 服务
- **保持兼容**：文本到视频场景仍使用原来的 `MiniMaxVideoApiClient`

## 修改详情

### 1. 依赖注入修改

**添加的导入**：
```java
import com.wlink.agent.client.MinimaxFalClient;
import com.wlink.agent.client.model.minimax.MinimaxHailuo02ImageToVideoInput;
import com.wlink.agent.client.model.minimax.MinimaxHailuo02ImageToVideoOutput;
```

**添加的依赖注入**：
```java
private final MinimaxFalClient minimaxFalClient;
```

### 2. submitMiniMaxVideoTask 方法重构

#### 原有逻辑
```java
// 根据是否有首帧和尾帧图片选择不同的API方法
if (StringUtils.hasText(task.getFirstFrameImage()) && StringUtils.hasText(task.getLastFrameImage())) {
    // 首尾帧生成（实际上MiniMax不支持，使用首帧）
} else if (StringUtils.hasText(task.getFirstFrameImage())) {
    // 图生视频
} else {
    // 文生视频
}
// 统一使用 miniMaxVideoApiClient.submitVideoGeneration()
```

#### 新的逻辑
```java
// 根据是否有首帧图片选择不同的API客户端
if (StringUtils.hasText(task.getFirstFrameImage())) {
    // 图生视频 - 使用 MinimaxFalClient
    // 构建 FAL API 请求参数并调用
    // 直接完成任务（因为FAL API同步返回结果）
} else {
    // 文生视频 - 使用原来的 MiniMaxVideoApiClient
    // 保持原有的异步处理逻辑
}
```

### 3. 关键变化

#### 图像到视频场景（使用 MinimaxFalClient）

1. **参数构建**：
   ```java
   MinimaxHailuo02ImageToVideoInput falInput = MinimaxHailuo02ImageToVideoInput.builder()
           .prompt(task.getPrompt())
           .imageUrl(MediaUrlPrefixUtil.getMediaUrl(task.getFirstFrameImage()))
           .duration(String.valueOf(task.getDuration()))
           .promptOptimizer(true)
           .build();
   ```

2. **提交异步请求**：
   ```java
   var queueStatus = minimaxFalClient.submitImageToVideoRequest(falInput);
   ```

3. **保存 request_id**：
   ```java
   // 更新任务信息，保存 request_id
   taskInfo.put(KEY_VIDEO_TASK_ID, queueStatus.getRequestId());
   taskInfo.put(KEY_VIDEO_TYPE, "IMAGE_TO_VIDEO_FAL");
   updateTaskInfo(task.getId(), taskInfo);

   // 更新任务状态为处理中
   startProcessing(task.getId(), queueStatus.getRequestId());
   ```

#### 文本到视频场景（保持原有逻辑）

继续使用 `MiniMaxVideoApiClient`，保持原有的异步处理流程。

### 4. 特殊处理

#### 首尾帧场景
- **原有行为**：MiniMax 不支持首尾帧，使用首帧图生视频
- **新的行为**：FAL API 也不支持首尾帧，记录警告日志并忽略尾帧图片

```java
if (StringUtils.hasText(task.getLastFrameImage())) {
    log.warn("MiniMax FAL API 不支持首尾帧生成，将忽略尾帧图片: taskId={}", task.getId());
}
```

### 4. 定时任务处理

新增了 `MinimaxFalVideoStatusCheckJob` 定时任务来处理异步结果：

1. **状态轮询**：每分钟检查处理中的 FAL 任务状态
2. **结果获取**：任务完成后获取视频URL
3. **文件上传**：将视频上传到 OSS 存储
4. **数据更新**：更新相关数据库表状态

## 优势分析

### 1. 架构优势
- **真正异步**：不阻塞主线程，提高系统吞吐量
- **解耦处理**：任务提交和结果处理分离，提高系统稳定性
- **统一管理**：通过定时任务统一管理所有 FAL 任务状态

### 2. 可靠性优势
- **FAL API**：基于队列的稳定服务，支持高并发
- **重试机制**：完整的重试和错误处理机制
- **状态追踪**：详细的状态追踪和日志记录

### 3. 功能优势
- **专业化**：FAL API 专门针对图像到视频优化
- **质量提升**：可能获得更好的视频生成质量
- **扩展性**：易于扩展支持更多 FAL 服务

## 兼容性保证

### 1. 接口兼容
- 对外接口保持不变
- 调用方无需修改代码

### 2. 功能兼容
- 文本到视频功能完全保持原有逻辑
- 图像到视频功能增强但保持兼容

### 3. 数据兼容
- 任务记录格式保持兼容
- 新增 `IMAGE_TO_VIDEO_FAL` 类型标识

## 新增组件

### 1. MinimaxFalVideoStatusCheckJob

**功能**：定时检查 MiniMax FAL 视频生成任务状态

**执行频率**：每分钟执行一次

**主要流程**：
```java
1. 查询所有处理中的 FAL 任务
2. 遍历每个任务，调用 FAL API 查询状态
3. 根据状态进行处理：
   - COMPLETED: 获取结果 → 上传OSS → 更新数据库
   - IN_QUEUE/IN_PROGRESS: 继续等待
   - 其他状态: 标记为失败
4. 错误处理和重试机制
```

**涉及的数据库表**：
- `ai_video_generation`: 主要任务表
- `ai_canvas_shot`: 分镜状态表
- `ai_canvas_video`: 画布视频表
- `ai_canvas_material`: 画布材料表

### 2. XXL-Job 配置

**任务配置**：
- 任务名称：`minimaxFalVideoStatusCheck`
- Cron表达式：`0 */1 * * * ?` (每分钟执行)
- 运行模式：BEAN
- 超时时间：300秒

## 测试覆盖

### 1. 单元测试
- `testSubmitMiniMaxVideoTask_ImageToVideo_UsesFalClient()`：验证图生视频使用新客户端
- `testSubmitMiniMaxVideoTask_TextToVideo_UsesOriginalClient()`：验证文生视频使用原客户端
- `testSubmitMiniMaxVideoTask_ImageToVideoWithLastFrame_IgnoresLastFrame()`：验证首尾帧处理

### 2. 集成测试
建议添加集成测试验证：
- 端到端的图像到视频生成流程
- 定时任务的状态检查逻辑
- 错误场景的处理
- 性能对比测试

## 配置要求

### 1. 必需配置
确保 `application.properties` 中配置了 FAL API 密钥：
```properties
fal.ai.api-key=your-fal-api-key-here
```

### 2. 可选配置
可以根据需要调整 FAL 客户端的超时和重试参数。

## 监控和日志

### 1. 新增日志
- 使用哪个客户端的决策日志
- FAL API 调用成功/失败日志
- 首尾帧忽略警告日志

### 2. 监控指标
建议监控：
- 图生视频成功率对比
- 处理时间对比
- 错误率统计

## 回滚方案

如果需要回滚到原有实现：

1. **代码回滚**：恢复 `submitMiniMaxVideoTask` 方法到原有实现
2. **依赖移除**：移除 `MinimaxFalClient` 相关依赖
3. **配置清理**：移除 FAL API 相关配置

## 后续优化建议

### 1. 短期优化
- 添加更详细的性能监控
- 优化错误处理和重试逻辑
- 添加配置开关控制使用哪个客户端

### 2. 长期优化
- 考虑将文本到视频也迁移到 FAL API（如果支持）
- 实现智能路由，根据负载选择最优客户端
- 添加 A/B 测试支持，对比两种实现的效果

## 风险评估

### 1. 低风险
- 文本到视频功能完全不受影响
- 有完整的错误处理和回滚机制

### 2. 中等风险
- 新的 FAL API 可能有不同的行为特征
- 需要监控初期的稳定性

### 3. 缓解措施
- 充分的测试覆盖
- 详细的监控和告警
- 快速回滚能力
