# XXL-JOB调度器架构方案

## 概述

本方案彻底去掉了MQ的复杂性，使用XXL-JOB定时任务扫描数据库表来处理图片生成任务。这种架构更加简单、稳定、易维护。

## 架构对比

### 原有架构（MQ + Redis信号量）
```
任务提交 → 保存数据库 → 发送MQ消息 → MQ消费者 → Redis信号量控制 → 处理任务
```

**问题**：
- MQ消息可能丢失
- Redis信号量可能泄漏
- 组件多，故障点多
- 调试困难

### 新架构（XXL-JOB + 数据库）
```
任务提交 → 保存数据库 → XXL-JOB定时扫描 → 数据库并发控制 → 处理任务
```

**优势**：
- 组件少，架构简单
- 数据库保证一致性
- 易于监控和调试
- 支持任务优先级

## 核心组件

### 1. ImageTaskSchedulerJob - 任务调度器
负责扫描数据库并调度任务：

#### 主要任务：
- `imageTaskScheduler`: 每30秒扫描待处理的生成任务（排除编辑任务）
- `editImageTaskScheduler`: 每20秒专门处理EDIT和CANVAS_EDIT类型的任务
- `highPriorityImageTaskScheduler`: 每10秒处理高优先级任务
- `imageTaskStatistics`: 每5分钟生成统计报告
- `cleanupHistoryTasks`: 每天清理历史数据

#### 调度策略：
- **生成任务**: 按全局队列位置排序（FIFO），排除EDIT和CANVAS_EDIT类型
- **编辑任务**: 专门调度器处理EDIT和CANVAS_EDIT类型，按重试次数和创建时间排序
- **高优先级任务**: 按重试次数排序（重试少的优先），排除编辑任务
- **并发控制**: 检查可用槽位，不超量调度

### 2. SimpleImageTaskService - 任务提交服务
简化的任务提交逻辑：
- 直接保存到数据库
- 自动分配队列位置
- 支持批量提交
- 提供统计信息

### 3. DatabaseConcurrencyControlService - 并发控制
基于数据库的并发控制：
- 乐观锁获取执行权限
- 自动超时清理
- 实时状态监控

### 4. StableImageGenerationService - 任务处理
稳定的任务处理服务：
- 去掉MQ依赖
- 完善的异常处理
- 自动重试机制

## 配置说明

### application.yml 配置
```yaml
app:
  concurrency:
    # 最大并发任务数
    max-concurrent-tasks: 5
    # 任务超时时间（分钟）
    task-timeout-minutes: 10
    # 最大重试次数
    max-retry-count: 3
    # 重试延迟基础时间（秒）
    retry-delay-seconds: 30
    # 启用数据库并发控制
    enable-database-concurrency-control: true
    
    # 调度器配置
    scheduler:
      # 主调度器执行间隔（秒）
      main-scheduler-interval-seconds: 30
      # 高优先级调度器执行间隔（秒）
      high-priority-scheduler-interval-seconds: 10
      # 统计任务执行间隔（分钟）
      statistics-interval-minutes: 5
      # 每次调度处理的最大任务数
      max-tasks-per-schedule: 10
```

### XXL-JOB 任务配置

| 任务名称 | Cron表达式 | 描述 |
|---------|-----------|------|
| imageTaskScheduler | 0/30 * * * * ? | 每30秒扫描待处理的生成任务 |
| editImageTaskScheduler | 0/20 * * * * ? | 每20秒处理编辑任务(EDIT/CANVAS_EDIT) |
| highPriorityImageTaskScheduler | 0/10 * * * * ? | 每10秒处理高优先级任务 |
| imageTaskStatistics | 0 */5 * * * ? | 每5分钟生成统计 |
| taskTimeoutCleanup | 0 */2 * * * ? | 每2分钟清理超时任务 |
| systemStatusMonitor | 0 */5 * * * ? | 每5分钟系统状态监控 |
| cleanupHistoryTasks | 0 0 2 * * ? | 每天凌晨2点清理历史数据 |

## 工作流程

### 1. 任务提交流程
```
1. 用户提交图片生成请求
2. SimpleImageTaskService.submitImageTask()
3. 构建请求参数JSON
4. 分配全局队列位置
5. 保存到ai_image_task_queue表，状态为PENDING
6. 返回任务ID给用户
```

### 2. 任务调度流程
```
1. XXL-JOB触发imageTaskScheduler
2. 查询系统可用并发槽位
3. 按优先级查询PENDING状态任务
4. 异步提交任务到线程池处理
5. 记录调度日志和统计信息
```

### 3. 任务处理流程
```
1. StableImageGenerationService.processImageTask()
2. DatabaseConcurrencyControlService.tryAcquireTask()
3. 数据库乐观锁：PENDING → PROCESSING
4. 调用图片生成API
5. 更新任务状态：PROCESSING → COMPLETED/FAILED
6. 自动释放并发槽位
```

### 4. 异常恢复流程
```
1. TaskTimeoutCleanupJob定时执行
2. 查询超时的PROCESSING任务
3. 重置状态：PROCESSING → PENDING
4. 等待下次调度重新处理
```

## 监控和管理

### 1. 系统状态接口
```bash
# 查看并发状态
GET /api/system/monitor/concurrency-status

# 系统健康检查
GET /api/system/monitor/health

# 手动清理超时任务
POST /api/system/monitor/cleanup-timeout-tasks
```

### 2. 日志监控
关键日志关键词：
- `图片任务调度完成`: 调度器执行结果
- `任务 {} 成功获取执行权限`: 并发控制成功
- `图片生成任务完成`: 任务处理完成
- `清理超时任务完成`: 超时清理结果

### 3. 数据库监控
关键SQL监控：
```sql
-- 查看各状态任务数量
SELECT task_status, COUNT(*) FROM ai_image_task_queue GROUP BY task_status;

-- 查看处理中任务的处理时间
SELECT id, TIMESTAMPDIFF(MINUTE, update_time, NOW()) as processing_minutes 
FROM ai_image_task_queue 
WHERE task_status = 'PROCESSING' 
ORDER BY processing_minutes DESC;

-- 查看待处理任务队列
SELECT COUNT(*) as pending_count FROM ai_image_task_queue WHERE task_status = 'PENDING';
```

## 性能优化

### 1. 数据库优化
```sql
-- 添加索引优化查询性能
CREATE INDEX idx_task_status_position ON ai_image_task_queue(task_status, global_queue_position);
CREATE INDEX idx_task_status_retry ON ai_image_task_queue(task_status, retry_count, create_time);
CREATE INDEX idx_processing_timeout ON ai_image_task_queue(task_status, update_time);
```

### 2. 调度器优化
- 根据系统负载动态调整调度间隔
- 使用线程池异步处理，避免阻塞调度器
- 批量查询任务，减少数据库访问

### 3. 并发控制优化
- 使用数据库连接池
- 优化事务范围
- 合理设置超时时间

## 部署步骤

### 1. 数据库准备
确保ai_image_task_queue表有必要的索引：
```sql
-- 检查现有索引
SHOW INDEX FROM ai_image_task_queue;

-- 添加必要索引（如果不存在）
CREATE INDEX idx_task_status_position ON ai_image_task_queue(task_status, global_queue_position);
```

### 2. 配置更新
更新application.yml配置文件，启用新架构。

### 3. XXL-JOB配置
在XXL-JOB管理界面配置所有定时任务。

### 4. 验证部署
1. 提交测试任务
2. 观察调度器日志
3. 检查任务处理结果
4. 验证监控接口

## 故障排查

### 1. 任务不被调度
- 检查XXL-JOB任务是否正常运行
- 查看调度器日志
- 确认任务状态是否为PENDING

### 2. 任务处理失败
- 查看任务处理日志
- 检查外部API是否正常
- 确认数据库连接是否正常

### 3. 并发控制异常
- 查看并发状态接口
- 检查超时清理是否正常
- 确认数据库事务是否正常

## 优势总结

1. **简单性**: 去掉MQ，架构更简单
2. **稳定性**: 基于数据库，数据一致性有保障
3. **可观测性**: 所有状态都在数据库中，易于监控
4. **可维护性**: 组件少，逻辑清晰，易于维护
5. **灵活性**: 支持任务优先级，可动态调整参数
6. **可靠性**: 自动超时恢复，不会丢失任务
