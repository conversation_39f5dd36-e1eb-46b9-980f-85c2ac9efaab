# 任务类型调度策略

## 概述

为了更好地管理不同类型的图片任务，我们将任务分为两大类进行调度：**生成任务**和**编辑任务**。

## 任务分类

### 生成任务（Generation Tasks）
包括以下任务类型：
- `GENERATE` - 基础图片生成
- `RETAIN` - 角色保留任务
- `REDRAW` - 图片重绘任务
- `GENERATE_DOUBAO` - 豆包生成任务
- `GENERATE_FLUX` - Flux生成任务
- `GENERATE_CANVAS` - 画布生成任务

### 编辑任务（Edit Tasks）
包括以下任务类型：
- `EDIT` - 图片编辑任务
- `EDIT_CANVAS` - 画布编辑任务

## 调度策略

### 1. 主任务调度器 (imageTaskScheduler)
- **执行频率**: 每30秒
- **处理任务**: 生成任务（排除编辑任务）
- **排序规则**: 按全局队列位置排序（FIFO）
- **过滤条件**: `NOT IN ('EDIT', 'CANVAS_EDIT')`

```sql
SELECT * FROM ai_image_task_queue 
WHERE task_status = 'PENDING' 
  AND task_type NOT IN ('EDIT', 'CANVAS_EDIT')
ORDER BY global_queue_position ASC 
LIMIT ?
```

### 2. 编辑任务调度器 (editImageTaskScheduler)
- **执行频率**: 每20秒
- **处理任务**: 编辑任务（EDIT和CANVAS_EDIT）
- **排序规则**: 按重试次数和创建时间排序
- **过滤条件**: `IN ('EDIT', 'CANVAS_EDIT')`

```sql
SELECT * FROM ai_image_task_queue 
WHERE task_status = 'PENDING' 
  AND task_type IN ('EDIT', 'CANVAS_EDIT')
ORDER BY retry_count ASC, create_time ASC 
LIMIT ?
```

### 3. 高优先级任务调度器 (highPriorityImageTaskScheduler)
- **执行频率**: 每10秒
- **处理任务**: 生成任务中的高优先级任务（重试任务）
- **排序规则**: 按重试次数和创建时间排序
- **过滤条件**: `NOT IN ('EDIT', 'CANVAS_EDIT')`

```sql
SELECT * FROM ai_image_task_queue 
WHERE task_status = 'PENDING' 
  AND task_type NOT IN ('EDIT', 'CANVAS_EDIT')
ORDER BY retry_count ASC, create_time ASC 
LIMIT ?
```

## 设计原理

### 1. 任务隔离
- **生成任务**和**编辑任务**使用不同的调度器
- 避免一种类型的任务堵塞另一种类型
- 可以针对不同类型设置不同的调度频率

### 2. 优先级处理
- 编辑任务通常是用户交互操作，需要更快的响应
- 编辑任务调度器频率更高（20秒 vs 30秒）
- 重试任务优先处理，避免长时间等待

### 3. 负载均衡
- 所有调度器共享同一个并发控制池
- 根据系统可用槽位动态调度
- 避免系统过载

## 监控和统计

### 任务统计格式
```
图片任务统计 - 待处理: 50 (生成: 35, 编辑: 15), 处理中: 3, 已完成: 1200, 失败: 8
```

### 关键指标
- **待处理生成任务数**: 反映系统生成任务负载
- **待处理编辑任务数**: 反映用户交互任务负载
- **处理中任务数**: 反映当前系统并发使用情况

### 告警规则
- 待处理任务总数 > 100：系统负载过高
- 待处理编辑任务数 > 50：编辑任务堵塞
- 处理中任务数 > 10：可能存在任务堵塞

## 配置参数

### XXL-JOB任务配置
```
imageTaskScheduler:
  - Cron: 0/30 * * * * ?
  - 描述: 处理生成任务

editImageTaskScheduler:
  - Cron: 0/20 * * * * ?
  - 描述: 处理编辑任务

highPriorityImageTaskScheduler:
  - Cron: 0/10 * * * * ?
  - 描述: 处理高优先级任务
```

### 应用配置
```yaml
app:
  concurrency:
    max-concurrent-tasks: 5  # 总并发数
    scheduler:
      max-tasks-per-schedule: 10  # 每次调度最大任务数
```

## 扩展性

### 添加新任务类型
如果需要添加新的任务类型，需要考虑：

1. **归类决策**: 属于生成任务还是编辑任务？
2. **优先级**: 是否需要特殊的优先级处理？
3. **调度频率**: 是否需要独立的调度器？

### 示例：添加新任务类型
```java
// 如果添加 SUPER_RESOLUTION 任务类型
// 1. 在 TaskType 枚举中添加
SUPER_RESOLUTION("SUPER_RESOLUTION")

// 2. 决定归类（假设归为生成任务）
// 无需修改调度器代码，会自动被 imageTaskScheduler 处理

// 3. 如果需要特殊处理，可以创建专门的调度器
@XxlJob("superResolutionTaskScheduler")
public ReturnT<String> scheduleSuperResolutionTasks(String param) {
    // 专门处理超分辨率任务
}
```

## 最佳实践

### 1. 任务提交
- 根据业务需求选择合适的任务类型
- 编辑任务应该有更高的优先级
- 批量任务建议分批提交

### 2. 监控运维
- 定期检查各类型任务的处理情况
- 根据业务负载调整调度频率
- 设置合理的告警阈值

### 3. 性能优化
- 根据实际情况调整并发数
- 优化数据库查询性能
- 合理设置任务超时时间

## 故障排查

### 编辑任务处理慢
1. 检查 `editImageTaskScheduler` 是否正常运行
2. 查看编辑任务队列长度
3. 确认并发槽位是否被占满

### 生成任务堵塞
1. 检查 `imageTaskScheduler` 执行情况
2. 查看是否有长时间处理的任务
3. 考虑增加并发数或调整调度频率

### 任务分类错误
1. 检查任务类型枚举定义
2. 确认任务提交时的类型设置
3. 验证调度器的过滤条件
