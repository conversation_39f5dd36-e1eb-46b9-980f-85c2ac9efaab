# CANVAS_EDIT任务定时处理修改

## 修改概述

已成功修改`ImageTaskScanJob.java`中的`executeSingleTask`方法，专门处理`CANVAS_EDIT`类型的图像编辑任务。该方法现在会查询最近5条待运行的`CANVAS_EDIT`任务，并异步调用豆包图像编辑API进行处理。

## 主要修改内容

### 1. 添加依赖注入

```java
// 添加导入
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.DoubaoImageEditApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;

// 添加依赖注入
@Autowired
private DoubaoImageEditApiClient doubaoImageEditApiClient;

@Autowired
private ObjectMapper objectMapper;

@Autowired
private ThreadPoolTaskExecutor taskExecutor;
```

### 2. 修改executeSingleTask方法

**修改前**：处理单个任务，需要账号分配
**修改后**：专门处理CANVAS_EDIT任务，查询最近5条并异步执行

```java
@XxlJob("singleTaskExecuteJobHandler")
public ReturnT<String> executeSingleTask() {
    log.info("开始执行CANVAS_EDIT类型的图像编辑任务");

    try {
        // 1. 查询最近5条CANVAS_EDIT类型的待处理任务
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TASK_STATUS_PENDING)
                .eq(AiImageTaskQueuePo::getTaskType, TaskType.EDIT_CANVAS.getValue())
                .orderByAsc(AiImageTaskQueuePo::getGlobalQueuePosition)
                .last("limit 5"); // 取最近5条记录

        List<AiImageTaskQueuePo> pendingTasks = imageTaskQueueMapper.selectList(queryWrapper);
        if (pendingTasks.isEmpty()) {
            log.info("没有待处理的CANVAS_EDIT任务");
            return ReturnT.SUCCESS;
        }

        // 2. 异步执行每个任务
        for (AiImageTaskQueuePo task : pendingTasks) {
            taskExecutor.execute(() -> processCanvasEditTask(task));
        }

        log.info("已提交{}个CANVAS_EDIT任务到异步执行线程池", pendingTasks.size());
        return ReturnT.SUCCESS;
    } catch (Exception e) {
        log.error("执行CANVAS_EDIT任务处理时发生异常", e);
        return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
    }
}
```

### 3. 新增processCanvasEditTask方法

```java
private void processCanvasEditTask(AiImageTaskQueuePo task) {
    log.info("开始处理CANVAS_EDIT任务: taskId={}, requestParams={}", task.getId(), task.getRequestParams());

    try {
        // 1. 更新任务状态为处理中
        imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.PROCESSING.getValue(), null, null, null);

        // 2. 解析请求参数
        DoubaoImageEditRequest editRequest = parseRequestParams(task.getRequestParams());
        if (editRequest == null) {
            imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), null, "解析请求参数失败", null);
            return;
        }

        // 3. 调用豆包图像编辑API
        com.wlink.agent.client.model.volcengine.ImageGenerateRes editResult = doubaoImageEditApiClient.editImage(editRequest);

        // 4. 处理结果并更新状态
        if (editResult != null && editResult.getImageUrl() != null && !editResult.getImageUrl().trim().isEmpty()) {
            // 成功
            imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.COMPLETED.getValue(), editResult, null, null);
        } else {
            // 失败
            String errorMessage = editResult != null ? editResult.getMessage() : "豆包图像编辑API返回结果为空";
            imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), editResult, errorMessage, null);
        }

    } catch (Exception e) {
        log.error("处理CANVAS_EDIT任务时发生异常: taskId={}", task.getId(), e);
        imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), null, "处理任务时发生异常: " + e.getMessage(), null);
    }
}
```

### 4. 新增parseRequestParams方法

```java
private DoubaoImageEditRequest parseRequestParams(String requestParams) {
    try {
        // 解析JSON参数为DoubaoImageEditRequest对象
        DoubaoImageEditRequest request = objectMapper.readValue(requestParams, DoubaoImageEditRequest.class);
        
        // 验证必要字段并设置默认值
        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            request.setModel("doubao-seededit-3-0-i2i-250628");
        }
        
        // 验证prompt和image字段
        if (request.getPrompt() == null || request.getPrompt().trim().isEmpty()) {
            return null;
        }
        
        if (request.getImage() == null || request.getImage().trim().isEmpty()) {
            return null;
        }

        // 设置默认值
        if (request.getResponseFormat() == null) request.setResponseFormat("url");
        if (request.getSize() == null) request.setSize("adaptive");
        if (request.getSeed() == null) request.setSeed(-1);
        if (request.getGuidanceScale() == null) request.setGuidanceScale(5.5);
        if (request.getWatermark() == null) request.setWatermark(true);

        return request;

    } catch (Exception e) {
        log.error("解析请求参数时发生异常: requestParams={}", requestParams, e);
        return null;
    }
}
```

## 功能特性

### ✅ 专门处理CANVAS_EDIT任务
- 只查询`task_type`为`CANVAS_EDIT`的任务
- 按照`global_queue_position`排序，优先处理排队靠前的任务

### ✅ 批量异步处理
- 一次查询最多5条待处理任务
- 每个任务都在独立的线程中异步执行
- 避免单个任务失败影响其他任务

### ✅ 完整的参数解析
- 从`ai_image_task_queue.request_params`字段解析JSON参数
- 自动验证必要字段（model、prompt、image）
- 为缺失字段设置合理的默认值

### ✅ 状态管理
- 任务开始时更新状态为`PROCESSING`
- 成功时更新状态为`COMPLETED`
- 失败时更新状态为`FAILED`并记录错误原因

### ✅ 错误处理
- 参数解析失败的处理
- API调用异常的处理
- 详细的错误日志记录

## 数据库交互

### 查询条件
```sql
SELECT * FROM ai_image_task_queue 
WHERE task_status = 'PENDING' 
  AND task_type = 'CANVAS_EDIT' 
ORDER BY global_queue_position ASC 
LIMIT 5;
```

### 状态更新
通过`ImageTaskQueueService.updateTaskStatus`方法更新：
- `task_status`: PROCESSING → COMPLETED/FAILED
- `image_result`: 存储API返回的图片URL
- `error_reason`: 失败时的错误原因

## 请求参数格式

`ai_image_task_queue.request_params`字段应包含以下JSON格式：

```json
{
  "model": "doubao-seededit-3-0-i2i-250628",
  "prompt": "把图中的红色汽车改成蓝色",
  "image": "https://example.com/original-image.jpg",
  "responseFormat": "url",
  "size": "adaptive",
  "seed": -1,
  "guidanceScale": 5.5,
  "watermark": true
}
```

### 必填字段
- `prompt`: 编辑提示词
- `image`: 原始图像URL或Base64编码

### 可选字段（有默认值）
- `model`: 默认"doubao-seededit-3-0-i2i-250628"
- `responseFormat`: 默认"url"
- `size`: 默认"adaptive"
- `seed`: 默认-1
- `guidanceScale`: 默认5.5
- `watermark`: 默认true

## 日志输出

### 成功案例
```
INFO - 开始执行CANVAS_EDIT类型的图像编辑任务
INFO - 获取到5个待处理的CANVAS_EDIT任务
INFO - 已提交5个CANVAS_EDIT任务到异步执行线程池
INFO - 开始处理CANVAS_EDIT任务: taskId=12345, requestParams={"model":"doubao-seededit-3-0-i2i-250628",...}
INFO - 成功解析请求参数: model=doubao-seededit-3-0-i2i-250628, prompt=把图中的红色汽车改成蓝色, image=https://example.com/...
INFO - CANVAS_EDIT任务处理成功: taskId=12345, imageUrl=https://generated-image.jpg
```

### 失败案例
```
ERROR - 解析请求参数失败: taskId=12345, requestParams={"invalid":"json"}
ERROR - CANVAS_EDIT任务处理失败: taskId=12345, error=豆包图像编辑API返回结果为空
ERROR - 处理CANVAS_EDIT任务时发生异常: taskId=12345
```

## 配置要求

### XXL-Job配置
- 任务名称: `singleTaskExecuteJobHandler`
- 执行频率: 建议每30秒执行一次
- 超时时间: 建议300秒

### 线程池配置
确保`ThreadPoolTaskExecutor`有足够的线程处理并发任务：
```properties
# 建议配置
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=20
spring.task.execution.pool.queue-capacity=100
```

## 注意事项

1. **并发控制**: 每次最多处理5个任务，避免系统负载过高
2. **错误重试**: 当前版本不包含重试机制，失败的任务需要手动重新提交
3. **API限流**: 注意豆包API的调用频率限制
4. **内存使用**: Base64格式的图像会占用较多内存
5. **日志监控**: 建议监控错误日志，及时发现问题

## 扩展建议

1. **重试机制**: 为失败的任务添加自动重试功能
2. **优先级处理**: 根据任务优先级调整处理顺序
3. **批量优化**: 考虑批量调用API以提高效率
4. **监控告警**: 添加任务处理失败的告警机制
