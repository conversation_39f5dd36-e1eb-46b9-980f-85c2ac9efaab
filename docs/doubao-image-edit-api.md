# 豆包图像编辑API客户端

## 概述

豆包图像编辑API客户端是基于豆包SeedEdit 3.0模型实现的Java客户端，用于调用豆包图像编辑服务。该客户端支持通过文本提示词对图像进行编辑。

## 功能特性

- ✅ 图像编辑（基于文本提示词）
- ✅ 支持URL和Base64格式的图像输入
- ✅ 自适应图像尺寸
- ✅ 可配置的引导尺度和随机种子
- ✅ 水印控制
- ✅ 完整的错误处理
- ✅ 使用OkHttp进行HTTP调用

## 快速开始

### 1. 配置API密钥

在应用配置文件中配置豆包API相关参数：

```properties
# 豆包图像编辑API配置
doubao.image.edit.api.url=https://ark.cn-beijing.volces.com/api/v3/images/generations
doubao.image.edit.api.key=your-api-key-here
```

### 2. 基本使用

#### 通过控制器接口调用

```bash
# 编辑图像
curl -X POST "http://localhost:8080/agent/doubao/image-edit/edit" \
  -H "Content-Type: application/json" \
  -d '{
    "shotId": 123456789,
    "image": "https://example.com/image.jpg",
    "prompt": "把图中的红色汽车改成蓝色",
    "model": "doubao-seededit-3-0-i2i-250628",
    "responseFormat": "url",
    "size": "adaptive",
    "seed": -1,
    "guidanceScale": 5.5,
    "watermark": true
  }'

# 使用指定API密钥编辑图像
curl -X POST "http://localhost:8080/agent/doubao/image-edit/edit-with-key" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-custom-api-key" \
  -d '{
    "shotId": 123456789,
    "image": "https://example.com/image.jpg",
    "prompt": "把图中的红色汽车改成蓝色"
  }'
```

#### 通过服务类调用

```java
@Autowired
private DoubaoImageEditService doubaoImageEditService;

// 构建请求
DoubaoImageEditReq req = new DoubaoImageEditReq();
req.setShotId(123456789L);
req.setImage("https://example.com/image.jpg");
req.setPrompt("把图中的红色汽车改成蓝色");

// 调用服务
ImageGenerateRes result = doubaoImageEditService.editImage(req);
```

#### 直接使用API客户端

```java
@Autowired
private DoubaoImageEditApiClient apiClient;

// 构建请求
DoubaoImageEditRequest request = new DoubaoImageEditRequest();
request.setModel("doubao-seededit-3-0-i2i-250628");
request.setPrompt("把图中的红色汽车改成蓝色");
request.setImage("https://example.com/image.jpg");

// 调用API
ImageGenerateRes result = apiClient.editImage(request);
```

## API参数说明

### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| shotId | Long | 否 | - | 分镜ID |
| image | String | 是 | - | 需要编辑的图像URL或Base64编码 |
| prompt | String | 是 | - | 编辑提示词 |
| model | String | 否 | doubao-seededit-3-0-i2i-250628 | 模型名称 |
| responseFormat | String | 否 | url | 响应格式(url/b64_json) |
| size | String | 否 | adaptive | 图片尺寸 |
| seed | Integer | 否 | -1 | 随机种子(-1到2147483647) |
| guidanceScale | Double | 否 | 5.5 | 引导尺度(1.0到10.0) |
| watermark | Boolean | 否 | true | 是否添加水印 |

### 图像要求

- **格式**: jpeg、png
- **宽高比**: 在范围 (1/3, 3) 内
- **尺寸**: 宽高长度 > 14px
- **大小**: 不超过 10MB

### 响应格式

```json
{
  "success": true,
  "data": {
    "imageUrl": "https://example.com/generated-image.jpg",
    "code": null,
    "message": null
  },
  "errCode": null,
  "errMessage": null
}
```

## 错误处理

客户端包含完整的错误处理机制：

- **参数验证错误**: 请求参数不符合要求
- **API调用错误**: HTTP请求失败
- **业务逻辑错误**: 豆包API返回的业务错误
- **网络错误**: 网络连接问题

所有错误都会被包装为`BizException`并包含详细的错误信息。

## 配置说明

### 超时配置

HTTP客户端使用OkHttp，默认超时配置：
- 连接超时: 30秒
- 读取超时: 300秒
- 写入超时: 30秒

### API配置

可通过以下配置项自定义API行为：

```properties
# API地址
doubao.image.edit.api.url=https://ark.cn-beijing.volces.com/api/v3/images/generations

# 默认API密钥
doubao.image.edit.api.key=your-default-api-key
```

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在代码中硬编码
2. **图像格式**: 确保输入图像符合格式要求
3. **提示词质量**: 清晰、具体的提示词能获得更好的编辑效果
4. **并发控制**: 注意API调用频率限制
5. **错误重试**: 建议实现适当的重试机制

## 扩展功能

### 自定义API密钥

支持为不同用户或场景使用不同的API密钥：

```java
ImageGenerateRes result = doubaoImageEditService.editImageWithApiKey(req, "custom-api-key");
```

### 批量处理

可以通过循环调用实现批量图像编辑：

```java
List<DoubaoImageEditReq> requests = // 准备请求列表
List<ImageGenerateRes> results = new ArrayList<>();

for (DoubaoImageEditReq req : requests) {
    try {
        ImageGenerateRes result = doubaoImageEditService.editImage(req);
        results.add(result);
    } catch (Exception e) {
        log.error("批量处理失败: {}", e.getMessage());
    }
}
```

## 故障排查

### 常见问题

1. **API密钥无效**: 检查配置文件中的API密钥是否正确
2. **图像格式不支持**: 确认图像格式为jpeg或png
3. **网络连接失败**: 检查网络连接和防火墙设置
4. **请求超时**: 调整超时配置或检查网络状况

### 日志配置

启用详细日志以便调试：

```properties
logging.level.com.wlink.agent.client.DoubaoImageEditApiClient=DEBUG
logging.level.com.wlink.agent.service.impl.DoubaoImageEditServiceImpl=DEBUG
```
