# MiniMax视频生成客户端使用指南

## 快速开始

### 1. 添加配置

在 `application.yml` 中添加配置：

```yaml
minimax:
  api:
    key: "your-minimax-api-key"
```

### 2. 注入客户端

```java
@Resource
private MiniMaxVideoApiClient miniMaxVideoApiClient;
```

### 3. 基础使用

```java
// 文生视频
CompletableFuture<MiniMaxVideoGenerationResponse> future = miniMaxVideoApiClient.textToVideo(
    "MiniMax-Hailuo-02",
    "一只可爱的小猫在花园里玩耍",
    6,
    "1080P"
);

future.thenAccept(response -> {
    if (response.isSuccess()) {
        System.out.println("任务ID: " + response.getTaskId());
    }
});
```

## 详细使用示例

### 文生视频

```java
public void generateTextToVideo() {
    // 基础文生视频
    miniMaxVideoApiClient.textToVideo(
        "MiniMax-Hailuo-02",
        "美丽的日落景色",
        6,
        "1080P"
    ).thenAccept(response -> {
        if (response.isSuccess()) {
            log.info("视频生成任务提交成功: {}", response.getTaskId());
        }
    });
}
```

### 图生视频

```java
public void generateImageToVideo() {
    String imageUrl = "https://example.com/image.jpg";
    
    miniMaxVideoApiClient.imageToVideo(
        "MiniMax-Hailuo-02",
        "图片中的人物开始微笑并挥手",
        imageUrl,
        6,
        "1080P"
    ).thenAccept(response -> {
        if (response.isSuccess()) {
            log.info("图生视频任务提交成功: {}", response.getTaskId());
        }
    });
}
```

### 运镜控制

```java
public void generateWithCameraMovement() {
    // 使用运镜控制
    String prompt = "一个人在海边散步" + 
                   MiniMaxVideoApiClient.CameraMovement.LEFT_PAN + 
                   "，海浪轻拍着沙滩" +
                   MiniMaxVideoApiClient.CameraMovement.ZOOM_IN;
    
    miniMaxVideoApiClient.textToVideo(
        "T2V-01-Director",  // 使用支持运镜的模型
        prompt,
        6,
        "768P"
    ).thenAccept(response -> {
        log.info("运镜视频任务提交: {}", response.getTaskId());
    });
}
```

### 组合运镜

```java
public void generateWithCombinedMovement() {
    // 组合多个运镜效果
    String combinedMovement = MiniMaxVideoApiClient.CameraMovement.combine(
        MiniMaxVideoApiClient.CameraMovement.LEFT_PAN,
        MiniMaxVideoApiClient.CameraMovement.ZOOM_OUT
    );
    
    String prompt = "舞者在舞台上表演" + combinedMovement;
    
    miniMaxVideoApiClient.textToVideo(
        "T2V-01-Director",
        prompt,
        6,
        "768P"
    );
}
```

### 一键生成（提交并等待完成）

```java
public void generateAndWait() {
    MiniMaxVideoGenerationRequest request = MiniMaxVideoGenerationRequest
        .buildTextToVideoRequest(
            "MiniMax-Hailuo-02",
            "春天的樱花飞舞",
            6,
            "1080P"
        )
        .withCallbackUrl("https://your-domain.com/callback");
    
    miniMaxVideoApiClient.generateAndWait(request, 10)
        .thenAccept(result -> {
            if (result.isSuccess()) {
                log.info("视频生成完成!");
                log.info("视频URL: {}", result.getVideoUrl());
                log.info("视频尺寸: {}x{}", result.getVideoWidth(), result.getVideoHeight());
            } else {
                log.error("视频生成失败: {}", result.getErrorMessage());
            }
        });
}
```

### 主体参考视频生成

```java
public void generateSubjectReferenceVideo() {
    miniMaxVideoApiClient.subjectReferenceVideo(
        "在公园里跑步",
        "https://example.com/person.jpg",
        "一个穿着运动服的年轻人",
        6,
        "768P"
    ).thenAccept(response -> {
        if (response.isSuccess()) {
            log.info("主体参考视频任务提交成功: {}", response.getTaskId());
        }
    });
}
```

### 状态查询

```java
public void checkVideoStatus(String taskId) {
    miniMaxVideoApiClient.getVideoStatus(taskId)
        .thenAccept(status -> {
            switch (status.getStatus().toLowerCase()) {
                case "processing":
                    log.info("视频生成中...");
                    break;
                case "success":
                    log.info("视频生成成功: {}", status.getVideoUrl());
                    break;
                case "failed":
                    log.error("视频生成失败: {}", status.getErrorMessage());
                    break;
            }
        });
}
```

### 批量生成

```java
public void batchGeneration() {
    String[] prompts = {
        "春天的花朵盛开",
        "夏天的海浪拍岸", 
        "秋天的落叶飞舞",
        "冬天的雪花飘落"
    };
    
    List<CompletableFuture<MiniMaxVideoGenerationResponse>> futures = 
        Arrays.stream(prompts)
            .map(prompt -> miniMaxVideoApiClient.textToVideo(
                "MiniMax-Hailuo-02", prompt, 6, "768P"))
            .collect(Collectors.toList());
    
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
        .thenRun(() -> {
            log.info("所有视频生成任务已提交");
        });
}
```

## 回调处理

### 设置回调URL

```java
MiniMaxVideoGenerationRequest request = MiniMaxVideoGenerationRequest
    .buildTextToVideoRequest("MiniMax-Hailuo-02", "提示词", 6, "1080P")
    .withCallbackUrl("https://your-domain.com/minimax/callback");
```

### 处理回调请求

```java
@RestController
@RequestMapping("/minimax")
public class MiniMaxCallbackController {
    
    @Resource
    private MiniMaxVideoApiClient miniMaxVideoApiClient;
    
    @PostMapping("/callback")
    public ResponseEntity<Map<String, String>> handleCallback(
            @RequestBody MiniMaxVideoCallbackRequest callbackRequest) {
        
        // 处理验证请求
        if (callbackRequest.isVerificationRequest()) {
            MiniMaxVideoCallbackRequest response = 
                miniMaxVideoApiClient.handleCallbackVerification(callbackRequest);
            
            Map<String, String> result = new HashMap<>();
            result.put("challenge", response.getChallenge());
            return ResponseEntity.ok(result);
        }
        
        // 处理状态回调
        log.info("收到视频生成回调: taskId={}, status={}", 
                callbackRequest.getTaskId(), callbackRequest.getStatus());
        
        if (callbackRequest.isSuccess()) {
            log.info("视频生成成功: {}", callbackRequest.getVideoUrl());
            // 处理成功逻辑
        } else if (callbackRequest.isFailed()) {
            log.error("视频生成失败: {}", callbackRequest.getErrorMessage());
            // 处理失败逻辑
        }
        
        Map<String, String> result = new HashMap<>();
        result.put("status", "success");
        return ResponseEntity.ok(result);
    }
}
```

## 最佳实践

### 1. 异步处理
```java
// 推荐：使用异步方式
miniMaxVideoApiClient.textToVideo(model, prompt, duration, resolution)
    .thenAccept(response -> {
        // 处理响应
    })
    .exceptionally(throwable -> {
        // 处理异常
        return null;
    });
```

### 2. 错误处理
```java
public void handleVideoGeneration() {
    miniMaxVideoApiClient.textToVideo("MiniMax-Hailuo-02", "提示词", 6, "1080P")
        .thenAccept(response -> {
            if (response.isSuccess()) {
                // 成功处理
                processSuccess(response.getTaskId());
            } else {
                // 失败处理
                processError(response.getErrorMessage());
            }
        })
        .exceptionally(throwable -> {
            // 异常处理
            log.error("视频生成请求异常", throwable);
            return null;
        });
}
```

### 3. 资源管理
```java
// 合理设置超时时间
miniMaxVideoApiClient.generateAndWait(request, 15) // 15分钟超时
    .orTimeout(20, TimeUnit.MINUTES) // 额外的超时保护
    .thenAccept(result -> {
        // 处理结果
    });
```

### 4. 日志记录
```java
// 记录关键信息
log.info("提交视频生成请求: model={}, prompt={}, duration={}s, resolution={}", 
         model, prompt, duration, resolution);

// 记录任务ID用于追踪
log.info("视频生成任务ID: {}", response.getTaskId());
```
