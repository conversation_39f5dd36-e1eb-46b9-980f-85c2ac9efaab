# MiniMax视频生成回调配置指南

## 配置说明

为了支持MiniMax视频生成回调功能，需要在应用配置文件中添加以下配置：

### application.yml 配置示例

```yaml
minimax:
  api:
    key: "your-minimax-api-key"
    download-url: "https://api.minimaxi.com/v1/files/retrieve_content"
    group-id: "your-group-id"
    timeout: 60
```

### application.properties 配置示例

```properties
# MiniMax API配置
minimax.api.key=your-minimax-api-key
minimax.api.download-url=https://api.minimaxi.com/v1/files/retrieve_content
minimax.api.group-id=your-group-id
minimax.api.timeout=60
```

## 配置参数说明

| 参数 | 说明 | 是否必填 | 默认值 |
|------|------|----------|--------|
| `minimax.api.key` | MiniMax API密钥 | 是 | 无 |
| `minimax.api.download-url` | 文件下载接口地址 | 否 | https://api.minimaxi.com/v1/files/retrieve_content |
| `minimax.api.group-id` | 用户所属的组ID | 是 | 无 |
| `minimax.api.timeout` | 请求超时时间（秒） | 否 | 60 |

## 回调接口

### 接口地址
```
POST /agent/video/minimax/callback
```

### 请求参数
```json
{
    "task_id": "176843862716480",
    "status": "Success",
    "file_id": "176844028768320",
    "video_width": 1920,
    "video_height": 1080,
    "base_resp": {
        "status_code": 0,
        "status_msg": "success"
    }
}
```

### 状态说明
- `Preparing` - 准备中
- `Queueing` - 队列中
- `Processing` - 生成中
- `Success` - 成功
- `Fail` - 失败

### 响应格式
```json
"success"  // 处理成功
"failed"   // 处理失败
"error"    // 处理异常
```

## 处理流程

1. **接收回调** - 系统接收MiniMax的回调请求
2. **验证任务** - 根据task_id查找对应的内部任务
3. **状态处理**：
   - **Success状态**：异步下载视频文件并上传到OSS
   - **Fail状态**：标记任务失败
   - **Processing状态**：更新任务状态
4. **更新记录** - 异步更新视频生成记录表
5. **返回响应** - 返回处理结果

## 文件下载流程

当收到Success状态的回调时：

1. **异步下载** - 使用file_id调用MiniMax文件下载接口
2. **上传OSS** - 将下载的视频文件上传到阿里云OSS
3. **更新任务** - 更新任务状态为完成，并设置视频URL
4. **更新记录** - 更新ai_video_generation_record表

## 错误处理

- 任务不存在：返回错误并记录日志
- 文件下载失败：标记任务失败并记录错误信息
- OSS上传失败：标记任务失败并记录错误信息
- 其他异常：记录详细错误日志并返回错误响应

## 注意事项

1. **异步处理**：文件下载和上传采用异步处理，避免阻塞回调响应
2. **错误恢复**：下载或上传失败时会自动标记任务失败
3. **日志记录**：所有关键步骤都有详细的日志记录
4. **超时设置**：文件下载设置了较长的超时时间（5分钟）以适应大文件
5. **重试机制**：HTTP客户端配置了连接失败重试

## 测试

可以使用以下curl命令测试回调接口：

```bash
curl -X POST http://localhost:8080/agent/video/minimax/callback \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "test_task_123",
    "status": "Success",
    "file_id": "test_file_456",
    "video_width": 1920,
    "video_height": 1080,
    "base_resp": {
        "status_code": 0,
        "status_msg": "success"
    }
  }'
```
