# 视频模型级联结构示例

## 数据结构说明

新的级联结构将视频模型配置分为三个层级，每个层级都有独立的积分成本：

### 1. 模型层级 (ai_video_model)
- 存储模型基础信息
- 包含提供商、模型类型、帧率等通用配置

### 2. 尺寸层级 (ai_video_model_size_config)
- 每个模型可以有多个尺寸配置
- 包含图片数量支持、时长支持、尺寸级别的积分成本

### 3. 分辨率层级 (ai_video_model_resolution_config)
- 每个尺寸可以有多个分辨率配置
- 包含具体的像素尺寸、分辨率级别的积分成本

## 积分成本计算

总积分成本 = 尺寸积分成本 + 分辨率积分成本

例如：
- MiniMax-Hailuo-02 -> 768P -> 16:9 = 80 + 5 = 85积分
- MiniMax-Hailuo-02 -> 1080P -> 16:9 = 120 + 10 = 130积分

## 图片数量说明

| 数值 | 含义 | 适用场景 |
|------|------|----------|
| 0 | 不支持图片 | 纯文生视频(T2V) |
| 1 | 支持首帧 | 图生视频(I2V) |
| 2 | 支持首尾帧 | 首尾帧图生视频(FLF2V) |

## 实际数据示例

### MiniMax海螺02模型
```
模型: MiniMax-Hailuo-02 (T2V,I2V)
├── 768P标清 (图片数量:1, 积分:80)
│   ├── 16:9横屏 (1344×768, 积分:5)
│   ├── 4:3标准 (1024×768, 积分:5)
│   ├── 1:1正方形 (768×768, 积分:5)
│   ├── 3:4竖屏 (768×1024, 积分:5)
│   ├── 9:16竖屏 (768×1344, 积分:5)
│   └── 21:9超宽屏 (1792×768, 积分:5)
└── 1080P高清 (图片数量:1, 积分:120)
    ├── 16:9横屏 (1920×1080, 积分:10)
    ├── 4:3标准 (1440×1080, 积分:10)
    ├── 1:1正方形 (1080×1080, 积分:10)
    ├── 3:4竖屏 (1080×1440, 积分:10)
    ├── 9:16竖屏 (1080×1920, 积分:10)
    └── 21:9超宽屏 (2520×1080, 积分:10)
```

### 豆包Seedance Pro模型
```
模型: doubao-seedance-1-0-pro (T2V,I2V)
├── 480p标清 (图片数量:2, 积分:60)
│   ├── 16:9横屏 (864×480, 积分:3)
│   ├── 4:3标准 (736×544, 积分:3)
│   ├── 1:1正方形 (640×640, 积分:3)
│   ├── 3:4竖屏 (544×736, 积分:3)
│   ├── 9:16竖屏 (480×864, 积分:3)
│   └── 21:9超宽屏 (960×416, 积分:3)
├── 720p高清 (图片数量:2, 积分:80)
│   └── ... (类似结构)
└── 1080p超清 (图片数量:2, 积分:100)
    └── ... (类似结构)
```

## API查询示例

### 1. 查询所有模型配置
```bash
GET /agent/video-model-config/list
```

### 2. 查询支持首帧的模型
```bash
GET /agent/video-model-config/image-count/1
```

### 3. 查询支持首尾帧的模型
```bash
GET /agent/video-model-config/image-count/2
```

### 4. 根据条件筛选
```bash
POST /agent/video-model-config/search
{
  "provider": "DOUBAO",
  "supportFirstFrame": true,
  "minPointsCost": 50,
  "maxPointsCost": 100
}
```

## 前端使用建议

### 1. 模型选择流程
1. 用户选择模型类型（文生视频/图生视频/首尾帧视频）
2. 根据类型筛选可用模型
3. 用户选择具体模型
4. 显示该模型支持的尺寸选项
5. 用户选择尺寸后显示可用的分辨率选项
6. 计算并显示总积分成本

### 2. 积分成本显示
```javascript
// 计算总成本
const totalCost = sizeConfig.pointsCost + resolutionConfig.pointsCost;

// 显示成本明细
console.log(`尺寸成本: ${sizeConfig.pointsCost}积分`);
console.log(`分辨率成本: ${resolutionConfig.pointsCost}积分`);
console.log(`总成本: ${totalCost}积分`);
```

### 3. 图片上传控制
```javascript
// 根据图片数量控制上传组件
const imageCount = sizeConfig.imageCount;
if (imageCount === 0) {
  // 隐藏图片上传组件
} else if (imageCount === 1) {
  // 显示首帧图片上传
} else if (imageCount === 2) {
  // 显示首帧和尾帧图片上传
}
```

## 数据库查询优化

由于采用了级联结构，建议在查询时使用适当的索引和批量查询：

1. 为外键字段添加索引
2. 使用批量查询减少数据库访问次数
3. 考虑使用缓存存储热门配置

## 扩展性

这种级联结构具有良好的扩展性：

1. **新增模型**: 只需在模型表中添加记录，然后配置对应的尺寸和分辨率
2. **新增尺寸**: 在尺寸配置表中添加记录，配置对应的分辨率
3. **调整积分**: 可以独立调整每个层级的积分成本
4. **功能扩展**: 可以在任何层级添加新的配置字段
