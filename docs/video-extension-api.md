# 视频延长功能 API 文档

## 概述

视频延长功能允许用户在当前分镜后面增加一个新的分镜，并智能处理音频拆分。当当前分镜的音频总时长超过视频时长时，系统会按照音频顺序智能分配，对旁白音频按标点符号拆分文本，重新生成音频，并将超出部分分配到新创建的分镜中。

## API 接口

### 视频延长

**接口地址：** `POST /agent/canvas/shot/extend-video`

**请求参数：**

```json
{
  "shotId": 123456789,
  "lastFrameImageUrl": "https://example.com/last-frame.jpg"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| shotId | Long | 是 | 画布分镜ID |
| lastFrameImageUrl | String | 是 | 当前分镜视频素材的最后一帧图片URL |

**响应示例：**

```json
{
  "success": true,
  "data": 123456790,
  "errorCode": null,
  "errorMessage": null
}
```

**响应说明：**

- `data`: 新创建的分镜ID

## 功能流程

### 1. 基本验证
- 验证分镜是否存在
- 验证用户权限
- 验证分镜是否有视频素材

### 2. 音频处理逻辑

#### 2.1 无音频情况
如果当前分镜没有音频，直接创建新分镜并使用传入的图片作为素材。

#### 2.2 音频时长不超过视频时长
如果音频总时长不超过视频时长，直接创建新分镜并使用传入的图片作为素材。

#### 2.3 音频时长超过视频时长
当音频总时长超过视频时长时，执行以下步骤：

1. **按序处理音频**：按照sortOrder顺序逐个处理音频
2. **智能分配策略**：
   - 非旁白音频（音效、背景音乐）：如果能完整放入当前分镜则保留，否则移到下一分镜
   - 旁白音频：如果会导致超时，则进行文本拆分处理
3. **旁白音频拆分**：
   - 按标点符号（，。！？｜）拆分文本
   - 估算每个文本段落的音频时长
   - 找到合适的拆分点，确保当前分镜不超时
   - 将拆分后的文本分别分配到当前分镜和下一分镜
4. **音频重新生成**：
   - 删除原有音频
   - 为当前分镜重新生成音频（基于分配的音频）
   - 为新分镜生成音频（基于剩余音频）

### 3. 新分镜创建
- 创建新分镜，排序序号为当前分镜+1
- 更新后续分镜的排序序号
- 为新分镜创建图片素材（使用传入的最后一帧图片）

## 音频处理示例

### 场景描述
假设有一个10秒的视频，包含以下音频（按sortOrder排序）：
1. 旁白1：3秒 - "这是第一段旁白。"
2. 音效：2秒 - "鸟叫声"
3. 旁白2：8秒 - "这是第二段旁白，内容比较长，需要拆分处理。包含多个句子！"
4. 背景音乐：5秒

### 处理过程
1. **旁白1（3秒）**：可以完整放入当前分镜，累计时长3秒
2. **音效（2秒）**：可以完整放入当前分镜，累计时长5秒
3. **旁白2（8秒）**：会导致超时（5+8=13秒 > 10秒），需要拆分
   - 剩余时长：10-5=5秒
   - 文本拆分：["这是第二段旁白，", "内容比较长，", "需要拆分处理。", "包含多个句子！"]
   - 根据估算时长分配前两个段落到当前分镜，后两个到下一分镜
4. **背景音乐（5秒）**：由于已达到时长限制，直接放到下一分镜

### 最终结果
- **当前分镜**：旁白1 + 音效 + 旁白2前半部分（总时长≤10秒）
- **下一分镜**：旁白2后半部分 + 背景音乐

## 文本拆分规则

### 支持的标点符号
- 中文逗号：，
- 中文句号：。
- 中文感叹号：！
- 中文问号：？
- 竖线：｜

### 拆分示例

**原文本：**
```
这是第一段文本，这是第二段文本。这是第三段文本！
```

**拆分结果：**
```
1. "这是第一段文本，"
2. "这是第二段文本。"
3. "这是第三段文本！"
```

## 音频时长估算

系统使用以下规则估算音频时长：
- 每个有效字符（不包括标点符号和空格）约150毫秒
- 仅计算中文字符、数字和英文字母

**示例：**
- 文本："这是测试文本，包含标点。"
- 有效字符：11个（这是测试文本包含标点）
- 估算时长：11 × 150 = 1650毫秒

## 错误处理

### 常见错误码

| 错误信息 | 说明 |
|----------|------|
| 分镜不存在 | 指定的分镜ID不存在或已删除 |
| 画布不存在 | 分镜对应的画布不存在或已删除 |
| 无权限操作此画布 | 当前用户无权限操作该画布 |
| 当前分镜没有视频素材 | 分镜缺少视频素材，无法进行延长操作 |

## 注意事项

1. **权限验证**：只有画布的所有者才能执行视频延长操作
2. **音频类型处理**：
   - 旁白音频（audioType=1）：支持文本拆分
   - 音效音频（audioType=2）：整体迁移，不拆分
   - 背景音乐（audioType=3）：整体迁移，不拆分
3. **多旁白支持**：支持处理多个旁白音频的拆分
4. **时序保持**：严格按照sortOrder顺序处理，确保音频时序正确
5. **TTS重新生成**：拆分后的旁白音频会重新调用TTS服务生成
6. **排序更新**：新分镜插入后，会自动更新后续分镜的排序序号
7. **事务处理**：整个操作在事务中执行，确保数据一致性
8. **图片宽高比**：新分镜的图片会自动继承当前分镜视频的宽高比，确保视觉一致性

## 使用建议

1. **图片质量**：确保传入的最后一帧图片质量良好，作为新分镜的素材
2. **文本格式**：旁白文本应包含适当的标点符号，以便正确拆分
3. **音频排序**：确保音频的sortOrder正确设置，以保证处理顺序
4. **时长控制**：建议控制单个分镜的音频时长，避免过长的音频需要复杂拆分
