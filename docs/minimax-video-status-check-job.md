# MiniMax视频生成状态检查定时任务

## 概述

`MiniMaxVideoGenerationStatusCheckJob` 是一个定时任务，用于检查MiniMax视频生成任务的状态，并在任务完成时下载视频文件并上传到OSS。

## 功能特性

- 定时查询MiniMax模型（MiniMax-Hailuo-02）的未完成任务
- 调用MiniMax API查询任务状态
- 处理成功任务：下载视频文件并上传到OSS
- 更新相关数据库表的状态
- 支持失败任务的错误处理

## 配置

### XXL-Job配置

在XXL-Job管理后台配置定时任务：

- **JobHandler**: `miniMaxVideoGenerationStatusCheckJobHandler`
- **执行频率**: 建议每5分钟执行一次
- **Cron表达式**: `0 */5 * * * ?`

### 应用配置

确保以下配置项已正确设置：

```yaml
minimax:
  api:
    key: "your-minimax-api-key"
    download-url: "https://api.minimaxi.com/v1/files/retrieve_content"

app:
  env: "prod"  # 或其他环境标识
```

## 工作流程

### 1. 任务查询
- 查询 `ai_video_generation_record` 表中模型为 `MiniMax-Hailuo-02` 的未完成任务
- 筛选创建时间超过5分钟的任务（避免频繁查询刚提交的任务）

### 2. 状态检查
调用MiniMax API查询每个任务的状态：
```
GET https://api.minimaxi.com/v1/query/video_generation?task_id={task_id}
```

### 3. 状态处理

#### 成功状态 (Success)
1. 使用 `file_id` 调用MiniMax文件下载接口下载视频文件：
   ```
   GET https://api.minimaxi.com/v1/files/retrieve_content?file_id={file_id}&GroupId={group_id}
   Headers:
   - Authorization: Bearer {api_key}
   - Content-Type: application/json
   ```
2. 上传视频到OSS
3. 更新以下表的状态：
   - `ai_video_generation_record`: 状态设为 `succeeded`，设置 `video_url`
   - `ai_video_generation`: 状态设为 `2`（处理完成），设置 `video_url`
   - `ai_canvas_shot`: 分镜状态设为 `2`（已完成）
   - `ai_canvas_video`: 视频状态设为 `completed`，设置 `video_url`

#### 失败状态 (Fail)
1. 更新以下表的状态：
   - `ai_video_generation_record`: 状态设为 `failed`，设置错误信息
   - `ai_video_generation`: 状态设为 `3`（处理失败）
   - `ai_canvas_shot`: 分镜状态设为 `3`（失败）

#### 处理中状态 (Processing/Preparing/Queueing)
- 仅更新 `ai_video_generation_record` 表的状态
- 不做其他处理，等待下次检查

## 数据库表结构

### ai_video_generation_record
主要字段：
- `task_id`: MiniMax任务ID
- `model`: 模型名称（MiniMax-Hailuo-02）
- `status`: 任务状态（queued, running, succeeded, failed等）
- `video_url`: 生成的视频URL
- `error_msg`: 错误信息

### ai_video_generation
主要字段：
- `generate_task_id`: 外部任务ID
- `status`: 状态（0-排队中,1-处理中,2-处理完成,3-处理失败）
- `video_url`: 视频URL
- `shot_id`: 关联的分镜ID

### ai_canvas_shot
主要字段：
- `shot_status`: 分镜状态（0-初始,1-处理中,2-已完成,3-失败）

### ai_canvas_video
主要字段：
- `video_url`: 视频URL
- `video_status`: 视频状态
- `shot_code`: 分镜编码

## 错误处理

### 常见错误场景
1. **任务ID为空**: 直接标记为失败
2. **API调用失败**: 记录错误日志，跳过该任务
3. **文件下载失败**: 标记任务为失败，记录错误信息
4. **OSS上传失败**: 标记任务为失败，记录错误信息
5. **数据库更新失败**: 记录错误日志，不影响其他任务

### 日志记录
- 使用 `log.info` 记录正常流程
- 使用 `log.error` 记录错误信息
- 使用 `XxlJobHelper.log` 记录到XXL-Job日志

## 监控和告警

### 关键指标
- 任务执行成功率
- 平均处理时间
- 失败任务数量
- API调用响应时间

### 建议告警规则
- 连续3次任务执行失败
- 单次任务处理时间超过10分钟
- 失败任务数量超过总任务数的20%

## 性能优化

### 批量处理
- 每次最多处理50个任务（可配置）
- 使用异步处理提高效率

### 超时控制
- API调用超时时间：30秒
- 文件下载超时时间：5分钟

### 重试机制
- API调用失败时不重试（由下次定时任务处理）
- 文件下载失败时不重试（避免重复下载）

## 使用示例

### 手动触发任务
```java
@Autowired
private MiniMaxVideoGenerationStatusCheckJob job;

// 手动执行任务检查
ReturnT<String> result = job.checkMiniMaxVideoGenerationTaskStatus("");
```

### 查看任务日志
在XXL-Job管理后台查看任务执行日志，包括：
- 任务开始和结束时间
- 处理的任务数量
- 成功和失败的任务详情
- 错误信息

## 注意事项

1. **API限流**: MiniMax API可能有调用频率限制，注意控制并发数
2. **存储空间**: 视频文件较大，注意OSS存储空间使用情况
3. **网络稳定性**: 文件下载依赖网络稳定性，建议配置重试机制
4. **数据一致性**: 确保多个表的状态更新保持一致性

## 故障排查

### 常见问题
1. **任务不执行**: 检查XXL-Job配置和网络连接
2. **API调用失败**: 检查API密钥和网络连接
3. **文件下载失败**: 检查网络连接和存储空间
4. **数据库更新失败**: 检查数据库连接和表结构

### 调试方法
1. 查看应用日志
2. 查看XXL-Job执行日志
3. 检查数据库表数据
4. 使用Postman测试MiniMax API
