# MiniMax FAL Client 使用文档

## 概述

MiniMax FAL Client 是基于 fal-ai/minimax/hailuo-02/standard/image-to-video OpenAPI 规范实现的 Java 客户端，用于调用 MiniMax Hailuo-02 标准图像到视频生成服务。

## 功能特性

- ✅ 图像到视频生成
- ✅ 同步和异步调用支持
- ✅ 自动重试机制
- ✅ 队列状态轮询
- ✅ 请求取消功能
- ✅ 批量处理支持
- ✅ 完整的错误处理
- ✅ 使用 JDK 17 新特性

## 快速开始

### 1. 配置 API 密钥

在 `application.properties` 中配置 FAL API 密钥：

```properties
fal.ai.api-key=your-fal-api-key-here
```

### 2. 基本使用

```java
@Autowired
private MinimaxFalClient minimaxFalClient;

// 构建请求参数
MinimaxHailuo02ImageToVideoInput input = MinimaxHailuo02ImageToVideoInput
    .buildStandardRequest(
        "A beautiful sunset over the ocean", // 提示词
        "https://example.com/image.jpg",     // 图片URL
        "6"                                  // 视频时长（秒）
    );

// 同步生成视频
MinimaxHailuo02ImageToVideoOutput result = minimaxFalClient.generateVideo(input);
System.out.println("生成的视频URL: " + result.getVideoUrl());
```

## API 参考

### MinimaxHailuo02ImageToVideoInput

图像到视频输入参数模型：

| 字段 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `prompt` | String | ✅ | - | 生成视频的描述提示词（最大2000字符） |
| `imageUrl` | String | ✅ | - | 输入图片的URL |
| `duration` | String | ❌ | "6" | 视频时长，可选值："6", "10" |
| `promptOptimizer` | Boolean | ❌ | true | 是否使用模型的提示优化器 |

### MinimaxHailuo02ImageToVideoOutput

图像到视频输出结果模型：

| 字段 | 类型 | 说明 |
|------|------|------|
| `video` | FalFile | 生成的视频文件信息 |
| `video.url` | String | 视频下载URL |
| `video.fileName` | String | 视频文件名 |
| `video.fileSize` | Integer | 视频文件大小（字节） |
| `video.contentType` | String | 视频MIME类型 |

### FalQueueStatus

队列状态模型：

| 字段 | 类型 | 说明 |
|------|------|------|
| `status` | String | 队列状态："IN_QUEUE", "IN_PROGRESS", "COMPLETED" |
| `requestId` | String | 请求ID |
| `queuePosition` | Integer | 队列位置（仅在IN_QUEUE状态时有效） |
| `responseUrl` | String | 结果获取URL |
| `statusUrl` | String | 状态查询URL |
| `cancelUrl` | String | 取消请求URL |

## 使用示例

### 1. 同步生成视频

```java
public MinimaxHailuo02ImageToVideoOutput generateVideoSync(String prompt, String imageUrl) {
    try {
        // 构建请求参数
        MinimaxHailuo02ImageToVideoInput input = MinimaxHailuo02ImageToVideoInput
            .buildStandardRequest(prompt, imageUrl, "6", true);
        
        // 同步生成视频
        MinimaxHailuo02ImageToVideoOutput result = minimaxFalClient.generateVideo(input);
        
        log.info("视频生成成功: {}", result.getVideoUrl());
        return result;
        
    } catch (IOException e) {
        log.error("同步生成视频失败: {}", e.getMessage(), e);
        throw new RuntimeException("同步生成视频失败", e);
    }
}
```

### 2. 异步生成视频

```java
public CompletableFuture<MinimaxHailuo02ImageToVideoOutput> generateVideoAsync(String prompt, String imageUrl) {
    // 构建请求参数
    MinimaxHailuo02ImageToVideoInput input = MinimaxHailuo02ImageToVideoInput
        .buildStandardRequest(prompt, imageUrl, "10", true);
    
    // 异步生成视频
    return minimaxFalClient.generateVideoAsync(input)
        .thenApply(result -> {
            log.info("异步视频生成成功: {}", result.getVideoUrl());
            return result;
        })
        .exceptionally(throwable -> {
            log.error("异步生成视频失败: {}", throwable.getMessage(), throwable);
            return null;
        });
}
```

### 3. 手动控制生成流程

```java
public MinimaxHailuo02ImageToVideoOutput generateVideoManual(String prompt, String imageUrl) throws IOException {
    // 1. 构建请求参数
    MinimaxHailuo02ImageToVideoInput input = MinimaxHailuo02ImageToVideoInput.builder()
        .prompt(prompt)
        .imageUrl(imageUrl)
        .duration("6")
        .promptOptimizer(true)
        .build();
    
    // 2. 提交请求
    FalQueueStatus queueStatus = minimaxFalClient.submitImageToVideoRequest(input);
    String requestId = queueStatus.getRequestId();
    
    // 3. 轮询状态
    while (!queueStatus.isCompleted()) {
        Thread.sleep(2000); // 等待2秒
        queueStatus = minimaxFalClient.checkQueueStatus(requestId);
        
        if (queueStatus.isInQueue()) {
            log.info("在队列中，位置: {}", queueStatus.getQueuePositionDescription());
        } else if (queueStatus.isInProgress()) {
            log.info("正在处理中...");
        }
    }
    
    // 4. 获取结果
    return minimaxFalClient.getResult(requestId);
}
```

### 4. 取消请求

```java
public boolean cancelRequest(String requestId) throws IOException {
    return minimaxFalClient.cancelRequest(requestId);
}
```

### 5. 批量生成视频

```java
public CompletableFuture<Void> batchGenerateVideos(String[] prompts, String[] imageUrls) {
    CompletableFuture<?>[] futures = new CompletableFuture[prompts.length];
    
    for (int i = 0; i < prompts.length; i++) {
        futures[i] = minimaxFalClient.generateVideoAsync(
            MinimaxHailuo02ImageToVideoInput.buildStandardRequest(prompts[i], imageUrls[i])
        );
    }
    
    return CompletableFuture.allOf(futures);
}
```

## 配置参数

客户端支持以下配置参数：

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 连接超时 | 30秒 | HTTP连接超时时间 |
| 读取超时 | 120秒 | HTTP读取超时时间 |
| 写入超时 | 30秒 | HTTP写入超时时间 |
| 最大重试次数 | 3次 | API调用失败时的重试次数 |
| 轮询间隔 | 2秒 | 状态轮询间隔时间 |
| 最大等待时间 | 10分钟 | 视频生成的最大等待时间 |

## 错误处理

客户端提供完整的错误处理机制：

1. **网络错误**：自动重试，支持指数退避
2. **API错误**：抛出 `BizException` 包含详细错误信息
3. **超时错误**：在指定时间内未完成时抛出超时异常
4. **参数验证**：在请求前验证输入参数的有效性

## 注意事项

1. **API密钥**：确保配置正确的FAL API密钥
2. **图片URL**：确保图片URL可公开访问
3. **提示词长度**：提示词最大长度为2000字符
4. **视频时长**：1080p分辨率不支持10秒视频
5. **并发限制**：注意API的并发调用限制
6. **费用控制**：每次调用都会产生费用，请合理使用

## 依赖要求

- Java 17+
- Spring Boot 2.7+
- OkHttp 4.x
- Jackson 2.x
- Lombok

## 更新日志

### v1.0.0
- 初始版本发布
- 支持图像到视频生成
- 支持同步和异步调用
- 完整的错误处理和重试机制
