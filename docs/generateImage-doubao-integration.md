# generateImage方法豆包图像编辑API集成

## 修改概述

已成功修改`AiCanvasMaterialServiceImpl.java`中的`generateImage`方法，当传入参考图时，自动调用豆包图像编辑API进行图像编辑，而不是使用原来的FLUX模型。

## 主要修改内容

### 1. 添加依赖注入

```java
// 添加导入
import com.wlink.agent.client.DoubaoImageEditApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;

// 添加依赖注入
private final DoubaoImageEditApiClient doubaoImageEditApiClient;
```

### 2. 修改generateImage方法逻辑

**修改前**：
```java
if (StringUtils.hasText(req.getReferenceImageUrl())) {
    // 有参考图使用FLUX模型
    imageModel = "FLUX";
    taskType = TaskType.GENERATE_FLUX.getValue();
} else {
    // 没有参考图使用DOUBAO模型
    imageModel = "DOUBAO";
    taskType = TaskType.GENERATE_DOUBAO.getValue();
}
```

**修改后**：
```java
if (StringUtils.hasText(req.getReferenceImageUrl())) {
    // 有参考图使用豆包图像编辑API
    log.info("检测到参考图，使用豆包图像编辑API: {}", req.getReferenceImageUrl());
    return handleImageEditWithDoubao(req, shotPo, aiCanvasPo);
} else {
    // 没有参考图使用DOUBAO模型
    imageModel = "DOUBAO";
    taskType = TaskType.GENERATE_DOUBAO.getValue();
}
```

### 3. 新增handleImageEditWithDoubao方法

```java
private ImageGenerateRes handleImageEditWithDoubao(ImageGenerateReq req, AiCanvasShotPo shotPo, AiCanvasPo aiCanvasPo) {
    log.info("使用豆包图像编辑API处理图像生成: shotId={}, prompt={}", req.getShotId(), req.getPrompt());
    
    try {
        // 构建豆包图像编辑请求
        DoubaoImageEditRequest editRequest = new DoubaoImageEditRequest();
        editRequest.setModel("doubao-seededit-3-0-i2i-250628");
        editRequest.setPrompt(req.getPrompt());
        editRequest.setImage(MediaUrlPrefixUtil.getMediaUrl(req.getReferenceImageUrl()));
        editRequest.setResponseFormat("url");
        editRequest.setSize("adaptive");
        editRequest.setSeed(aiCanvasPo.getSeed() != null ? aiCanvasPo.getSeed().intValue() : -1);
        editRequest.setGuidanceScale(req.getStrength() != null ? req.getStrength().doubleValue() : 5.5);
        editRequest.setWatermark(false);
        
        // 调用豆包图像编辑API
        com.wlink.agent.client.model.volcengine.ImageGenerateRes editResult = doubaoImageEditApiClient.editImage(editRequest);
        
        // 处理结果...
        
    } catch (Exception e) {
        // 错误处理...
    }
}
```

## 功能特性

### ✅ 自动检测参考图
- 当`ImageGenerateReq.referenceImageUrl`不为空时，自动使用豆包图像编辑API
- 当没有参考图时，继续使用原来的DOUBAO文生图模型

### ✅ 参数映射
- **模型**: 使用`doubao-seededit-3-0-i2i-250628`（SeedEdit 3.0）
- **提示词**: 直接使用`req.getPrompt()`
- **参考图**: 使用`req.getReferenceImageUrl()`
- **种子**: 使用画布的种子值或默认-1
- **引导尺度**: 使用`req.getStrength()`或默认5.5
- **水印**: 设置为false（不添加水印）

### ✅ 数据库更新
- 更新分镜状态为完成/失败
- 更新或插入`ai_canvas_image`表记录
- 创建`ai_canvas_material`表记录

### ✅ 错误处理
- 完整的异常捕获和处理
- 失败时更新分镜状态为失败
- 详细的日志记录

## 使用示例

### 1. 有参考图的请求

```java
ImageGenerateReq req = new ImageGenerateReq();
req.setShotId(123456L);
req.setPrompt("把图中的红色汽车改成蓝色");
req.setReferenceImageUrl("https://example.com/car.jpg");
req.setAspectRatio("16:9");
req.setStrength(7.0f);

// 调用generateImage方法
ImageGenerateRes result = aiCanvasMaterialService.generateImage(req);
// 此时会自动使用豆包图像编辑API
```

### 2. 无参考图的请求

```java
ImageGenerateReq req = new ImageGenerateReq();
req.setShotId(123456L);
req.setPrompt("一辆蓝色的汽车");
req.setAspectRatio("16:9");
req.setStrength(7.0f);
// 注意：没有设置referenceImageUrl

// 调用generateImage方法
ImageGenerateRes result = aiCanvasMaterialService.generateImage(req);
// 此时会使用原来的DOUBAO文生图模型
```

## 日志输出

### 成功案例
```
INFO - 检测到参考图，使用豆包图像编辑API: https://example.com/car.jpg
INFO - 使用豆包图像编辑API处理图像生成: shotId=123456, prompt=把图中的红色汽车改成蓝色
INFO - 豆包图像编辑成功: shotId=123456, imageUrl=https://generated-image.jpg
INFO - 更新图片记录成功: imageId=789, imageUrl=https://generated-image.jpg
INFO - 豆包图像编辑完成，创建素材记录: materialId=101112
```

### 失败案例
```
ERROR - 豆包图像编辑失败: shotId=123456, error=豆包图像编辑API返回结果为空
```

## 配置要求

确保在应用配置中设置了豆包API相关参数：

```properties
# 豆包图像编辑API配置
doubao.image.edit.api.url=https://ark.cn-beijing.volces.com/api/v3/images/generations
doubao.image.edit.api.key=your-api-key-here
```

## 注意事项

1. **API密钥**: 确保配置了有效的豆包API密钥
2. **图像格式**: 参考图必须是jpeg或png格式
3. **图像大小**: 参考图不能超过10MB
4. **网络访问**: 确保参考图URL可以被豆包API访问
5. **错误重试**: 建议在上层调用中实现重试机制

## 兼容性

- ✅ 向后兼容：没有参考图的请求继续使用原来的逻辑
- ✅ 数据库兼容：使用相同的表结构和字段
- ✅ 接口兼容：返回值格式保持不变

## 测试建议

1. **单元测试**: 测试有参考图和无参考图两种情况
2. **集成测试**: 测试完整的图像生成流程
3. **错误测试**: 测试各种错误情况的处理
4. **性能测试**: 对比豆包API和原FLUX模型的性能差异
