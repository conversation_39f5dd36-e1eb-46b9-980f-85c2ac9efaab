# 视频模型配置API文档（级联结构版）

## 概述

视频模型配置API提供了完整的视频生成模型配置管理功能，采用级联结构设计：**模型 -> 尺寸配置 -> 分辨率配置**，每个层级都有独立的积分成本配置。

## 数据结构说明

### 级联结构
1. **模型层级**: 基础模型信息（提供商、类型、帧率等）
2. **尺寸层级**: 每个模型支持的尺寸配置（480p、720p、1080P等），包含图片数量和积分成本
3. **分辨率层级**: 每个尺寸下支持的宽高比配置（16:9、4:3等），包含具体像素尺寸和积分成本

### 图片数量说明
- **0**: 不支持图片（纯文生视频）
- **1**: 支持首帧（图生视频）
- **2**: 支持首尾帧（首尾帧图生视频）

## API接口列表

### 1. 查询所有启用的视频模型配置

**接口地址：** `GET /agent/video-model-config/list`

**接口描述：** 获取所有启用状态的视频模型配置信息

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "modelName": "MiniMax-Hailuo-02",
      "modelDisplayName": "MiniMax海螺02",
      "modelDescription": "MiniMax最新的海螺模型，支持文生视频和图生视频",
      "provider": "MINIMAX",
      "providerName": "MiniMax",
      "modelTypes": ["T2V", "I2V"],
      "modelTypeNames": ["文生视频", "图生视频"],
      "fps": 24,
      "supportWatermark": true,
      "supportSeed": true,
      "supportCameraFixed": true,
      "sortOrder": 1,
      "sizeConfigs": [
        {
          "id": 1,
          "sizeName": "768P",
          "sizeDisplayName": "768P标清",
          "supportedDurations": [6, 10],
          "imageCount": 1,
          "imageCountDesc": "支持首帧",
          "pointsCost": 80,
          "sortOrder": 1,
          "resolutionConfigs": [
            {
              "id": 1,
              "ratio": "16:9",
              "ratioDisplayName": "横屏16:9",
              "width": 1344,
              "height": 768,
              "pixelSize": "1344×768",
              "pointsCost": 5,
              "sortOrder": 1
            },
            {
              "id": 2,
              "ratio": "4:3",
              "ratioDisplayName": "标准4:3",
              "width": 1024,
              "height": 768,
              "pixelSize": "1024×768",
              "pointsCost": 5,
              "sortOrder": 2
            }
          ]
        },
        {
          "id": 2,
          "sizeName": "1080P",
          "sizeDisplayName": "1080P高清",
          "supportedDurations": [6, 10],
          "imageCount": 1,
          "imageCountDesc": "支持首帧",
          "pointsCost": 120,
          "sortOrder": 2,
          "resolutionConfigs": [
            {
              "id": 7,
              "ratio": "16:9",
              "ratioDisplayName": "横屏16:9",
              "width": 1920,
              "height": 1080,
              "pixelSize": "1920×1080",
              "pointsCost": 10,
              "sortOrder": 1
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. 根据条件查询视频模型配置

**接口地址：** `POST /agent/video-model-config/search`

**请求参数：**
```json
{
  "provider": "MINIMAX",
  "modelType": "T2V",
  "supportFirstFrame": true,
  "supportLastFrame": false,
  "minPointsCost": 0,
  "maxPointsCost": 100,
  "keyword": "海螺"
}
```

### 3. 根据提供商查询配置

**接口地址：** `GET /agent/video-model-config/provider/{provider}`

**路径参数：**
- `provider`: 提供商代码（MINIMAX, DOUBAO）

### 4. 根据模型类型查询配置

**接口地址：** `GET /agent/video-model-config/model-type/{modelType}`

**路径参数：**
- `modelType`: 模型类型（T2V, I2V, FLF2V）

### 5. 根据模型名称获取详情

**接口地址：** `GET /agent/video-model-config/detail/{modelName}`

**路径参数：**
- `modelName`: 模型名称

### 6. 根据提供商和模型类型查询

**接口地址：** `GET /agent/video-model-config/provider/{provider}/model-type/{modelType}`

**路径参数：**
- `provider`: 提供商代码
- `modelType`: 模型类型

### 7. 获取支持的提供商列表

**接口地址：** `GET /agent/video-model-config/providers`

### 8. 获取支持的模型类型列表

**接口地址：** `GET /agent/video-model-config/model-types`

## 数据模型说明

### 视频模型配置响应 (VideoModelConfigRes)

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| id | Long | 主键ID | 1 |
| modelName | String | 模型名称 | "MiniMax-Hailuo-02" |
| modelDisplayName | String | 模型显示名称 | "MiniMax海螺02" |
| modelDescription | String | 模型描述 | "支持文生视频和图生视频" |
| provider | String | 提供商代码 | "MINIMAX" |
| providerName | String | 提供商名称 | "MiniMax" |
| modelTypes | List<String> | 模型类型列表 | ["T2V", "I2V"] |
| modelTypeNames | List<String> | 模型类型名称列表 | ["文生视频", "图生视频"] |
| supportedResolutions | List<String> | 支持的分辨率 | ["768P", "1080P"] |
| supportedRatios | List<String> | 支持的宽高比 | ["16:9", "4:3"] |
| supportedDurations | List<Integer> | 支持的时长(秒) | [6, 10] |
| fps | Integer | 帧率 | 24 |
| supportFirstFrame | Boolean | 是否支持首帧 | true |
| supportLastFrame | Boolean | 是否支持尾帧 | false |
| supportWatermark | Boolean | 是否包含水印 | true |
| supportSeed | Boolean | 是否支持种子 | true |
| supportCameraFixed | Boolean | 是否支持固定摄像头 | true |
| pointsCost | Integer | 生成需要的积分 | 100 |
| sortOrder | Integer | 排序顺序 | 1 |
| resolutionConfigs | List<ResolutionConfig> | 分辨率详细配置 | - |

### 分辨率配置 (ResolutionConfig)

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| resolution | String | 分辨率 | "720p" |
| ratioConfigs | List<RatioConfig> | 宽高比配置列表 | - |

### 宽高比配置 (RatioConfig)

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| ratio | String | 宽高比 | "16:9" |
| pixelSize | String | 实际像素尺寸 | "1280×720" |
| width | Integer | 宽度 | 1280 |
| height | Integer | 高度 | 720 |

## 支持的模型列表

### MiniMax模型

| 模型名称 | 显示名称 | 类型 | 分辨率 | 时长 | 积分成本 |
|----------|----------|------|--------|------|----------|
| MiniMax-Hailuo-02 | MiniMax海螺02 | T2V,I2V | 768P,1080P | 6,10秒 | 100 |

### 豆包模型

| 模型名称 | 显示名称 | 类型 | 分辨率 | 时长 | 积分成本 |
|----------|----------|------|--------|------|----------|
| doubao-seedance-1-0-pro | 豆包Seedance Pro | T2V,I2V | 480p,720p,1080p | 5,10秒 | 80 |
| doubao-seedance-1-0-lite-t2v | 豆包Seedance Lite T2V | T2V | 480p,720p,1080p | 5,10秒 | 60 |
| doubao-seedance-1-0-lite-i2v | 豆包Seedance Lite I2V | I2V | 480p,720p | 5,10秒 | 70 |
| doubao-seaweed | 豆包Seaweed | T2V,I2V | 480p,720p | 5秒 | 50 |
| wan2-1-14b-t2v | Wan2 T2V | T2V | 480p,720p | 5秒 | 40 |
| wan2-1-14b-i2v | Wan2 I2V | I2V | 480p,720p | 5秒 | 45 |
| wan2-1-14b-flf2v | Wan2 FLF2V | FLF2V | 720p | 5秒 | 55 |

## 使用示例

### JavaScript示例

```javascript
// 查询所有模型配置
fetch('/agent/video-model-config/list')
  .then(response => response.json())
  .then(data => {
    console.log('所有模型配置:', data.data);
  });

// 查询MiniMax的文生视频模型
fetch('/agent/video-model-config/provider/MINIMAX/model-type/T2V')
  .then(response => response.json())
  .then(data => {
    console.log('MiniMax文生视频模型:', data.data);
  });

// 根据条件搜索
fetch('/agent/video-model-config/search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    provider: 'DOUBAO',
    supportFirstFrame: true,
    maxPointsCost: 80
  })
})
.then(response => response.json())
.then(data => {
  console.log('搜索结果:', data.data);
});
```

### Java示例

```java
@Resource
private VideoModelConfigService videoModelConfigService;

// 查询所有启用的配置
List<VideoModelConfigRes> allConfigs = videoModelConfigService.listAllEnabledConfigs();

// 查询豆包的图生视频模型
List<VideoModelConfigRes> doubaoI2VConfigs = 
    videoModelConfigService.listConfigsByProviderAndModelType("DOUBAO", "I2V");

// 根据模型名称获取详情
VideoModelConfigRes config = 
    videoModelConfigService.getConfigByModelName("MiniMax-Hailuo-02");
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 404 | 模型配置不存在 | 检查模型名称是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |
