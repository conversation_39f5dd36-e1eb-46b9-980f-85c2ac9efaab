# 音频合成服务 (AudioSynthesisService)

## 概述

音频合成服务是一个用于将多个音频文件合成为一个音频文件的服务。它通过调用ComfyUI API来实现音频合成功能，支持异步处理和状态查询。

**重要更新**: 现在音频合成和对口型处理已经分离，音频合成完成后会自动触发对口型处理。

## 功能特性

- 支持最多7个音频文件的合成
- 异步处理，支持任务状态查询
- 自动处理ComfyUI回调结果
- 支持任务取消功能
- 完整的错误处理和日志记录
- **音频合成完成后自动触发对口型处理**
- **分离音频合成和对口型处理流程**

## 数据库表结构

### ai_audio_synthesis_task 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| task_id | varchar(100) | 任务ID（ComfyUI返回） |
| shot_id | bigint | 分镜ID |
| user_id | varchar(100) | 用户ID |
| audio_urls | text | 音频URL集合（JSON格式） |
| audio_count | int | 音频文件数量 |
| webapp_id | varchar(100) | ComfyUI WebApp ID |
| api_key | varchar(100) | ComfyUI API Key |
| client_id | varchar(100) | 客户端ID |
| net_wss_url | varchar(500) | WebSocket连接URL |
| status | varchar(20) | 任务状态 |
| result_audio_url | varchar(500) | 合成结果音频地址 |
| error_message | text | 错误信息 |
| task_cost_time | bigint | 任务耗时（毫秒） |
| processing_start_time | datetime | 处理开始时间 |
| processing_end_time | datetime | 处理结束时间 |
| request_params | text | 请求参数（JSON格式） |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| del_flag | tinyint | 删除标志 |

## API 接口

### 1. 提交音频合成任务

**接口地址：** `POST /agent/audio-synthesis/submit`

**请求参数：**
```json
{
  "shotId": 12345,
  "userId": "user123",
  "audioUrls": [
    "https://example.com/audio1.wav",
    "https://example.com/audio2.wav",
    "https://example.com/audio3.wav"
  ],
  "webappId": "1950797978980253697",
  "apiKey": "264fec3cd17144c59ec690b37a016972"
}
```

**响应结果：**
```json
{
  "success": true,
  "data": {
    "taskId": "12345",
    "shotId": 12345,
    "status": "PROCESSING",
    "netWssUrl": "wss://example.com/ws",
    "clientId": "client123"
  }
}
```

### 2. 查询任务状态

**接口地址：** `GET /agent/audio-synthesis/status/{taskId}`

**响应结果：**
```json
{
  "success": true,
  "data": {
    "taskId": "12345",
    "shotId": 12345,
    "success": true,
    "status": "COMPLETED",
    "resultAudioUrl": "https://example.com/result.wav",
    "taskCostTime": 5000,
    "startTime": 1640995200000,
    "endTime": 1640995205000
  }
}
```

### 3. 查询分镜最新任务

**接口地址：** `GET /agent/audio-synthesis/latest-by-shot/{shotId}`

### 4. 取消任务

**接口地址：** `POST /agent/audio-synthesis/cancel/{taskId}`

### 5. 测试接口

**接口地址：** `POST /agent/audio-synthesis/test`

**请求参数：**
- shotId: 分镜ID
- userId: 用户ID
- audioUrls: 音频URL列表

## 使用示例

### Java 代码示例

```java
@Autowired
private AudioSynthesisService audioSynthesisService;

// 创建音频合成请求
AudioSynthesisRequest request = new AudioSynthesisRequest();
request.setShotId(12345L);
request.setUserId("user123");
request.setAudioUrls(Arrays.asList(
    "https://example.com/audio1.wav",
    "https://example.com/audio2.wav"
));

// 提交任务
AudioSynthesisResult result = audioSynthesisService.submitSynthesisTask(request);
System.out.println("任务ID: " + result.getTaskId());

// 查询任务状态
AudioSynthesisResult status = audioSynthesisService.getTaskStatus(result.getTaskId());
if (status.getSuccess() != null && status.getSuccess()) {
    System.out.println("合成完成，结果URL: " + status.getResultAudioUrl());
}
```

### cURL 示例

```bash
# 提交音频合成任务
curl -X POST "http://localhost:8080/agent/audio-synthesis/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "shotId": 12345,
    "userId": "user123",
    "audioUrls": [
      "https://wlpaas.weilitech.cn/dify/dev/1916418011341144064/stable-audio/6c9911cb2b004776b0b8c0937f1e94a4.wav",
      "https://wlpaas.weilitech.cn/dify/dev/1916418011341144064/stable-audio/c8defd48b23a4dc195d4df66059cffb8.wav"
    ]
  }'

# 查询任务状态
curl -X GET "http://localhost:8080/agent/audio-synthesis/status/12345"

# 取消任务
curl -X POST "http://localhost:8080/agent/audio-synthesis/cancel/12345"
```

## ComfyUI 节点配置

音频合成服务使用以下ComfyUI节点配置：

- 节点19-25：音频输入节点（最多7个）
- 每个节点的fieldName为"value"
- 空的音频位置使用空字符串""

## 任务状态说明

- **PENDING**: 等待中
- **PROCESSING**: 处理中
- **COMPLETED**: 已完成
- **FAILED**: 失败
- **CANCELLED**: 已取消

## 错误处理

服务包含完整的错误处理机制：

1. 请求参数验证
2. ComfyUI API调用异常处理
3. 数据库操作异常处理
4. 回调处理异常处理

## 注意事项

1. 最多支持7个音频文件合成
2. 音频URL必须是有效的HTTP/HTTPS地址
3. 任务提交后会立即返回，需要通过回调或轮询获取结果
4. 建议在生产环境中配置适当的超时和重试机制

## 监控和日志

服务提供详细的日志记录，包括：

- 任务提交日志
- ComfyUI API调用日志
- 回调处理日志
- 错误异常日志

可以通过日志监控服务的运行状态和性能指标。

## 新的工作流程

### 分镜对口型处理流程更新

现在分镜对口型处理已经分为两个阶段：

#### 阶段1：音频处理
1. 用户调用 `ShotLipSyncService.submitLipSync()` 提交对口型任务
2. 系统检查分镜下的音频数据
3. **如果只有一个音频**：直接跳到阶段2，开始对口型处理
4. **如果有多个音频**：调用 `AudioSynthesisService.submitSynthesisTask()` 提交音频合成任务
5. 返回任务信息，状态为 `RUNNING`（单个音频）或 `AUDIO_SYNTHESIS`（多个音频）
6. 分镜状态更新为"处理中"

#### 阶段2：对口型处理
**单个音频路径**：
1. **检查音频类型：如果audioType是2，抛出"此分镜不支持对口型"异常**
2. 直接调用 `ShotLipSyncService.startLipSyncAfterAudioSynthesis()`
3. 开始对口型处理，调用ComfyUI对口型API
4. 保存对口型任务记录
5. **对口型完成后，从回调结果中提取第一个MP4文件**
6. **上传MP4文件到OSS**
7. **更新分镜状态和类型为video**
8. **更新或创建ai_canvas_video表记录**

**多个音频路径**：
1. **检查音频类型：如果所有音频的audioType都是2，抛出"此分镜不支持对口型"异常**
2. ComfyUI完成音频合成后，通过回调通知系统
3. `AudioSynthesisService.handleCallback()` 处理回调结果
4. **如果音频合成成功，先上传音频到OSS**
5. **使用OSS返回的URL更新任务记录**
6. 使用OSS URL调用 `ShotLipSyncService.startLipSyncAfterAudioSynthesis()`
7. 开始对口型处理，调用ComfyUI对口型API
8. 保存对口型任务记录
9. **对口型完成后，从回调结果中提取第一个MP4文件**
10. **上传MP4文件到OSS**
11. **更新分镜状态和类型为video**
12. **更新或创建ai_canvas_video表记录**

### 流程图

#### 单个音频流程（优化路径）
```
用户提交对口型任务
        ↓
    检查音频数据
        ↓
   只有1个音频？
        ↓ 是
   检查音频类型
        ↓
  audioType是2？
        ↓ 是
   抛出"此分镜不支持对口型"异常
        ↓ 否
   直接触发对口型处理
        ↓
   ComfyUI处理对口型
        ↓
    对口型完成回调
        ↓
      任务完成
```

#### 多个音频流程（合成路径）
```
用户提交对口型任务
        ↓
    检查音频数据
        ↓
   有多个音频？
        ↓ 是
   检查所有音频类型
        ↓
  所有audioType都是2？
        ↓ 是
   抛出"此分镜不支持对口型"异常
        ↓ 否
   提交音频合成任务
        ↓
    返回合成任务信息
        ↓
   ComfyUI处理音频合成
        ↓
    音频合成完成回调
        ↓
     上传音频到OSS
        ↓
   使用OSS URL触发对口型处理
        ↓
   ComfyUI处理对口型
        ↓
    对口型完成回调
        ↓
      任务完成
```

### 优势

1. **解耦处理**: 音频合成和对口型处理完全分离，便于维护和扩展
2. **异步处理**: 两个阶段都是异步处理，提高系统响应性
3. **状态追踪**: 可以清楚地追踪每个阶段的处理状态
4. **错误隔离**: 音频合成失败不会影响对口型处理的代码逻辑
5. **资源优化**: 可以独立控制音频合成和对口型处理的并发数
6. **性能优化**: 单个音频跳过合成步骤，直接进行对口型处理，减少延迟
7. **智能路由**: 根据音频数量自动选择最优处理路径
8. **OSS集成**: 音频合成完成后自动上传到OSS，确保文件的持久化存储
9. **URL管理**: 使用OSS URL进行对口型处理，提高文件访问的稳定性

### 状态说明

- `AUDIO_SYNTHESIS`: 音频合成阶段（多个音频）
- `DIRECT_LIP_SYNC`: 直接对口型处理（单个音频，内部状态）
- `RUNNING`: 对口型处理阶段
- `COMPLETED`: 整个流程完成
- `FAILED`: 任何阶段失败

### 监控建议

建议监控以下指标：
- 音频合成任务的成功率和耗时
- 对口型任务的成功率和耗时
- 从音频合成到对口型处理的延迟时间
- 各阶段的错误率和错误类型
- OSS上传的成功率和耗时
- OSS文件访问的稳定性

## OSS上传功能

### OSS上传流程

音频合成完成后，系统会自动将合成的音频文件上传到OSS：

1. **接收ComfyUI回调**: 音频合成完成后，ComfyUI通过回调返回音频文件URL
2. **上传到OSS**: 系统自动下载音频文件并上传到OSS
3. **更新任务记录**: 使用OSS URL更新数据库中的任务记录
4. **触发对口型**: 使用OSS URL开始对口型处理

### OSS路径规则

OSS文件路径遵循以下规则：
```
dify/{env}/{userId}/audio-synthesis/synthesis_{UUID}.{extension}
```

- `{env}`: 环境标识（如：dev、test、prod）
- `{userId}`: 用户ID
- `{UUID}`: 唯一标识符
- `{extension}`: 音频文件扩展名（wav、mp3、ogg等）

### 支持的音频格式

系统支持以下音频格式的自动识别和处理：
- **WAV**: 默认格式，高质量无损音频
- **MP3**: 常用的压缩音频格式
- **OGG**: 开源的音频压缩格式
- **M4A**: Apple的音频格式
- **FLAC**: 无损压缩音频格式
- **AAC**: 高效的音频编码格式

### 错误处理

如果OSS上传失败：
1. 任务状态会被标记为`FAILED`
2. 错误信息会被记录到数据库
3. 不会触发后续的对口型处理
4. 用户可以通过API查询到具体的错误信息

### 配置要求

使用OSS上传功能需要确保：
1. OSS工具类（`OssUtils`）已正确配置
2. OSS访问权限已设置
3. 环境变量`spring.profiles.active`已配置
4. 网络连接稳定，支持文件下载和上传

### 性能优化

- **异步上传**: OSS上传在回调处理中异步执行
- **错误重试**: 可以配置上传失败时的重试机制
- **文件清理**: 可以配置定期清理临时文件的策略
- **并发控制**: 可以限制同时进行的OSS上传数量

## 对口型回调处理

### 回调数据格式

对口型任务完成后，ComfyUI会返回包含多个文件的回调数据：

```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "fileUrl": "https://rh-images.xiaoyaoyou.com/output/video.mp4",
            "fileType": "mp4",
            "taskCostTime": 345,
            "nodeId": "111"
        },
        {
            "fileUrl": "https://rh-images.xiaoyaoyou.com/output/frame1.png",
            "fileType": "png",
            "taskCostTime": 345,
            "nodeId": "164"
        }
    ]
}
```

### 处理逻辑

1. **提取MP4文件**: 从回调结果中找到第一个`fileType`为`mp4`的文件
2. **上传到OSS**: 将MP4文件上传到OSS，路径格式为`dify/{env}/{userId}/lip-sync/lipsync_{UUID}.mp4`
3. **更新对口型记录**: 使用OSS URL更新对口型任务记录状态为`COMPLETED`
4. **更新分镜信息**:
   - 将分镜的`type`字段更新为`video`
   - 将分镜的`shotStatus`更新为`2`（已完成）
5. **更新视频记录**: 在`ai_canvas_video`表中更新或创建视频记录

### 数据库更新

#### ai_canvas_shot表更新
- `type`: 更新为`"video"`
- `shot_status`: 更新为`2`（已完成）
- `update_time`: 更新为当前时间

#### ai_canvas_video表更新/创建
- 如果存在记录：更新`video_url`和`video_status`
- 如果不存在记录：创建新记录，包含画布ID、分镜编码、视频URL等信息
- `volume`: 默认设置为1.0
- `video_status`: 设置为`"COMPLETED"`

### 错误处理

- **未找到MP4文件**: 如果回调结果中没有MP4文件，任务标记为失败
- **OSS上传失败**: 上传失败时记录错误信息，任务标记为失败
- **数据库更新失败**: 任何数据库操作失败都会回滚事务

### 监控指标

建议监控以下对口型相关指标：
- 对口型任务的成功率
- MP4文件提取成功率
- OSS上传成功率（视频文件）
- 数据库更新成功率
- 端到端处理时间（从对口型开始到完成）

## 音频类型检查功能

### 功能概述

在`processFinalAudio`方法中新增了音频类型检查功能，用于判断分镜是否支持对口型处理。

### 检查规则

- **检查条件**: 如果分镜中所有音频的`audioType`字段都等于`2`
- **检查结果**: 抛出`BizException`异常，消息为"此分镜不支持对口型"
- **检查时机**: 在音频处理的最开始阶段，优先于音频合成和对口型处理

### 实现逻辑

```java
// 检查音频类型，如果所有音频的audioType都是2，则不支持对口型
boolean allAudioType2 = audioList.stream()
        .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);

if (allAudioType2) {
    log.warn("分镜所有音频类型都是2，不支持对口型: shotId={}", shotId);
    throw new BizException("此分镜不支持对口型");
}
```

### 应用场景

#### 单个音频场景
- 如果唯一的音频`audioType`为`2`，直接抛出异常
- 如果音频`audioType`不为`2`，继续执行对口型处理

#### 多个音频场景
- 如果所有音频的`audioType`都为`2`，直接抛出异常
- 如果存在任何一个音频的`audioType`不为`2`，继续执行音频合成和对口型处理

### 日志记录

- **警告日志**: 当检测到不支持对口型时记录警告
- **调试日志**: 记录每个音频的详细信息（URL和类型）
- **信息日志**: 增强现有日志，包含音频类型信息

### 错误处理

- **异常类型**: `BizException`
- **异常消息**: "此分镜不支持对口型"
- **处理时机**: 在音频处理的最早阶段，避免不必要的资源消耗

### 边界情况处理

- **null值处理**: `audioType`为`null`的音频不被视为类型`2`
- **空列表处理**: 空音频列表会通过检查（但会在后续步骤中处理）
- **混合类型处理**: 只要有一个音频不是类型`2`，就允许继续处理

### 性能优化

- **早期检查**: 在资源密集型操作之前进行检查
- **流式处理**: 使用Java Stream API进行高效的类型检查
- **短路求值**: 一旦发现非类型`2`的音频，立即停止检查

### 监控建议

建议监控以下指标：
- 音频类型检查的触发频率
- 因音频类型限制而被拒绝的请求数量
- 不同音频类型的分布情况
- 用户对"不支持对口型"错误的反馈
