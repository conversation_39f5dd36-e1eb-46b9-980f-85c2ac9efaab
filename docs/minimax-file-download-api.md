# MiniMax文件下载API接口文档

## 概述

本文档描述了用于下载MiniMax生成的视频文件的REST API接口。这些接口允许客户端通过任务ID和文件ID下载MiniMax生成的视频文件。

## 接口列表

### 1. 下载视频文件（流式）

**接口地址**: `GET /agent/video/minimax/download`

**接口描述**: 下载MiniMax生成的视频文件，返回文件流

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| taskId | String | 是 | MiniMax任务ID |
| fileId | String | 是 | MiniMax文件ID |

**请求示例**:
```http
GET /agent/video/minimax/download?taskId=test-task-123&fileId=file-456
```

**响应**:
- **成功响应** (200 OK):
  - Content-Type: `application/octet-stream`
  - Content-Disposition: `attachment; filename="video_test-task-123.mp4"`
  - Body: 视频文件二进制流

- **任务不存在** (404 Not Found):
  ```json
  {
    "error": "Task not found"
  }
  ```

- **无权限** (401 Unauthorized):
  ```json
  {
    "error": "API key not available"
  }
  ```

- **服务器错误** (500 Internal Server Error):
  ```json
  {
    "error": "Internal server error"
  }
  ```

### 2. 下载视频文件（字节数组）

**接口地址**: `GET /agent/video/minimax/download/bytes`

**接口描述**: 下载MiniMax生成的视频文件，返回字节数组

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| taskId | String | 是 | MiniMax任务ID |
| fileId | String | 是 | MiniMax文件ID |

**请求示例**:
```http
GET /agent/video/minimax/download/bytes?taskId=test-task-123&fileId=file-456
```

**响应**:
- **成功响应** (200 OK):
  - Content-Type: `application/octet-stream`
  - Content-Disposition: `attachment; filename="video_test-task-123.mp4"`
  - Content-Length: 文件大小（字节）
  - Body: 视频文件字节数组

- **错误响应**: 与流式下载接口相同

## 使用说明

### 1. 获取任务ID和文件ID

在调用下载接口之前，您需要：

1. **任务ID**: 从MiniMax视频生成API响应中获取，或从`ai_video_generation_record`表中查询
2. **文件ID**: 从MiniMax任务状态查询API响应中获取

### 2. 选择下载方式

- **流式下载** (`/download`): 适合大文件，内存占用较少，支持断点续传
- **字节数组下载** (`/download/bytes`): 适合小文件，一次性加载到内存

### 3. 错误处理

接口可能返回以下错误：

- **404 Not Found**: 任务ID不存在或已被删除
- **401 Unauthorized**: 无法获取对应的API密钥
- **500 Internal Server Error**: 服务器内部错误，如网络问题、MiniMax API异常等

## 代码示例

### JavaScript (Fetch API)

```javascript
// 下载视频文件
async function downloadVideo(taskId, fileId) {
    try {
        const response = await fetch(`/agent/video/minimax/download?taskId=${taskId}&fileId=${fileId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `video_${taskId}.mp4`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
    } catch (error) {
        console.error('下载失败:', error);
    }
}

// 使用示例
downloadVideo('test-task-123', 'file-456');
```

### Java (OkHttp)

```java
import okhttp3.*;
import java.io.*;

public class MiniMaxDownloader {
    private final OkHttpClient client = new OkHttpClient();
    
    public void downloadVideo(String taskId, String fileId) throws IOException {
        String url = String.format("/agent/video/minimax/download?taskId=%s&fileId=%s", taskId, fileId);
        
        Request request = new Request.Builder()
                .url(url)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("下载失败: " + response);
            }
            
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new IOException("响应体为空");
            }
            
            // 保存到文件
            try (InputStream inputStream = responseBody.byteStream();
                 FileOutputStream outputStream = new FileOutputStream("video_" + taskId + ".mp4")) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
        }
    }
}
```

### Python (requests)

```python
import requests

def download_video(task_id, file_id):
    url = f"/agent/video/minimax/download"
    params = {
        'taskId': task_id,
        'fileId': file_id
    }
    
    try:
        response = requests.get(url, params=params, stream=True)
        response.raise_for_status()
        
        filename = f"video_{task_id}.mp4"
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"视频下载成功: {filename}")
        
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")

# 使用示例
download_video('test-task-123', 'file-456')
```

## 注意事项

### 1. API密钥管理

当前实现中，`getApiKeyForRecord`方法返回null，需要根据实际业务逻辑实现：

```java
private String getApiKeyForRecord(AiVideoGenerationRecordPo record) {
    // TODO: 根据用户ID、模型类型等获取对应的API密钥
    // 示例实现：
    if ("MiniMax-Hailuo-02".equals(record.getModel())) {
        return apiKeyService.getApiKeyByUserId(record.getUserId());
    }
    return null;
}
```

### 2. 文件大小限制

- 建议对下载文件大小进行限制，避免内存溢出
- 对于大文件，推荐使用流式下载

### 3. 安全考虑

- 验证用户权限，确保只能下载自己的文件
- 添加访问频率限制，防止恶意下载
- 考虑添加文件访问日志

### 4. 性能优化

- 使用CDN加速文件下载
- 实现文件缓存机制
- 考虑使用异步处理大文件下载

## 故障排查

### 常见问题

1. **404错误**: 检查taskId是否正确，任务是否存在
2. **401错误**: 检查API密钥配置是否正确
3. **500错误**: 查看服务器日志，检查网络连接和MiniMax API状态
4. **下载缓慢**: 检查网络连接，考虑使用CDN

### 调试方法

1. 查看应用日志中的详细错误信息
2. 检查`ai_video_generation_record`表中的任务状态
3. 验证MiniMax API的可用性
4. 测试网络连接和文件访问权限
