# XXL-Job 定时任务配置

## 用户资源状态同步任务

### 任务信息

- **任务名称**: 用户资源状态同步任务
- **JobHandler**: `userResourceStatusSyncJobHandler`
- **执行频率**: 每2分钟执行一次
- **Cron表达式**: `0 */2 * * * ?`

### 任务描述

该定时任务用于同步用户资源的生成状态，主要功能包括：

1. 查询所有状态为 `PENDING` 的用户资源记录
2. 调用外部API查询任务状态
3. 更新本地资源记录状态
4. 处理超时任务（超过10分钟的任务标记为失败）
5. 保存生成成功的资源结果

### 配置步骤

1. **登录XXL-Job管理界面**
   - 访问: `http://your-xxl-job-admin-url`
   - 使用管理员账号登录

2. **创建执行器**
   - 执行器名称: `smart-agent`
   - 注册方式: 自动注册
   - 机器地址: 自动获取

3. **创建任务**
   - 基本信息:
     - 执行器: `smart-agent`
     - 任务描述: `用户资源状态同步任务`
     - 负责人: `admin`
     - 报警邮件: `<EMAIL>`
   
   - 调度配置:
     - 调度类型: `CRON`
     - Cron表达式: `0 */2 * * * ?`
     - 运行模式: `BEAN`
     - JobHandler: `userResourceStatusSyncJobHandler`
   
   - 高级配置:
     - 路由策略: `第一个`
     - 子任务: 无
     - 任务超时时间: `300` (5分钟)
     - 失败重试次数: `3`

4. **启动任务**
   - 保存任务配置
   - 点击"启动"按钮启动任务

### 监控和日志

1. **任务执行日志**
   - 在XXL-Job管理界面的"调度日志"中查看任务执行情况
   - 可以查看每次执行的开始时间、结束时间、执行结果等

2. **应用日志**
   - 任务执行过程中的详细日志会输出到应用日志文件
   - 日志级别: INFO、WARN、ERROR

3. **关键监控指标**
   - 任务执行成功率
   - 每次处理的资源数量
   - 状态同步成功/失败数量
   - 任务执行耗时

### 故障处理

1. **任务执行失败**
   - 检查网络连接是否正常
   - 检查外部API是否可用
   - 检查数据库连接是否正常
   - 查看详细错误日志

2. **任务执行超时**
   - 检查是否有大量待处理的资源
   - 考虑调整任务超时时间
   - 优化查询性能

3. **资源状态同步异常**
   - 检查外部API返回的数据格式
   - 验证资源记录的完整性
   - 检查JSON解析是否正常

### 性能优化

1. **批量处理**
   - 每次最多处理50个资源记录
   - 避免一次性处理过多数据

2. **超时控制**
   - 设置合理的任务超时时间
   - 对超时任务进行标记处理

3. **错误重试**
   - 对临时性错误进行重试
   - 设置最大重试次数避免无限重试

### 配置示例

```yaml
# application.yml
xxl:
  job:
    admin:
      addresses: http://your-xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: smart-agent
      ip: 
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
    accessToken: your-access-token
```

### 相关代码

```java
@XxlJob("userResourceStatusSyncJobHandler")
public ReturnT<String> syncUserResourceStatus(String param) {
    log.info("开始执行用户资源状态同步任务...");
    // 任务执行逻辑
    return ReturnT.SUCCESS;
}
```

### 注意事项

1. **数据一致性**
   - 使用事务确保数据更新的一致性
   - 避免并发更新同一资源记录

2. **API限流**
   - 注意外部API的调用频率限制
   - 合理控制并发请求数量

3. **资源清理**
   - 定期清理过期的资源记录
   - 避免数据库存储过多历史数据

4. **监控告警**
   - 设置任务执行失败告警
   - 监控资源状态同步的成功率
