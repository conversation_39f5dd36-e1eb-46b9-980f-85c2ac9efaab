# 用户视频记录查询分享功能增强

## 功能概述

在 `queryUserVideoRecords` 方法中，当 `videoType` 为 2（渲染导出视频）时，增加了视频分享状态相关字段的返回，让用户能够在查询视频记录时看到视频的分享状态。

## 修改内容

### 1. UserVideoRecordRes 响应类增强

在 `UserVideoRecordRes` 类中新增了以下分享相关字段：

```java
@Schema(description = "分享状态", example = "0", allowableValues = {"0", "1"})
private Integer shareStatus;

@Schema(description = "分享状态描述", example = "未分享")
private String shareStatusDesc;

@Schema(description = "分享码", example = "ABC123DEF")
private String shareCode;

@Schema(description = "分享时间", example = "2024-01-01T12:10:00Z")
private Date shareTime;
```

同时添加了 `getShareStatusDesc()` 方法来自动生成分享状态描述：
- `shareStatus = 0` → "未分享"
- `shareStatus = 1` → "已分享"
- `shareStatus = null` → "未分享"（默认值）

### 2. 转换方法增强

修改了 `convertToVideoResFromRenderExport` 方法，在转换 `AiVideoRenderExportPo` 到 `UserVideoRecordRes` 时，增加了分享字段的设置：

```java
// 设置分享相关字段
res.setShareStatus(po.getShareStatus() != null ? po.getShareStatus() : 0);
res.setShareCode(po.getShareCode());
res.setShareTime(po.getShareTime());
```

## 业务逻辑

### 查询条件
- 当 `videoType = 1` 时：查询 `ai_video_generation` 表（AI生成视频），不包含分享字段
- 当 `videoType = 2` 时：查询 `ai_video_render_export` 表（渲染导出视频），**包含分享字段**

### 分享字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| shareStatus | Integer | 分享状态：0-未分享，1-已分享 | 0 |
| shareStatusDesc | String | 分享状态描述（自动生成） | "未分享" |
| shareCode | String | 分享码（8位随机字符串） | "ABC123DEF" |
| shareTime | Date | 分享时间 | "2024-01-01T12:10:00Z" |

### 默认值处理
- `shareStatus`：如果数据库中为 null，则默认设置为 0（未分享）
- `shareCode`：如果未分享，则为 null
- `shareTime`：如果未分享，则为 null

## API 响应示例

### videoType = 1 的响应（AI生成视频）
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "userId": "user123",
      "shotId": 456,
      "model": "runway-gen3",
      "prompt": "一只可爱的小猫在花园里玩耍",
      "videoUrl": "https://example.com/video1.mp4",
      "status": 2,
      "createTime": "2024-01-01T10:00:00Z"
      // 不包含分享相关字段
    }
  ]
}
```

### videoType = 2 的响应（渲染导出视频）
```json
{
  "success": true,
  "data": [
    {
      "id": 789,
      "userId": "user123",
      "prompt": "我的画布作品",
      "videoUrl": "https://example.com/video2.mp4",
      "status": 2,
      "shareStatus": 1,
      "shareStatusDesc": "已分享",
      "shareCode": "ABC123DEF",
      "shareTime": "2024-01-01T12:10:00Z",
      "createTime": "2024-01-01T10:00:00Z"
    },
    {
      "id": 790,
      "userId": "user123",
      "prompt": "另一个画布作品",
      "videoUrl": "https://example.com/video3.mp4",
      "status": 2,
      "shareStatus": 0,
      "shareStatusDesc": "未分享",
      "shareCode": null,
      "shareTime": null,
      "createTime": "2024-01-01T11:00:00Z"
    }
  ]
}
```

## 使用场景

1. **用户查看自己的视频列表**：可以直接看到哪些视频已经分享，哪些还未分享
2. **前端UI展示**：可以根据 `shareStatus` 显示不同的图标或状态
3. **快速分享操作**：前端可以根据分享状态决定显示"分享"还是"取消分享"按钮
4. **分享管理**：用户可以方便地管理自己的分享视频

## 兼容性

- ✅ **向后兼容**：对于 `videoType = 1` 的查询，响应格式保持不变
- ✅ **数据安全**：只有视频所有者才能看到分享状态信息
- ✅ **性能优化**：分享字段直接从数据库查询，无需额外的关联查询

## 测试建议

1. **测试 videoType = 1**：确认响应中不包含分享字段
2. **测试 videoType = 2**：确认响应中包含正确的分享字段
3. **测试已分享视频**：确认 shareStatus = 1 且包含分享码和分享时间
4. **测试未分享视频**：确认 shareStatus = 0 且分享码和分享时间为 null
5. **测试分享状态描述**：确认 shareStatusDesc 字段正确显示状态描述
