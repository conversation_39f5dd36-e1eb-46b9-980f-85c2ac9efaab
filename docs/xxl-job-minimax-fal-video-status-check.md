# XXL-Job 配置：MiniMax FAL 视频状态检查任务

## 任务配置

### 基本信息
- **任务名称**: `minimaxFalVideoStatusCheck`
- **任务描述**: MiniMax FAL 视频生成状态检查定时任务
- **负责人**: 开发团队
- **报警邮件**: 配置相应的邮件地址

### 调度配置
- **调度类型**: CRON
- **Cron表达式**: `0 */1 * * * ?` (每分钟执行一次)
- **运行模式**: BEAN
- **JobHandler**: `minimaxFalVideoStatusCheck`

### 高级配置
- **路由策略**: 第一个
- **子任务**: 无
- **任务超时时间**: 300秒
- **失败重试次数**: 1次
- **负责人**: admin
- **报警邮件**: 根据实际情况配置

## 任务功能说明

### 主要功能
1. **状态轮询**: 每分钟检查所有处理中的 MiniMax FAL 视频生成任务
2. **结果获取**: 对于已完成的任务，获取生成的视频URL
3. **文件上传**: 将生成的视频上传到 OSS 存储
4. **数据更新**: 更新相关数据库表的状态和结果

### 处理流程
```
1. 查询处理中的 FAL 任务
   ↓
2. 遍历每个任务
   ↓
3. 调用 FAL API 查询状态
   ↓
4. 根据状态进行处理：
   - COMPLETED: 获取结果 → 上传OSS → 更新数据库
   - IN_QUEUE/IN_PROGRESS: 记录日志，继续等待
   - 其他状态: 标记为失败
   ↓
5. 错误处理和重试机制
```

### 涉及的数据库表
- `ai_video_generation`: 主要的视频生成任务表
- `ai_canvas_shot`: 分镜表，更新分镜状态
- `ai_canvas_video`: 画布视频表，保存视频信息
- `ai_canvas_material`: 画布材料表，记录生成的视频资源

## 监控和告警

### 关键指标
- 任务执行频率：每分钟
- 任务执行时长：正常情况下应在30秒内完成
- 处理成功率：应保持在95%以上
- 失败重试次数：单个任务最多重试3次

### 告警规则
1. **执行超时**: 任务执行时间超过5分钟
2. **连续失败**: 连续3次执行失败
3. **处理异常**: 单个任务处理失败率超过10%

### 日志监控
- 关注 `MinimaxFalVideoStatusCheckJob` 类的日志输出
- 重点监控 ERROR 和 WARN 级别的日志
- 定期检查任务处理统计信息

## 部署说明

### 环境要求
- XXL-Job 调度中心已部署并运行
- 应用已注册到 XXL-Job 执行器
- 确保 FAL API 密钥配置正确

### 配置步骤
1. 登录 XXL-Job 管理后台
2. 进入任务管理页面
3. 点击"新增"按钮
4. 填写上述配置信息
5. 保存并启动任务

### 验证方法
1. 提交一个图像到视频的生成任务
2. 观察任务状态变化
3. 检查日志输出是否正常
4. 验证最终结果是否正确

## 故障排查

### 常见问题

#### 1. 任务不执行
- 检查 XXL-Job 调度中心连接状态
- 确认任务是否已启动
- 检查 Cron 表达式是否正确

#### 2. API 调用失败
- 检查 FAL API 密钥是否正确
- 确认网络连接是否正常
- 查看 API 限流情况

#### 3. 文件上传失败
- 检查 OSS 配置是否正确
- 确认存储权限是否足够
- 验证网络连接稳定性

#### 4. 数据库更新失败
- 检查数据库连接状态
- 确认表结构是否正确
- 查看事务是否正常提交

### 日志分析
```bash
# 查看任务执行日志
grep "MinimaxFalVideoStatusCheckJob" application.log

# 查看错误日志
grep "ERROR.*MinimaxFalVideoStatusCheckJob" application.log

# 查看任务处理统计
grep "找到.*个需要检查状态的.*任务" application.log
```

## 性能优化

### 建议优化点
1. **批量处理**: 考虑批量查询和更新以提高效率
2. **并发控制**: 对于大量任务，可以考虑并发处理
3. **缓存机制**: 对频繁查询的数据进行缓存
4. **异步处理**: 文件上传等耗时操作可以异步处理

### 资源使用
- CPU: 轻量级任务，CPU 使用率较低
- 内存: 主要用于临时存储任务数据
- 网络: 主要用于 API 调用和文件上传
- 存储: 临时文件存储需求较小

## 版本更新记录

### v1.0.0 (初始版本)
- 实现基本的状态检查功能
- 支持视频文件上传到 OSS
- 完整的错误处理和重试机制
- 数据库状态同步功能

### 后续版本规划
- 支持批量处理优化
- 增加更详细的监控指标
- 支持动态调整检查频率
- 增加任务优先级处理
