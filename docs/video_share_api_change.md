# 视频分享接口参数格式变更

## 变更概述

将 `toggleVideoShare` 接口的请求参数从查询参数（Query Parameter）改为JSON格式的请求体（Request Body），提供更好的API设计和扩展性。

## 变更详情

### 变更前
```http
PUT /agent/video-render/share/{taskId}?share=true
```

**请求参数**：
- `taskId` (路径参数) - 视频任务ID
- `share` (查询参数) - 分享状态，true/false

### 变更后
```http
PUT /agent/video-render/share/{taskId}
Content-Type: application/json

{
  "share": true
}
```

**请求参数**：
- `taskId` (路径参数) - 视频任务ID
- `req` (请求体) - VideoShareToggleReq对象

## 新增请求类

### VideoShareToggleReq.java
```java
@Data
@Schema(description = "视频分享切换请求")
public class VideoShareToggleReq {
    
    @Schema(description = "分享状态：true-分享，false-取消分享", required = true, example = "true")
    @NotNull(message = "分享状态不能为空")
    private Boolean share;
}
```

## 控制器方法变更

### 变更前
```java
@PutMapping("/share/{taskId}")
public SingleResponse<String> toggleVideoShare(
        @PathVariable("taskId") Long taskId,
        @RequestParam("share") Boolean share) {
    // ...
}
```

### 变更后
```java
@PutMapping("/share/{taskId}")
public SingleResponse<String> toggleVideoShare(
        @PathVariable("taskId") Long taskId,
        @Valid @RequestBody VideoShareToggleReq req) {
    // ...
}
```

## API 使用示例

### 分享视频
```bash
curl -X PUT "http://localhost:8080/agent/video-render/share/123" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "share": true
     }'
```

**响应**：
```json
{
  "success": true,
  "data": "ABC123DEF",
  "errorCode": null,
  "errorMessage": null
}
```

### 取消分享视频
```bash
curl -X PUT "http://localhost:8080/agent/video-render/share/123" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "share": false
     }'
```

**响应**：
```json
{
  "success": true,
  "data": null,
  "errorCode": null,
  "errorMessage": null
}
```

## 变更优势

### 1. 更好的API设计
- **RESTful风格**：使用JSON请求体更符合RESTful API设计规范
- **类型安全**：通过请求类提供更好的类型检查和验证
- **文档友好**：Swagger文档更清晰，参数结构更明确

### 2. 扩展性
- **易于扩展**：未来如需添加更多参数，只需在请求类中添加字段
- **版本兼容**：可以通过添加可选字段保持向后兼容

### 3. 验证增强
- **参数验证**：使用 `@Valid` 和 `@NotNull` 提供更严格的参数验证
- **错误处理**：更好的错误信息和异常处理

## 兼容性影响

### ⚠️ 破坏性变更
这是一个**破坏性变更**，现有的客户端需要更新调用方式：

**旧的调用方式（不再支持）**：
```javascript
// JavaScript 示例
fetch(`/agent/video-render/share/${taskId}?share=true`, {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
```

**新的调用方式**：
```javascript
// JavaScript 示例
fetch(`/agent/video-render/share/${taskId}`, {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    share: true
  })
})
```

## 迁移指南

### 前端代码迁移
1. **添加 Content-Type 头**：确保请求头包含 `Content-Type: application/json`
2. **修改参数传递**：将查询参数改为JSON请求体
3. **更新错误处理**：处理新的验证错误响应

### 测试更新
1. **API测试**：更新所有相关的API测试用例
2. **集成测试**：确保前后端集成正常工作
3. **文档更新**：更新API文档和使用说明

## 验证清单

部署前请确认：

- [ ] 前端代码已更新调用方式
- [ ] API测试用例已更新
- [ ] 文档已更新
- [ ] 错误处理已测试
- [ ] 参数验证已测试

## 回滚方案

如果需要回滚到旧版本，可以：

1. **临时兼容**：同时支持两种参数格式
2. **版本控制**：使用不同的API版本路径
3. **代码回滚**：回滚到变更前的代码版本

但建议尽快适配新的JSON格式，因为它提供了更好的API设计和扩展性。
