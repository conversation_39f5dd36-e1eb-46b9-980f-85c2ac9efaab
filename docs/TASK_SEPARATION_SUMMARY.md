# 任务分离实现总结

## 修改概述

根据需求，我们将图片任务分为两个独立的处理流程：**生成任务**和**编辑任务**，实现了完全的并发隔离。

## 核心修改

### 1. 任务分类

#### 生成任务（Generation Tasks）
- `GENERATE` - 基础图片生成
- `RETAIN` - 角色保留任务  
- `REDRAW` - 图片重绘任务
- `GENERATE_DOUBAO` - 豆包生成任务
- `GENERATE_FLUX` - Flux生成任务
- `GENERATE_CANVAS` - 画布生成任务

#### 编辑任务（Edit Tasks）
- `EDIT` - 图片编辑任务
- `CANVAS_EDIT` - 画布编辑任务

### 2. 调度器分离

#### imageTaskScheduler（生成任务调度器）
```java
// 查询条件：排除编辑任务
.notIn(AiImageTaskQueuePo::getTaskType, 
       TaskType.EDIT.getValue(), 
       TaskType.EDIT_CANVAS.getValue())
```

#### editImageTaskScheduler（编辑任务调度器）
```java
// 查询条件：只包含编辑任务
.in(AiImageTaskQueuePo::getTaskType, 
    TaskType.EDIT.getValue(), 
    TaskType.EDIT_CANVAS.getValue())
```

#### highPriorityImageTaskScheduler（高优先级生成任务调度器）
```java
// 查询条件：排除编辑任务，只处理生成任务的重试
.notIn(AiImageTaskQueuePo::getTaskType, 
       TaskType.EDIT.getValue(), 
       TaskType.EDIT_CANVAS.getValue())
```

### 3. 并发控制分离

#### 生成任务状态获取
```java
private DatabaseConcurrencyControlService.ConcurrencyStatus getGenerationTaskStatus() {
    // 只统计生成任务的处理中和待处理数量
    // 排除 EDIT 和 CANVAS_EDIT 类型
}
```

#### 编辑任务状态获取
```java
private DatabaseConcurrencyControlService.ConcurrencyStatus getEditTaskStatus() {
    // 只统计编辑任务的处理中和待处理数量
    // 只包含 EDIT 和 CANVAS_EDIT 类型
}
```

## 关键方法修改

### 1. scheduleImageTasks 方法
```java
@XxlJob("imageTaskScheduler")
public ReturnT<String> scheduleImageTasks(String param) {
    // 获取生成任务的系统状态（排除编辑任务）
    DatabaseConcurrencyControlService.ConcurrencyStatus status = getGenerationTaskStatus();
    
    // 查询待处理的生成任务，排除编辑任务
    List<AiImageTaskQueuePo> pendingTasks = getPendingGenerationTasks(status.getAvailable());
    
    // 处理逻辑...
}
```

### 2. scheduleEditTasks 方法
```java
@XxlJob("editImageTaskScheduler")
public ReturnT<String> scheduleEditTasks(String param) {
    // 获取编辑任务的系统状态
    DatabaseConcurrencyControlService.ConcurrencyStatus status = getEditTaskStatus();
    
    // 查询编辑类型的待处理任务
    List<AiImageTaskQueuePo> editTasks = getEditTasks(status.getAvailable());
    
    // 处理逻辑...
}
```

### 3. scheduleHighPriorityTasks 方法
```java
@XxlJob("highPriorityImageTaskScheduler")
public ReturnT<String> scheduleHighPriorityTasks(String param) {
    // 获取生成任务的系统状态（排除编辑任务）
    DatabaseConcurrencyControlService.ConcurrencyStatus status = getGenerationTaskStatus();
    
    // 查询高优先级待处理的生成任务（排除编辑任务）
    List<AiImageTaskQueuePo> highPriorityTasks = getHighPriorityGenerationTasks(status.getAvailable());
    
    // 处理逻辑...
}
```

## 数据库查询优化

### 生成任务查询
```sql
-- 待处理的生成任务
SELECT * FROM ai_image_task_queue 
WHERE task_status = 'PENDING' 
  AND task_type NOT IN ('EDIT', 'CANVAS_EDIT')
ORDER BY global_queue_position ASC 
LIMIT ?

-- 处理中的生成任务统计
SELECT COUNT(*) FROM ai_image_task_queue 
WHERE task_status = 'PROCESSING' 
  AND task_type NOT IN ('EDIT', 'CANVAS_EDIT')
```

### 编辑任务查询
```sql
-- 待处理的编辑任务
SELECT * FROM ai_image_task_queue 
WHERE task_status = 'PENDING' 
  AND task_type IN ('EDIT', 'CANVAS_EDIT')
ORDER BY retry_count ASC, create_time ASC 
LIMIT ?

-- 处理中的编辑任务统计
SELECT COUNT(*) FROM ai_image_task_queue 
WHERE task_status = 'PROCESSING' 
  AND task_type IN ('EDIT', 'CANVAS_EDIT')
```

## 监控和统计改进

### 统计信息格式
```
图片任务统计 - 待处理: 50 (生成: 35, 编辑: 15), 处理中: 3, 已完成: 1200, 失败: 8
```

### 告警规则
- 待处理生成任务数 > 100：生成任务负载过高
- 待处理编辑任务数 > 50：编辑任务堵塞，需要检查编辑任务调度器
- 处理中任务数 > 10：可能存在任务堵塞

## XXL-JOB 配置

| 任务名称 | Cron表达式 | 描述 | 处理任务类型 |
|---------|-----------|------|-------------|
| imageTaskScheduler | 0/30 * * * * ? | 每30秒处理生成任务 | 生成任务（排除编辑） |
| editImageTaskScheduler | 0/20 * * * * ? | 每20秒处理编辑任务 | 编辑任务（EDIT/CANVAS_EDIT） |
| highPriorityImageTaskScheduler | 0/10 * * * * ? | 每10秒处理高优先级生成任务 | 生成任务重试（排除编辑） |

## 并发控制策略

### 独立并发池
- **生成任务**：使用独立的并发控制，最大并发数由配置决定
- **编辑任务**：使用独立的并发控制，最大并发数由配置决定
- **互不影响**：生成任务的堵塞不会影响编辑任务的处理，反之亦然

### 配置参数
```yaml
app:
  concurrency:
    # 生成任务最大并发数
    max-concurrent-tasks: 5
    # 编辑任务最大并发数（可以单独配置）
    max-edit-concurrent-tasks: 3
```

## 测试验证

### 单元测试覆盖
- `testScheduleImageTasks_ShouldFilterOutEditTasks`: 验证生成任务调度器过滤编辑任务
- `testScheduleEditTasks_ShouldOnlyProcessEditTasks`: 验证编辑任务调度器只处理编辑任务
- `testScheduleHighPriorityTasks_ShouldFilterOutEditTasks`: 验证高优先级调度器过滤编辑任务

### 验证要点
1. 生成任务调度器不会处理 `EDIT` 和 `CANVAS_EDIT` 类型的任务
2. 编辑任务调度器只处理 `EDIT` 和 `CANVAS_EDIT` 类型的任务
3. 高优先级调度器只处理生成任务的重试，不处理编辑任务
4. 并发控制完全独立，互不影响

## 部署注意事项

### 1. XXL-JOB 配置
需要在 XXL-JOB 管理界面新增 `editImageTaskScheduler` 任务。

### 2. 数据库索引
确保以下索引存在以优化查询性能：
```sql
CREATE INDEX idx_task_status_type ON ai_image_task_queue(task_status, task_type);
CREATE INDEX idx_task_status_type_position ON ai_image_task_queue(task_status, task_type, global_queue_position);
CREATE INDEX idx_task_status_type_retry ON ai_image_task_queue(task_status, task_type, retry_count, create_time);
```

### 3. 监控配置
- 分别监控生成任务和编辑任务的处理情况
- 设置独立的告警阈值
- 关注两种任务类型的处理速度和成功率

## 优势总结

1. **完全隔离**：生成任务和编辑任务完全独立，互不影响
2. **响应优化**：编辑任务调度频率更高（20秒），响应更快
3. **负载均衡**：可以根据业务需求独立调整两种任务的并发数
4. **监控清晰**：可以分别监控两种任务的处理情况
5. **扩展性好**：未来可以轻松添加新的任务类型或调度策略
