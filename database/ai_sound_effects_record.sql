-- 音效生成记录表
CREATE TABLE `ai_sound_effects_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` varchar(64) NOT NULL COMMENT '用户ID',
    `session_id` varchar(128) DEFAULT NULL COMMENT '会话ID',
    `content_id` varchar(128) DEFAULT NULL COMMENT '内容ID',
    `audio_index` int(11) DEFAULT NULL COMMENT '音频索引',
    `request_id` varchar(128) DEFAULT NULL COMMENT '第三方请求ID',
    `prompt` text NOT NULL COMMENT '音效描述提示词',
    `duration` int(11) NOT NULL COMMENT '音频时长(秒)',
    `original_audio_url` text COMMENT '原始音频URL',
    `oss_audio_url` text COMMENT 'OSS音频URL',
    `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
    `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
    `content_type` varchar(64) DEFAULT NULL COMMENT '文件类型',
    `generation_status` varchar(16) DEFAULT 'PENDING' COMMENT '生成状态(PENDING/PROCESSING/SUCCESS/FAILED)',
    `error_code` varchar(64) DEFAULT NULL COMMENT '错误码',
    `error_message` text COMMENT '错误信息',
    `generation_time` bigint(20) DEFAULT NULL COMMENT '生成耗时(毫秒)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_content_id` (`content_id`),
    KEY `idx_request_id` (`request_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_generation_status` (`generation_status`),
    KEY `idx_content_index` (`content_id`, `index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音效生成记录表';