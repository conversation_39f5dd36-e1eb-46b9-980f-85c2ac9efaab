# Flux AI Java 8 示例

本项目提供了几种使用Java 8兼容代码调用Flux AI API的示例。

## 环境要求

- Java 8 或更高版本
- Maven 或 Gradle（用于依赖管理）
- FAL API 密钥（设置为环境变量`FAL_KEY`）

## 项目结构

- **FluxExample.java**: 基本的同步API调用示例
- **FluxExampleAlternative.java**: 使用匿名内部类而非Lambda表达式的替代实现（适用于较老的Java 8环境）
- **FluxExampleAsync.java**: 使用CompletableFuture的异步API调用示例

## 安装依赖

### Maven

在您的pom.xml中添加以下依赖：

```xml
<!-- 同步客户端 -->
<dependency>
    <groupId>ai.fal.client</groupId>
    <artifactId>fal-client</artifactId>
    <version>0.7.1</version>
</dependency>

<!-- 异步客户端（如果需要） -->
<dependency>
    <groupId>ai.fal.client</groupId>
    <artifactId>fal-client-async</artifactId>
    <version>0.7.1</version>
</dependency>
```

### Gradle

```groovy
// 同步客户端
implementation 'ai.fal.client:fal-client:0.7.1'

// 异步客户端（如果需要）
implementation 'ai.fal.client:fal-client-async:0.7.1'
```

## 使用方法

### 设置API密钥

在运行示例前，需要设置FAL API密钥：

```bash
# Linux/macOS
export FAL_KEY=your_api_key_here

# Windows
set FAL_KEY=your_api_key_here
```

或者在代码中设置：

```java
fal.config()
   .credentials("your_api_key_here")
   .baseUrl("https://queue.fal.ai/")  // 可选，默认URL
   .build();
```

### 运行示例

编译并运行示例：

```bash
javac -cp ".:lib/*" FluxExample.java
java -cp ".:lib/*" FluxExample
```

## 注意事项

- 确保使用正确的类型导入，特别是`JsonNode`类。
- 异步版本需要处理CompletableFuture的异常。
- API调用可能会因为网络或服务问题而失败，确保适当的异常处理。

## 更多资源

- [FAL AI官方文档](https://docs.fal.ai/clients/java/)
- [FAL AI GitHub仓库](https://github.com/fal-ai/fal-java)
