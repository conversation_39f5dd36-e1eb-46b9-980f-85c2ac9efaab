#!/bin/bash

# 视频分享API测试脚本
# 使用方法: ./video_share_api_test.sh <base_url> <token> <task_id>

# 检查参数
if [ $# -ne 3 ]; then
    echo "使用方法: $0 <base_url> <token> <task_id>"
    echo "示例: $0 http://localhost:8080 your_token_here 123"
    exit 1
fi

BASE_URL=$1
TOKEN=$2
TASK_ID=$3

echo "=== 视频分享API测试 ==="
echo "Base URL: $BASE_URL"
echo "Task ID: $TASK_ID"
echo ""

# 测试分享视频
echo "1. 测试分享视频..."
SHARE_RESPONSE=$(curl -s -X PUT "$BASE_URL/agent/video-render/share/$TASK_ID" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer $TOKEN" \
     -d '{"share": true}')

echo "分享响应: $SHARE_RESPONSE"
echo ""

# 从响应中提取分享码（假设响应格式为 {"success":true,"data":"ABC123DEF",...}）
SHARE_CODE=$(echo $SHARE_RESPONSE | grep -o '"data":"[^"]*"' | cut -d'"' -f4)
echo "提取的分享码: $SHARE_CODE"
echo ""

# 测试根据分享码查询视频（如果分享码存在）
if [ ! -z "$SHARE_CODE" ] && [ "$SHARE_CODE" != "null" ]; then
    echo "2. 测试根据分享码查询视频..."
    QUERY_RESPONSE=$(curl -s -X GET "$BASE_URL/agent/video-render/shared/$SHARE_CODE")
    echo "查询响应: $QUERY_RESPONSE"
    echo ""
fi

# 测试分页查询已分享视频
echo "3. 测试分页查询已分享视频..."
LIST_RESPONSE=$(curl -s -X POST "$BASE_URL/agent/video-render/shared/list" \
     -H "Content-Type: application/json" \
     -d '{"pageNum":1,"pageSize":10}')

echo "分页查询响应: $LIST_RESPONSE"
echo ""

# 测试取消分享
echo "4. 测试取消分享视频..."
UNSHARE_RESPONSE=$(curl -s -X PUT "$BASE_URL/agent/video-render/share/$TASK_ID" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer $TOKEN" \
     -d '{"share": false}')

echo "取消分享响应: $UNSHARE_RESPONSE"
echo ""

# 验证取消分享后的状态
echo "5. 验证取消分享后，分享码查询应该失败..."
if [ ! -z "$SHARE_CODE" ] && [ "$SHARE_CODE" != "null" ]; then
    VERIFY_RESPONSE=$(curl -s -X GET "$BASE_URL/agent/video-render/shared/$SHARE_CODE")
    echo "验证响应: $VERIFY_RESPONSE"
    echo ""
fi

echo "=== 测试完成 ==="
