package com.wlink.agent.controller;

import com.wlink.agent.client.MiniMaxFileDownloadClient;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import com.wlink.agent.service.MiniMaxVideoCallbackService;
import com.wlink.agent.service.VideoGenerationQueueService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 视频生成控制器测试
 */
@ExtendWith(MockitoExtension.class)
class VideoGenerationControllerTest {

    @Mock
    private VideoGenerationQueueService queueService;

    @Mock
    private MiniMaxVideoCallbackService miniMaxCallbackService;

    @Mock
    private MiniMaxFileDownloadClient miniMaxFileDownloadClient;

    @Mock
    private AiVideoGenerationRecordMapper videoGenerationRecordMapper;

    @InjectMocks
    private VideoGenerationController controller;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void testDownloadMiniMaxVideo_Success() throws Exception {
        // Given
        String taskId = "test-task-id";
        String fileId = "test-file-id";
        
        AiVideoGenerationRecordPo record = AiVideoGenerationRecordPo.builder()
                .id(1L)
                .taskId(taskId)
                .model("MiniMax-Hailuo-02")
                .status("succeeded")
                .createTime(LocalDateTime.now())
                .build();

        byte[] videoData = "test video data".getBytes();
        InputStream inputStream = new ByteArrayInputStream(videoData);

        when(videoGenerationRecordMapper.selectOne(any()))
                .thenReturn(record);
        when(miniMaxFileDownloadClient.downloadFile(anyString(), anyString()))
                .thenReturn(CompletableFuture.completedFuture(inputStream));

        // When
        CompletableFuture<ResponseEntity<InputStreamResource>> future = 
                controller.downloadMiniMaxVideo(taskId, fileId);
        ResponseEntity<InputStreamResource> response = future.get();

        // Then
        // 由于getApiKeyForRecord返回null，应该返回401 Unauthorized
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    void testDownloadMiniMaxVideo_TaskNotFound() throws Exception {
        // Given
        String taskId = "non-existent-task";
        String fileId = "test-file-id";

        when(videoGenerationRecordMapper.selectOne(any()))
                .thenReturn(null);

        // When
        CompletableFuture<ResponseEntity<InputStreamResource>> future = 
                controller.downloadMiniMaxVideo(taskId, fileId);
        ResponseEntity<InputStreamResource> response = future.get();

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }

    @Test
    void testDownloadMiniMaxVideoAsBytes_Success() throws Exception {
        // Given
        String taskId = "test-task-id";
        String fileId = "test-file-id";
        
        AiVideoGenerationRecordPo record = AiVideoGenerationRecordPo.builder()
                .id(1L)
                .taskId(taskId)
                .model("MiniMax-Hailuo-02")
                .status("succeeded")
                .createTime(LocalDateTime.now())
                .build();

        byte[] videoData = "test video data".getBytes();

        when(videoGenerationRecordMapper.selectOne(any()))
                .thenReturn(record);
        when(miniMaxFileDownloadClient.downloadFileAsBytes(anyString(), anyString()))
                .thenReturn(CompletableFuture.completedFuture(videoData));

        // When
        CompletableFuture<ResponseEntity<byte[]>> future = 
                controller.downloadMiniMaxVideoAsBytes(taskId, fileId);
        ResponseEntity<byte[]> response = future.get();

        // Then
        // 由于getApiKeyForRecord返回null，应该返回401 Unauthorized
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    void testDownloadMiniMaxVideoAsBytes_TaskNotFound() throws Exception {
        // Given
        String taskId = "non-existent-task";
        String fileId = "test-file-id";

        when(videoGenerationRecordMapper.selectOne(any()))
                .thenReturn(null);

        // When
        CompletableFuture<ResponseEntity<byte[]>> future = 
                controller.downloadMiniMaxVideoAsBytes(taskId, fileId);
        ResponseEntity<byte[]> response = future.get();

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }

    @Test
    void testDownloadMiniMaxVideo_WithMockMvc() throws Exception {
        // Given
        String taskId = "test-task-id";
        String fileId = "test-file-id";

        when(videoGenerationRecordMapper.selectOne(any()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/agent/video/minimax/download")
                        .param("taskId", taskId)
                        .param("fileId", fileId))
                .andExpect(status().isNotFound());
    }

    @Test
    void testDownloadMiniMaxVideoAsBytes_WithMockMvc() throws Exception {
        // Given
        String taskId = "test-task-id";
        String fileId = "test-file-id";

        when(videoGenerationRecordMapper.selectOne(any()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/agent/video/minimax/download/bytes")
                        .param("taskId", taskId)
                        .param("fileId", fileId))
                .andExpect(status().isNotFound());
    }

    @Test
    void testDownloadMiniMaxVideo_MissingParameters() throws Exception {
        // When & Then - 缺少taskId参数
        mockMvc.perform(get("/agent/video/minimax/download")
                        .param("fileId", "test-file-id"))
                .andExpect(status().isBadRequest());

        // When & Then - 缺少fileId参数
        mockMvc.perform(get("/agent/video/minimax/download")
                        .param("taskId", "test-task-id"))
                .andExpect(status().isBadRequest());
    }
}
