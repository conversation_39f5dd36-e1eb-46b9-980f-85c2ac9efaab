package com.wlink.agent.controller;

import com.wlink.agent.model.req.GenerateStableAudioReq;
import com.wlink.agent.model.res.GenerateStableAudioRes;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Stable Audio 控制器测试
 */
public class StableAudioControllerTest {

    @Test
    public void testGenerateStableAudioReqCreation() {
        // 测试请求对象创建
        GenerateStableAudioReq req = new GenerateStableAudioReq();
        req.setPrompt("128 BPM tech house drum loop");
        req.setDuration(30);
        req.setSteps(100);
        req.setSecondsStart(0);
        req.setConversationId("canvas_12345");
        req.setContentId("shot_67890");
        req.setIndex(1);

        assertTrue(req.isValid());
        assertEquals("128 BPM tech house drum loop", req.getPrompt());
        assertEquals(30, req.getDuration());
        assertEquals(100, req.getSteps());
        assertEquals(0, req.getSecondsStart());
        assertEquals("canvas_12345", req.getConversationId());
        assertEquals("shot_67890", req.getContentId());
        assertEquals(1, req.getIndex());
    }

    @Test
    public void testGenerateStableAudioReqValidation() {
        // 测试无效请求
        GenerateStableAudioReq invalidReq1 = new GenerateStableAudioReq();
        invalidReq1.setPrompt(""); // 空提示词
        invalidReq1.setDuration(30);
        invalidReq1.setConversationId("canvas_12345");
        assertFalse(invalidReq1.isValid());

        GenerateStableAudioReq invalidReq2 = new GenerateStableAudioReq();
        invalidReq2.setPrompt("Valid prompt");
        invalidReq2.setDuration(50); // 超过最大时长
        invalidReq2.setConversationId("canvas_12345");
        assertFalse(invalidReq2.isValid());

        GenerateStableAudioReq invalidReq3 = new GenerateStableAudioReq();
        invalidReq3.setPrompt("Valid prompt");
        invalidReq3.setDuration(30);
        invalidReq3.setSteps(2000); // 超过最大步数
        invalidReq3.setConversationId("canvas_12345");
        assertFalse(invalidReq3.isValid());

        GenerateStableAudioReq invalidReq4 = new GenerateStableAudioReq();
        invalidReq4.setPrompt("Valid prompt");
        invalidReq4.setDuration(30);
        invalidReq4.setConversationId(""); // 空会话ID
        assertFalse(invalidReq4.isValid());

        // 测试有效请求
        GenerateStableAudioReq validReq = new GenerateStableAudioReq();
        validReq.setPrompt("Valid prompt");
        validReq.setDuration(30);
        validReq.setConversationId("canvas_12345");
        assertTrue(validReq.isValid());
    }

    @Test
    public void testGenerateStableAudioReqToStableAudioRequest() {
        GenerateStableAudioReq req = new GenerateStableAudioReq();
        req.setPrompt("Electronic music");
        req.setDuration(25);
        req.setSteps(150);
        req.setSecondsStart(5);

        var stableAudioRequest = req.toStableAudioRequest();
        
        assertEquals("Electronic music", stableAudioRequest.prompt());
        assertEquals(25, stableAudioRequest.secondsTotal());
        assertEquals(150, stableAudioRequest.steps());
        assertEquals(5, stableAudioRequest.secondsStart());
    }

    @Test
    public void testGenerateStableAudioReqDescription() {
        GenerateStableAudioReq req = new GenerateStableAudioReq();
        req.setPrompt("Ambient sounds");
        req.setDuration(20);
        req.setSteps(120);
        req.setSecondsStart(3);

        String description = req.getDescription();
        assertTrue(description.contains("Ambient sounds"));
        assertTrue(description.contains("20秒"));
        assertTrue(description.contains("120步"));
        assertTrue(description.contains("3秒"));
    }

    @Test
    public void testGenerateStableAudioResCreation() {
        // 测试基础创建方法
        GenerateStableAudioRes res1 = GenerateStableAudioRes.of(
                "https://example.com/audio.wav",
                "Test audio",
                30
        );

        assertEquals("https://example.com/audio.wav", res1.getAudioUrl());
        assertEquals("Test audio", res1.getPrompt());
        assertEquals(30, res1.getDuration());
        assertEquals("SUCCESS", res1.getStatus());
        assertTrue(res1.isSuccess());

        // 测试完整创建方法
        GenerateStableAudioRes res2 = GenerateStableAudioRes.of(
                "https://example.com/audio2.wav",
                "Test audio 2",
                25,
                150,
                5
        );

        assertEquals("https://example.com/audio2.wav", res2.getAudioUrl());
        assertEquals("Test audio 2", res2.getPrompt());
        assertEquals(25, res2.getDuration());
        assertEquals(150, res2.getSteps());
        assertEquals(5, res2.getSecondsStart());
        assertEquals("SUCCESS", res2.getStatus());
    }

    @Test
    public void testGenerateStableAudioResWithFileInfo() {
        GenerateStableAudioRes res = GenerateStableAudioRes.of(
                "https://example.com/audio.wav",
                "Test audio",
                30
        );

        res.withFileInfo("test_audio.wav", "2.5 MB", "audio/wav");

        assertEquals("test_audio.wav", res.getFileName());
        assertEquals("2.5 MB", res.getFileSize());
        assertEquals("audio/wav", res.getContentType());
    }

    @Test
    public void testGenerateStableAudioResWithGenerationInfo() {
        GenerateStableAudioRes res = GenerateStableAudioRes.of(
                "https://example.com/audio.wav",
                "Test audio",
                30
        );

        res.withGenerationInfo("SUCCESS", 45000L);

        assertEquals("SUCCESS", res.getStatus());
        assertEquals(45000L, res.getGenerationTime());
        assertTrue(res.isSuccess());
    }

    @Test
    public void testGenerateStableAudioResFormattedGenerationTime() {
        GenerateStableAudioRes res = new GenerateStableAudioRes();

        // 测试毫秒
        res.setGenerationTime(500L);
        assertEquals("500ms", res.getFormattedGenerationTime());

        // 测试秒
        res.setGenerationTime(5500L);
        assertEquals("5.5秒", res.getFormattedGenerationTime());

        // 测试分钟
        res.setGenerationTime(125000L); // 2分5秒
        assertEquals("2分5秒", res.getFormattedGenerationTime());

        // 测试null
        res.setGenerationTime(null);
        assertEquals("未知", res.getFormattedGenerationTime());
    }

    @Test
    public void testGenerateStableAudioResDescription() {
        GenerateStableAudioRes res = GenerateStableAudioRes.of(
                "https://example.com/audio.wav",
                "Test audio",
                30,
                100,
                0
        );

        String description = res.getDescription();
        assertTrue(description.contains("Test audio"));
        assertTrue(description.contains("30秒"));
        assertTrue(description.contains("100步"));
        assertTrue(description.contains("SUCCESS"));
    }

    @Test
    public void testGenerateStableAudioResIsSuccess() {
        // 成功的响应
        GenerateStableAudioRes successRes = GenerateStableAudioRes.of(
                "https://example.com/audio.wav",
                "Test audio",
                30
        );
        assertTrue(successRes.isSuccess());

        // 失败的响应（无URL）
        GenerateStableAudioRes failRes1 = new GenerateStableAudioRes();
        failRes1.setStatus("SUCCESS");
        failRes1.setAudioUrl(null);
        assertFalse(failRes1.isSuccess());

        // 失败的响应（状态失败）
        GenerateStableAudioRes failRes2 = new GenerateStableAudioRes();
        failRes2.setStatus("FAILED");
        failRes2.setAudioUrl("https://example.com/audio.wav");
        assertFalse(failRes2.isSuccess());
    }
}
