package com.wlink.agent.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 豆包图像编辑API客户端测试
 */
@ExtendWith(MockitoExtension.class)
class DoubaoImageEditApiClientTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private OkHttpClient okHttpClient;

    @InjectMocks
    private DoubaoImageEditApiClient doubaoImageEditApiClient;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(doubaoImageEditApiClient, "apiUrl", 
            "https://ark.cn-beijing.volces.com/api/v3/images/generations");
        ReflectionTestUtils.setField(doubaoImageEditApiClient, "defaultApiKey", 
            "test-api-key");
    }

    @Test
    void testBuildDoubaoImageEditRequest() {
        // 测试构建请求对象
        DoubaoImageEditRequest request = new DoubaoImageEditRequest();
        request.setModel("doubao-seededit-3-0-i2i-250628");
        request.setPrompt("把图中的红色汽车改成蓝色");
        request.setImage("https://example.com/test-image.jpg");
        request.setResponseFormat("url");
        request.setSize("adaptive");
        request.setSeed(-1);
        request.setGuidanceScale(5.5);
        request.setWatermark(true);

        // 验证请求对象
        assertNotNull(request);
        assertEquals("doubao-seededit-3-0-i2i-250628", request.getModel());
        assertEquals("把图中的红色汽车改成蓝色", request.getPrompt());
        assertEquals("https://example.com/test-image.jpg", request.getImage());
        assertEquals("url", request.getResponseFormat());
        assertEquals("adaptive", request.getSize());
        assertEquals(-1, request.getSeed());
        assertEquals(5.5, request.getGuidanceScale());
        assertTrue(request.getWatermark());
    }

    @Test
    void testValidateRequestParameters() {
        // 测试参数验证
        DoubaoImageEditRequest validRequest = new DoubaoImageEditRequest(
            "doubao-seededit-3-0-i2i-250628",
            "把图中的红色汽车改成蓝色",
            "https://example.com/test-image.jpg"
        );

        assertNotNull(validRequest.getModel());
        assertNotNull(validRequest.getPrompt());
        assertNotNull(validRequest.getImage());
        assertFalse(validRequest.getModel().trim().isEmpty());
        assertFalse(validRequest.getPrompt().trim().isEmpty());
        assertFalse(validRequest.getImage().trim().isEmpty());
    }

    @Test
    void testDefaultValues() {
        // 测试默认值
        DoubaoImageEditRequest request = new DoubaoImageEditRequest();
        
        assertEquals("url", request.getResponseFormat());
        assertEquals("adaptive", request.getSize());
        assertEquals(-1, request.getSeed());
        assertEquals(5.5, request.getGuidanceScale());
        assertTrue(request.getWatermark());
    }

    @Test
    void testImageUrlValidation() {
        // 测试图像URL格式
        String[] validUrls = {
            "https://example.com/image.jpg",
            "https://example.com/image.png",
            "http://example.com/path/to/image.jpeg"
        };

        String[] validBase64 = {
            "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
        };

        // 验证URL格式
        for (String url : validUrls) {
            assertTrue(url.startsWith("http://") || url.startsWith("https://"));
        }

        // 验证Base64格式
        for (String base64 : validBase64) {
            assertTrue(base64.startsWith("data:image/"));
            assertTrue(base64.contains("base64,"));
        }
    }

    @Test
    void testGuidanceScaleRange() {
        // 测试引导尺度范围
        DoubaoImageEditRequest request = new DoubaoImageEditRequest();
        
        // 测试有效范围
        double[] validScales = {1.0, 5.5, 10.0};
        for (double scale : validScales) {
            request.setGuidanceScale(scale);
            assertTrue(request.getGuidanceScale() >= 1.0 && request.getGuidanceScale() <= 10.0);
        }
    }

    @Test
    void testSeedRange() {
        // 测试种子值范围
        DoubaoImageEditRequest request = new DoubaoImageEditRequest();
        
        // 测试有效范围
        int[] validSeeds = {-1, 0, 12345, 2147483647};
        for (int seed : validSeeds) {
            request.setSeed(seed);
            assertTrue(request.getSeed() >= -1 && request.getSeed() <= 2147483647);
        }
    }
}
