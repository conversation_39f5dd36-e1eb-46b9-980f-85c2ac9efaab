package com.wlink.agent.model.req;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TtsGenerateReq emotion 参数验证测试
 */
public class TtsGenerateReqTest {

    @Test
    public void testValidEmotionValues() {
        TtsGenerateReq req = new TtsGenerateReq();
        
        // 测试有效值
        String[] validEmotions = {"happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"};
        
        for (String emotion : validEmotions) {
            req.setEmotion(emotion);
            assertEquals(emotion, req.getEmotion(), "有效的emotion值应该被保留: " + emotion);
        }
    }

    @Test
    public void testInvalidEmotionValues() {
        TtsGenerateReq req = new TtsGenerateReq();
        
        // 测试无效值，应该被重置为null
        String[] invalidEmotions = {"invalid", "Happy", "SAD", "ANGRY", "joyful", "excited", "", " "};
        
        for (String emotion : invalidEmotions) {
            req.setEmotion(emotion);
            assertNull(req.getEmotion(), "无效的emotion值应该被重置为null: " + emotion);
        }
    }

    @Test
    public void testNullEmotionValue() {
        TtsGenerateReq req = new TtsGenerateReq();
        
        // 测试null值，应该保持null
        req.setEmotion(null);
        assertNull(req.getEmotion(), "null值应该保持null");
    }

    @Test
    public void testEmotionCaseSensitive() {
        TtsGenerateReq req = new TtsGenerateReq();
        
        // 测试大小写敏感
        req.setEmotion("Happy");
        assertNull(req.getEmotion(), "大写的Happy应该被重置为null");
        
        req.setEmotion("happy");
        assertEquals("happy", req.getEmotion(), "小写的happy应该被保留");
    }
}
