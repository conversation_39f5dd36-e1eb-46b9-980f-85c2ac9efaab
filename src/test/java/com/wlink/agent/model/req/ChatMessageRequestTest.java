package com.wlink.agent.model.req;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试ChatMessageRequest中文件集合功能
 */
public class ChatMessageRequestTest {

    @Test
    public void testChatMessageRequestWithFiles() {
        // 创建测试请求
        ChatMessageRequest request = new ChatMessageRequest();
        request.setQuery("请分析这张图片");
        request.setResponse_mode("streaming");
        request.setConversation_id("conv_12345");
        request.setUser("<EMAIL>");

        // 创建文件信息
        ChatMessageRequest.FileInfo file1 = new ChatMessageRequest.FileInfo();
        file1.setType("image");
        file1.setTransferMethod("remote_url");
        file1.setUrl("https://cloud.dify.ai/logo/logo-site.png");

        ChatMessageRequest.FileInfo file2 = new ChatMessageRequest.FileInfo();
        file2.setType("image");
        file2.setTransferMethod("remote_url");
        file2.setUrl("https://example.com/test-image.jpg");

        List<ChatMessageRequest.FileInfo> files = Arrays.asList(file1, file2);
        request.setFiles(files);

        // 验证基本字段
        assertEquals("请分析这张图片", request.getQuery());
        assertEquals("streaming", request.getResponse_mode());
        assertEquals("conv_12345", request.getConversation_id());
        assertEquals("<EMAIL>", request.getUser());

        // 验证文件集合
        assertNotNull(request.getFiles(), "文件集合不应为空");
        assertEquals(2, request.getFiles().size(), "应该有2个文件");

        // 验证第一个文件
        ChatMessageRequest.FileInfo firstFile = request.getFiles().get(0);
        assertEquals("image", firstFile.getType());
        assertEquals("remote_url", firstFile.getTransferMethod());
        assertEquals("https://cloud.dify.ai/logo/logo-site.png", firstFile.getUrl());

        // 验证第二个文件
        ChatMessageRequest.FileInfo secondFile = request.getFiles().get(1);
        assertEquals("image", secondFile.getType());
        assertEquals("remote_url", secondFile.getTransferMethod());
        assertEquals("https://example.com/test-image.jpg", secondFile.getUrl());
    }

    @Test
    public void testChatMessageRequestWithoutFiles() {
        // 测试没有文件的情况
        ChatMessageRequest request = new ChatMessageRequest();
        request.setQuery("普通文本查询");
        request.setResponse_mode("streaming");
        request.setConversation_id("conv_67890");
        request.setUser("<EMAIL>");

        // 验证基本字段
        assertEquals("普通文本查询", request.getQuery());
        assertEquals("streaming", request.getResponse_mode());
        assertEquals("conv_67890", request.getConversation_id());
        assertEquals("<EMAIL>", request.getUser());

        // 验证文件集合为空
        assertNull(request.getFiles(), "文件集合应该为空");
    }

    @Test
    public void testFileInfoStructure() {
        // 测试FileInfo内部类结构
        ChatMessageRequest.FileInfo fileInfo = new ChatMessageRequest.FileInfo();
        fileInfo.setType("document");
        fileInfo.setTransferMethod("local_file");
        fileInfo.setUrl("https://example.com/document.pdf");

        assertEquals("document", fileInfo.getType());
        assertEquals("local_file", fileInfo.getTransferMethod());
        assertEquals("https://example.com/document.pdf", fileInfo.getUrl());
    }

    @Test
    public void testJsonSerialization() {
        // 测试JSON序列化
        ChatMessageRequest request = new ChatMessageRequest();
        request.setQuery("测试JSON序列化");
        request.setResponse_mode("streaming");
        request.setConversation_id("conv_json_test");
        request.setUser("<EMAIL>");

        // 添加文件
        ChatMessageRequest.FileInfo file = new ChatMessageRequest.FileInfo();
        file.setType("image");
        file.setTransferMethod("remote_url");
        file.setUrl("https://test.com/image.png");
        request.setFiles(Arrays.asList(file));

        // 序列化为JSON
        String json = JSON.toJSONString(request);
        assertNotNull(json, "JSON序列化结果不应为空");
        assertTrue(json.contains("files"), "JSON应包含files字段");
        assertTrue(json.contains("transfer_method"), "JSON应包含transfer_method字段");
        assertTrue(json.contains("remote_url"), "JSON应包含transfer_method的值");

        // 反序列化
        ChatMessageRequest deserializedRequest = JSON.parseObject(json, ChatMessageRequest.class);
        assertNotNull(deserializedRequest, "反序列化结果不应为空");
        assertEquals(request.getQuery(), deserializedRequest.getQuery());
        assertEquals(1, deserializedRequest.getFiles().size());
        assertEquals("image", deserializedRequest.getFiles().get(0).getType());
        assertEquals("remote_url", deserializedRequest.getFiles().get(0).getTransferMethod());
        assertEquals("https://test.com/image.png", deserializedRequest.getFiles().get(0).getUrl());
    }
}
