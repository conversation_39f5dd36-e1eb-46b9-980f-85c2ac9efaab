package com.wlink.agent.model.req;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;

/**
 * UpdateNarrationAudioReq 参数验证测试
 */
public class UpdateNarrationAudioReqTest {

    @Test
    public void testNarrationModeParameters() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();

        // 测试旁白模式参数设置
        req.setSessionId("test-session-123");
        req.setSegmentIds(Arrays.asList("segment-1", "segment-2"));
        req.setSoundId(456L);
        // characterId为空，表示旁白模式

        // 验证参数
        assertEquals("test-session-123", req.getSessionId());
        assertEquals(2, req.getSegmentIds().size());
        assertEquals("segment-1", req.getSegmentIds().get(0));
        assertEquals("segment-2", req.getSegmentIds().get(1));
        assertEquals(456L, req.getSoundId());
        assertNull(req.getCharacterId());
    }

    @Test
    public void testCharacterModeParameters() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();

        // 测试角色模式参数设置
        req.setSessionId("test-session-123");
        req.setSegmentIds(Arrays.asList("segment-1"));
        req.setSoundId(456L);
        req.setCharacterId("ZhaoGao_aA3p"); // 指定角色ID，表示角色模式

        // 验证参数
        assertEquals("test-session-123", req.getSessionId());
        assertEquals(1, req.getSegmentIds().size());
        assertEquals("segment-1", req.getSegmentIds().get(0));
        assertEquals(456L, req.getSoundId());
        assertEquals("ZhaoGao_aA3p", req.getCharacterId());
    }

    @Test
    public void testOptionalParameters() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        
        // 测试可选参数
        req.setSessionId("test-session");
        req.setSoundId(null); // 音色ID可以为空，使用会话默认音色
        req.setSegmentIds(null); // 章节ID可以为空，处理所有章节
        req.setCharacterId(null); // 角色ID可以为空，处理旁白

        assertEquals("test-session", req.getSessionId());
        assertNull(req.getSoundId());
        assertNull(req.getSegmentIds());
        assertNull(req.getCharacterId());
    }

    @Test
    public void testEmptySegmentIds() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        
        req.setSessionId("test-session");
        req.setSegmentIds(Arrays.asList()); // 空数组
        req.setCharacterId("character-123");

        assertEquals("test-session", req.getSessionId());
        assertTrue(req.getSegmentIds().isEmpty());
        assertEquals("character-123", req.getCharacterId());
    }
}
