package com.wlink.agent.utils;

import org.junit.jupiter.api.Test;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 文本拆分工具类测试
 */
public class TextSplitUtilsTest {

    @Test
    public void testSplitTextByPunctuation() {
        // 测试正常拆分
        String text = "这是第一段文本，这是第二段文本。这是第三段文本！这是第四段文本？这是第五段文本｜";
        List<String> segments = TextSplitUtils.splitTextByPunctuation(text);

        assertEquals(5, segments.size());
        assertEquals("这是第一段文本", segments.get(0));
        assertEquals("这是第二段文本", segments.get(1));
        assertEquals("这是第三段文本", segments.get(2));
        assertEquals("这是第四段文本", segments.get(3));
        assertEquals("这是第五段文本", segments.get(4));
    }

    @Test
    public void testSplitTextWithEmptyInput() {
        // 测试空输入
        List<String> segments = TextSplitUtils.splitTextByPunctuation("");
        assertTrue(segments.isEmpty());
        
        segments = TextSplitUtils.splitTextByPunctuation(null);
        assertTrue(segments.isEmpty());
    }

    @Test
    public void testSplitTextWithNoPunctuation() {
        // 测试没有标点符号的文本
        String text = "这是一段没有标点符号的文本";
        List<String> segments = TextSplitUtils.splitTextByPunctuation(text);
        
        assertEquals(1, segments.size());
        assertEquals("这是一段没有标点符号的文本", segments.get(0));
    }

    @Test
    public void testEstimateAudioDuration() {
        // 测试音频时长估算
        String text = "这是一段测试文本";
        long duration = TextSplitUtils.estimateAudioDuration(text);

        // 8个有效字符 * 150毫秒 = 1200毫秒
        assertEquals(1200L, duration);
    }

    @Test
    public void testEstimateAudioDurationWithPunctuation() {
        // 测试包含标点符号的音频时长估算
        String text = "这是一段测试文本，包含标点符号。";
        long duration = TextSplitUtils.estimateAudioDuration(text);

        // 16个有效字符（不包括标点和空格）* 150毫秒 = 2400毫秒
        assertEquals(2400L, duration);
    }

    @Test
    public void testEstimateAudioDurationWithEmptyInput() {
        // 测试空输入的音频时长估算
        assertEquals(0L, TextSplitUtils.estimateAudioDuration(""));
        assertEquals(0L, TextSplitUtils.estimateAudioDuration(null));
    }
}
