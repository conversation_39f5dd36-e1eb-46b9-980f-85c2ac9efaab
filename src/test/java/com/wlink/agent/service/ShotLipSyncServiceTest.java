package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiShotLipSyncPo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分镜对口型服务测试
 */
@SpringBootTest
public class ShotLipSyncServiceTest {

    @Test
    public void testAiShotLipSyncPoFields() {
        // 测试实体类字段是否正确
        AiShotLipSyncPo lipSyncPo = new AiShotLipSyncPo();
        
        // 设置基本字段
        lipSyncPo.setShotId(123L);
        lipSyncPo.setTaskId("1234567890");
        lipSyncPo.setClientId("test-client-id");
        lipSyncPo.setAudioUrl("https://example.com/audio.wav");
        lipSyncPo.setImageUrl("https://example.com/image.jpg");
        lipSyncPo.setStatus("RUNNING");
        lipSyncPo.setUserId("test-user");
        
        // 验证字段设置正确
        assertEquals(123L, lipSyncPo.getShotId());
        assertEquals("1234567890", lipSyncPo.getTaskId());
        assertEquals("test-client-id", lipSyncPo.getClientId());
        assertEquals("https://example.com/audio.wav", lipSyncPo.getAudioUrl());
        assertEquals("https://example.com/image.jpg", lipSyncPo.getImageUrl());
        assertEquals("RUNNING", lipSyncPo.getStatus());
        assertEquals("test-user", lipSyncPo.getUserId());
        
        // 验证不再有旧字段（这些方法应该不存在）
        // lipSyncPo.setAudioData() - 应该不存在
        // lipSyncPo.setImageData() - 应该不存在
        // lipSyncPo.setNetWssUrl() - 应该不存在
        
        System.out.println("AiShotLipSyncPo 字段测试通过");
    }
}
