package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiSessionImagePo;
import com.wlink.agent.model.req.CreateConversationRequest;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试ChatService中图片URL集合功能
 */
public class ChatServiceImageUrlTest {

    @Test
    public void testCreateConversationRequestWithImageUrls() {
        // 准备测试数据
        CreateConversationRequest request = new CreateConversationRequest();
        request.setPrompt("测试创建会话");
        request.setImageUrls(Arrays.asList(
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        ));

        // 验证图片URL数据结构
        List<String> imageUrls = request.getImageUrls();
        assertNotNull(imageUrls, "图片URL列表不应为空");
        assertEquals(3, imageUrls.size(), "应该有3个图片URL");
        assertEquals("https://example.com/image1.jpg", imageUrls.get(0));
        assertEquals("https://example.com/image2.jpg", imageUrls.get(1));
        assertEquals("https://example.com/image3.jpg", imageUrls.get(2));
    }

    @Test
    public void testCreateConversationWithEmptyImageUrls() {
        // 测试空图片URL列表的情况
        CreateConversationRequest request = new CreateConversationRequest();
        request.setPrompt("测试创建会话");
        request.setImageUrls(null); // 设置为null

        // 验证请求对象结构正确
        assertNull(request.getImageUrls(), "图片URL列表应该为null");
        assertEquals("测试创建会话", request.getPrompt());
    }

    @Test
    public void testSessionImagePoStructure() {
        // 测试AiSessionImagePo实体类结构
        AiSessionImagePo sessionImage = new AiSessionImagePo();
        sessionImage.setSessionId("test-session-123");
        sessionImage.setImageUrl("https://example.com/test-image.jpg");
        sessionImage.setSortOrder(1);

        assertEquals("test-session-123", sessionImage.getSessionId());
        assertEquals("https://example.com/test-image.jpg", sessionImage.getImageUrl());
        assertEquals(Integer.valueOf(1), sessionImage.getSortOrder());
    }

    @Test
    public void testBatchInsertDataStructure() {
        // 测试批量插入的数据结构
        List<AiSessionImagePo> sessionImageList = new ArrayList<>();
        String sessionId = "test-session-batch-123";
        List<String> imageUrls = Arrays.asList(
            "https://example.com/batch1.jpg",
            "https://example.com/batch2.jpg",
            "https://example.com/batch3.jpg"
        );

        // 模拟批量插入的数据准备逻辑
        for (int i = 0; i < imageUrls.size(); i++) {
            AiSessionImagePo sessionImagePo = new AiSessionImagePo();
            sessionImagePo.setSessionId(sessionId);
            sessionImagePo.setImageUrl(imageUrls.get(i));
            sessionImagePo.setSortOrder(i + 1); // 排序从1开始
            sessionImageList.add(sessionImagePo);
        }

        // 验证批量数据结构
        assertEquals(3, sessionImageList.size(), "应该有3条记录准备批量插入");
        assertEquals(sessionId, sessionImageList.get(0).getSessionId());
        assertEquals("https://example.com/batch1.jpg", sessionImageList.get(0).getImageUrl());
        assertEquals(Integer.valueOf(1), sessionImageList.get(0).getSortOrder());

        assertEquals("https://example.com/batch2.jpg", sessionImageList.get(1).getImageUrl());
        assertEquals(Integer.valueOf(2), sessionImageList.get(1).getSortOrder());

        assertEquals("https://example.com/batch3.jpg", sessionImageList.get(2).getImageUrl());
        assertEquals(Integer.valueOf(3), sessionImageList.get(2).getSortOrder());
    }
}
