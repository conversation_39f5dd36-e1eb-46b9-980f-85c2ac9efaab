package com.wlink.agent.service;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackEventData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 对口型回调处理测试
 */
public class ShotLipSyncCallbackTest {

    @Test
    @DisplayName("测试解析对口型回调数据")
    public void testParseLipSyncCallbackData() {
        // 模拟回调数据
        String callbackJson = "{\n" +
                "    \"code\": 0,\n" +
                "    \"msg\": \"success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/0e687eefef37f1a44d61d85f2a3defe1/output/WanVideoWrapper_X3_60FPS_I2V_00001-audio_meuqd_1753954598.mp4\",\n" +
                "            \"fileType\": \"mp4\",\n" +
                "            \"taskCostTime\": 345,\n" +
                "            \"nodeId\": \"111\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/0e687eefef37f1a44d61d85f2a3defe1/output/ComparingTwoFrames_00004_oqoep_1753954591.png\",\n" +
                "            \"fileType\": \"png\",\n" +
                "            \"taskCostTime\": 345,\n" +
                "            \"nodeId\": \"148.0.0.3.0.0.164\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/0e687eefef37f1a44d61d85f2a3defe1/output/ComparingTwoFrames_00003_cfhou_1753954593.png\",\n" +
                "            \"fileType\": \"png\",\n" +
                "            \"taskCostTime\": 345,\n" +
                "            \"nodeId\": \"148.0.0.3.0.0.164\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/0e687eefef37f1a44d61d85f2a3defe1/output/ComparingTwoFrames_00002_besng_1753954478.png\",\n" +
                "            \"fileType\": \"png\",\n" +
                "            \"taskCostTime\": 345,\n" +
                "            \"nodeId\": \"164\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/0e687eefef37f1a44d61d85f2a3defe1/output/ComparingTwoFrames_00001_tbgqi_1753954480.png\",\n" +
                "            \"fileType\": \"png\",\n" +
                "            \"taskCostTime\": 345,\n" +
                "            \"nodeId\": \"164\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/0e687eefef37f1a44d61d85f2a3defe1/output/ComfyUI_00001_jspyl_1753954604.png\",\n" +
                "            \"fileType\": \"png\",\n" +
                "            \"taskCostTime\": 345,\n" +
                "            \"nodeId\": \"106\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        // 解析回调数据
        ComfyUICallbackEventData eventData = JSON.parseObject(callbackJson, ComfyUICallbackEventData.class);
        
        // 验证解析结果
        assertNotNull(eventData, "解析后的事件数据不应为空");
        assertEquals(0, eventData.getCode(), "事件代码应为0");
        assertEquals("success", eventData.getMsg(), "事件消息应为success");
        
        // 获取结果数据列表
        List<ComfyUICallbackEventData.ComfyUIResultData> resultDataList = eventData.getResultDataList();
        assertNotNull(resultDataList, "结果数据列表不应为空");
        assertEquals(6, resultDataList.size(), "结果数据列表应包含6个项目");
        
        // 验证第一个结果是MP4文件
        ComfyUICallbackEventData.ComfyUIResultData firstResult = resultDataList.get(0);
        assertEquals("mp4", firstResult.getFileType(), "第一个结果应该是MP4文件");
        assertTrue(firstResult.getFileUrl().endsWith(".mp4"), "第一个结果的URL应该以.mp4结尾");
        assertEquals(345, firstResult.getTaskCostTime(), "任务耗时应为345");
        assertEquals("111", firstResult.getNodeId(), "节点ID应为111");
        
        // 验证其他结果是PNG文件
        for (int i = 1; i < resultDataList.size(); i++) {
            ComfyUICallbackEventData.ComfyUIResultData result = resultDataList.get(i);
            assertEquals("png", result.getFileType(), "其他结果应该是PNG文件");
            assertTrue(result.getFileUrl().endsWith(".png"), "其他结果的URL应该以.png结尾");
        }
    }

    @Test
    @DisplayName("测试手动构建回调数据")
    public void testBuildCallbackData() {
        // 手动构建回调数据
        ComfyUICallbackEventData eventData = new ComfyUICallbackEventData();
        eventData.setCode(0);
        eventData.setMsg("success");
        
        List<ComfyUICallbackEventData.ComfyUIResultData> resultDataList = new ArrayList<>();
        
        // 添加MP4结果
        ComfyUICallbackEventData.ComfyUIResultData mp4Result = new ComfyUICallbackEventData.ComfyUIResultData();
        mp4Result.setFileUrl("https://example.com/test.mp4");
        mp4Result.setFileType("mp4");
        mp4Result.setTaskCostTime(500L);
        mp4Result.setNodeId("111");
        resultDataList.add(mp4Result);
        
        // 添加PNG结果
        ComfyUICallbackEventData.ComfyUIResultData pngResult = new ComfyUICallbackEventData.ComfyUIResultData();
        pngResult.setFileUrl("https://example.com/test.png");
        pngResult.setFileType("png");
        pngResult.setTaskCostTime(300L);
        pngResult.setNodeId("222");
        resultDataList.add(pngResult);
        
        // 设置数据
        eventData.setData(resultDataList);
        
        // 验证结果
        List<ComfyUICallbackEventData.ComfyUIResultData> retrievedList = eventData.getResultDataList();
        assertNotNull(retrievedList, "获取的结果列表不应为空");
        assertEquals(2, retrievedList.size(), "结果列表应包含2个项目");
        
        // 验证第一个结果
        ComfyUICallbackEventData.ComfyUIResultData firstResult = retrievedList.get(0);
        assertEquals("mp4", firstResult.getFileType(), "第一个结果应该是MP4文件");
        assertEquals("https://example.com/test.mp4", firstResult.getFileUrl(), "第一个结果URL应该匹配");
        
        // 验证第二个结果
        ComfyUICallbackEventData.ComfyUIResultData secondResult = retrievedList.get(1);
        assertEquals("png", secondResult.getFileType(), "第二个结果应该是PNG文件");
        assertEquals("https://example.com/test.png", secondResult.getFileUrl(), "第二个结果URL应该匹配");
        
        // 转换为JSON并验证
        String json = JSON.toJSONString(eventData);
        assertNotNull(json, "转换的JSON不应为空");
        assertTrue(json.contains("\"fileType\":\"mp4\""), "JSON应包含MP4文件类型");
        assertTrue(json.contains("\"fileType\":\"png\""), "JSON应包含PNG文件类型");
    }
}
