package com.wlink.agent.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.model.req.ChatMessageRequest;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试ChatMessageRequest中'None'值的处理
 */
public class ChatServiceNoneValueTest {

    @Test
    public void testSerializationWithNoneValues() {
        // 创建包含'None'值的请求
        ChatMessageRequest request = new ChatMessageRequest();
        request.setQuery("开始创作");
        request.setResponse_mode("streaming");
        request.setConversation_id("");
        request.setUser("1924701824068521984");
        
        JSONObject inputs = new JSONObject();
        inputs.put("TaskID", "1948598360389115904");
        request.setInputs(inputs);
        
        // 创建文件信息
        ChatMessageRequest.FileInfo file = new ChatMessageRequest.FileInfo();
        file.setType("image");
        file.setTransferMethod("remote_url");
        file.setUrl("https://wlpaas.weilitech.cn/dify/dev/1924701824068521984/image1753417020674_2qg9sy.jpeg");
        request.setFiles(Arrays.asList(file));
        
        // 序列化
        String json = JSON.toJSONString(request);
        System.out.println("Serialized JSON: " + json);
        
        // 检查是否包含'None'
        assertFalse(json.contains("\"None\""), "JSON should not contain 'None' values");
        assertFalse(json.contains("'None'"), "JSON should not contain 'None' values");
        
        // 验证关键字段
        assertTrue(json.contains("\"query\":\"开始创作\""));
        assertTrue(json.contains("\"response_mode\":\"streaming\""));
        assertTrue(json.contains("\"transfer_method\":\"remote_url\""));
    }

    @Test
    public void testSerializationWithExplicitNoneValues() {
        // 创建包含显式'None'值的请求
        ChatMessageRequest request = new ChatMessageRequest();
        request.setQuery("None");  // 显式设置为'None'
        request.setResponse_mode("None");
        request.setConversation_id("None");
        request.setUser("None");
        
        // 创建包含'None'值的文件信息
        ChatMessageRequest.FileInfo file = new ChatMessageRequest.FileInfo();
        file.setType("None");
        file.setTransferMethod("None");
        file.setUrl("None");
        request.setFiles(Arrays.asList(file));
        
        // 序列化
        String json = JSON.toJSONString(request);
        System.out.println("JSON with explicit None values: " + json);
        
        // 这个测试应该显示问题
        if (json.contains("\"None\"") || json.contains("'None'")) {
            System.out.println("Found 'None' values in JSON - this is the problem!");
        }
    }

    @Test
    public void testSafeStringMethod() {
        // 模拟safeString方法的行为
        assertEquals("", safeString(null));
        assertEquals("", safeString("None"));
        assertEquals("", safeString("null"));
        assertEquals("test", safeString("test"));
        
        assertEquals("default", safeString(null, "default"));
        assertEquals("default", safeString("None", "default"));
        assertEquals("test", safeString("test", "default"));
    }

    // 模拟ChatServiceImpl中的safeString方法
    private String safeString(String value) {
        return safeString(value, "");
    }

    private String safeString(String value, String defaultValue) {
        if (value == null || "None".equals(value) || "null".equals(value)) {
            return defaultValue;
        }
        return value;
    }
}
