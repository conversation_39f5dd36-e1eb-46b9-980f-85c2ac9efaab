package com.wlink.agent.service;

import com.wlink.agent.model.dto.AudioSynthesisRequest;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 音频合成服务测试
 */
public class AudioSynthesisServiceTest {

    // 注释掉需要Spring上下文的测试，专注于单元测试
    // @Test
    // public void testSubmitSynthesisTask() {
    //     // 这个测试需要Spring上下文和真实的ComfyUI API调用
    //     // 在实际项目中应该使用Mock来测试
    // }

    @Test
    public void testInvalidRequest() {
        // 测试无效请求
        AudioSynthesisRequest request = new AudioSynthesisRequest();
        
        // 缺少分镜ID
        assertFalse(request.isValid(), "缺少分镜ID的请求应该无效");
        
        // 设置分镜ID但缺少用户ID
        request.setShotId(12345L);
        assertFalse(request.isValid(), "缺少用户ID的请求应该无效");
        
        // 设置用户ID但缺少音频URL
        request.setUserId("test-user");
        assertFalse(request.isValid(), "缺少音频URL的请求应该无效");
        
        // 设置空的音频URL列表
        request.setAudioUrls(Arrays.asList());
        assertFalse(request.isValid(), "空音频URL列表的请求应该无效");
        
        // 设置过多的音频URL（超过7个）
        List<String> tooManyUrls = Arrays.asList(
            "url1", "url2", "url3", "url4", "url5", "url6", "url7", "url8"
        );
        request.setAudioUrls(tooManyUrls);
        assertFalse(request.isValid(), "超过7个音频URL的请求应该无效");
        
        // 设置有效的音频URL列表
        List<String> validUrls = Arrays.asList(
            "https://example.com/audio1.wav",
            "https://example.com/audio2.wav"
        );
        request.setAudioUrls(validUrls);
        assertTrue(request.isValid(), "有效的请求应该通过验证");
    }

    // 注释掉需要Spring上下文的测试
    // @Test
    // public void testGetTaskStatus() {
    //     // 需要Spring上下文和数据库
    // }

    // @Test
    // public void testGetLatestTaskByShot() {
    //     // 需要Spring上下文和数据库
    // }

    // @Test
    // public void testCancelTask() {
    //     // 需要Spring上下文和数据库
    // }
}
