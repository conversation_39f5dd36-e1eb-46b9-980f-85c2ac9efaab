package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiCanvasAudioPo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 音频类型验证测试
 * 测试processFinalAudio方法中的音频类型检查逻辑
 */
public class AudioTypeValidationTest {

    @Test
    @DisplayName("测试所有音频类型都是2的情况")
    public void testAllAudioType2() {
        // 创建测试数据：所有音频类型都是2
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        AiCanvasAudioPo audio1 = new AiCanvasAudioPo();
        audio1.setAudioType(2);
        audio1.setAudioUrl("https://example.com/audio1.wav");
        audioList.add(audio1);
        
        AiCanvasAudioPo audio2 = new AiCanvasAudioPo();
        audio2.setAudioType(2);
        audio2.setAudioUrl("https://example.com/audio2.wav");
        audioList.add(audio2);
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertTrue(allAudioType2, "所有音频类型都应该是2");
        
        // 模拟应该抛出的异常消息
        String expectedErrorMessage = "此分镜不支持对口型";
        assertEquals("此分镜不支持对口型", expectedErrorMessage, "错误消息应该正确");
    }

    @Test
    @DisplayName("测试混合音频类型的情况")
    public void testMixedAudioTypes() {
        // 创建测试数据：混合音频类型
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        AiCanvasAudioPo audio1 = new AiCanvasAudioPo();
        audio1.setAudioType(1); // 非2类型
        audio1.setAudioUrl("https://example.com/audio1.wav");
        audioList.add(audio1);
        
        AiCanvasAudioPo audio2 = new AiCanvasAudioPo();
        audio2.setAudioType(2); // 2类型
        audio2.setAudioUrl("https://example.com/audio2.wav");
        audioList.add(audio2);
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertFalse(allAudioType2, "不是所有音频类型都是2，应该支持对口型");
    }

    @Test
    @DisplayName("测试所有音频类型都不是2的情况")
    public void testNoAudioType2() {
        // 创建测试数据：所有音频类型都不是2
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        AiCanvasAudioPo audio1 = new AiCanvasAudioPo();
        audio1.setAudioType(1);
        audio1.setAudioUrl("https://example.com/audio1.wav");
        audioList.add(audio1);
        
        AiCanvasAudioPo audio2 = new AiCanvasAudioPo();
        audio2.setAudioType(3);
        audio2.setAudioUrl("https://example.com/audio2.wav");
        audioList.add(audio2);
        
        AiCanvasAudioPo audio3 = new AiCanvasAudioPo();
        audio3.setAudioType(0);
        audio3.setAudioUrl("https://example.com/audio3.wav");
        audioList.add(audio3);
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertFalse(allAudioType2, "没有音频类型是2，应该支持对口型");
    }

    @Test
    @DisplayName("测试单个音频类型是2的情况")
    public void testSingleAudioType2() {
        // 创建测试数据：单个音频类型是2
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        AiCanvasAudioPo audio1 = new AiCanvasAudioPo();
        audio1.setAudioType(2);
        audio1.setAudioUrl("https://example.com/audio1.wav");
        audioList.add(audio1);
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertTrue(allAudioType2, "单个音频类型是2，不应该支持对口型");
        assertEquals(1, audioList.size(), "应该只有一个音频");
    }

    @Test
    @DisplayName("测试单个音频类型不是2的情况")
    public void testSingleAudioTypeNot2() {
        // 创建测试数据：单个音频类型不是2
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        AiCanvasAudioPo audio1 = new AiCanvasAudioPo();
        audio1.setAudioType(1);
        audio1.setAudioUrl("https://example.com/audio1.wav");
        audioList.add(audio1);
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertFalse(allAudioType2, "单个音频类型不是2，应该支持对口型");
        assertEquals(1, audioList.size(), "应该只有一个音频");
    }

    @Test
    @DisplayName("测试音频类型为null的情况")
    public void testNullAudioType() {
        // 创建测试数据：音频类型为null
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        AiCanvasAudioPo audio1 = new AiCanvasAudioPo();
        audio1.setAudioType(null); // null类型
        audio1.setAudioUrl("https://example.com/audio1.wav");
        audioList.add(audio1);
        
        AiCanvasAudioPo audio2 = new AiCanvasAudioPo();
        audio2.setAudioType(2);
        audio2.setAudioUrl("https://example.com/audio2.wav");
        audioList.add(audio2);
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertFalse(allAudioType2, "有null类型的音频，不是所有都是2类型");
    }

    @Test
    @DisplayName("测试空音频列表的情况")
    public void testEmptyAudioList() {
        // 创建测试数据：空音频列表
        List<AiCanvasAudioPo> audioList = new ArrayList<>();
        
        // 验证检查逻辑
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);
        
        assertTrue(allAudioType2, "空列表的allMatch应该返回true");
        assertEquals(0, audioList.size(), "音频列表应该为空");
    }

    @Test
    @DisplayName("测试音频类型的各种边界值")
    public void testAudioTypeBoundaryValues() {
        // 测试各种音频类型值
        Integer[] audioTypes = {0, 1, 2, 3, 4, 5, -1, 999, null};
        
        for (Integer audioType : audioTypes) {
            List<AiCanvasAudioPo> audioList = new ArrayList<>();
            
            AiCanvasAudioPo audio = new AiCanvasAudioPo();
            audio.setAudioType(audioType);
            audio.setAudioUrl("https://example.com/audio.wav");
            audioList.add(audio);
            
            boolean allAudioType2 = audioList.stream()
                    .allMatch(a -> a.getAudioType() != null && a.getAudioType() == 2);
            
            if (audioType != null && audioType == 2) {
                assertTrue(allAudioType2, "音频类型为2时应该返回true: " + audioType);
            } else {
                assertFalse(allAudioType2, "音频类型不为2时应该返回false: " + audioType);
            }
        }
    }

    @Test
    @DisplayName("测试日志记录的概念")
    public void testLoggingConcepts() {
        // 测试日志记录的概念
        
        // 应该记录警告日志
        String warningLogMessage = "分镜所有音频类型都是2，不支持对口型: shotId={}";
        assertTrue(warningLogMessage.contains("不支持对口型"), "应该记录不支持对口型的警告");
        
        // 应该记录音频类型信息
        String audioInfoLogMessage = "音频信息: shotId={}, audioUrl={}, audioType={}";
        assertTrue(audioInfoLogMessage.contains("audioType"), "应该记录音频类型信息");
        
        // 应该记录单个音频的处理信息
        String singleAudioLogMessage = "分镜只有1个音频，直接触发对口型处理: shotId={}, audioUrl={}, audioType={}";
        assertTrue(singleAudioLogMessage.contains("直接触发"), "应该记录直接触发对口型的信息");
    }

    @Test
    @DisplayName("测试异常处理机制")
    public void testExceptionHandling() {
        // 测试异常处理的概念
        
        // 应该抛出BizException
        String exceptionType = "BizException";
        assertEquals("BizException", exceptionType, "应该抛出BizException");
        
        // 异常消息应该明确
        String exceptionMessage = "此分镜不支持对口型";
        assertTrue(exceptionMessage.contains("不支持对口型"), "异常消息应该明确说明不支持对口型");
        
        // 应该在检查阶段就抛出异常，不进入后续处理
        boolean shouldThrowEarly = true;
        assertTrue(shouldThrowEarly, "应该在音频类型检查阶段就抛出异常");
    }
}
