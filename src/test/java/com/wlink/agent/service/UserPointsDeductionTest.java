package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiShotImageEditPo;
import com.wlink.agent.dao.po.AiShotLipSyncPo;
import com.wlink.agent.enums.TransactionTypeEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户积分扣除功能测试
 * 测试图片编辑和对口型成功时的积分扣除逻辑
 */
public class UserPointsDeductionTest {

    @Test
    @DisplayName("测试图片编辑积分扣除参数")
    public void testImageEditPointsDeduction() {
        // 准备测试数据
        String userId = "test-user-123";
        String taskId = "image-edit-task-456";
        int expectedPoints = 30;
        String expectedDescription = "图片编辑成功";
        TransactionTypeEnum expectedTransactionType = TransactionTypeEnum.IMAGE_EDIT;

        // 创建编辑记录
        AiShotImageEditPo editRecord = new AiShotImageEditPo();
        editRecord.setUserId(userId);
        editRecord.setTaskId(taskId);
        editRecord.setShotId(789L);
        editRecord.setPrompt("测试编辑提示词");

        // 验证数据设置正确
        assertEquals(userId, editRecord.getUserId(), "用户ID应该正确");
        assertEquals(taskId, editRecord.getTaskId(), "任务ID应该正确");
        assertEquals(789L, editRecord.getShotId(), "分镜ID应该正确");
        assertEquals("测试编辑提示词", editRecord.getPrompt(), "提示词应该正确");

        // 验证积分扣除参数
        assertEquals(30, expectedPoints, "图片编辑应该扣除30积分");
        assertEquals("图片编辑成功", expectedDescription, "描述应该正确");
        assertEquals(TransactionTypeEnum.IMAGE_EDIT, expectedTransactionType, "交易类型应该是图片编辑");
    }

    @Test
    @DisplayName("测试对口型积分扣除参数")
    public void testLipSyncPointsDeduction() {
        // 准备测试数据
        String userId = "test-user-456";
        String taskId = "lip-sync-task-789";
        int expectedPoints = 150;
        String expectedDescription = "对口型成功";
        TransactionTypeEnum expectedTransactionType = TransactionTypeEnum.LIP_SYNC;

        // 创建对口型记录
        AiShotLipSyncPo lipSyncRecord = new AiShotLipSyncPo();
        lipSyncRecord.setUserId(userId);
        lipSyncRecord.setTaskId(taskId);
        lipSyncRecord.setShotId(123L);
        lipSyncRecord.setStatus("COMPLETED");

        // 验证数据设置正确
        assertEquals(userId, lipSyncRecord.getUserId(), "用户ID应该正确");
        assertEquals(taskId, lipSyncRecord.getTaskId(), "任务ID应该正确");
        assertEquals(123L, lipSyncRecord.getShotId(), "分镜ID应该正确");
        assertEquals("COMPLETED", lipSyncRecord.getStatus(), "状态应该正确");

        // 验证积分扣除参数
        assertEquals(150, expectedPoints, "对口型应该扣除150积分");
        assertEquals("对口型成功", expectedDescription, "描述应该正确");
        assertEquals(TransactionTypeEnum.LIP_SYNC, expectedTransactionType, "交易类型应该是对口型");
    }

    @Test
    @DisplayName("测试新增的交易类型枚举")
    public void testNewTransactionTypes() {
        // 验证图片编辑交易类型
        TransactionTypeEnum imageEditType = TransactionTypeEnum.IMAGE_EDIT;
        assertEquals(11, imageEditType.getCode(), "图片编辑交易类型编码应该是11");
        assertEquals("图片编辑消耗", imageEditType.getDesc(), "图片编辑交易类型描述应该正确");

        // 验证对口型交易类型
        TransactionTypeEnum lipSyncType = TransactionTypeEnum.LIP_SYNC;
        assertEquals(12, lipSyncType.getCode(), "对口型交易类型编码应该是12");
        assertEquals("对口型消耗", lipSyncType.getDesc(), "对口型交易类型描述应该正确");
    }

    @Test
    @DisplayName("测试积分扣除异常处理逻辑")
    public void testPointsDeductionExceptionHandling() {
        // 准备测试数据
        String userId = "test-user-exception";
        String taskId = "exception-task-123";

        // 验证异常处理逻辑（在实际实现中，异常应该被捕获并记录日志，不影响主流程）
        assertDoesNotThrow(() -> {
            try {
                // 模拟可能抛出异常的积分扣除操作
                if (userId.contains("exception")) {
                    throw new RuntimeException("积分不足");
                }
            } catch (Exception e) {
                // 在实际实现中，异常应该被捕获并记录日志，不影响主流程
                System.out.println("积分扣除异常被正确处理: " + e.getMessage());
                // 异常被正确捕获，不会影响主流程
            }
        }, "积分扣除异常不应该影响主流程");

        // 验证异常处理的预期行为
        assertTrue(userId.contains("exception"), "测试用户ID包含exception标识");
        assertEquals("exception-task-123", taskId, "任务ID应该正确");
    }

    @Test
    @DisplayName("测试积分扣除的业务逻辑集成")
    public void testPointsDeductionIntegration() {
        // 验证积分扣除逻辑的集成点

        // 1. 图片编辑成功时的集成点
        String imageEditIntegrationPoint = "ShotImageEditServiceImpl.updateBusinessTablesOnSuccess()";
        assertTrue(imageEditIntegrationPoint.contains("updateBusinessTablesOnSuccess"), 
                "图片编辑积分扣除应该集成在updateBusinessTablesOnSuccess方法中");

        // 2. 对口型成功时的集成点
        String lipSyncIntegrationPoint = "ShotLipSyncServiceImpl.handleLipSyncCallback()";
        assertTrue(lipSyncIntegrationPoint.contains("handleLipSyncCallback"), 
                "对口型积分扣除应该集成在handleLipSyncCallback方法中");

        // 3. 验证积分扣除时机
        String[] correctTimings = {
                "图片编辑任务状态为COMPLETED时",
                "对口型回调处理成功时",
                "业务表更新完成后"
        };

        for (String timing : correctTimings) {
            assertNotNull(timing, "积分扣除时机应该明确定义");
        }
    }
}
