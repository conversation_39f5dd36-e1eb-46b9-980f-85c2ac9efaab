package com.wlink.agent.service.impl;

import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.model.req.UpdateNarrationAudioReq;
import com.wlink.agent.model.res.NarrationCountRes;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.model.res.UpdateNarrationAudioRes;
import com.wlink.agent.service.AiCreationContentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class UpdateAudioTest {

    @Mock
    private AiShotMapper aiShotMapper;

    @Mock
    private AiChapterMapper aiChapterMapper;

    @Mock
    private AiCreationSessionMapper aiCreationSessionMapper;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Object> bucket;

    @Mock
    private AiCreationContentService aiCreationContentService;

    @Mock
    private AgentSoundMapper agentSoundMapper;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private AiShotServiceImpl aiShotService;

    private String testSessionId = "test-session-123";

    @BeforeEach
    void setUp() {
        lenient().when(redissonClient.getBucket(anyString())).thenReturn(bucket);
    }

    @Test
    void testUpdateNarrationAudio_NarrationMode() {
        // 准备测试数据 - 旁白模式
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId(testSessionId);
        req.setSegmentIds(Arrays.asList("segment-1"));
        req.setSoundId(123L);
        // characterId为空，表示修改旁白

        // Mock音色验证
        AgentSoundPo soundPo = new AgentSoundPo();
        soundPo.setId(123L);
        soundPo.setUserName("Test Voice");
        when(agentSoundMapper.selectOne(any())).thenReturn(soundPo);

        // Mock Redis中的统计数据
        NarrationCountRes statisticsData = new NarrationCountRes();
        statisticsData.setSessionId(testSessionId);
        
        NarrationCountRes.ChapterStatInfo chapter = new NarrationCountRes.ChapterStatInfo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("Chapter 1");
        
        // 旁白详情
        NarrationCountRes.ContentDetail narrationDetail = new NarrationCountRes.ContentDetail();
        narrationDetail.setShotId("shot-1");
        narrationDetail.setId(1);
        narrationDetail.setContent("Test narration content");
        narrationDetail.setCharacterId(null);
        narrationDetail.setCharacterName(null);
        
        chapter.setNarrationDetails(Arrays.asList(narrationDetail));
        statisticsData.setChapters(Arrays.asList(chapter));

        when(bucket.get()).thenReturn(statisticsData);

        // Mock TTS生成结果
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("http://example.com/narration.mp3");
        ttsRes.setDuration(5000L);
        ttsRes.setRecordId(123L);

        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        // 执行测试
        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertNull(result.getCharacterId());
        assertEquals("narration", result.getAudioType());
        assertEquals(1, result.getProcessedChapterCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(1, result.getProcessDetails().size());

        UpdateNarrationAudioRes.ProcessDetail processDetail = result.getProcessDetails().get(0);
        assertEquals("segment-1", processDetail.getSegmentId());
        assertEquals("Chapter 1", processDetail.getSegmentName());
        assertEquals("shot-1", processDetail.getShotId());
        assertEquals(1, processDetail.getContentId());
        assertEquals("Test narration content", processDetail.getContentText());
        assertNull(processDetail.getCharacterId());
        assertNull(processDetail.getCharacterName());
        assertEquals("SUCCESS", processDetail.getStatus());
        assertEquals("http://example.com/narration.mp3", processDetail.getAudioUrl());

        // 验证TTS服务被调用
        verify(aiCreationContentService).generateTts(any());
    }

    @Test
    void testUpdateNarrationAudio_CharacterMode() {
        // 准备测试数据 - 角色模式
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId(testSessionId);
        req.setSegmentIds(Arrays.asList("segment-1"));
        req.setSoundId(123L);
        req.setCharacterId("ZhaoGao_aA3p"); // 指定角色ID

        // Mock音色验证
        AgentSoundPo soundPo = new AgentSoundPo();
        soundPo.setId(123L);
        soundPo.setUserName("Test Voice");
        when(agentSoundMapper.selectOne(any())).thenReturn(soundPo);

        // Mock Redis中的统计数据
        NarrationCountRes statisticsData = new NarrationCountRes();
        statisticsData.setSessionId(testSessionId);
        
        NarrationCountRes.ChapterStatInfo chapter = new NarrationCountRes.ChapterStatInfo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("Chapter 1");
        
        // 角色详情
        NarrationCountRes.ContentDetail characterDetail = new NarrationCountRes.ContentDetail();
        characterDetail.setShotId("shot-1");
        characterDetail.setId(2);
        characterDetail.setContent("陛下，臣特意进献一匹千里良驹");
        characterDetail.setCharacterId("ZhaoGao_aA3p");
        characterDetail.setCharacterName("赵高");
        
        // 角色分组
        NarrationCountRes.CharacterGroup characterGroup = new NarrationCountRes.CharacterGroup();
        characterGroup.setCharacterId("ZhaoGao_aA3p");
        characterGroup.setCharacterName("赵高");
        characterGroup.setCount(1);
        characterGroup.setDetails(Arrays.asList(characterDetail));
        
        chapter.setCharacterGroups(Arrays.asList(characterGroup));
        statisticsData.setChapters(Arrays.asList(chapter));

        when(bucket.get()).thenReturn(statisticsData);

        // Mock TTS生成结果
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("http://example.com/character.mp3");
        ttsRes.setDuration(3000L);
        ttsRes.setRecordId(456L);

        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        // 执行测试
        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals("ZhaoGao_aA3p", result.getCharacterId());
        assertEquals("character", result.getAudioType());
        assertEquals(1, result.getProcessedChapterCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(1, result.getProcessDetails().size());

        UpdateNarrationAudioRes.ProcessDetail processDetail = result.getProcessDetails().get(0);
        assertEquals("segment-1", processDetail.getSegmentId());
        assertEquals("Chapter 1", processDetail.getSegmentName());
        assertEquals("shot-1", processDetail.getShotId());
        assertEquals(2, processDetail.getContentId());
        assertEquals("陛下，臣特意进献一匹千里良驹", processDetail.getContentText());
        assertEquals("ZhaoGao_aA3p", processDetail.getCharacterId());
        assertEquals("赵高", processDetail.getCharacterName());
        assertEquals("SUCCESS", processDetail.getStatus());
        assertEquals("http://example.com/character.mp3", processDetail.getAudioUrl());

        // 验证TTS服务被调用
        verify(aiCreationContentService).generateTts(any());
    }

    @Test
    void testUpdateNarrationAudio_WithSessionVoice() {
        // 测试使用会话默认音色的情况
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId(testSessionId);
        // 不设置soundId，应该使用会话默认音色

        // Mock会话信息
        AiCreationSessionPo sessionPo = new AiCreationSessionPo();
        sessionPo.setSessionId(testSessionId);
        sessionPo.setSoundId(456L);

        when(aiCreationSessionMapper.selectOne(any())).thenReturn(sessionPo);

        // Mock音色验证
        AgentSoundPo soundPo = new AgentSoundPo();
        soundPo.setId(456L);
        soundPo.setUserName("Session Voice");
        when(agentSoundMapper.selectOne(any())).thenReturn(soundPo);

        // Mock Redis中的统计数据
        NarrationCountRes statisticsData = new NarrationCountRes();
        statisticsData.setSessionId(testSessionId);
        
        NarrationCountRes.ChapterStatInfo chapter = new NarrationCountRes.ChapterStatInfo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("Chapter 1");
        
        NarrationCountRes.ContentDetail narrationDetail = new NarrationCountRes.ContentDetail();
        narrationDetail.setShotId("shot-1");
        narrationDetail.setId(1);
        narrationDetail.setContent("Test narration content");
        
        chapter.setNarrationDetails(Arrays.asList(narrationDetail));
        statisticsData.setChapters(Arrays.asList(chapter));

        when(bucket.get()).thenReturn(statisticsData);

        // Mock TTS生成结果
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("http://example.com/audio.mp3");
        ttsRes.setDuration(5000L);
        ttsRes.setRecordId(123L);

        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        // 执行测试
        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getSuccessCount());
        
        // 验证使用了会话默认音色
        verify(aiCreationSessionMapper).selectOne(any());
        verify(aiCreationContentService).generateTts(any());
    }
}
