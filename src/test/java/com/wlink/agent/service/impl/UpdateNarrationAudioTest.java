package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.model.req.UpdateNarrationAudioReq;
import com.wlink.agent.model.res.NarrationCountRes;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.model.res.UpdateNarrationAudioRes;
import com.wlink.agent.service.AiCreationContentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.concurrent.Executor;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class UpdateNarrationAudioTest {

    @Mock
    private AiShotMapper aiShotMapper;

    @Mock
    private AiChapterMapper aiChapterMapper;

    @Mock
    private AiCreationSessionMapper aiCreationSessionMapper;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Object> bucket;

    @Mock
    private AiCreationContentService aiCreationContentService;

    @Mock
    private AgentSoundMapper agentSoundMapper;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private AiShotServiceImpl aiShotService;

    private String testSessionId = "test-session-123";

    @BeforeEach
    void setUp() {
        lenient().when(redissonClient.getBucket(anyString())).thenReturn(bucket);
    }

    @Test
    void testUpdateNarrationAudio_Success() {
        // 准备测试数据
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId(testSessionId);
        req.setSegmentIds(Arrays.asList("segment-1"));
        req.setSoundId(123L);

        // Mock Redis中的旁白统计数据
        NarrationCountRes narrationData = new NarrationCountRes();
        narrationData.setSessionId(testSessionId);
        
        NarrationCountRes.ChapterNarrationInfo chapter = new NarrationCountRes.ChapterNarrationInfo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("Chapter 1");
        
        NarrationCountRes.NarrationDetail detail = new NarrationCountRes.NarrationDetail();
        detail.setShotId("shot-1");
        detail.setNarrationId(1);
        detail.setNarrationLine("Test narration content");
        
        chapter.setNarrationDetails(Arrays.asList(detail));
        narrationData.setChapters(Arrays.asList(chapter));

        when(bucket.get()).thenReturn(narrationData);

        // Mock音色验证
        AgentSoundPo soundPo = new AgentSoundPo();
        soundPo.setId(123L);
        soundPo.setUserName("Test Voice");
        when(agentSoundMapper.selectOne(any())).thenReturn(soundPo);

        // Mock TTS生成结果
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("http://example.com/audio.mp3");
        ttsRes.setDuration(5000L);
        ttsRes.setRecordId(123L);

        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        // 执行测试
        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(1, result.getProcessedChapterCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(1, result.getProcessDetails().size());

        UpdateNarrationAudioRes.ProcessDetail processDetail = result.getProcessDetails().get(0);
        assertEquals("segment-1", processDetail.getSegmentId());
        assertEquals("Chapter 1", processDetail.getSegmentName());
        assertEquals("shot-1", processDetail.getShotId());
        assertEquals(1, processDetail.getNarrationId());
        assertEquals("Test narration content", processDetail.getNarrationLine());
        assertEquals("SUCCESS", processDetail.getStatus());
        assertEquals("http://example.com/audio.mp3", processDetail.getAudioUrl());
        assertEquals(5000L, processDetail.getDuration());
        assertEquals(123L, processDetail.getRecordId());

        // 验证TTS服务被调用
        verify(aiCreationContentService).generateTts(any());
    }

    @Test
    void testUpdateNarrationAudio_WithSessionVoice() {
        // 测试使用会话默认音色的情况
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId(testSessionId);
        // 不设置voiceId，应该使用会话默认音色

        // Mock会话信息
        AiCreationSessionPo sessionPo = new AiCreationSessionPo();
        sessionPo.setSessionId(testSessionId);
        sessionPo.setSoundId(456L);

        when(aiCreationSessionMapper.selectOne(any())).thenReturn(sessionPo);

        // Mock Redis中的旁白统计数据
        NarrationCountRes narrationData = new NarrationCountRes();
        narrationData.setSessionId(testSessionId);
        
        NarrationCountRes.ChapterNarrationInfo chapter = new NarrationCountRes.ChapterNarrationInfo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("Chapter 1");
        
        NarrationCountRes.NarrationDetail detail = new NarrationCountRes.NarrationDetail();
        detail.setShotId("shot-1");
        detail.setNarrationId(1);
        detail.setNarrationLine("Test narration content");
        
        chapter.setNarrationDetails(Arrays.asList(detail));
        narrationData.setChapters(Arrays.asList(chapter));

        when(bucket.get()).thenReturn(narrationData);

        // Mock TTS生成结果
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("http://example.com/audio.mp3");
        ttsRes.setDuration(5000L);
        ttsRes.setRecordId(123L);

        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        // 执行测试
        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getSuccessCount());
        
        // 验证使用了会话默认音色
        verify(aiCreationSessionMapper).selectOne(any());
        verify(aiCreationContentService).generateTts(any());
    }

    @Test
    void testUpdateNarrationAudio_EmptySessionId() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId("");

        assertThrows(Exception.class, () -> {
            aiShotService.updateNarrationAudio(req);
        });
    }

    @Test
    void testUpdateNarrationAudio_NoRedisData() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        req.setSessionId(testSessionId);

        // Mock Redis中没有数据
        when(bucket.get()).thenReturn(null);

        // Mock重新计算的结果
        when(aiShotMapper.selectList(any())).thenReturn(Arrays.asList());
        when(aiChapterMapper.selectList(any())).thenReturn(Arrays.asList());

        // 执行测试
        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(0, result.getProcessedChapterCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertTrue(result.getProcessDetails().isEmpty());
    }
}
