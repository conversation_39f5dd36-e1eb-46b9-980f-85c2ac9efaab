package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.model.req.NarrationCountReq;
import com.wlink.agent.model.res.NarrationCountRes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class NarrationCountTest {

    @Mock
    private AiShotMapper aiShotMapper;

    @Mock
    private AiChapterMapper aiChapterMapper;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Object> bucket;

    @InjectMocks
    private AiShotServiceImpl aiShotService;

    private String testSessionId = "test-session-123";

    @BeforeEach
    void setUp() {
        lenient().when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        lenient().when(bucket.get()).thenReturn(null); // 模拟Redis中没有缓存
    }

    @Test
    void testGetNarrationCount_ComprehensiveMode() {
        // 准备测试数据 - 综合统计模式（同时统计旁白和角色）
        String shotDataWithMixed = """
            {
                "shotId": null,
                "id": "C01-SC01-25",
                "type": "Medium Shot + Eye-Level",
                "movement": "Dolly Out",
                "line_list": [
                    {
                        "name": "旁白",
                        "line": "这是一段旁白内容",
                        "charID": "NARRATOR001",
                        "id": 1,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "陛下，臣特意进献一匹千里良驹",
                        "charID": "ZhaoGao_aA3p",
                        "id": 2,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "请陛下过目！",
                        "charID": "ZhaoGao_aA3p",
                        "id": 3,
                        "voice": null
                    }
                ]
            }
            """;

        // Mock分镜数据
        AiShotPo shot = new AiShotPo();
        shot.setId(1L);
        shot.setSessionId(testSessionId);
        shot.setSegmentId("segment-1");
        shot.setShotId("C01-SC01-25");
        shot.setShotData(shotDataWithMixed);

        List<AiShotPo> shotList = Arrays.asList(shot);

        // Mock章节数据
        AiChapterPo chapter = new AiChapterPo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("第一章");
        List<AiChapterPo> chapterList = Arrays.asList(chapter);

        // 设置Mock行为
        when(aiShotMapper.selectList(any())).thenReturn(shotList);
        when(aiChapterMapper.selectList(any())).thenReturn(chapterList);

        // 执行测试 - 综合统计
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId(testSessionId);

        NarrationCountRes result = aiShotService.getNarrationCount(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(1, result.getTotalNarrationCount()); // 1个旁白
        assertEquals(2, result.getTotalCharacterCount()); // 2个角色音频
        assertEquals(1, result.getChapters().size());

        // 验证角色统计汇总
        assertEquals(1, result.getCharacterSummary().size());
        NarrationCountRes.CharacterSummary summary = result.getCharacterSummary().get(0);
        assertEquals("ZhaoGao_aA3p", summary.getCharacterId());
        assertEquals("赵高", summary.getCharacterName());
        assertEquals(2, summary.getTotalCount());

        // 验证章节统计
        NarrationCountRes.ChapterStatInfo chapterInfo = result.getChapters().get(0);
        assertEquals("segment-1", chapterInfo.getSegmentId());
        assertEquals("第一章", chapterInfo.getSegmentName());
        assertEquals(1, chapterInfo.getNarrationCount());
        assertEquals(2, chapterInfo.getCharacterCount());

        // 验证旁白详情
        assertEquals(1, chapterInfo.getNarrationDetails().size());
        NarrationCountRes.ContentDetail narrationDetail = chapterInfo.getNarrationDetails().get(0);
        assertEquals("C01-SC01-25", narrationDetail.getShotId());
        assertEquals(1, narrationDetail.getId());
        assertEquals("这是一段旁白内容", narrationDetail.getContent());
        assertNull(narrationDetail.getCharacterId());

        // 验证角色详情
        assertEquals(2, chapterInfo.getCharacterDetails().size());
        NarrationCountRes.ContentDetail characterDetail1 = chapterInfo.getCharacterDetails().get(0);
        assertEquals("C01-SC01-25", characterDetail1.getShotId());
        assertEquals(2, characterDetail1.getId());
        assertEquals("陛下，臣特意进献一匹千里良驹", characterDetail1.getContent());
        assertEquals("ZhaoGao_aA3p", characterDetail1.getCharacterId());
        assertEquals("赵高", characterDetail1.getCharacterName());

        // 验证角色分组
        assertEquals(1, chapterInfo.getCharacterGroups().size());
        NarrationCountRes.CharacterGroup group = chapterInfo.getCharacterGroups().get(0);
        assertEquals("ZhaoGao_aA3p", group.getCharacterId());
        assertEquals("赵高", group.getCharacterName());
        assertEquals(2, group.getCount());
        assertEquals(2, group.getDetails().size());
    }

    @Test
    void testGetNarrationCount_MultipleCharacters() {
        // 准备测试数据 - 多角色统计
        String shotDataWithMultipleCharacters = """
            {
                "shotId": null,
                "id": "C01-SC03-02",
                "type": "Close-Up + Eye-Level",
                "movement": "Static Shot",
                "line_list": [
                    {
                        "name": "旁白",
                        "line": "这是一段旁白内容",
                        "charID": "NARRATOR001",
                        "id": 1,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "陛下，臣特意进献一匹千里良驹，此马神骏异常！",
                        "charID": "ZhaoGao_aA3p",
                        "id": 2,
                        "voice": null
                    },
                    {
                        "name": "胡亥",
                        "line": "这是什么？",
                        "charID": "HuHai_bB4q",
                        "id": 3,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "请陛下过目！",
                        "charID": "ZhaoGao_aA3p",
                        "id": 4,
                        "voice": null
                    }
                ]
            }
            """;

        // Mock分镜数据
        AiShotPo shot = new AiShotPo();
        shot.setId(1L);
        shot.setSessionId(testSessionId);
        shot.setSegmentId("segment-1");
        shot.setShotId("C01-SC03-02");
        shot.setShotData(shotDataWithMultipleCharacters);

        List<AiShotPo> shotList = Arrays.asList(shot);

        // Mock章节数据
        AiChapterPo chapter = new AiChapterPo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("第一章");
        List<AiChapterPo> chapterList = Arrays.asList(chapter);

        // 设置Mock行为
        when(aiShotMapper.selectList(any())).thenReturn(shotList);
        when(aiChapterMapper.selectList(any())).thenReturn(chapterList);

        // 执行测试 - 多角色统计
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId(testSessionId);

        NarrationCountRes result = aiShotService.getNarrationCount(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(1, result.getTotalNarrationCount()); // 1个旁白
        assertEquals(3, result.getTotalCharacterCount()); // 3个角色音频

        // 验证角色统计汇总（应该有2个角色）
        assertEquals(2, result.getCharacterSummary().size());

        // 验证赵高的统计
        NarrationCountRes.CharacterSummary zhaoGaoSummary = result.getCharacterSummary().stream()
                .filter(s -> "ZhaoGao_aA3p".equals(s.getCharacterId()))
                .findFirst().orElse(null);
        assertNotNull(zhaoGaoSummary);
        assertEquals("赵高", zhaoGaoSummary.getCharacterName());
        assertEquals(2, zhaoGaoSummary.getTotalCount());

        // 验证胡亥的统计
        NarrationCountRes.CharacterSummary huHaiSummary = result.getCharacterSummary().stream()
                .filter(s -> "HuHai_bB4q".equals(s.getCharacterId()))
                .findFirst().orElse(null);
        assertNotNull(huHaiSummary);
        assertEquals("胡亥", huHaiSummary.getCharacterName());
        assertEquals(1, huHaiSummary.getTotalCount());

        // 验证章节角色分组
        NarrationCountRes.ChapterStatInfo chapterInfo = result.getChapters().get(0);
        assertEquals(2, chapterInfo.getCharacterGroups().size());
    }

    @Test
    void testGetNarrationCount_EmptySession() {
        // Mock空的分镜列表
        when(aiShotMapper.selectList(any())).thenReturn(Arrays.asList());

        // 执行测试
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId(testSessionId);

        NarrationCountRes result = aiShotService.getNarrationCount(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(0, result.getTotalNarrationCount());
        assertEquals(0, result.getTotalCharacterCount());
        assertTrue(result.getChapters().isEmpty());
        assertTrue(result.getCharacterSummary().isEmpty());
    }

    @Test
    void testGetNarrationCount_InvalidSessionId() {
        // 测试空的sessionId
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId("");

        assertThrows(Exception.class, () -> {
            aiShotService.getNarrationCount(req);
        });

        req.setSessionId(null);
        assertThrows(Exception.class, () -> {
            aiShotService.getNarrationCount(req);
        });
    }
}
