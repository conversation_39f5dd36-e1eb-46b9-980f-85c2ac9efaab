package com.wlink.agent.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AiCanvasMaterialServiceImpl 测试类
 */
class AiCanvasMaterialServiceImplTest {

    private AiCanvasMaterialServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new AiCanvasMaterialServiceImpl(
                null, null, null, null, null, null, null, null, null, null
        );
    }

    @Test
    void testCalculateSizeFromAspectRatio() {
        // 测试常见的宽高比
        assertEquals("1280x720", invokeCalculateSize("16:9"));
        assertEquals("1024x1024", invokeCalculateSize("1:1"));
        assertEquals("720x1280", invokeCalculateSize("9:16"));
        
        // 测试接近的宽高比
        assertEquals("1280x768", invokeCalculateSize("5:3")); // 1.67 最接近
        assertEquals("1152x864", invokeCalculateSize("4:3")); // 1.33 最接近
        
        // 测试极端宽高比
        assertEquals("1536x512", invokeCalculateSize("3:1"));
        assertEquals("512x1536", invokeCalculateSize("1:3"));
        
        // 测试无效输入
        assertEquals("adaptive", invokeCalculateSize(""));
        assertEquals("adaptive", invokeCalculateSize(null));
        assertEquals("adaptive", invokeCalculateSize("invalid"));
        assertEquals("adaptive", invokeCalculateSize("16"));
        assertEquals("adaptive", invokeCalculateSize("16:"));
        assertEquals("adaptive", invokeCalculateSize(":9"));
    }

    @Test
    void testCalculateSizeFromAspectRatioEdgeCases() {
        // 测试小数宽高比
        assertEquals("1280x768", invokeCalculateSize("1.67:1")); // 应该解析为 1.67
        assertEquals("1024x1024", invokeCalculateSize("1.0:1.0")); // 应该解析为 1.0
        
        // 测试带空格的输入
        assertEquals("1280x720", invokeCalculateSize(" 16 : 9 "));
        
        // 测试零值
        assertEquals("adaptive", invokeCalculateSize("0:9"));
        assertEquals("adaptive", invokeCalculateSize("16:0"));
    }

    /**
     * 通过反射调用私有方法进行测试
     */
    private String invokeCalculateSize(String aspectRatio) {
        try {
            return (String) ReflectionTestUtils.invokeMethod(service, "calculateSizeFromAspectRatio", aspectRatio);
        } catch (Exception e) {
            fail("Failed to invoke calculateSizeFromAspectRatio method: " + e.getMessage());
            return null;
        }
    }
}
