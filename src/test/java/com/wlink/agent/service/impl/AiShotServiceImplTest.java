package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.model.res.NarrationCountRes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class AiShotServiceImplTest {

    @Mock
    private AiShotMapper aiShotMapper;

    @Mock
    private AiChapterMapper aiChapterMapper;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Object> bucket;

    @InjectMocks
    private AiShotServiceImpl aiShotService;

    private String testSessionId = "test-session-123";

    @BeforeEach
    void setUp() {
        lenient().when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        lenient().when(bucket.get()).thenReturn(null); // 模拟Redis中没有缓存
    }

    @Test
    void testGetNarrationCountBySession_Success() {
        // 准备测试数据
        String shotDataWithNarration = """
            {
                "shotId": null,
                "id": "C01-SC01-25",
                "type": "Medium Shot + Eye-Level",
                "movement": "Dolly Out",
                "composition": null,
                "image": "dify/dev/1917046010101100544/image/c6884365342f42e98ce608423d3564ab.png",
                "imageStatus": "COMPLETED",
                "voice": null,
                "duration": null,
                "characters": ["专家讲解员"],
                "line_list": [
                    {
                        "name": "旁白",
                        "line": "希望这些建议能帮助你更好地面对经济萧条。我们一起加油！",
                        "charID": "NARRATOR001",
                        "id": 1,
                        "voice": null
                    },
                    {
                        "name": "对话",
                        "line": "这不是旁白",
                        "charID": "CHAR001",
                        "id": 2,
                        "voice": null
                    }
                ],
                "narration": null,
                "image_info": "画面开始时，专家讲解员处于中景构图...",
                "queue": "25"
            }
            """;

        // Mock分镜数据
        AiShotPo shot1 = new AiShotPo();
        shot1.setId(1L);
        shot1.setSessionId(testSessionId);
        shot1.setSegmentId("segment-1");
        shot1.setShotId("C01-SC01-25");
        shot1.setShotData(shotDataWithNarration);

        AiShotPo shot2 = new AiShotPo();
        shot2.setId(2L);
        shot2.setSessionId(testSessionId);
        shot2.setSegmentId("segment-1");
        shot2.setShotId("C01-SC01-26");
        shot2.setShotData(shotDataWithNarration); // 同样的数据，应该统计2个旁白

        List<AiShotPo> shotList = Arrays.asList(shot1, shot2);

        // Mock章节数据
        AiChapterPo chapter = new AiChapterPo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("第一章");
        List<AiChapterPo> chapterList = Arrays.asList(chapter);

        // 设置Mock行为
        when(aiShotMapper.selectList(any())).thenReturn(shotList);
        when(aiChapterMapper.selectList(any())).thenReturn(chapterList);

        // 执行测试
        NarrationCountRes result = aiShotService.getNarrationCountBySession(testSessionId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(2, result.getTotalNarrationCount()); // 2个分镜，每个1个旁白
        assertEquals(1, result.getChapters().size());

        NarrationCountRes.ChapterNarrationInfo chapterInfo = result.getChapters().get(0);
        assertEquals("segment-1", chapterInfo.getSegmentId());
        assertEquals("第一章", chapterInfo.getSegmentName());
        assertEquals(2, chapterInfo.getNarrationCount());
        assertEquals(2, chapterInfo.getNarrationDetails().size());

        // 验证Redis保存操作被调用
        verify(bucket).set(eq(result), anyLong(), any());
    }

    @Test
    void testGetNarrationCountBySession_EmptySession() {
        // Mock空的分镜列表
        when(aiShotMapper.selectList(any())).thenReturn(Arrays.asList());

        // 执行测试
        NarrationCountRes result = aiShotService.getNarrationCountBySession(testSessionId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals(0, result.getTotalNarrationCount());
        assertTrue(result.getChapters().isEmpty());
    }

    @Test
    void testGetNarrationCountBySession_InvalidSessionId() {
        // 测试空的sessionId
        assertThrows(Exception.class, () -> {
            aiShotService.getNarrationCountBySession("");
        });

        assertThrows(Exception.class, () -> {
            aiShotService.getNarrationCountBySession(null);
        });
    }
}
