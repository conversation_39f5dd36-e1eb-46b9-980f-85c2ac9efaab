package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiCanvasAudioPo;
import com.wlink.agent.utils.TextSplitUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AiCanvasShotService 音频拆分逻辑测试
 */
public class AiCanvasShotServiceTest {

    @Test
    public void testProcessNarrationAudioSplitLogic() {
        // 创建测试音频
        AiCanvasAudioPo audio = createTestAudio();
        long remainingDuration = 5000L; // 5秒剩余时长
        
        List<AiCanvasAudioPo> currentShotAudios = new ArrayList<>();
        List<AiCanvasAudioPo> nextShotAudios = new ArrayList<>();
        
        // 模拟新的拆分逻辑
        processTestNarrationSplit(audio, remainingDuration, currentShotAudios, nextShotAudios);
        
        // 验证结果
        assertFalse(currentShotAudios.isEmpty(), "当前分镜应该有音频");
        assertFalse(nextShotAudios.isEmpty(), "下一个分镜应该有音频");
        
        // 验证每个音频都有独立的URL和时长
        for (AiCanvasAudioPo audioItem : currentShotAudios) {
            assertNotNull(audioItem.getAudioUrl(), "当前分镜音频应该有URL");
            assertNotNull(audioItem.getAudioDuration(), "当前分镜音频应该有时长");
            assertNotNull(audioItem.getText(), "当前分镜音频应该有文本");
        }
        
        for (AiCanvasAudioPo audioItem : nextShotAudios) {
            assertNotNull(audioItem.getAudioUrl(), "下一个分镜音频应该有URL");
            assertNotNull(audioItem.getAudioDuration(), "下一个分镜音频应该有时长");
            assertNotNull(audioItem.getText(), "下一个分镜音频应该有文本");
        }
        
        // 验证音频总数等于原始段落数
        List<String> textSegments = TextSplitUtils.splitTextByPunctuation(audio.getText());
        assertEquals(textSegments.size(), currentShotAudios.size() + nextShotAudios.size(),
                "生成的音频总数应该等于文本段落数");
        
        System.out.println("当前分镜音频数量: " + currentShotAudios.size());
        System.out.println("下一个分镜音频数量: " + nextShotAudios.size());
        
        // 打印详细信息
        for (int i = 0; i < currentShotAudios.size(); i++) {
            AiCanvasAudioPo audioItem = currentShotAudios.get(i);
            System.out.println("当前分镜音频 " + (i + 1) + ": " + audioItem.getText() + 
                    " (时长: " + audioItem.getAudioDuration() + "ms, URL: " + audioItem.getAudioUrl() + ")");
        }
        
        for (int i = 0; i < nextShotAudios.size(); i++) {
            AiCanvasAudioPo audioItem = nextShotAudios.get(i);
            System.out.println("下一个分镜音频 " + (i + 1) + ": " + audioItem.getText() + 
                    " (时长: " + audioItem.getAudioDuration() + "ms, URL: " + audioItem.getAudioUrl() + ")");
        }
    }

    private AiCanvasAudioPo createTestAudio() {
        AiCanvasAudioPo audio = new AiCanvasAudioPo();
        audio.setId(1L);
        audio.setCanvasId(100L);
        audio.setShotCode("TEST-SHOT-001");
        audio.setAudioType(1); // 旁白类型
        audio.setText("这是第一段旁白内容，讲述故事的开始。这是第二段旁白内容，描述主角的背景！这是第三段旁白内容，介绍故事的环境？这是第四段旁白内容，推进剧情发展。");
        audio.setVoiceId("test_voice_001");
        audio.setAudioDuration(10000L); // 原始音频10秒
        audio.setSortOrder(1);
        return audio;
    }

    /**
     * 模拟新的音频拆分逻辑
     */
    private void processTestNarrationSplit(AiCanvasAudioPo audio, long remainingDuration,
                                          List<AiCanvasAudioPo> currentShotAudios,
                                          List<AiCanvasAudioPo> nextShotAudios) {
        String text = audio.getText();
        if (text == null || text.trim().isEmpty()) {
            nextShotAudios.add(audio);
            return;
        }
        
        // 使用工具类拆分文本
        List<String> textSegments = TextSplitUtils.splitTextByPunctuation(text);
        
        if (textSegments.size() <= 1) {
            nextShotAudios.add(audio);
            return;
        }
        
        // 按顺序生成每个段落的音频（模拟实际生成过程）
        List<AiCanvasAudioPo> generatedAudios = new ArrayList<>();
        long accumulatedDuration = 0L;
        int splitIndex = 0;
        
        for (int i = 0; i < textSegments.size(); i++) {
            String segment = textSegments.get(i);
            
            // 创建新的音频对象
            AiCanvasAudioPo segmentAudio = createAudioFromOriginal(audio, segment);
            
            // 模拟TTS生成，使用估算时长
            long segmentDuration = TextSplitUtils.estimateAudioDuration(segment);
            segmentAudio.setAudioDuration(segmentDuration);
            segmentAudio.setAudioUrl("mock_audio_url_" + (i + 1) + ".wav"); // 模拟生成的音频URL
            
            // 检查是否可以放入当前分镜
            if (accumulatedDuration + segmentDuration <= remainingDuration) {
                accumulatedDuration += segmentDuration;
                splitIndex = i + 1;
            }
            
            // 将生成的音频添加到列表中，无论是否能放入当前分镜
            generatedAudios.add(segmentAudio);
        }
        
        // 将前splitIndex个音频放入当前分镜
        for (int i = 0; i < splitIndex; i++) {
            currentShotAudios.add(generatedAudios.get(i));
        }
        
        // 将剩余的音频放入下一个分镜
        for (int i = splitIndex; i < generatedAudios.size(); i++) {
            nextShotAudios.add(generatedAudios.get(i));
        }
    }

    private AiCanvasAudioPo createAudioFromOriginal(AiCanvasAudioPo original, String newText) {
        AiCanvasAudioPo newAudio = new AiCanvasAudioPo();
        BeanUtils.copyProperties(original, newAudio);
        newAudio.setId(null); // 清空ID，让数据库自动生成
        newAudio.setText(newText);
        newAudio.setAudioUrl(null); // 清空URL，需要重新生成
        newAudio.setAudioDuration(null); // 清空时长，由调用方设置实际时长
        return newAudio;
    }
}
