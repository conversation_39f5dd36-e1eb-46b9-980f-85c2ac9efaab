package com.wlink.agent.job;

import com.wlink.agent.client.MiniMaxFileDownloadClient;
import com.wlink.agent.client.MiniMaxVideoApiClient;
import com.wlink.agent.client.model.minimax.MiniMaxVideoStatusResponse;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import com.wlink.agent.service.impl.VideoGenerationQueueServiceImpl;
import com.wlink.agent.utils.OssUtils;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * MiniMax视频生成状态检查定时任务测试
 */
@ExtendWith(MockitoExtension.class)
class MiniMaxVideoGenerationStatusCheckJobTest {

    @Mock
    private AiVideoGenerationRecordMapper videoGenerationRecordMapper;
    
    @Mock
    private AiVideoGenerationMapper videoGenerationMapper;
    
    @Mock
    private AiCanvasShotMapper aiCanvasShotMapper;
    
    @Mock
    private AiCanvasVideoMapper aiCanvasVideoMapper;
    
    @Mock
    private VideoGenerationQueueServiceImpl videoGenerationQueueService;
    
    @Mock
    private MiniMaxVideoApiClient miniMaxVideoApiClient;
    
    @Mock
    private MiniMaxFileDownloadClient miniMaxFileDownloadClient;
    
    @Mock
    private OssUtils ossUtils;

    @InjectMocks
    private MiniMaxVideoGenerationStatusCheckJob job;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(job, "env", "test");
    }

    @Test
    void testCheckMiniMaxVideoGenerationTaskStatus_NoTasks() {
        // Given
        when(videoGenerationRecordMapper.getUncompletedTasksByModel(anyString(), any(LocalDateTime.class)))
                .thenReturn(new ArrayList<>());

        // When
        ReturnT<String> result = job.checkMiniMaxVideoGenerationTaskStatus("");

        // Then
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testCheckMiniMaxVideoGenerationTaskStatus_WithSuccessTask() throws Exception {
        // Given
        List<AiVideoGenerationRecordPo> tasks = new ArrayList<>();
        AiVideoGenerationRecordPo task = AiVideoGenerationRecordPo.builder()
                .id(1L)
                .taskId("test-task-id")
                .userId(123L)
                .model("MiniMax-Hailuo-02")
                .status("processing")
                .build();
        tasks.add(task);

        MiniMaxVideoStatusResponse response = MiniMaxVideoStatusResponse.builder()
                .taskId("test-task-id")
                .status("Success")
                .fileId("test-file-id")
                .build();

        when(videoGenerationRecordMapper.getUncompletedTasksByModel(anyString(), any(LocalDateTime.class)))
                .thenReturn(tasks);
        when(miniMaxVideoApiClient.getVideoStatus(anyString()))
                .thenReturn(CompletableFuture.completedFuture(response));
        when(miniMaxFileDownloadClient.downloadFileAsBytes(anyString()))
                .thenReturn(CompletableFuture.completedFuture(new byte[1024]));
        when(ossUtils.uploadStream(any(), anyString()))
                .thenReturn("https://test.oss.com/video.mp4");

        // When
        ReturnT<String> result = job.checkMiniMaxVideoGenerationTaskStatus("");

        // Then
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testCheckMiniMaxVideoGenerationTaskStatus_WithFailedTask() throws Exception {
        // Given
        List<AiVideoGenerationRecordPo> tasks = new ArrayList<>();
        AiVideoGenerationRecordPo task = AiVideoGenerationRecordPo.builder()
                .id(1L)
                .taskId("test-task-id")
                .userId(123L)
                .model("MiniMax-Hailuo-02")
                .status("processing")
                .build();
        tasks.add(task);

        MiniMaxVideoStatusResponse response = MiniMaxVideoStatusResponse.builder()
                .taskId("test-task-id")
                .status("Fail")
                .build();

        when(videoGenerationRecordMapper.getUncompletedTasksByModel(anyString(), any(LocalDateTime.class)))
                .thenReturn(tasks);
        when(miniMaxVideoApiClient.getVideoStatus(anyString()))
                .thenReturn(CompletableFuture.completedFuture(response));

        // When
        ReturnT<String> result = job.checkMiniMaxVideoGenerationTaskStatus("");

        // Then
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testCheckMiniMaxVideoGenerationTaskStatus_WithProcessingTask() throws Exception {
        // Given
        List<AiVideoGenerationRecordPo> tasks = new ArrayList<>();
        AiVideoGenerationRecordPo task = AiVideoGenerationRecordPo.builder()
                .id(1L)
                .taskId("test-task-id")
                .userId(123L)
                .model("MiniMax-Hailuo-02")
                .status("processing")
                .build();
        tasks.add(task);

        MiniMaxVideoStatusResponse response = MiniMaxVideoStatusResponse.builder()
                .taskId("test-task-id")
                .status("Processing")
                .build();

        when(videoGenerationRecordMapper.getUncompletedTasksByModel(anyString(), any(LocalDateTime.class)))
                .thenReturn(tasks);
        when(miniMaxVideoApiClient.getVideoStatus(anyString()))
                .thenReturn(CompletableFuture.completedFuture(response));

        // When
        ReturnT<String> result = job.checkMiniMaxVideoGenerationTaskStatus("");

        // Then
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testCheckMiniMaxVideoGenerationTaskStatus_WithException() {
        // Given
        when(videoGenerationRecordMapper.getUncompletedTasksByModel(anyString(), any(LocalDateTime.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ReturnT<String> result = job.checkMiniMaxVideoGenerationTaskStatus("");

        // Then
        assertEquals(ReturnT.FAIL_CODE, result.getCode());
    }
}
