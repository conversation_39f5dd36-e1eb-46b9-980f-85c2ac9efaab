package com.wlink.agent.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.config.ConcurrencyControlConfig;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.service.impl.DatabaseConcurrencyControlService;
import com.wlink.agent.service.impl.StableImageGenerationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * ImageTaskSchedulerJob 测试类
 * 验证任务类型过滤逻辑
 */
@ExtendWith(MockitoExtension.class)
public class ImageTaskSchedulerJobTest {

    @Mock
    private AiImageTaskQueueMapper taskQueueMapper;

    @Mock
    private DatabaseConcurrencyControlService concurrencyControlService;

    @Mock
    private StableImageGenerationService imageGenerationService;

    @Mock
    private ConcurrencyControlConfig config;

    @InjectMocks
    private ImageTaskSchedulerJob imageTaskSchedulerJob;

    @BeforeEach
    void setUp() {
        when(config.getMaxConcurrentTasks()).thenReturn(5);
    }

    @Test
    void testScheduleImageTasks_ShouldFilterOutEditTasks() {
        // 准备测试数据
        AiImageTaskQueuePo generateTask = createTask(1L, TaskType.GENERATE.getValue());
        AiImageTaskQueuePo editTask = createTask(2L, TaskType.EDIT.getValue());
        AiImageTaskQueuePo canvasEditTask = createTask(3L, TaskType.EDIT_CANVAS.getValue());
        AiImageTaskQueuePo doubaoTask = createTask(4L, TaskType.GENERATE_DOUBAO.getValue());

        // Mock 查询结果 - 应该只返回生成任务，过滤掉编辑任务
        when(taskQueueMapper.selectCount(any(LambdaQueryWrapper.class)))
            .thenReturn(0L) // processing count
            .thenReturn(2L); // pending generation tasks count

        when(taskQueueMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Arrays.asList(generateTask, doubaoTask)); // 只返回生成任务

        // 执行测试
        imageTaskSchedulerJob.scheduleImageTasks("");

        // 验证查询条件
        verify(taskQueueMapper, times(3)).selectCount(any(LambdaQueryWrapper.class));
        verify(taskQueueMapper, times(1)).selectList(any(LambdaQueryWrapper.class));

        // 验证任务处理 - 应该只处理生成任务
        verify(imageGenerationService, times(2)).processImageTask(anyString());
        verify(imageGenerationService).processImageTask("1");
        verify(imageGenerationService).processImageTask("4");
        verify(imageGenerationService, never()).processImageTask("2"); // 编辑任务不应该被处理
        verify(imageGenerationService, never()).processImageTask("3"); // 画布编辑任务不应该被处理
    }

    @Test
    void testScheduleEditTasks_ShouldOnlyProcessEditTasks() {
        // 准备测试数据
        AiImageTaskQueuePo generateTask = createTask(1L, TaskType.GENERATE.getValue());
        AiImageTaskQueuePo editTask = createTask(2L, TaskType.EDIT.getValue());
        AiImageTaskQueuePo canvasEditTask = createTask(3L, TaskType.EDIT_CANVAS.getValue());

        // Mock 查询结果 - 应该只返回编辑任务
        when(taskQueueMapper.selectCount(any(LambdaQueryWrapper.class)))
            .thenReturn(0L) // processing count
            .thenReturn(2L); // pending edit tasks count

        when(taskQueueMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Arrays.asList(editTask, canvasEditTask)); // 只返回编辑任务

        // 执行测试
        imageTaskSchedulerJob.scheduleEditTasks("");

        // 验证任务处理 - 应该只处理编辑任务
        verify(imageGenerationService, times(2)).processImageTask(anyString());
        verify(imageGenerationService).processImageTask("2");
        verify(imageGenerationService).processImageTask("3");
        verify(imageGenerationService, never()).processImageTask("1"); // 生成任务不应该被处理
    }

    @Test
    void testScheduleHighPriorityTasks_ShouldFilterOutEditTasks() {
        // 准备测试数据
        AiImageTaskQueuePo retryGenerateTask = createTask(1L, TaskType.GENERATE.getValue());
        retryGenerateTask.setRetryCount(1);
        
        AiImageTaskQueuePo retryEditTask = createTask(2L, TaskType.EDIT.getValue());
        retryEditTask.setRetryCount(1);

        // Mock 查询结果 - 应该只返回生成任务的重试任务
        when(taskQueueMapper.selectCount(any(LambdaQueryWrapper.class)))
            .thenReturn(0L) // processing count
            .thenReturn(1L); // pending generation tasks count

        when(taskQueueMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Arrays.asList(retryGenerateTask)); // 只返回生成任务

        // 执行测试
        imageTaskSchedulerJob.scheduleHighPriorityTasks("");

        // 验证任务处理 - 应该只处理生成任务的重试
        verify(imageGenerationService, times(1)).processImageTask(anyString());
        verify(imageGenerationService).processImageTask("1");
        verify(imageGenerationService, never()).processImageTask("2"); // 编辑任务不应该被处理
    }

    private AiImageTaskQueuePo createTask(Long id, String taskType) {
        AiImageTaskQueuePo task = new AiImageTaskQueuePo();
        task.setId(id);
        task.setTaskType(taskType);
        task.setTaskStatus(TaskStatus.PENDING.getValue());
        task.setRetryCount(0);
        return task;
    }
}
