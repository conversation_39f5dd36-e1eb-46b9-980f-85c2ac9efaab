<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">

    <!-- 定义日志文件路径变量 -->
    <property name="LOG_HOME" value="/data/logs" />
    <property name="APP_NAME" value="smart-agent" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!-- 格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度，%logger{50} 表示logger名字最长50个字符，否则按照句点分割,%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- INFO及以上级别日志文件输出 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件路径和名称 -->
        <file>${LOG_HOME}/${APP_NAME}/info.log</file>
        <!-- 滚动策略，按时间滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档文件名模式 -->
            <fileNamePattern>${LOG_HOME}/${APP_NAME}/info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 保留历史记录的天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制，触发滚动 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，只记录INFO级别及以上的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!-- ERROR级别日志文件输出 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件路径和名称 -->
        <file>${LOG_HOME}/${APP_NAME}/error.log</file>
        <!-- 滚动策略，按时间滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档文件名模式 -->
            <fileNamePattern>${LOG_HOME}/${APP_NAME}/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 保留历史记录的天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制，触发滚动 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 根logger配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="INFO_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </root>

    <!-- 特定包的日志级别（可选） -->
    <!-- 例如，设置 com.wlink 包的日志级别为 DEBUG -->
    <!-- 仅在本地开发环境启用DEBUG日志 -->
    <springProfile name="local">
        <logger name="com.wlink.agent" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="INFO_FILE" />
            <appender-ref ref="ERROR_FILE" />
        </logger>
    </springProfile>

</configuration>