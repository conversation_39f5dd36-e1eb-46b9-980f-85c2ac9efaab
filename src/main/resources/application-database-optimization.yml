# 数据库连接池和事务优化配置
spring:
  datasource:
    hikari:
      # 连接池配置
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      # 连接测试
      connection-test-query: SELECT 1
      validation-timeout: 5000
      # 泄漏检测
      leak-detection-threshold: 60000
      
  # 事务配置
  transaction:
    default-timeout: 30
    rollback-on-commit-failure: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # SQL执行超时时间（秒）
    default-statement-timeout: 30
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 日志配置
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 音频合成服务优化配置
audio:
  merge:
    # 数据库操作重试配置
    db-retry:
      max-attempts: 3
      initial-delay: 1000
      max-delay: 5000
      multiplier: 2.0
    # 任务超时配置
    task-timeout-minutes: 15
    # 并发控制
    max-concurrent-tasks: 2
