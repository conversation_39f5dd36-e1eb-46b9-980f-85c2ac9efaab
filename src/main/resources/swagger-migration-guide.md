# Swagger 迁移到 SpringDoc OpenAPI 指南

## 注解映射关系

将SpringFox的注解替换为SpringDoc对应的注解：

| SpringFox 注解 | SpringDoc 注解 | 包路径 |
|--------------|--------------|-------|
| `@Api` | `@Tag` | `io.swagger.v3.oas.annotations.tags.Tag` |
| `@Operation` | `@Operation` | `io.swagger.v3.oas.annotations.Operation` |
| `@ApiParam` | `@Parameter` | `io.swagger.v3.oas.annotations.Parameter` |
| `@ApiModel` | `@Schema` | `io.swagger.v3.oas.annotations.media.Schema` |
| `@ApiModelProperty` | `@Schema` | `io.swagger.v3.oas.annotations.media.Schema` |
| `@ApiIgnore` | `@Hidden` | `io.swagger.v3.oas.annotations.Hidden` |
| `@ApiImplicitParams` | `@Parameters` | `io.swagger.v3.oas.annotations.Parameters` |
| `@ApiImplicitParam` | `@Parameter` | `io.swagger.v3.oas.annotations.Parameter` |
| `@ApiResponse` | `@ApiResponse` | `io.swagger.v3.oas.annotations.responses.ApiResponse` |
| `@ApiResponses` | `@ApiResponses` | `io.swagger.v3.oas.annotations.responses.ApiResponses` |

## 示例转换

### 1. 控制器类注解

**旧注解:**
```java
@Api(tags = "用户信息接口")
@RestController
@RequestMapping("/api/users")
public class UserController {
```

**新注解:**
```java
@Tag(name = "用户信息接口")
@RestController
@RequestMapping("/api/users")
public class UserController {
```

### 2. 方法注解

**旧注解:**
```java
@ApiOperation("查询当前用户信息")
@GetMapping("/me")
public Response<UserProfileRes> getCurrentUser() {
```

**新注解:**
```java
@Operation(summary = "查询当前用户信息")
@GetMapping("/me")
public Response<UserProfileRes> getCurrentUser() {
```

### 3. 参数注解

**旧注解:**
```java
public Response<Page<PointsHistoryRes>> getPointsHistory(
    @ApiParam(value = "页码 (从1开始)", required = true, defaultValue = "1") @RequestParam("page") int page,
    @ApiParam(value = "每页数量", required = true, defaultValue = "10") @RequestParam("size") int size) {
```

**新注解:**
```java
public Response<Page<PointsHistoryRes>> getPointsHistory(
    @Parameter(description = "页码 (从1开始)", required = true) @RequestParam(defaultValue = "1") int page,
    @Parameter(description = "每页数量", required = true) @RequestParam(defaultValue = "10") int size) {
```

### 4. 模型类注解

**旧注解:**
```java
@ApiModel("用户登录响应")
public class UserLoginRes {
    @ApiModelProperty(value = "认证令牌 (JWT Token)", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String token;
}
```

**新注解:**
```java
@Schema(description = "用户登录响应")
public class UserLoginRes {
    @Schema(description = "认证令牌 (JWT Token)", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String token;
}
```

## 批量替换模式 (IDE搜索替换)

使用IDE的全局搜索替换功能，按以下顺序替换：

1. 导入包替换:
   - `import io.swagger.annotations.Api` → `import io.swagger.v3.oas.annotations.tags.Tag`
   - `import io.swagger.annotations.ApiOperation` → `import io.swagger.v3.oas.annotations.Operation`
   - `import io.swagger.annotations.ApiParam` → `import io.swagger.v3.oas.annotations.Parameter`
   - `import io.swagger.annotations.ApiModel` → `import io.swagger.v3.oas.annotations.media.Schema`
   - `import io.swagger.annotations.ApiModelProperty` → `import io.swagger.v3.oas.annotations.media.Schema`
   - `import io.swagger.annotations.ApiIgnore` → `import io.swagger.v3.oas.annotations.Hidden`

2. 注解替换:
   - `@Api(tags = "` → `@Tag(name = "`
   - `@ApiOperation("` → `@Operation(summary = "`
   - `@ApiOperation(value = "` → `@Operation(summary = "`
   - `@ApiParam(value = "` → `@Parameter(description = "`
   - `@ApiModel("` → `@Schema(description = "`
   - `@ApiModel(description = "` → `@Schema(description = "`
   - `@ApiModelProperty("` → `@Schema(description = "`
   - `@ApiModelProperty(value = "` → `@Schema(description = "`
   - `@ApiIgnore` → `@Hidden`

## 注意事项

1. 替换后可能需要手动调整部分注解参数，确保语法正确
2. 类级别的 `@ApiModel` 和属性级别的 `@ApiModelProperty` 都替换为 `@Schema`，但用法稍有不同
3. 需要重新导入包，删除旧的 `io.swagger` 包引用
4. 检查并修复任何编译错误，确保替换后的注解参数格式正确 