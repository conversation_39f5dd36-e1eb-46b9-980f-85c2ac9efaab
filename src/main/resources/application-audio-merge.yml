# 音频合成服务配置
audio:
  merge:
    # 服务类型选择：simple(推荐), redis, database, debug(调试模式)
    service-type: simple
    # 最大并发任务数（建议不超过CPU核数的一半）
    max-concurrent-tasks: 2
    # 最大内存使用（MB）
    max-memory-mb: 512
    # CPU限制（核数）
    cpu-limit: 2
    # 临时文件目录
    temp-dir: /tmp/audio-merge
    # FFmpeg可执行文件路径
    ffmpeg-path: ffmpeg
    # 默认音频参数
    default:
      # 输出格式
      output-format: wav
      # 采样率
      sample-rate: 44100
      # 比特率
      bit-rate: 128k
      # 声道数
      channels: 2
      # 是否标准化音量
      normalize-volume: true
      # 最大处理时间（秒）
      max-processing-time-seconds: 300
    # 任务超时时间（分钟）
    task-timeout-minutes: 30
