#!/bin/bash
# 这是一个指导文档，帮助使用IDE批量替换所有Controller文件中的Swagger注解

# 第一步：使用IDE全局搜索替换以下导入包:
# `import io.swagger.annotations.Api` → `import io.swagger.v3.oas.annotations.tags.Tag`
# `import io.swagger.annotations.Operation` → `import io.swagger.v3.oas.annotations.Operation`
# `import io.swagger.annotations.ApiParam` → `import io.swagger.v3.oas.annotations.Parameter`
# `import io.swagger.annotations.ApiModel` → `import io.swagger.v3.oas.annotations.media.Schema`
# `import io.swagger.annotations.ApiModelProperty` → `import io.swagger.v3.oas.annotations.media.Schema`
# `import io.swagger.annotations.ApiIgnore` → `import io.swagger.v3.oas.annotations.Hidden`

# 第二步：使用IDE全局搜索替换以下注解:
# `@Api(tags = "` → `@Tag(name = "`
# `@Operation("` → `@Operation(summary = "`
# `@Operation(value = "` → `@Operation(summary = "`
# `@ApiParam(value = "` → `@Parameter(description = "`
# `@ApiModel("` → `@Schema(description = "`
# `@ApiModel(description = "` → `@Schema(description = "`
# `@ApiModelProperty("` → `@Schema(description = "`
# `@ApiModelProperty(value = "` → `@Schema(description = "`
# `@ApiIgnore` → `@Hidden`

# 第三步：注解参数映射 (如果有特殊参数，需要手动调整):
# notes → description
# example → example (保持不变)
# required → required (保持不变)
# hidden → hidden (保持不变)

# 第四步：替换javax到jakarta:
# `import javax.validation` → `import jakarta.validation`
# `import javax.annotation` → `import jakarta.annotation`

# 特别说明:
# 对于@Api注解中的description参数，在@Tag注解中保留
# 对于复杂的注解参数，可能需要手动调整

# 以下是批量替换的代表性案例:
# 1. @Api(tags = "内容资源服务接口", description = "提供图片风格和标签等内容的查询功能")
#    替换为
#    @Tag(name = "内容资源服务接口", description = "提供图片风格和标签等内容的查询功能")

# 2. @Operation(value = "获取可用图片风格列表", notes = "查询并返回所有可用的图片风格")
#    替换为
#    @Operation(summary = "获取可用图片风格列表", description = "查询并返回所有可用的图片风格")

# 3. @ApiParam(value = "修改操作的唯一编码", required = true, example = "snow_flake_id_string")
#    替换为
#    @Parameter(description = "修改操作的唯一编码", required = true, example = "snow_flake_id_string")

# 4. @ApiModelProperty(value = "头像", example = "https://example.com/avatar.jpg")
#    替换为
#    @Schema(description = "头像", example = "https://example.com/avatar.jpg")
