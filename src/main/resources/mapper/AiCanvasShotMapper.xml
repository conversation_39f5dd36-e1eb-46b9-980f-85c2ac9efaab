<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasShotMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasShotPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="originalShotId" column="original_shot_id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="composition" column="composition" jdbcType="VARCHAR"/>
            <result property="movement" column="movement" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="shotStatus" column="shot_status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,canvas_id,code,
        original_shot_id,type,composition,
        movement,sort_order,shot_status,
        create_time,update_time,del_flag
    </sql>
</mapper>
