<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiRoleFavoriteMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiRoleFavoritePo">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="user_id" property="userId"/>
        <result column="session_id" property="sessionId"/>
        <result column="role_id" property="roleId"/>
        <result column="role_data" property="roleData"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

</mapper> 