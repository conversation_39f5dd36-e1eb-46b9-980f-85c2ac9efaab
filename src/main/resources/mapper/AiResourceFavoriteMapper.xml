<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiResourceFavoriteMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiResourceFavoritePo">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="user_id" property="userId"/>
        <result column="session_id" property="sessionId"/>
        <result column="resource_id" property="resourceId"/>
        <result column="resource_type" property="resourceType"/>
        <result column="resource_subtype" property="resourceSubtype"/>
        <result column="resource_name" property="resourceName"/>
        <result column="resource_url" property="resourceUrl"/>
        <result column="resource_data" property="resourceData"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 根据用户ID和资源类型查询收藏列表 -->
    <select id="selectByUserIdAndType" resultMap="BaseResultMap">
        SELECT *
        FROM ai_resource_favorite
        WHERE user_id = #{userId}
        AND resource_type = #{resourceType}
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID、资源类型和子类型查询收藏列表 -->
    <select id="selectByUserIdAndTypeAndSubtype" resultMap="BaseResultMap">
        SELECT *
        FROM ai_resource_favorite
        WHERE user_id = #{userId}
        AND resource_type = #{resourceType}
        AND resource_subtype = #{resourceSubtype}
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

</mapper> 