<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasImageMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasImagePo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
            <result property="shotCode" column="shot_code" jdbcType="VARCHAR"/>
            <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
            <result property="imagePrompt" column="image_prompt" jdbcType="VARCHAR"/>
            <result property="imageDesc" column="image_desc" jdbcType="VARCHAR"/>
            <result property="imageAspectRatio" column="image_aspect_ratio" jdbcType="VARCHAR"/>
            <result property="imageStatus" column="image_status" jdbcType="VARCHAR"/>
            <result property="referenceImage" column="reference_image" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,canvas_id,shot_code,
        image_url,image_prompt,image_desc,
        image_aspect_ratio,image_status,reference_image,
        create_time,update_time,del_flag
    </sql>
</mapper>
