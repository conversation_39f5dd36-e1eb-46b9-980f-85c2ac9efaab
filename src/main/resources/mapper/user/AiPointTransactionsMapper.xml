<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiPointTransactionsMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiPointTransactionsPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="points" column="points" jdbcType="INTEGER"/>
            <result property="balance" column="balance" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="referenceId" column="reference_id" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,points,
        balance,type,reference_id,
        description,create_time,update_time,
        del_flag
    </sql>
</mapper>
