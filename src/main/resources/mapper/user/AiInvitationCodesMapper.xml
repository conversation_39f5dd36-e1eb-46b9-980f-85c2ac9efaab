<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiInvitationCodesMapper">

    <!-- 其他 ResultMap 和 SQL 语句 -->
    <!-- ... existing xml ... -->

    <!-- 批量插入指定列 -->
    <insert id="insertBatchSomeColumn" parameterType="java.util.List">
        INSERT INTO ai_invitation_codes (code, create_user_id, status, create_time, expire_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.code,jdbcType=VARCHAR},
            #{item.createUserId,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.expireTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>