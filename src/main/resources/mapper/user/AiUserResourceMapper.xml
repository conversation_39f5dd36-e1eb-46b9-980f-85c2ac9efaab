<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiUserResourceMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiUserResourcePo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="resourceType" column="resource_type" jdbcType="TINYINT"/>
        <result property="generationStatus" column="generation_status" jdbcType="VARCHAR"/>
        <result property="resourceUrls" column="resource_urls" jdbcType="LONGVARCHAR"/>
        <result property="resourceSize" column="resource_size" jdbcType="BIGINT"/>
        <result property="width" column="width" jdbcType="INTEGER"/>
        <result property="height" column="height" jdbcType="INTEGER"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="modelType" column="model_type" jdbcType="VARCHAR"/>
        <result property="prompt" column="prompt" jdbcType="LONGVARCHAR"/>
        <result property="referenceImages" column="reference_images" jdbcType="LONGVARCHAR"/>
        <result property="generationParams" column="generation_params" jdbcType="LONGVARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="LONGVARCHAR"/>
        <result property="externalRequestId" column="external_request_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, code, user_id, resource_type, generation_status, resource_urls, resource_size,
        width, height, duration, model_type, prompt, reference_images, generation_params,
        error_message, external_request_id, create_time, update_time, del_flag
    </sql>

    <select id="selectByUserIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_user_resource
        WHERE user_id = #{userId}
        AND resource_type = #{resourceType}
        AND del_flag = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_user_resource
        WHERE user_id = #{userId}
        AND del_flag = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_user_resource
        WHERE generation_status = #{generationStatus}
        AND del_flag = 0
        ORDER BY create_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_user_resource
        WHERE code = #{code}
        AND del_flag = 0
    </select>

    <select id="selectByCodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_user_resource
        WHERE code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

</mapper>
