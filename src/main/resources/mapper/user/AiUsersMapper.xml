<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiUsersMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiUsersPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="passwordHash" column="password_hash" jdbcType="VARCHAR"/>
            <result property="salt" column="salt" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="registerIp" column="register_ip" jdbcType="VARCHAR"/>
            <result property="lastLoginIp" column="last_login_ip" jdbcType="VARCHAR"/>
            <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
            <result property="points" column="points" jdbcType="INTEGER"/>
            <result property="activationTime" column="activation_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,nickname,
        email,phone,password_hash,
        salt,status,avatar,
        register_ip,last_login_ip,last_login_time,
        points,activation_time,create_time,
        update_time,del_flag
    </sql>
</mapper>
