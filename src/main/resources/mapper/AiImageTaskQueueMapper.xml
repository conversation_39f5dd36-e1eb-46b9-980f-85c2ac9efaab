<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiImageTaskQueueMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiImageTaskQueuePo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="contentType" column="content_type" jdbcType="INTEGER"/>
        <result property="contentId" column="content_id" jdbcType="VARCHAR"/>
        <result property="requestParams" column="request_params" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="VARCHAR"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="imageResult" column="image_result" jdbcType="VARCHAR"/>
        <result property="errorReason" column="error_reason" jdbcType="VARCHAR"/>
        <result property="sessionQueuePosition" column="session_queue_position" jdbcType="INTEGER"/>
        <result property="globalQueuePosition" column="global_queue_position" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,session_id,task_type,content_id,content_type,
        request_params,task_status,result,image_result, error_reason,
        session_queue_position,global_queue_position,
        create_time,update_time,del_flag
    </sql>
    
    <!-- 统计正在处理的任务数量 -->
    <select id="countProcessingTasks" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ai_image_task_queue
        WHERE task_status = 'PROCESSING'
        AND del_flag = 0
    </select>
    
    <!-- 获取下一个待处理任务 -->
    <select id="findNextPendingTask" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_image_task_queue
        WHERE task_status = 'PENDING'
        AND del_flag = 0
        ORDER BY global_queue_position ASC
        LIMIT 1
    </select>
    
    <!-- 根据会话ID和任务状态查询任务列表 -->
    <select id="findBySessionIdAndTaskStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_image_task_queue
        <where>
            <if test="sessionId != null and sessionId != ''">
                AND session_id = #{sessionId}
            </if>
            <if test="taskStatus != null and taskStatus != ''">
                AND task_status = #{taskStatus}
            </if>
            AND deleted = 0
        </where>
        ORDER BY session_queue_position ASC
    </select>
    
    <!-- 查询所有任务的最大全局队列位置 -->
    <select id="findMaxGlobalQueuePosition" resultType="java.lang.Integer">
        SELECT MAX(global_queue_position)
        FROM ai_image_task_queue
        WHERE del_flag = 0
    </select>
    
    <!-- 查询指定会话的最大会话队列位置 -->
    <select id="findMaxSessionQueuePosition" resultType="java.lang.Integer">
        SELECT MAX(session_queue_position)
        FROM ai_image_task_queue
        WHERE session_id = #{sessionId}
        AND del_flag = 0
    </select>
</mapper> 