<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiUserActivityMapper">

    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiUserActivityPo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="activity_date" property="activityDate"/>
        <result column="last_active_time" property="lastActiveTime"/>
        <result column="request_count" property="requestCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, user_id, activity_date, last_active_time, request_count, create_time, update_time
    </sql>

    <!-- 统计指定日期的活跃用户数 -->
    <select id="countActiveUsersByDate" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT user_id)
        FROM ai_user_activity
        WHERE activity_date = #{activityDate}
    </select>

    <!-- 统计指定日期范围内的每日活跃用户数 -->
    <select id="countDailyActiveUsers" resultMap="BaseResultMap">
        SELECT activity_date, COUNT(DISTINCT user_id) as request_count
        FROM ai_user_activity
        WHERE activity_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY activity_date
        ORDER BY activity_date
    </select>

</mapper> 