<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiSessionImageMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiSessionImagePo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
            <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,session_id,image_url,sort_order,
        create_time,update_time,del_flag
    </sql>

    <!-- 批量插入会话图片关联记录 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ai_session_image (session_id, image_url, sort_order, create_time, update_time, del_flag)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.sessionId}, #{item.imageUrl}, #{item.sortOrder}, NOW(), NOW(), 0)
        </foreach>
    </insert>

</mapper>
