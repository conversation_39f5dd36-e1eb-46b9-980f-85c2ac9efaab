<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiTrackAudioMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiTrackAudioPo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="trackId" column="track_id" jdbcType="BIGINT"/>
        <result property="audioName" column="audio_name" jdbcType="VARCHAR"/>
        <result property="audioUrl" column="audio_url" jdbcType="VARCHAR"/>
        <result property="startPlayTime" column="start_play_time" jdbcType="BIGINT"/>
        <result property="endPlayTime" column="end_play_time" jdbcType="BIGINT"/>
        <result property="volume" column="volume" jdbcType="INTEGER"/>
        <result property="duration" column="duration" jdbcType="BIGINT"/>
        <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
        <result property="audioFormat" column="audio_format" jdbcType="VARCHAR"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
        <result property="muted" column="muted" jdbcType="TINYINT"/>
        <result property="fadeInTime" column="fade_in_time" jdbcType="BIGINT"/>
        <result property="fadeOutTime" column="fade_out_time" jdbcType="BIGINT"/>
        <result property="audioSource" column="audio_source" jdbcType="TINYINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, track_id, audio_name, audio_url, start_play_time, end_play_time, volume,
        duration, file_size, audio_format, sort_order, muted, fade_in_time, fade_out_time,
        audio_source, description, create_time, update_time, del_flag
    </sql>

    <!-- 根据音轨ID查询音频列表 -->
    <select id="selectByTrackId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_track_audio
        WHERE track_id = #{trackId}
        AND del_flag = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 根据音轨ID删除音频 -->
    <update id="deleteByTrackId">
        UPDATE ai_track_audio
        SET del_flag = 1, update_time = NOW()
        WHERE track_id = #{trackId}
        AND del_flag = 0
    </update>

    <!-- 根据音轨ID列表批量删除音频 -->
    <update id="deleteByTrackIds">
        UPDATE ai_track_audio
        SET del_flag = 1, update_time = NOW()
        WHERE track_id IN
        <foreach collection="trackIds" item="trackId" open="(" separator="," close=")">
            #{trackId}
        </foreach>
        AND del_flag = 0
    </update>

    <!-- 获取音轨下音频的最大排序号 -->
    <select id="getMaxSortOrder" resultType="java.lang.Integer">
        SELECT MAX(sort_order)
        FROM ai_track_audio
        WHERE track_id = #{trackId}
        AND del_flag = 0
    </select>

</mapper>
