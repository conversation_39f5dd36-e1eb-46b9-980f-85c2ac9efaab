<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasMaterialMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasMaterialPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
            <result property="materialType" column="material_type" jdbcType="TINYINT"/>
            <result property="materialSource" column="material_source" jdbcType="TINYINT"/>
            <result property="materialUrl" column="material_url" jdbcType="VARCHAR"/>
            <result property="materialName" column="material_name" jdbcType="VARCHAR"/>
            <result property="materialDesc" column="material_desc" jdbcType="LONGVARCHAR"/>
            <result property="generationRecordId" column="generation_record_id" jdbcType="BIGINT"/>
            <result property="firstFrameUrl" column="first_frame_url" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,canvas_id,material_type,
        material_source,material_url,material_name,
        material_desc,generation_record_id,first_frame_url,
        create_time,update_time,del_flag
    </sql>

</mapper>
