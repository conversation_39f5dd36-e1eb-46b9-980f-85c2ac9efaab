<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiAudioMergeTaskMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiAudioMergeTaskPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
            <result property="shotId" column="shot_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="audioUrls" column="audio_urls" jdbcType="LONGVARCHAR"/>
            <result property="audioCount" column="audio_count" jdbcType="INTEGER"/>
            <result property="mergedAudioUrl" column="merged_audio_url" jdbcType="VARCHAR"/>
            <result property="totalDurationMs" column="total_duration_ms" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="errorMessage" column="error_message" jdbcType="LONGVARCHAR"/>
            <result property="processingStartTime" column="processing_start_time" jdbcType="TIMESTAMP"/>
            <result property="processingEndTime" column="processing_end_time" jdbcType="TIMESTAMP"/>
            <result property="processingTimeMs" column="processing_time_ms" jdbcType="BIGINT"/>
            <result property="tempWorkDir" column="temp_work_dir" jdbcType="VARCHAR"/>
            <result property="requestParams" column="request_params" jdbcType="LONGVARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_id,shot_id,user_id,audio_urls,
        audio_count,merged_audio_url,total_duration_ms,status,error_message,
        processing_start_time,processing_end_time,processing_time_ms,temp_work_dir,request_params,
        create_time,update_time,del_flag
    </sql>

    <!-- 查询正在处理中的任务数量 -->
    <select id="countProcessingTasks" resultType="int">
        SELECT COUNT(1)
        FROM ai_audio_merge_task
        WHERE status = 'PROCESSING'
          AND del_flag = 0
    </select>

    <!-- 查询用户的音频合成任务列表 -->
    <select id="selectUserTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_audio_merge_task
        WHERE user_id = #{userId}
          AND del_flag = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询超时的处理中任务 -->
    <select id="selectTimeoutProcessingTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_audio_merge_task
        WHERE status = 'PROCESSING'
          AND del_flag = 0
          AND processing_start_time IS NOT NULL
          AND processing_start_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
    </select>
</mapper>
