<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiSmsRecordMapper">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, phone_number, content, template_code, template_param, request_id, biz_id, status, 
        error_code, error_message, send_time, user_id, business_type, create_time, update_time, del_flag
    </sql>
</mapper> 