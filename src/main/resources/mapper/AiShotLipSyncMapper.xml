<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiShotLipSyncMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiShotLipSyncPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="shotId" column="shot_id" jdbcType="BIGINT"/>
            <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
            <result property="clientId" column="client_id" jdbcType="VARCHAR"/>
            <result property="audioUrl" column="audio_url" jdbcType="VARCHAR"/>
            <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="resultVideoUrl" column="result_video_url" jdbcType="VARCHAR"/>
            <result property="errorMessage" column="error_message" jdbcType="LONGVARCHAR"/>
            <result property="taskCostTime" column="task_cost_time" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shot_id,task_id,
        client_id,audio_url,image_url,
        status,result_video_url,error_message,
        task_cost_time,user_id,create_time,
        update_time,del_flag
    </sql>
</mapper>
