<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiTaskProgressRecordMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiTaskProgressRecordPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
            <result property="progressData" column="progress_data" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,session_id,progress_data,
        create_time,update_time,del_flag
    </sql>
</mapper>
