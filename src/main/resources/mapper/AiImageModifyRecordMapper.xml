<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiImageModifyRecordMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiImageModifyRecordPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
            <result property="contentType" column="content_type" jdbcType="INTEGER"/>
            <result property="primaryId" column="primary_id" jdbcType="VARCHAR"/>
            <result property="secondaryId" column="secondary_id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="sourceImageUrl" column="source_image_url" jdbcType="VARCHAR"/>
            <result property="modifiedImageUrl" column="modified_image_url" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isCurrentUsed" column="is_current_used" jdbcType="TINYINT"/>
            <result property="failureReason" column="failure_reason" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,session_id,content_type,
        primary_id,secondary_id,user_id,
        source_image_url,modified_image_url,status,
        is_current_used,failure_reason,create_time,update_time,
        del_flag
    </sql>

   <!-- 根据会话ID、一级ID和二级ID查询源图片URL和修改编码列表 -->
   <select id="selectSourceImageUrlInfosBySessionAndIds" resultType="com.wlink.agent.model.res.ModifyImageUrlInfoRes">
       SELECT
           modified_image_url AS modifiedImageUrl,
           modify_code AS modifyCode
       FROM
           ai_image_modify_record
       WHERE
           session_id = #{sessionId}
         AND primary_id = #{primaryId}
         <if test="secondaryId != null and secondaryId != ''">
             AND secondary_id = #{secondaryId}
         </if>
         AND del_flag = 0
         AND status = 1  -- 只查询成功的记录
       ORDER BY
           create_time DESC -- 或者根据需要排序
   </select>
</mapper>
