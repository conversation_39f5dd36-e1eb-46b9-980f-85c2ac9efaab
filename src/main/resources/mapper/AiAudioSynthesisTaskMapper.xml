<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiAudioSynthesisTaskMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiAudioSynthesisTaskPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
            <result property="shotId" column="shot_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="audioUrls" column="audio_urls" jdbcType="LONGVARCHAR"/>
            <result property="audioCount" column="audio_count" jdbcType="INTEGER"/>
            <result property="webappId" column="webapp_id" jdbcType="VARCHAR"/>
            <result property="apiKey" column="api_key" jdbcType="VARCHAR"/>
            <result property="clientId" column="client_id" jdbcType="VARCHAR"/>
            <result property="netWssUrl" column="net_wss_url" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="resultAudioUrl" column="result_audio_url" jdbcType="VARCHAR"/>
            <result property="errorMessage" column="error_message" jdbcType="LONGVARCHAR"/>
            <result property="taskCostTime" column="task_cost_time" jdbcType="BIGINT"/>
            <result property="processingStartTime" column="processing_start_time" jdbcType="TIMESTAMP"/>
            <result property="processingEndTime" column="processing_end_time" jdbcType="TIMESTAMP"/>
            <result property="requestParams" column="request_params" jdbcType="LONGVARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_id,shot_id,
        user_id,audio_urls,audio_count,
        webapp_id,api_key,client_id,
        net_wss_url,status,result_audio_url,
        error_message,task_cost_time,processing_start_time,
        processing_end_time,request_params,create_time,
        update_time,del_flag
    </sql>
</mapper>
