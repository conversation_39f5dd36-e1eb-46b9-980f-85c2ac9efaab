<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiSoundEffectsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiSoundEffectsRecordPo">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="session_id" property="sessionId" />
        <result column="content_id" property="contentId" />
        <result column="index" property="audioIndex" />
        <result column="source" property="source" />
        <result column="request_id" property="requestId" />
        <result column="prompt" property="prompt" />
        <result column="duration" property="duration" />
        <result column="original_audio_url" property="originalAudioUrl" />
        <result column="oss_audio_url" property="ossAudioUrl" />
        <result column="file_name" property="fileName" />
        <result column="file_size" property="fileSize" />
        <result column="content_type" property="contentType" />
        <result column="generation_status" property="generationStatus" />
        <result column="error_code" property="errorCode" />
        <result column="error_message" property="errorMessage" />
        <result column="generation_time" property="generationTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, session_id, content_id, `index`, source, request_id, prompt, duration, original_audio_url, oss_audio_url,
        file_name, file_size, content_type, generation_status, error_code, error_message,
        generation_time, create_time, update_time
    </sql>

</mapper>