<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
            <result property="canvasName" column="canvas_name" jdbcType="VARCHAR"/>
            <result property="canvasDesc" column="canvas_desc" jdbcType="VARCHAR"/>
            <result property="coverImage" column="cover_image" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,user_id,
        session_id,canvas_name,canvas_desc,
        cover_image,status,sort_order,
        create_time,update_time,del_flag
    </sql>
</mapper>
