<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasBackgroundMusicMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasBackgroundMusicPo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
        <result property="audioUrl" column="audio_url" jdbcType="VARCHAR"/>
        <result property="audioName" column="audio_name" jdbcType="VARCHAR"/>
        <result property="audioDuration" column="audio_duration" jdbcType="BIGINT"/>
        <result property="startPlayTime" column="start_play_time" jdbcType="BIGINT"/>
        <result property="endPlayTime" column="end_play_time" jdbcType="BIGINT"/>
        <result property="startTrackTime" column="start_track_time" jdbcType="BIGINT"/>
        <result property="volume" column="volume" jdbcType="DOUBLE"/>
        <result property="fadeInTime" column="fade_in_time" jdbcType="BIGINT"/>
        <result property="fadeOutTime" column="fade_out_time" jdbcType="BIGINT"/>
        <result property="isLoop" column="is_loop" jdbcType="TINYINT"/>
        <result property="audioFormat" column="audio_format" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
        <result property="audioSource" column="audio_source" jdbcType="TINYINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, canvas_id, audio_url, audio_name, audio_duration, start_play_time, end_play_time,
        start_track_time, volume, fade_in_time, fade_out_time, is_loop, audio_format,
        file_size, audio_source, description, create_time, update_time, del_flag
    </sql>

    <!-- 根据画布ID查询背景音乐 -->
    <select id="selectByCanvasId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_canvas_background_music
        WHERE canvas_id = #{canvasId}
        AND del_flag = 0
    </select>

    <!-- 根据画布ID删除背景音乐 -->
    <update id="deleteByCanvasId">
        UPDATE ai_canvas_background_music
        SET del_flag = 1, update_time = NOW()
        WHERE canvas_id = #{canvasId}
        AND del_flag = 0
    </update>

</mapper>
