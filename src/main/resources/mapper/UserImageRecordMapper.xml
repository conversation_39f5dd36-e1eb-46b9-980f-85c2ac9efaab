<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.UserImageRecordMapper">

    <!-- 查询用户图片记录列表 -->
    <select id="findUserImageRecords" resultType="com.wlink.agent.dao.dto.UserImageRecordDTO">
        SELECT 
            t.id,
            t.session_id AS sessionId,
            t.user_id AS userId,
            t.task_type AS taskType,
            t.content_type AS contentType,
            t.content_id AS contentId,
            t.request_params AS requestParams,
            t.image_result AS imageUrl,
            t.create_time AS createTime,
            f.code AS favoriteCode,
            f.create_time AS favoriteTime,
            <if test="type == 2">
                1 AS isFavorite
            </if>
            <if test="type != 2">
                CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END AS isFavorite
            </if>
        FROM 
            ai_image_task_queue t
        <if test="type == 2">
            INNER JOIN ai_resource_favorite f ON t.id = f.resource_id AND f.user_id = #{userId} AND f.del_flag = 0
        </if>
        <if test="type != 2">
            LEFT JOIN ai_resource_favorite f ON t.id = f.resource_id AND f.user_id = #{userId} AND f.del_flag = 0
        </if>
        WHERE
            t.task_status = 'COMPLETED'
            AND t.user_id = #{userId}
            AND t.del_flag = 0
            <if test="sessionId != null and sessionId != ''">
                AND t.session_id = #{sessionId}
            </if>
            <if test="type == 3">
                AND t.content_type = 3 <!-- 角色 -->
            </if>
            <if test="type == 4">
                AND t.content_type = 4 <!-- 分镜 -->
            </if>
        ORDER BY 
            t.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 统计用户图片记录总数 -->
    <select id="countUserImageRecords" resultType="java.lang.Long">
        SELECT 
            COUNT(t.id)
        FROM 
            ai_image_task_queue t
        <if test="type == 2">
            INNER JOIN ai_resource_favorite f ON t.id = f.resource_id AND f.user_id = #{userId} AND f.del_flag = 0
        </if>
        WHERE
            t.task_status = 'COMPLETED'
            AND t.user_id = #{userId}
            AND t.del_flag = 0
            <if test="sessionId != null and sessionId != ''">
                AND t.session_id = #{sessionId}
            </if>
            <if test="type == 3">
                AND t.content_type = 3 <!-- 角色 -->
            </if>
            <if test="type == 4">
                AND t.content_type = 4 <!-- 分镜 -->
            </if>
    </select>
</mapper> 