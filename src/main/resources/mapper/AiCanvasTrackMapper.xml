<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasTrackMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasTrackPo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
        <result property="trackName" column="track_name" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="BIGINT"/>
        <result property="endTime" column="end_time" jdbcType="BIGINT"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
        <result property="trackType" column="track_type" jdbcType="TINYINT"/>
        <result property="volume" column="volume" jdbcType="INTEGER"/>
        <result property="muted" column="muted" jdbcType="TINYINT"/>
        <result property="locked" column="locked" jdbcType="TINYINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, canvas_id, track_name, start_time, end_time, sort_order, track_type, volume,
        muted, locked, description, create_time, update_time, del_flag
    </sql>

    <!-- 根据画布ID查询音轨列表 -->
    <select id="selectByCanvasId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_canvas_track
        WHERE canvas_id = #{canvasId}
        AND del_flag = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 根据画布ID删除音轨 -->
    <update id="deleteByCanvasId">
        UPDATE ai_canvas_track
        SET del_flag = 1, update_time = NOW()
        WHERE canvas_id = #{canvasId}
        AND del_flag = 0
    </update>

    <!-- 获取画布下音轨的最大排序号 -->
    <select id="getMaxSortOrder" resultType="java.lang.Integer">
        SELECT MAX(sort_order)
        FROM ai_canvas_track
        WHERE canvas_id = #{canvasId}
        AND del_flag = 0
    </select>

</mapper>
