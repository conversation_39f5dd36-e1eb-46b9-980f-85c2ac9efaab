<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasVideoMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasVideoPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
            <result property="shotCode" column="shot_code" jdbcType="VARCHAR"/>
            <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
            <result property="videoPrompt" column="video_prompt" jdbcType="VARCHAR"/>
            <result property="videoDesc" column="video_desc" jdbcType="VARCHAR"/>
            <result property="videoDuration" column="video_duration" jdbcType="INTEGER"/>
            <result property="videoAspectRatio" column="video_aspect_ratio" jdbcType="VARCHAR"/>
            <result property="videoStatus" column="video_status" jdbcType="VARCHAR"/>
            <result property="startFrameImage" column="start_frame_image" jdbcType="VARCHAR"/>
            <result property="endFrameImage" column="end_frame_image" jdbcType="VARCHAR"/>
            <result property="volume" column="volume" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,canvas_id,shot_code,
        video_url,video_prompt,video_desc,
        video_duration,video_aspect_ratio,video_status,
        start_frame_image,end_frame_image,volume,
        create_time,update_time,del_flag
    </sql>
</mapper>
