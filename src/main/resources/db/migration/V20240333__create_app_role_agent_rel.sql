CREATE TABLE `agent_app_role_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(32) NOT NULL COMMENT '房间ID',
  `app_tag` varchar(64) NOT NULL COMMENT '应用标识',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `agent_id` bigint(20) NOT NULL COMMENT '智能体ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_app_tag` (`app_tag`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用角色-智能体关联表'; 