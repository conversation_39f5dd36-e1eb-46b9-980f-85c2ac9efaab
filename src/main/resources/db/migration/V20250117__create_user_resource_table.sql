-- 创建用户资源表
CREATE TABLE IF NOT EXISTS `ai_user_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(64) NOT NULL COMMENT '资源唯一编码',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `resource_type` tinyint(1) NOT NULL COMMENT '资源类型(1-图片,2-视频,3-音频)',
  `generation_status` varchar(20) NOT NULL COMMENT '生成状态(PENDING-生成中,SUCCESS-成功,FAILED-失败)',
  `resource_urls` text COMMENT '资源URL集合(JSON格式)',
  `resource_size` bigint(20) DEFAULT NULL COMMENT '资源大小(字节)',
  `width` int(11) DEFAULT NULL COMMENT '图片/视频宽度',
  `height` int(11) DEFAULT NULL COMMENT '图片/视频高度',
  `duration` int(11) DEFAULT NULL COMMENT '视频/音频时长(秒)',
  `model_type` varchar(50) DEFAULT NULL COMMENT '使用的模型类型',
  `prompt` text COMMENT '生成提示词',
  `reference_images` text COMMENT '参考图片URL集合(JSON格式)',
  `generation_params` text COMMENT '生成参数(JSON格式)',
  `error_message` text COMMENT '错误信息',
  `external_request_id` varchar(100) DEFAULT NULL COMMENT '外部API请求ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_generation_status` (`generation_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资源表';
