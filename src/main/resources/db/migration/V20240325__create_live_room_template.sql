CREATE TABLE `live_room_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(64) NOT NULL COMMENT '模版名称',
  `description` varchar(255) DEFAULT NULL COMMENT '模版描述',
  `live_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '直播类型(1-单人直播，2-多人直播)',
  `dify_key` varchar(128) NOT NULL COMMENT 'dify应用key',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-禁用，1-启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `del_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常，1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间模版表'; 