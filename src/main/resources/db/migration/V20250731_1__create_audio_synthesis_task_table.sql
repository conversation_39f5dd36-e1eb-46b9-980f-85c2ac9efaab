-- 创建音频合成任务记录表
CREATE TABLE IF NOT EXISTS `ai_audio_synthesis_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID（ComfyUI返回的taskId）',
  `shot_id` bigint(20) NOT NULL COMMENT '分镜ID',
  `user_id` varchar(100) NOT NULL COMMENT '用户ID',
  `audio_urls` text NOT NULL COMMENT '音频URL集合（JSON格式）',
  `audio_count` int(11) NOT NULL DEFAULT 0 COMMENT '音频文件数量',
  `webapp_id` varchar(100) NOT NULL DEFAULT '1950797978980253697' COMMENT 'ComfyUI WebApp ID',
  `api_key` varchar(100) NOT NULL DEFAULT '264fec3cd17144c59ec690b37a016972' COMMENT 'ComfyUI API Key',
  `client_id` varchar(100) DEFAULT NULL COMMENT '客户端ID（ComfyUI返回的clientId）',
  `net_wss_url` varchar(500) DEFAULT NULL COMMENT 'WebSocket连接URL',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING-等待中, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败, CANCELLED-已取消',
  `result_audio_url` varchar(500) DEFAULT NULL COMMENT '合成结果音频地址',
  `error_message` text COMMENT '错误信息',
  `task_cost_time` bigint(20) DEFAULT NULL COMMENT '任务耗时（毫秒）',
  `processing_start_time` datetime DEFAULT NULL COMMENT '处理开始时间',
  `processing_end_time` datetime DEFAULT NULL COMMENT '处理结束时间',
  `request_params` text COMMENT '请求参数（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_shot_id` (`shot_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频合成任务记录表';
