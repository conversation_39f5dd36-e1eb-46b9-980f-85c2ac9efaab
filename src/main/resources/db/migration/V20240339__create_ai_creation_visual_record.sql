-- 创建AI创作视觉记录表
CREATE TABLE ai_creation_visual_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    content_data LONGTEXT NOT NULL COMMENT '视觉数据(JSON格式)',
    publish_status TINYINT NOT NULL DEFAULT 0 COMMENT '发布状态(0-未发布,1-已发布)',
    share_token VARCHAR(64) COMMENT '分享令牌',
    title VARCHAR(128) COMMENT '标题',
    cover_image VARCHAR(512) COMMENT '封面图片URL',
    subtitle VARCHAR(256) COMMENT '副标题/描述',
    publish_time DATETIME COMMENT '发布时间',
    user_id VARCHAR(64) NOT NULL COMMENT '创建用户ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    del_flag TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
    INDEX idx_session_id (session_id),
    INDEX idx_share_token (share_token),
    INDEX idx_publish_status (publish_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI创作视觉记录表'; 