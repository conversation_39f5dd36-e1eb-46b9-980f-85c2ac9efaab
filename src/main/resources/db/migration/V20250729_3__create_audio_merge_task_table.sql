-- 创建音频合成任务记录表
CREATE TABLE IF NOT EXISTS `ai_audio_merge_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID（UUID）',
  `shot_id` bigint(20) DEFAULT NULL COMMENT '分镜ID',
  `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `audio_urls` text NOT NULL COMMENT '原始音频URL列表（JSON格式）',
  `audio_count` int(11) NOT NULL DEFAULT 0 COMMENT '音频文件数量',
  `merged_audio_url` varchar(500) DEFAULT NULL COMMENT '合成后的音频地址',
  `total_duration_ms` bigint(20) DEFAULT NULL COMMENT '合成后音频总时长（毫秒）',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING-等待中, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败, CANCELLED-已取消',
  `error_message` text COMMENT '错误信息',
  `processing_start_time` datetime DEFAULT NULL COMMENT '处理开始时间',
  `processing_end_time` datetime DEFAULT NULL COMMENT '处理结束时间',
  `processing_time_ms` bigint(20) DEFAULT NULL COMMENT '处理耗时（毫秒）',
  `temp_work_dir` varchar(500) DEFAULT NULL COMMENT '临时工作目录',
  `request_params` text COMMENT '请求参数（JSON格式）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_shot_id` (`shot_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频合成任务记录表';
