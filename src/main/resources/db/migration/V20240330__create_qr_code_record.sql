CREATE TABLE `agent_qr_code_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qr_code_id` varchar(64) NOT NULL COMMENT '二维码ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态(0-等待扫码,1-已扫码,2-已确认,3-已过期,4-已取消)',
  `token` varchar(64) DEFAULT NULL COMMENT '登录token',
  `user_id` varchar(64) DEFAULT NULL COMMENT '扫码用户ID',
  `scan_time` datetime DEFAULT NULL COMMENT '扫码时间',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_qr_code_id` (`qr_code_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码记录表'; 