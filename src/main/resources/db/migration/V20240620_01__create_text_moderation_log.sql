-- 创建文本审核调用记录表
CREATE TABLE `ai_text_moderation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `content` text COMMENT '文本内容',
  `service_type` varchar(64) DEFAULT NULL COMMENT '检测服务类型(chat_detection等)',
  `suggestion` varchar(32) DEFAULT NULL COMMENT '审核建议(pass/review/block)',
  `labels` varchar(255) DEFAULT NULL COMMENT '审核标签（JSON数组）',
  `reason` varchar(255) DEFAULT NULL COMMENT '审核理由',
  `risk_score` double DEFAULT NULL COMMENT '风险分数',
  `request_id` varchar(64) DEFAULT NULL COMMENT '阿里云请求ID',
  `response_time` bigint(20) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `is_success` tinyint(1) DEFAULT NULL COMMENT '是否成功(0:失败,1:成功)',
  `error_code` varchar(64) DEFAULT NULL COMMENT '错误码',
  `error_message` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `details` text COMMENT '详细结果JSON',
  `data_id` varchar(64) DEFAULT NULL COMMENT '数据ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文本审核调用记录表'; 