CREATE TABLE `agent_rundown_rel` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rundown_id` bigint(20) NOT NULL COMMENT '节目单ID',
    `agent_id` bigint(20) NOT NULL COMMENT '智能体ID',
    `costume_code` varchar(64) DEFAULT NULL COMMENT '服装编码',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标识（0-未删除，1-已删除）',
    PRIMARY KEY (`id`),
    KEY `idx_rundown_id` (`rundown_id`),
    KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节目单-智能体关联表'; 