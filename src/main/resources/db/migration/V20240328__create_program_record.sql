CREATE TABLE `agent_program_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(32) NOT NULL COMMENT '房间ID',
  `requirement` text COMMENT '直播需求',
  `program_content` text COMMENT '生成的节目单内容',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态(0-生成中,1-成功,2-失败)',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节目单生成记录表'; 