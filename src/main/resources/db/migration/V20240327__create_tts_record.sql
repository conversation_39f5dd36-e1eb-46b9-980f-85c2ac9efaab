CREATE TABLE `agent_tts_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `text` text NOT NULL COMMENT '待转换文本',
  `voice_id` varchar(64) NOT NULL COMMENT '声音ID',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频URL',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态(0-转换中,1-成功,2-失败)',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `duration` int(11) DEFAULT NULL COMMENT '音频时长(毫秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='TTS调用记录表'; 