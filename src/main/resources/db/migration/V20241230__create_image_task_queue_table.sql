-- 创建图片任务队列表
CREATE TABLE IF NOT EXISTS `ai_image_task_queue` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `task_type` varchar(32) NOT NULL COMMENT '任务类型：GENERATE-文生图，RETAIN-人像IP保留',
  `content_id` varchar(64) DEFAULT NULL COMMENT '内容ID',
  `request_params` text NOT NULL COMMENT '请求参数（JSON格式）',
  `task_status` varchar(20) NOT NULL COMMENT '任务状态：PENDING-待处理，PROCESSING-处理中，COMPLETED-已完成，FAILED-失败',
  `result` text DEFAULT NULL COMMENT '处理结果（JSON格式）',
  `error_reason` text DEFAULT NULL COMMENT '错误原因',
  `session_queue_position` int(11) DEFAULT NULL COMMENT '会话内队列位置',
  `global_queue_position` int(11) DEFAULT NULL COMMENT '全局队列位置',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_global_position` (`global_queue_position`),
  KEY `idx_session_position` (`session_queue_position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片任务队列表'; 