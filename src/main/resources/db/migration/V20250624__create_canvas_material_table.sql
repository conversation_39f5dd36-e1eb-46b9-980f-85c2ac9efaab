-- 创建画布素材表
CREATE TABLE IF NOT EXISTS `ai_canvas_material` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `canvas_id` bigint(20) NOT NULL COMMENT '画布ID',
  `material_type` tinyint(1) NOT NULL COMMENT '素材类型(1-图片,2-视频)',
  `material_source` tinyint(1) NOT NULL COMMENT '素材来源(1-生成,2-上传)',
  `material_url` varchar(500) DEFAULT NULL COMMENT '素材URL',
  `material_name` varchar(255) DEFAULT NULL COMMENT '素材名称',
  `material_desc` text COMMENT '素材描述',
  `generation_record_id` bigint(20) DEFAULT NULL COMMENT '生成记录ID(来源为生成时有值)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_canvas_id` (`canvas_id`),
  KEY `idx_material_type` (`material_type`),
  KEY `idx_material_source` (`material_source`),
  KEY `idx_generation_record_id` (`generation_record_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='画布素材表';




ALTER TABLE `wl_avatar`.`ai_tts_record`
    ADD COLUMN `source` tinyint(4) NOT NULL DEFAULT 1 COMMENT '来源 1-会话   2-素材' AFTER `conversation_id`;
