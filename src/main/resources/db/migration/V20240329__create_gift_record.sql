CREATE TABLE `agent_live_gift_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `live_record_id` bigint(20) NOT NULL COMMENT '直播记录ID',
  `room_id` varchar(32) NOT NULL COMMENT '房间ID',
  `user_name` varchar(64) NOT NULL COMMENT '用户名称',
  `gift_name` varchar(64) NOT NULL COMMENT '礼物名称',
  `gift_count` int(11) NOT NULL COMMENT '礼物数量',
  `gift_price` decimal(10,2) NOT NULL COMMENT '礼物单价',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_live_record_id` (`live_record_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播礼物记录表'; 