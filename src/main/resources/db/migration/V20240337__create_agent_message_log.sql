CREATE TABLE `agent_message_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `room_id` varchar(64) DEFAULT NULL COMMENT '房间ID',
    `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
    `message_id` varchar(64) DEFAULT NULL COMMENT '消息ID',
    `message_type` varchar(32) NOT NULL COMMENT '消息类型',
    `direction` tinyint(4) NOT NULL COMMENT '消息方向(1-接收,2-发送)',
    `content` text COMMENT '消息内容(Base64)',
    `content_json` text COMMENT '消息内容(JSON)',
    `error_msg` varchar(512) DEFAULT NULL COMMENT '错误信息',
    `status` tinyint(4) DEFAULT '1' COMMENT '状态(1-成功,0-失败)',
    `cost_time` int(11) DEFAULT '0' COMMENT '耗时(毫秒)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_message_id` (`message_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息日志表'; 