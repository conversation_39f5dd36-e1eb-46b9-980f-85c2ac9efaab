-- 创建FalAi Flux API请求记录表
CREATE TABLE IF NOT EXISTS `ai_fal_flux_request_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_type` varchar(20) NOT NULL COMMENT '请求类型：TEXT_TO_IMAGE, IMAGE_TO_IMAGE',
  `prompt` text COMMENT '请求提示词',
  `image_url` varchar(1024) DEFAULT NULL COMMENT '图像URL（对于图像到图像请求）',
  `request_id` varchar(64) DEFAULT NULL COMMENT 'Fal.ai请求ID',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '请求状态：PENDING, IN_QUEUE, IN_PROGRESS, COMPLETED, FAILED, CANCELLED',
  `queue_position` int(11) DEFAULT NULL COMMENT '队列位置（如果在队列中）',
  `result_image_urls` text COMMENT '生成的图像URLs，JSON格式',
  `request_params` text COMMENT '请求参数，JSON格式',
  `response_data` text COMMENT '响应数据，JSON格式',
  `start_time` datetime DEFAULT NULL COMMENT '处理开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '处理结束时间',
  `processing_time` bigint(20) DEFAULT NULL COMMENT '处理时间（毫秒）',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `error_message` text COMMENT '错误消息，如果有的话',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID，如果适用',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务关联ID，可用于关联到特定业务实体',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='FalAi Flux API请求记录表'; 