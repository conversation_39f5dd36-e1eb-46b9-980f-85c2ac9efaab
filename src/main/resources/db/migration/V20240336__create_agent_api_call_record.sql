CREATE TABLE `agent_api_call_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `api_name` varchar(64) NOT NULL COMMENT 'API名称',
    `api_path` varchar(128) NOT NULL COMMENT 'API路径',
    `request_params` text COMMENT '请求参数',
    `response_data` text COMMENT '响应数据',
    `status` tinyint(4) DEFAULT '1' COMMENT '状态(1-成功,0-失败)',
    `error_msg` varchar(512) DEFAULT NULL COMMENT '错误信息',
    `cost_time` int(11) DEFAULT '0' COMMENT '耗时(毫秒)',
    `ip` varchar(64) DEFAULT NULL COMMENT '调用方IP',
    `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_api_name` (`api_name`),
    KEY `idx_api_path` (`api_path`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API调用记录表'; 