ALTER TABLE `ai_canvas_material`
    ADD COLUMN `material_duration` bigint(16) NULL COMMENT '素材时长' AFTER `material_name`;


CREATE TABLE `ai_shot_image_edit` (
                                      `id` bigint NOT NULL COMMENT '主键ID',
                                      `shot_id` bigint NOT NULL COMMENT '分镜ID',
                                      `task_id` varchar(100) NOT NULL COMMENT '任务ID',
                                      `client_id` varchar(100) DEFAULT NULL COMMENT '客户端ID',
                                      `net_wss_url` text COMMENT 'WebSocket连接URL',
                                      `original_image_url` varchar(500) NOT NULL COMMENT '原图URL',
                                      `mask_image_url` varchar(500) NOT NULL COMMENT '遮罩图URL',
                                      `prompt` text NOT NULL COMMENT '编辑提示词',
                                      `status` varchar(20) NOT NULL DEFAULT 'RUNNING' COMMENT '任务状态',
                                      `result_image_url` varchar(500) DEFAULT NULL COMMENT '结果图片URL',
                                      `error_message` text COMMENT '错误信息',
                                      `task_cost_time` bigint DEFAULT NULL COMMENT '任务耗时（毫秒）',
                                      `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_shot_id` (`shot_id`),
                                      KEY `idx_task_id` (`task_id`),
                                      KEY `idx_user_id` (`user_id`),
                                      KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜图片编辑记录表';