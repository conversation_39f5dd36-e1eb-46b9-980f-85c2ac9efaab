-- 创建章节表
CREATE TABLE IF NOT EXISTS `ai_chapter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `segment_id` varchar(64) NOT NULL COMMENT '章节ID',
  `segment_name` varchar(255) DEFAULT NULL COMMENT '章节名称',
  `scene_count` int(11) DEFAULT 0 COMMENT '场景数量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_session_segment` (`session_id`, `segment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='章节表';

-- 创建分镜表
CREATE TABLE IF NOT EXISTS `ai_shot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `segment_id` varchar(64) NOT NULL COMMENT '章节ID',
  `scene_id` varchar(64) NOT NULL COMMENT '场景ID',
  `scene_name` varchar(255) DEFAULT NULL COMMENT '场景名称',
  `shot_id` varchar(64) NOT NULL COMMENT '分镜ID',
  `shot_data` text COMMENT '分镜JSON数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_session_shot` (`session_id`, `shot_id`),
  KEY `idx_session_segment_scene` (`session_id`, `segment_id`, `scene_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜表'; 