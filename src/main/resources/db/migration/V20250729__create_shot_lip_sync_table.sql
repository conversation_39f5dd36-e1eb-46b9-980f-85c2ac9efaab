-- 创建分镜对口型记录表
CREATE TABLE IF NOT EXISTS `ai_shot_lip_sync` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shot_id` bigint(20) NOT NULL COMMENT '分镜ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID（ComfyUI返回的taskId）',
  `client_id` varchar(100) DEFAULT NULL COMMENT '客户端ID（ComfyUI返回的clientId）',
  `audio_url` varchar(500) NOT NULL COMMENT '音频地址',
  `image_url` varchar(500) NOT NULL COMMENT '图片地址',
  `status` varchar(20) NOT NULL DEFAULT 'RUNNING' COMMENT '任务状态：RUNNING-运行中, COMPLETED-已完成, FAILED-失败',
  `result_video_url` varchar(500) DEFAULT NULL COMMENT '生成的视频地址',
  `error_message` text COMMENT '错误信息',
  `task_cost_time` bigint(20) DEFAULT NULL COMMENT '任务耗时（毫秒）',
  `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_shot_id` (`shot_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分镜对口型记录表';
