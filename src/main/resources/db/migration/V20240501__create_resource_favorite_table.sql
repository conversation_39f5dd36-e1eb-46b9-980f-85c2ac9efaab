-- 创建资源收藏表
CREATE TABLE `ai_resource_favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(64) NOT NULL COMMENT '资源收藏唯一编码',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `resource_id` varchar(64) NOT NULL COMMENT '资源ID',
  `resource_type` tinyint(4) NOT NULL COMMENT '资源类型(1-角色,2-图片,3-视频,4-音频,5-文本)',
  `resource_subtype` tinyint(4) DEFAULT NULL COMMENT '资源子类型(对图片:1-角色图片,2-场景图片,3-分镜图片)',
  `resource_name` varchar(255) DEFAULT NULL COMMENT '资源名称',
  `resource_url` varchar(1024) DEFAULT NULL COMMENT '资源URL',
  `resource_data` text COMMENT '资源数据(JSO<PERSON>格式)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  UNIQUE KEY `idx_user_resource` (`user_id`, `resource_id`, `resource_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_resource_subtype` (`resource_subtype`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源收藏表';

-- 迁移现有角色收藏数据到新表
INSERT INTO ai_resource_favorite (
    code,
    user_id,
    session_id,
    resource_id,
    resource_type,
    resource_data,
    create_time,
    update_time,
    del_flag
)
SELECT 
    code,
    user_id,
    session_id,
    role_id,
    1, -- 资源类型为1(角色)
    role_data,
    create_time,
    update_time,
    del_flag
FROM ai_role_favorite
WHERE del_flag = 0; 