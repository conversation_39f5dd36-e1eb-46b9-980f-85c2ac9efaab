CREATE TABLE `ai_video_render_export`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`          varchar(64) NOT NULL COMMENT '用户ID',
    `canvas_id`        bigint(20) NOT NULL COMMENT '画布ID',
    `resolution`       varchar(32) NOT NULL COMMENT '分辨率',
    `ratio`            varchar(16) NOT NULL COMMENT '比例',
    `show_subtitle`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示字幕(0-不显示,1-显示)',
    `render_task_id` varchar(128) DEFAULT NULL COMMENT '渲染任务ID (Python渲染服务返回的任务ID)',
    `status`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '渲染状态(0-排队中,1-渲染中,2-已完成,3-失败)',
    `video_url`        varchar(512)         DEFAULT NULL COMMENT '渲染后的视频地址',
    `canvas_data_json` longtext COMMENT '画布数据JSON，发送给Python渲染接口',
    `error_message`    varchar(1024)        DEFAULT NULL COMMENT '错误信息',
    `start_time`       datetime             DEFAULT NULL COMMENT '渲染开始时间',
    `complete_time`    datetime             DEFAULT NULL COMMENT '渲染完成时间',
    `share_status`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '分享状态(0-未分享,1-已分享)',
    `share_code`       varchar(32)          DEFAULT NULL COMMENT '分享码',
    `share_time`       datetime             DEFAULT NULL COMMENT '分享时间',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
    PRIMARY KEY (`id`),
    KEY                `idx_user_id` (`user_id`),
    KEY                `idx_create_time` (`create_time`),
    KEY                `idx_user_canvas` (`user_id`, `canvas_id`),
    UNIQUE KEY         `idx_share_code` (`share_code`),
    KEY                `idx_share_status_time` (`share_status`, `share_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频渲染导出记录表';