-- 创建画布背景音乐表
CREATE TABLE IF NOT EXISTS `ai_canvas_background_music` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `canvas_id` bigint(20) NOT NULL COMMENT '画布ID',
  `audio_url` varchar(500) NOT NULL COMMENT '音频地址',
  `audio_name` varchar(255) DEFAULT NULL COMMENT '音频名称',
  `audio_duration` bigint(20) NOT NULL COMMENT '音频时长（毫秒）',
  `start_play_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始播放时间（毫秒）',
  `end_play_time` bigint(20) DEFAULT NULL COMMENT '结束播放时间（毫秒）',
  `start_track_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '音轨开始时间（毫秒）',
  `volume` int(11) NOT NULL DEFAULT '100' COMMENT '音量（0-100）',
  `fade_in_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '淡入时间（毫秒）',
  `fade_out_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '淡出时间（毫秒）',
  `is_loop` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否循环播放（0-否，1-是）',
  `audio_format` varchar(20) DEFAULT NULL COMMENT '音频格式（mp3、wav、ogg等）',
  `file_size` bigint(20) DEFAULT NULL COMMENT '音频文件大小（字节）',
  `audio_source` tinyint(1) NOT NULL DEFAULT '1' COMMENT '音频来源（1-上传，2-AI生成，3-素材库）',
  `description` varchar(1000) DEFAULT NULL COMMENT '音频描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_canvas_id` (`canvas_id`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='画布背景音乐表';

-- 添加索引说明
-- uk_canvas_id: 唯一索引，确保一个画布只能有一个背景音乐
-- idx_del_flag: 用于过滤已删除的记录
-- idx_create_time: 用于按创建时间排序查询
