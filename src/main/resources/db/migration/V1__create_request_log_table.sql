-- 创建请求日志表
CREATE TABLE IF NOT EXISTS `ai_content_request_log` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `conversation_id` VARCHAR(64) DEFAULT NULL COMMENT '会话ID，用于关联请求',
    `request_uri` VARCHAR(255) NOT NULL COMMENT '请求的URI路径',
    `http_method` VARCHAR(10) NOT NULL COMMENT '请求的HTTP方法',
    `method_name` VARCHAR(255) NOT NULL COMMENT '控制器方法名',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '操作描述',
    `request_params` TEXT COMMENT '请求参数，JSON格式',
    `request_body` TEXT COMMENT '请求体，JSON格式',
    `response_body` TEXT COMMENT '响应内容，JSON格式',
    `status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '请求状态，1表示成功，0表示失败',
    `error_message` TEXT COMMENT '错误消息，如果有的话',
    `processing_time` BIGINT(20) DEFAULT NULL COMMENT '请求处理时间（毫秒）',
    `request_time` DATETIME NOT NULL COMMENT '请求时间',
    `response_time` DATETIME NOT NULL COMMENT '响应时间',
    PRIMARY KEY (`id`),
    INDEX `idx_conversation_id` (`conversation_id`),
    INDEX `idx_request_time` (`request_time`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI内容请求日志表'; 