-- 创建语音克隆记录表
CREATE TABLE IF NOT EXISTS `ai_voice_clone_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(100) NOT NULL COMMENT '用户ID',
  `voice_id` varchar(100) NOT NULL COMMENT '声音ID（MiniMax返回的voice_id）',
  `name` varchar(255) NOT NULL COMMENT '声音名称',
  `sex` int(11) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `oss_url` varchar(500) NOT NULL COMMENT '原始音频OSS地址',
  `demo_audio` varchar(500) DEFAULT NULL COMMENT '试听音频OSS地址',
  `input_sensitive` tinyint(1) DEFAULT 0 COMMENT '输入是否敏感：0-否，1-是',
  `input_sensitive_type` int(11) DEFAULT NULL COMMENT '输入敏感类型',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待确认，CONFIRMED-已确认，CANCELLED-已取消',
  `confirmed_time` datetime DEFAULT NULL COMMENT '确认时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_voice_id` (`voice_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='语音克隆记录表';
