-- 1. 删除直播间表中的run_down_id字段
ALTER TABLE agent_live_room_info 
DROP COLUMN run_down_id;

-- 2. 创建直播间-节目单关联表
CREATE TABLE `agent_live_room_rundown_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(32) NOT NULL COMMENT '房间ID',
  `run_down_id` bigint(20) NOT NULL COMMENT '节目单ID',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态(0-禁用 1-启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `del_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_run_down_id` (`run_down_id`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间-节目单关联表'; 