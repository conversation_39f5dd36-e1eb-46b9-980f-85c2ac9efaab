-- 创建视频生成记录表
CREATE TABLE IF NOT EXISTS `ai_video_generation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
   generation_task_id varchar(64) DEFAULT NULL COMMENT '生成任务ID',
  `shot_id` bigint(20) NOT NULL COMMENT '分镜ID',
  `model`  varchar(64) NOT NULL COMMENT '模型名称',
  `prompt` text NOT NULL COMMENT '生成提示词',
  `first_frame_image` varchar(500) DEFAULT NULL COMMENT '首帧图片URL',
  `last_frame_image` varchar(500) DEFAULT NULL COMMENT '尾帧图片URL',
  `resolution` varchar(20) NOT NULL COMMENT '分辨率(如:1920x1080)',
  `ratio` varchar(10) NOT NULL COMMENT '视频比例(如:16:9)',
  `duration` int(11) NOT NULL COMMENT '视频时长(秒)',
  `fps` int(11) NOT NULL COMMENT '帧率',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态(0-排队中,1-处理中,2-处理完成,3-处理失败)',
  `video_url` varchar(500) DEFAULT NULL COMMENT '生成的视频URL',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
   `task_info` text DEFAULT NULL COMMENT '任务信息',
  `queue_position` int(11) DEFAULT NULL COMMENT '队列位置',
  `start_time` datetime DEFAULT NULL COMMENT '开始处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_queue_position` (`queue_position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频生成记录表';


ALTER TABLE `wl_avatar`.`ai_users`
    ADD COLUMN `api_key` varchar(255) NULL COMMENT 'apiKey' AFTER `avatar`;


ALTER TABLE `wl_avatar`.`ai_chapter`
    ADD COLUMN `prompt` varchar(1000) NULL COMMENT '章节提示词' AFTER `segment_name`;
