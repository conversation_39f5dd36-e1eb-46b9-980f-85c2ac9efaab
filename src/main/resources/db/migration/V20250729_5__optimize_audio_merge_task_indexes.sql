-- 优化音频合成任务表的索引
-- 为了减少锁等待时间，优化查询性能

-- 添加复合索引，优化状态查询
CREATE INDEX IF NOT EXISTS `idx_status_del_flag` ON `ai_audio_merge_task` (`status`, `del_flag`);

-- 添加复合索引，优化任务ID和删除标志查询
CREATE INDEX IF NOT EXISTS `idx_task_id_del_flag` ON `ai_audio_merge_task` (`task_id`, `del_flag`);

-- 添加复合索引，优化用户查询
CREATE INDEX IF NOT EXISTS `idx_user_id_status_create_time` ON `ai_audio_merge_task` (`user_id`, `status`, `create_time`);

-- 添加索引，优化超时任务查询
CREATE INDEX IF NOT EXISTS `idx_processing_start_time_status` ON `ai_audio_merge_task` (`processing_start_time`, `status`);

-- 分析表以更新统计信息
ANALYZE TABLE `ai_audio_merge_task`;
