-- 创建视频模型基础表
CREATE TABLE IF NOT EXISTS `ai_video_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(64) NOT NULL COMMENT '模型名称',
  `model_display_name` varchar(128) NOT NULL COMMENT '模型显示名称',
  `model_description` varchar(512) DEFAULT NULL COMMENT '模型描述',
  `provider` varchar(32) NOT NULL COMMENT '提供商(MINIMAX, DOUBAO)',
  `model_type` varchar(32) NOT NULL COMMENT '模型类型(T2V-文生视频, I2V-图生视频, FLF2V-首尾帧图生视频)',
  `fps` int(11) NOT NULL DEFAULT 24 COMMENT '帧率',
  `support_watermark` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否包含水印(0-不包含,1-包含)',
  `support_seed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持种子(0-不支持,1-支持)',
  `support_camera_fixed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持固定摄像头(0-不支持,1-支持)',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_name` (`model_name`),
  KEY `idx_provider` (`provider`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频模型基础表';

-- 创建视频模型尺寸配置表
CREATE TABLE IF NOT EXISTS `ai_video_model_size_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `size_name` varchar(32) NOT NULL COMMENT '尺寸名称(480p, 720p, 1080p, 768P, 1080P)',
  `size_display_name` varchar(64) NOT NULL COMMENT '尺寸显示名称',
  `supported_durations` varchar(128) NOT NULL COMMENT '支持的时长(JSON数组格式,单位秒)',
  `image_count` tinyint(4) NOT NULL DEFAULT 0 COMMENT '图片数量(0-不支持图片,1-支持首帧,2-支持首尾帧)',
  `points_cost` int(11) NOT NULL DEFAULT 0 COMMENT '该尺寸生成需要的积分',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_size` (`model_id`, `size_name`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_size_config_model` FOREIGN KEY (`model_id`) REFERENCES `ai_video_model` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频模型尺寸配置表';

-- 创建视频模型分辨率配置表
CREATE TABLE IF NOT EXISTS `ai_video_model_resolution_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `size_config_id` bigint(20) NOT NULL COMMENT '尺寸配置ID',
  `ratio` varchar(16) NOT NULL COMMENT '宽高比(16:9, 4:3, 1:1, 3:4, 9:16, 21:9, adaptive, keep_ratio)',
  `ratio_display_name` varchar(32) NOT NULL COMMENT '宽高比显示名称',
  `width` int(11) DEFAULT NULL COMMENT '宽度(像素)',
  `height` int(11) DEFAULT NULL COMMENT '高度(像素)',
  `pixel_size` varchar(32) DEFAULT NULL COMMENT '像素尺寸描述',
  `points_cost` int(11) NOT NULL DEFAULT 0 COMMENT '该分辨率生成需要的积分',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_size_ratio` (`size_config_id`, `ratio`),
  KEY `idx_size_config_id` (`size_config_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_resolution_config_size` FOREIGN KEY (`size_config_id`) REFERENCES `ai_video_model_size_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频模型分辨率配置表';

-- 插入视频模型基础数据
INSERT INTO `ai_video_model` (
  `model_name`, `model_display_name`, `model_description`, `provider`, `model_type`,
  `fps`, `support_watermark`, `support_seed`, `support_camera_fixed`, `status`, `sort_order`
) VALUES
-- MiniMax模型
('MiniMax-Hailuo-02', 'MiniMax海螺02', 'MiniMax最新的海螺模型，支持文生视频和图生视频', 'MINIMAX', 'T2V,I2V', 24, 1, 1, 1, 1, 1),

-- 豆包模型
('doubao-seedance-1-0-pro', '豆包Seedance Pro', '豆包Seedance专业版，支持文生视频和图生视频', 'DOUBAO', 'T2V,I2V', 24, 1, 1, 1, 1, 2),
('doubao-seedance-1-0-lite-t2v', '豆包Seedance Lite T2V', '豆包Seedance轻量版文生视频', 'DOUBAO', 'T2V', 24, 1, 1, 1, 1, 3),
('doubao-seedance-1-0-lite-i2v', '豆包Seedance Lite I2V', '豆包Seedance轻量版图生视频，基于首帧或首尾帧', 'DOUBAO', 'I2V', 24, 1, 1, 1, 1, 4),
('doubao-seaweed', '豆包Seaweed', '豆包Seaweed模型，支持文生视频和图生视频', 'DOUBAO', 'T2V,I2V', 24, 1, 1, 1, 1, 5),
('wan2-1-14b-t2v', 'Wan2 T2V', 'Wan2文生视频模型', 'DOUBAO', 'T2V', 16, 1, 1, 0, 1, 6),
('wan2-1-14b-i2v', 'Wan2 I2V', 'Wan2图生视频模型', 'DOUBAO', 'I2V', 16, 1, 1, 0, 1, 7),
('wan2-1-14b-flf2v', 'Wan2 FLF2V', 'Wan2首尾帧图生视频模型', 'DOUBAO', 'FLF2V', 16, 1, 1, 0, 1, 8);

-- 插入MiniMax海螺02模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- MiniMax-Hailuo-02 (model_id = 1)
(1, '768P', '768P标清', '[6, 10]', 1, 80, 1),
(1, '1080P', '1080P高清', '[6, 10]', 1, 120, 2);

-- 插入豆包Seedance Pro模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- doubao-seedance-1-0-pro (model_id = 2)
(2, '480p', '480p标清', '[5, 10]', 2, 60, 1),
(2, '720p', '720p高清', '[5, 10]', 2, 80, 2),
(2, '1080p', '1080p超清', '[5, 10]', 2, 100, 3);

-- 插入豆包Seedance Lite T2V模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- doubao-seedance-1-0-lite-t2v (model_id = 3)
(3, '480p', '480p标清', '[5, 10]', 0, 40, 1),
(3, '720p', '720p高清', '[5, 10]', 0, 60, 2),
(3, '1080p', '1080p超清', '[5, 10]', 0, 80, 3);

-- 插入豆包Seedance Lite I2V模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- doubao-seedance-1-0-lite-i2v (model_id = 4)
(4, '480p', '480p标清', '[5, 10]', 2, 50, 1),
(4, '720p', '720p高清', '[5, 10]', 2, 70, 2);

-- 插入豆包Seaweed模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- doubao-seaweed (model_id = 5)
(5, '480p', '480p标清', '[5]', 1, 30, 1),
(5, '720p', '720p高清', '[5]', 1, 50, 2);

-- 插入Wan2 T2V模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- wan2-1-14b-t2v (model_id = 6)
(6, '480p', '480p标清', '[5]', 0, 25, 1),
(6, '720p', '720p高清', '[5]', 0, 40, 2);

-- 插入Wan2 I2V模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- wan2-1-14b-i2v (model_id = 7)
(7, '480p', '480p标清', '[5]', 1, 30, 1),
(7, '720p', '720p高清', '[5]', 1, 45, 2);

-- 插入Wan2 FLF2V模型的尺寸配置
INSERT INTO `ai_video_model_size_config` (
  `model_id`, `size_name`, `size_display_name`, `supported_durations`, `image_count`, `points_cost`, `sort_order`
) VALUES
-- wan2-1-14b-flf2v (model_id = 8)
(8, '720p', '720p高清', '[5]', 2, 55, 1);

-- 插入MiniMax海螺02模型的分辨率配置
-- 768P分辨率配置 (size_config_id = 1)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(1, '16:9', '横屏16:9', 1344, 768, '1344×768', 5, 1),
(1, '4:3', '标准4:3', 1024, 768, '1024×768', 5, 2),
(1, '1:1', '正方形1:1', 768, 768, '768×768', 5, 3),
(1, '3:4', '竖屏3:4', 768, 1024, '768×1024', 5, 4),
(1, '9:16', '竖屏9:16', 768, 1344, '768×1344', 5, 5),
(1, '21:9', '超宽屏21:9', 1792, 768, '1792×768', 5, 6);

-- 1080P分辨率配置 (size_config_id = 2)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(2, '16:9', '横屏16:9', 1920, 1080, '1920×1080', 10, 1),
(2, '4:3', '标准4:3', 1440, 1080, '1440×1080', 10, 2),
(2, '1:1', '正方形1:1', 1080, 1080, '1080×1080', 10, 3),
(2, '3:4', '竖屏3:4', 1080, 1440, '1080×1440', 10, 4),
(2, '9:16', '竖屏9:16', 1080, 1920, '1080×1920', 10, 5),
(2, '21:9', '超宽屏21:9', 2520, 1080, '2520×1080', 10, 6);

-- 插入豆包Seedance Pro模型的分辨率配置
-- 480p分辨率配置 (size_config_id = 3)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(3, '16:9', '横屏16:9', 864, 480, '864×480', 3, 1),
(3, '4:3', '标准4:3', 736, 544, '736×544', 3, 2),
(3, '1:1', '正方形1:1', 640, 640, '640×640', 3, 3),
(3, '3:4', '竖屏3:4', 544, 736, '544×736', 3, 4),
(3, '9:16', '竖屏9:16', 480, 864, '480×864', 3, 5),
(3, '21:9', '超宽屏21:9', 960, 416, '960×416', 3, 6);

-- 720p分辨率配置 (size_config_id = 4)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(4, '16:9', '横屏16:9', 1248, 704, '1248×704', 5, 1),
(4, '4:3', '标准4:3', 1120, 832, '1120×832', 5, 2),
(4, '1:1', '正方形1:1', 960, 960, '960×960', 5, 3),
(4, '3:4', '竖屏3:4', 832, 1120, '832×1120', 5, 4),
(4, '9:16', '竖屏9:16', 704, 1248, '704×1248', 5, 5),
(4, '21:9', '超宽屏21:9', 1504, 640, '1504×640', 5, 6);

-- 1080p分辨率配置 (size_config_id = 5)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(5, '16:9', '横屏16:9', 1920, 1088, '1920×1088', 8, 1),
(5, '4:3', '标准4:3', 1664, 1248, '1664×1248', 8, 2),
(5, '1:1', '正方形1:1', 1440, 1440, '1440×1440', 8, 3),
(5, '3:4', '竖屏3:4', 1248, 1664, '1248×1664', 8, 4),
(5, '9:16', '竖屏9:16', 1088, 1920, '1088×1920', 8, 5),
(5, '21:9', '超宽屏21:9', 2176, 928, '2176×928', 8, 6);

-- 插入豆包Seedance Lite T2V模型的分辨率配置
-- 480p分辨率配置 (size_config_id = 6)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(6, '16:9', '横屏16:9', 864, 480, '864×480', 2, 1),
(6, '4:3', '标准4:3', 736, 544, '736×544', 2, 2),
(6, '1:1', '正方形1:1', 640, 640, '640×640', 2, 3),
(6, '3:4', '竖屏3:4', 544, 736, '544×736', 2, 4),
(6, '9:16', '竖屏9:16', 480, 864, '480×864', 2, 5),
(6, '21:9', '超宽屏21:9', 960, 416, '960×416', 2, 6);

-- 720p分辨率配置 (size_config_id = 7)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(7, '16:9', '横屏16:9', 1248, 704, '1248×704', 3, 1),
(7, '4:3', '标准4:3', 1120, 832, '1120×832', 3, 2),
(7, '1:1', '正方形1:1', 960, 960, '960×960', 3, 3),
(7, '3:4', '竖屏3:4', 832, 1120, '832×1120', 3, 4),
(7, '9:16', '竖屏9:16', 704, 1248, '704×1248', 3, 5),
(7, '21:9', '超宽屏21:9', 1504, 640, '1504×640', 3, 6);

-- 1080p分辨率配置 (size_config_id = 8)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(8, '16:9', '横屏16:9', 1920, 1088, '1920×1088', 5, 1),
(8, '4:3', '标准4:3', 1664, 1248, '1664×1248', 5, 2),
(8, '1:1', '正方形1:1', 1440, 1440, '1440×1440', 5, 3),
(8, '3:4', '竖屏3:4', 1248, 1664, '1248×1664', 5, 4),
(8, '9:16', '竖屏9:16', 1088, 1920, '1088×1920', 5, 5),
(8, '21:9', '超宽屏21:9', 2176, 928, '2176×928', 5, 6);

-- 插入豆包Seedance Lite I2V模型的分辨率配置
-- 480p分辨率配置 (size_config_id = 9)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(9, '16:9', '横屏16:9', 864, 480, '864×480', 3, 1),
(9, '4:3', '标准4:3', 736, 544, '736×544', 3, 2),
(9, '1:1', '正方形1:1', 640, 640, '640×640', 3, 3),
(9, '3:4', '竖屏3:4', 544, 736, '544×736', 3, 4),
(9, '9:16', '竖屏9:16', 480, 864, '480×864', 3, 5),
(9, '21:9', '超宽屏21:9', 960, 416, '960×416', 3, 6);

-- 720p分辨率配置 (size_config_id = 10)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(10, '16:9', '横屏16:9', 1248, 704, '1248×704', 4, 1),
(10, '4:3', '标准4:3', 1120, 832, '1120×832', 4, 2),
(10, '1:1', '正方形1:1', 960, 960, '960×960', 4, 3),
(10, '3:4', '竖屏3:4', 832, 1120, '832×1120', 4, 4),
(10, '9:16', '竖屏9:16', 704, 1248, '704×1248', 4, 5),
(10, '21:9', '超宽屏21:9', 1504, 640, '1504×640', 4, 6);

-- 插入豆包Seaweed模型的分辨率配置
-- 480p分辨率配置 (size_config_id = 11)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(11, '16:9', '横屏16:9', 848, 480, '848×480', 2, 1),
(11, '4:3', '标准4:3', 640, 480, '640×480', 2, 2),
(11, '1:1', '正方形1:1', 480, 480, '480×480', 2, 3),
(11, '3:4', '竖屏3:4', 480, 640, '480×640', 2, 4),
(11, '9:16', '竖屏9:16', 480, 848, '480×848', 2, 5),
(11, '21:9', '超宽屏21:9', 1120, 480, '1120×480', 2, 6),
(11, '9:21', '竖屏9:21', 480, 1120, '480×1120', 2, 7);

-- 720p分辨率配置 (size_config_id = 12)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(12, '16:9', '横屏16:9', 1280, 720, '1280×720', 3, 1),
(12, '4:3', '标准4:3', 960, 720, '960×720', 3, 2),
(12, '1:1', '正方形1:1', 720, 720, '720×720', 3, 3),
(12, '3:4', '竖屏3:4', 720, 960, '720×960', 3, 4),
(12, '9:16', '竖屏9:16', 720, 1280, '720×1280', 3, 5),
(12, '21:9', '超宽屏21:9', 1280, 544, '1280×544', 3, 6),
(12, '9:21', '竖屏9:21', 544, 1280, '544×1280', 3, 7);

-- 插入Wan2 T2V模型的分辨率配置
-- 480p分辨率配置 (size_config_id = 13)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(13, '16:9', '横屏16:9', 832, 480, '832×480', 1, 1),
(13, '9:16', '竖屏9:16', 480, 832, '480×832', 1, 2);

-- 720p分辨率配置 (size_config_id = 14)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(14, '16:9', '横屏16:9', 1280, 720, '1280×720', 2, 1),
(14, '9:16', '竖屏9:16', 720, 1280, '720×1280', 2, 2);

-- 插入Wan2 I2V模型的分辨率配置
-- 480p分辨率配置 (size_config_id = 15)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(15, '16:9', '横屏16:9', 832, 480, '832×480', 2, 1),
(15, '9:16', '竖屏9:16', 480, 832, '480×832', 2, 2);

-- 720p分辨率配置 (size_config_id = 16)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(16, '16:9', '横屏16:9', 1280, 720, '1280×720', 3, 1),
(16, '9:16', '竖屏9:16', 720, 1280, '720×1280', 3, 2);

-- 插入Wan2 FLF2V模型的分辨率配置
-- 720p分辨率配置 (size_config_id = 17)
INSERT INTO `ai_video_model_resolution_config` (
  `size_config_id`, `ratio`, `ratio_display_name`, `width`, `height`, `pixel_size`, `points_cost`, `sort_order`
) VALUES
(17, 'keep_ratio', '保持原比例', NULL, NULL, '自适应', 5, 1);
