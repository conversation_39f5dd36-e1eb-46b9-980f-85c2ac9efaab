-- 更新分镜对口型记录表结构
-- 如果表已经存在，则修改字段结构

-- 检查表是否存在，如果存在则进行字段修改
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'ai_shot_lip_sync');

-- 如果表存在，则修改字段
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE ai_shot_lip_sync 
     DROP COLUMN IF EXISTS net_wss_url,
     CHANGE COLUMN audio_data audio_url VARCHAR(500) NOT NULL COMMENT "音频地址",
     CHANGE COLUMN image_data image_url VARCHAR(500) NOT NULL COMMENT "图片地址"',
    'SELECT "Table ai_shot_lip_sync does not exist, skipping migration" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
