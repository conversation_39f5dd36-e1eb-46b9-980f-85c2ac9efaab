spring:
  config:
    import: nacos:
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  main:
    allow-bean-definition-overriding: true
  application:
    name: smart-agent
  jackson:
    dateFormat: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization.FAIL_ON_UNWRAPPED_TYPE_IDENTIFIERS: false
  cloud:
    nacos:
      server-addr: mse-b3b509b0-nacos-ans.mse.aliyuncs.com:8848
      discovery:
        server-addr: mse-b3b509b0-nacos-ans.mse.aliyuncs.com:8848
        namespace: 867f8e5c-5bf5-4eba-a4fa-11df5fc38a49
        group: test
      config:
        server-addr: mse-b3b509b0-nacos-ans.mse.aliyuncs.com:8848
        namespace: 867f8e5c-5bf5-4eba-a4fa-11df5fc38a49
        group: test
        file-extension: yaml
        shared-configs[0]:
          data-id: wl-avatar-mysql.yaml
          group: test
          refresh: true
        shared-configs[1]:
          data-id: wl-avatar-redis.yaml
          group: test
          refresh: true
  profiles:
    active: test


lark:
  msg.on: true
  appId: ********************
  appSecret: eAQn8Z3bW8RWqoOtX7cQ5Dj3AgdL3JAY
  org.webhook: https://open.feishu.cn/open-apis/bot/v2/hook/0295cdae-3871-4264-a855-4abe0610ebd4

approve.lark.appId: ********************
approve.lark.appSecret: WJpYNTmqXhr30A2vmxltmTmw0eo85hwi

http.maxTotal: 100
http.defaultMaxPerRoute: 20
http.connectTimeout: 60000
http.connectionRequestTimeout: 60000
http.socketTimeout: 60000
management:
  endpoints:
    web:
      base-path: /  # 将基础路径设置为根路径
      exposure:
        include: prometheus
      path-mapping:
        prometheus: metrics  # 不需要加前导斜杠



