spring:
  config:
    import: nacos:
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  main:
    allow-bean-definition-overriding: true
  application:
    name: smart-agent
  jackson:
    dateFormat: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization.FAIL_ON_UNWRAPPED_TYPE_IDENTIFIERS: false
  cloud:
    nacos:
      server-addr: mse-b3b509b0-nacos-ans.mse.aliyuncs.com:8848
      discovery:
        server-addr: mse-b3b509b0-nacos-ans.mse.aliyuncs.com:8848
        namespace: 3a7ae83f-641a-4b98-a64e-bac58aaf9e7f
        group: pre
      config:
        server-addr: mse-b3b509b0-nacos-ans.mse.aliyuncs.com:8848
        namespace: 3a7ae83f-641a-4b98-a64e-bac58aaf9e7f
        group: pre
        file-extension: yaml
        shared-configs[0]:
          data-id: wl-avatar-mysql.yaml
          group: pre
          refresh: true
        shared-configs[1]:
          data-id: wl-avatar-redis.yaml
          group: pre
          refresh: true
  profiles:
    active: pre

lark:
  msg.on: true
  appId: ********************
  appSecret: eAQn8Z3bW8RWqoOtX7cQ5Dj3AgdL3JAY
  org.webhook: https://open.feishu.cn/open-apis/bot/v2/hook/0295cdae-3871-4264-a855-4abe0610ebd4

approve.lark.appId: ********************
approve.lark.appSecret: WJpYNTmqXhr30A2vmxltmTmw0eo85hwi

http.maxTotal: 100
http.defaultMaxPerRoute: 20
http.connectTimeout: 60000
http.connectionRequestTimeout: 60000
http.socketTimeout: 60000



