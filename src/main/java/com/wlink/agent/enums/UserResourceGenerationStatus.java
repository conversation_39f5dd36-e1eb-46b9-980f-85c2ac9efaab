package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户资源生成状态枚举
 */
@Getter
@AllArgsConstructor
public enum UserResourceGenerationStatus {
    
    /**
     * 生成中
     */
    PENDING("PENDING", "生成中"),
    
    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败");
    
    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     */
    public static UserResourceGenerationStatus fromValue(String value) {
        if (value == null) {
            return null;
        }
        
        for (UserResourceGenerationStatus status : UserResourceGenerationStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的用户资源生成状态值: " + value);
    }
    
    /**
     * 判断是否为生成中状态
     */
    public boolean isPending() {
        return this == PENDING;
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 判断是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * 判断是否为最终状态（成功或失败）
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }
}
