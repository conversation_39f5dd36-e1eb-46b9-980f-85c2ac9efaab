package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源子类型枚举
 */
@Getter
@AllArgsConstructor
public enum ResourceSubtypeEnum {

    // 图片子类型
    ROLE_IMAGE(ResourceTypeEnum.IMAGE.getCode(), 1, "角色图片"),
    SCENE_IMAGE(ResourceTypeEnum.IMAGE.getCode(), 2, "场景图片"),
    STORYBOARD_IMAGE(ResourceTypeEnum.IMAGE.getCode(), 3, "分镜图片"),
    
    // 视频子类型
    SHORT_VIDEO(ResourceTypeEnum.VIDEO.getCode(), 1, "短视频"),
    LONG_VIDEO(ResourceTypeEnum.VIDEO.getCode(), 2, "长视频"),
    
    // 音频子类型
    VOICE(ResourceTypeEnum.AUDIO.getCode(), 1, "语音"),
    MUSIC(ResourceTypeEnum.AUDIO.getCode(), 2, "音乐"),
    
    // 文本子类型
    STORY(ResourceTypeEnum.TEXT.getCode(), 1, "故事"),
    SCRIPT(ResourceTypeEnum.TEXT.getCode(), 2, "剧本");

    /**
     * 主类型编码
     */
    private final Integer mainType;
    
    /**
     * 子类型编码
     */
    private final Integer code;

    /**
     * 子类型描述
     */
    private final String desc;

    /**
     * 根据主类型和子类型编码获取枚举
     *
     * @param mainType 主类型编码
     * @param code 子类型编码
     * @return 枚举
     */
    public static ResourceSubtypeEnum getByCode(Integer mainType, Integer code) {
        if (mainType == null || code == null) {
            return null;
        }
        for (ResourceSubtypeEnum subtype : values()) {
            if (subtype.getMainType().equals(mainType) && subtype.getCode().equals(code)) {
                return subtype;
            }
        }
        return null;
    }
} 