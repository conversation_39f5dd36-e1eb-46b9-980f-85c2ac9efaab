package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频生成状态枚举
 */
@Getter
@AllArgsConstructor
public enum VideoGenerationStatus {
    
    /**
     * 排队中
     */
    QUEUED(0, "排队中"),
    
    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),
    
    /**
     * 处理完成
     */
    COMPLETED(2, "处理完成"),
    
    /**
     * 处理失败
     */
    FAILED(3, "处理失败");
    
    /**
     * 状态值
     */
    private final Integer value;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     */
    public static VideoGenerationStatus fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        
        for (VideoGenerationStatus status : VideoGenerationStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的视频生成状态值: " + value);
    }
    
    /**
     * 判断是否为排队中状态
     */
    public boolean isQueued() {
        return this == QUEUED;
    }
    
    /**
     * 判断是否为处理中状态
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }
    
    /**
     * 判断是否为完成状态
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }
    
    /**
     * 判断是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * 判断是否为最终状态（完成或失败）
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }
    
    /**
     * 判断是否可以开始处理
     */
    public boolean canStartProcessing() {
        return this == QUEUED;
    }
}
