package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskStatus {
    /**
     * 待提交
     */
    PENDING_SUBMISSION("PENDING_SUBMISSION"),
    
    /**
     * 等待处理
     */
    PENDING("PENDING"),
    
    /**
     * 处理中
     */
    PROCESSING("PROCESSING"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED"),
    
    /**
     * 失败
     */
    FAILED("FAILED");
    
    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 根据字符串获取枚举
     *
     * @param value 状态值
     * @return 任务状态枚举
     */
    public static TaskStatus fromString(String value) {
        return Arrays.stream(values())
                .filter(status -> status.getValue().equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown task status: " + value));
    }
} 