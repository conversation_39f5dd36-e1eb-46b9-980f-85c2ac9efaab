package com.wlink.agent.enums;

/**
 * 外部任务状态枚举
 */
public enum ExternalTaskStatus {
    
    PENDING("待处理", 0),
    RUNNING("执行中", 1),
    COMPLETED("已完成", 2),
    FAILED("失败", 3),
    CANCELLED("已取消", 3); // 取消状态也映射为失败
    
    private final String description;
    private final Integer internalStatus;
    
    ExternalTaskStatus(String description, Integer internalStatus) {
        this.description = description;
        this.internalStatus = internalStatus;
    }
    
    public String getDescription() {
        return description;
    }
    
    public Integer getInternalStatus() {
        return internalStatus;
    }
    
    /**
     * 根据外部状态获取内部状态值
     *
     * @param externalStatus 外部状态
     * @return 内部状态值
     */
    public static Integer getInternalStatus(String externalStatus) {
        if (externalStatus == null) {
            return null;
        }
        
        for (ExternalTaskStatus status : values()) {
            if (status.name().equals(externalStatus)) {
                return status.getInternalStatus();
            }
        }
        
        return null;
    }
    
    /**
     * 判断是否为终态
     *
     * @param externalStatus 外部状态
     * @return 是否为终态
     */
    public static boolean isFinalStatus(String externalStatus) {
        return COMPLETED.name().equals(externalStatus) || 
               FAILED.name().equals(externalStatus) || 
               CANCELLED.name().equals(externalStatus);
    }
}
