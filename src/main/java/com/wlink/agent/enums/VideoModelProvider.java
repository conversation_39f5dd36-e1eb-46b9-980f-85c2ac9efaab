package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频模型提供商枚举
 */
@Getter
@AllArgsConstructor
public enum VideoModelProvider {
    
    /**
     * MiniMax
     */
    MINIMAX("MINIMAX", "MiniMax"),
    
    /**
     * 豆包
     */
    DOUBAO("DOUBAO", "豆包");
    
    /**
     * 提供商代码
     */
    private final String code;
    
    /**
     * 提供商名称
     */
    private final String name;
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 提供商代码
     * @return 提供商枚举
     */
    public static VideoModelProvider fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (VideoModelProvider provider : VideoModelProvider.values()) {
            if (provider.getCode().equalsIgnoreCase(code)) {
                return provider;
            }
        }
        
        throw new IllegalArgumentException("未知的视频模型提供商代码: " + code);
    }
    
    /**
     * 判断是否为MiniMax提供商
     * 
     * @return true表示是MiniMax，false表示不是
     */
    public boolean isMiniMax() {
        return this == MINIMAX;
    }
    
    /**
     * 判断是否为豆包提供商
     * 
     * @return true表示是豆包，false表示不是
     */
    public boolean isDoubao() {
        return this == DOUBAO;
    }
}
