package com.wlink.agent.enums;

/**
 * @description: 
 * <AUTHOR>
 * @date 2025/6/25 11:30
 * @version 1.0
 */public enum ShotStatus {
    /**
     * 分镜状态(0-初始,1-处理中,2-已完成,3-失败)
     */
    INITIAL(0, "初始"),
    PROCESSING(1, "处理中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "失败");

    private final Integer value;
    private final String description;

    ShotStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ShotStatus fromValue(Integer value) {
        for (ShotStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown shot status: " + value);
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }



}
