package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 分镜资源状态枚举
 */
@Getter
@AllArgsConstructor
public enum ShotResourceStatus {
    /**
     * 未完成
     */
    NOT_EXIST("未完成"),
    
    /**
     * 排队中
     */
    QUEUED("排队中"),
    
    /**
     * 生成中
     */
    IN_PROGRESS("生成中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成");
    
    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 根据字符串获取枚举
     *
     * @param value 状态值
     * @return 资源状态枚举
     */
    public static ShotResourceStatus fromString(String value) {
        return Arrays.stream(values())
                .filter(status -> status.getValue().equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown resource status: " + value));
    }
} 