package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户资源类型枚举
 */
@Getter
@AllArgsConstructor
public enum UserResourceType {
    
    /**
     * 图片
     */
    IMAGE(1, "图片"),
    
    /**
     * 视频
     */
    VIDEO(2, "视频"),
    
    /**
     * 音频
     */
    AUDIO(3, "音频");
    
    /**
     * 类型值
     */
    private final Integer value;
    
    /**
     * 类型描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     */
    public static UserResourceType fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        
        for (UserResourceType type : UserResourceType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("未知的用户资源类型值: " + value);
    }
    
    /**
     * 判断是否为图片类型
     */
    public boolean isImage() {
        return this == IMAGE;
    }
    
    /**
     * 判断是否为视频类型
     */
    public boolean isVideo() {
        return this == VIDEO;
    }
    
    /**
     * 判断是否为音频类型
     */
    public boolean isAudio() {
        return this == AUDIO;
    }
}
