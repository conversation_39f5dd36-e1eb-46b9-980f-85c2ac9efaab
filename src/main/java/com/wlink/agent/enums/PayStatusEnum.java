package com.wlink.agent.enums;

import lombok.Getter;

/**
 * 支付状态枚举
 */
public class PayStatusEnum {

    /**
     * 订单状态枚举
     */
    @Getter
    public enum OrderStatus {
        UNPAID(0, "未支付"),
        PAYING(1, "支付中"),
        PAID(2, "已支付"),
        CANCELLED(3, "已取消"),
        REFUNDED(4, "已退款");

        private final Integer code;
        private final String desc;

        OrderStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static OrderStatus getByCode(Integer code) {
            for (OrderStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 交易状态枚举
     */
    @Getter
    public enum TransactionStatus {
        CREATED(0, "创建"),
        SUCCESS(1, "成功"),
        FAILURE(2, "失败");

        private final Integer code;
        private final String desc;

        TransactionStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static TransactionStatus getByCode(Integer code) {
            for (TransactionStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 支付方式枚举
     */
    @Getter
    public enum PayType {
        WECHAT(1, "微信支付");

        private final Integer code;
        private final String desc;

        PayType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static PayType getByCode(Integer code) {
            for (PayType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
} 