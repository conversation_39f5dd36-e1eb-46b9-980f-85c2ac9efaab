package com.wlink.agent.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/18 11:50
 */
public enum CmdTypeEnum {

    LIVE_DM("LIVE_DM"),
    LIVE_SEND_GIFT("LIVE_SEND_GIFT"),
    LIVE_SYSTEM("LIVE_SYSTEM");

    private final String cmd;

    CmdTypeEnum(String cmd) {
        this.cmd = cmd;
    }

    public String getCmd() {
        return cmd;
    }

    public static CmdTypeEnum fromString(String cmd) {
        for (CmdTypeEnum type : CmdTypeEnum.values()) {
            if (type.getCmd().equals(cmd)) {
                return type;
            }
        }
        return null;
    }
}
