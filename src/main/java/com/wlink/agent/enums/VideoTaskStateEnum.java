package com.wlink.agent.enums;

import lombok.Getter;

/**
 * 视频生成任务状态枚举
 */
@Getter
public enum VideoTaskStateEnum {
    
    CREATED("created", "已创建"),
    QUEUEING("queueing", "排队中"),
    SCHEDULING("scheduling", "调度中"),
    PROCESSING("processing", "处理中"),
    SUCCESS("success", "生成成功"),
    FAILED("failed", "生成失败");

    private final String state;
    private final String desc;

    VideoTaskStateEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    /**
     * 根据状态值获取枚举
     */
    public static VideoTaskStateEnum getByState(String state) {
        if (state == null) {
            return null;
        }
        for (VideoTaskStateEnum stateEnum : values()) {
            if (stateEnum.getState().equals(state)) {
                return stateEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态（成功或失败）
     */
    public static boolean isFail(String state) {
        return FAILED.getState().equals(state);
    }

    public static boolean isSuccess(String state) {
        return SUCCESS.getState().equals(state);
    }

    /**
     * 判断是否为处理中状态
     */
    public static boolean isProcessing(String state) {
        return PROCESSING.getState().equals(state) || QUEUEING.getState().equals(state) || SCHEDULING.getState().equals(state) || CREATED.getState().equals(state);
    }
} 