package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频模型类型枚举
 */
@Getter
@AllArgsConstructor
public enum VideoModelType {
    
    /**
     * 文生视频
     */
    T2V("T2V", "文生视频"),
    
    /**
     * 图生视频
     */
    I2V("I2V", "图生视频"),
    
    /**
     * 首尾帧图生视频
     */
    FLF2V("FLF2V", "首尾帧图生视频");
    
    /**
     * 类型代码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 类型代码
     * @return 类型枚举
     */
    public static VideoModelType fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (VideoModelType type : VideoModelType.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("未知的视频模型类型代码: " + code);
    }
    
    /**
     * 判断是否为文生视频
     * 
     * @return true表示是文生视频，false表示不是
     */
    public boolean isTextToVideo() {
        return this == T2V;
    }
    
    /**
     * 判断是否为图生视频
     * 
     * @return true表示是图生视频，false表示不是
     */
    public boolean isImageToVideo() {
        return this == I2V;
    }
    
    /**
     * 判断是否为首尾帧图生视频
     * 
     * @return true表示是首尾帧图生视频，false表示不是
     */
    public boolean isFirstLastFrameToVideo() {
        return this == FLF2V;
    }
}
