package com.wlink.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务类型枚举
 */
@Getter
@AllArgsConstructor
public enum TaskType {
    /**
     * 图片生成任务
     */
    GENERATE("GENERATE"),
    
    /**
     * 角色保留任务
     */
    RETAIN("RETAIN"),
    
    /**
     * 图片编辑任务
     */
    EDIT("EDIT"),


    /**
     * 图片重绘任务
     *
     */
    REDRAW("REDRAW"),


    /**
     * 豆包生成任务
     */
    GENERATE_DOUBAO("GENERATE_DOUBAO"),
    
    /**
     * Flux生成任务
     */
    GENERATE_FLUX("GENERATE_FLUX"),

    /**
     * 图片生成任务(内容类型4)
     */
    GENERATE_CANVAS("CANVAS"),

    /**
     * 图片生成任务(内容类型4)
     */
    EDIT_CANVAS("CANVAS_EDIT");

    /**
     * 类型值
     */
    private final String value;
    
    /**
     * 根据字符串获取枚举
     *
     * @param value 类型值
     * @return 任务类型枚举
     */
    public static TaskType fromString(String value) {
        return Arrays.stream(values())
                .filter(type -> type.getValue().equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown task type: " + value));
    }
} 