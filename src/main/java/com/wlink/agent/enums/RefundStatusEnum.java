package com.wlink.agent.enums;

import lombok.Getter;

/**
 * 退款状态枚举
 */
public class RefundStatusEnum {

    /**
     * 退款状态枚举
     */
    @Getter
    public enum Status {
        PROCESSING(0, "处理中"),
        SUCCESS(1, "成功"),
        FAILURE(2, "失败");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 是否扣除积分枚举
     */
    @Getter
    public enum DeductPoints {
        NO(0, "否"),
        YES(1, "是");

        private final Integer code;
        private final String desc;

        DeductPoints(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static DeductPoints getByCode(Integer code) {
            for (DeductPoints status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
} 