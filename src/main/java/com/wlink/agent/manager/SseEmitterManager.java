package com.wlink.agent.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages active SseEmitter instances locally for each server node.
 */
@Slf4j
@Component
public class SseEmitterManager {

    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    /**
     * Registers an SseEmitter for a given conversation ID.
     *
     * @param conversationId The conversation ID.
     * @param emitter        The SseEmitter instance.
     */
    public void register(String conversationId, SseEmitter emitter) {
        if (conversationId == null || emitter == null) {
            log.warn("Attempted to register null conversationId or emitter");
            return;
        }
        log.info("Registering SseEmitter for conversationId: {}", conversationId);
        emitters.put(conversationId, emitter);

        // Ensure removal on completion, timeout, or error
        emitter.onCompletion(() -> remove(conversationId, "completed"));
        emitter.onTimeout(() -> remove(conversationId, "timed out"));
        // Note: onError might be handled elsewhere, but adding removal here is safer
        emitter.onError(e -> remove(conversationId, "errored: " + e.getMessage()));
    }

    /**
     * Removes an SseEmitter for a given conversation ID.
     *
     * @param conversationId The conversation ID.
     */
    public void remove(String conversationId, String reason) {
        if (conversationId == null) {
            return;
        }
        SseEmitter removed = emitters.remove(conversationId);
        if (removed != null) {
            log.info("Removing SseEmitter for conversationId: {} due to: {}", conversationId, reason);
        } else {
            // log.debug("Attempted to remove non-existent emitter for conversationId: {}", conversationId);
        }
    }

    /**
     * Retrieves the SseEmitter for a given conversation ID.
     *
     * @param conversationId The conversation ID.
     * @return The SseEmitter, or null if not found on this node.
     */
    public SseEmitter get(String conversationId) {
        if (conversationId == null) {
            return null;
        }
        return emitters.get(conversationId);
    }
} 
