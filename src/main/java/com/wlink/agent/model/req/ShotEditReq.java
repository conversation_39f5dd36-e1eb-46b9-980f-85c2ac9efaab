package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 编辑分镜请求
 */
@Data
@Schema(description = "编辑分镜请求")
public class ShotEditReq {

    /**
     * 分镜ID
     */
    @NotNull(message = "分镜ID不能为空")
    @Schema(description = "分镜ID", required = true)
    private Long id;

    /**
     * 分镜类型
     */
    @Schema(description = "分镜类型")
    private String type;

    /**
     * 构图描述
     */
    @Schema(description = "构图描述")
    private String composition;

    /**
     * 运动类型
     */
    @Schema(description = "运动类型")
    private String movement;


    /**
     * 显示类型
     */
    @Schema(description = "显示类型")
    private String displayType;


    // ========== 图片信息字段 ==========

    /**
     * 图片URL
     */
    @Schema(description = "图片URL")
    private String imageUrl;

    /**
     * 图片生成提示词
     */
    @Schema(description = "图片生成提示词")
    private String imagePrompt;

    /**
     * 图片描述
     */
    @Schema(description = "图片描述")
    private String imageDesc;

    /**
     * 图片宽高比
     */
    @Schema(description = "图片宽高比")
    private String imageAspectRatio;

    /**
     * 图片状态
     */
    @Schema(description = "图片状态")
    private String imageStatus;

    /**
     * 参考图片URL
     */
    @Schema(description = "参考图片URL")
    private String referenceImage;

    // ========== 视频信息字段 ==========

    /**
     * 视频URL
     */
    @Schema(description = "视频URL")
    private String videoUrl;

    /**
     * 视频生成提示词
     */
    @Schema(description = "视频生成提示词")
    private String videoPrompt;

    /**
     * 视频描述
     */
    @Schema(description = "视频描述")
    private String videoDesc;

    /**
     * 视频时长(毫秒)
     */
    @Schema(description = "视频时长(毫秒)")
    private Integer videoDuration;

    /**
     * 视频宽高比
     */
    @Schema(description = "视频宽高比")
    private String videoAspectRatio;

    /**
     * 视频状态
     */
    @Schema(description = "视频状态")
    private String videoStatus;

    /**
     * 开始帧图片URL
     */
    @Schema(description = "开始帧图片URL")
    private String startFrameImage;

    /**
     * 结束帧图片URL
     */
    @Schema(description = "结束帧图片URL")
    private String endFrameImage;
}
