package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/17 13:50
 */
@NoArgsConstructor
@Data
@Schema(description = "视觉创作保存请求")
public class VisualSaveReq {
    @NotBlank(message = "cover不能为空")
    @JsonProperty("cover")
    @Schema(description = "封面信息", required = true)
    private CoverDTO cover;
    @NotNull(message = "backgroundMusic不能为空")
    @JsonProperty("background_music")
    @Schema(description = "背景音乐", required = true)
    private BackgroundMusicDTO backgroundMusic;
    @NotEmpty(message = "shots不能为空")
    @JsonProperty("shots")
    @Schema(description = "镜头列表", required = true)
    private List<ShotsDTO> shots;

    @NoArgsConstructor
    @Data
    @Schema(description = "封面信息")
    public static class CoverDTO {

        @JsonProperty("id")
        @Schema(description = "ID", required = false, example = "1")
        private String id;

        @JsonProperty("title")
        @Schema(description = "标题", required = true)
        private String title;
        
        @JsonProperty("subtitle2")
        @Schema(description = "副标", required = false)
        private String subtitle2;
        
        @JsonProperty("image")
        @Schema(description = "封面图片", required = true)
        private String image;
        
        @JsonProperty("subtitle")
        @Schema(description = "副标, required = true, notes = ", required = true)
        private String subtitle;
    }

    @NoArgsConstructor
    @Data
    @Schema(description = "背景音乐")
    public static class BackgroundMusicDTO {
        @JsonProperty("audio_url")
        @Schema(description = "音频URL", required = true)
        private String audioUrl;
        
        @JsonProperty("volume")
        @Schema(description = "音量", required = true, example = "0.8")
        private Double volume;
        
        @JsonProperty("loop")
        @Schema(description = "循环播放", required = true, example = "true")
        private String loop;
    }

    @NoArgsConstructor
    @Data
    @Schema(description = "镜头信息")
    public static class ShotsDTO {
        @JsonProperty("scene_id")
        @Schema(description = "场景ID", required = true, example = "1")
        private String sceneId;
        
        @JsonProperty("scene_image")
        @Schema(description = "场景图片", required = true)
        private String sceneImage;
        
        @JsonProperty("subtitle")
        @Schema(description = "字幕", required = false)
        private String subtitle;
        
        @JsonProperty("audio_clip")
        @Schema(description = "音频片段", required = false)
        private String audioClip;
        
        @JsonProperty("camera_effect")
        @Schema(description = "相机效果", required = false)
        private String cameraEffect;
        
        @JsonProperty("duration_seconds")
        @Schema(description = "持续时间", required = true, example = "5")
        private Integer durationSeconds;
        
        @JsonProperty("id")
        @Schema(description = "镜头ID", required = false, example = "1")
        private Integer id;
    }
}
