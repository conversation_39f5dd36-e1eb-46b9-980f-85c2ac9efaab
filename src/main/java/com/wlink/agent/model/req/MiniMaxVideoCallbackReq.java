package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * MiniMax视频生成回调请求
 */
@Data
@Schema(description = "MiniMax视频生成回调请求")
public class MiniMaxVideoCallbackReq {
    
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    @Schema(description = "任务ID", required = true)
    private String taskId;


    @JsonProperty("challenge")
    private String challenge;

    
    /**
     * 任务状态
     * Preparing-准备中
     * Queueing-队列中
     * Processing-生成中
     * Success-成功
     * Fail-失败
     */
    @JsonProperty("status")
    @Schema(description = "任务状态", required = true, 
            allowableValues = {"preparing", "queueing", "processing", "success", "fail"})
    private String status;
    
    /**
     * 文件ID（成功时返回）
     */
    @JsonProperty("file_id")
    @Schema(description = "文件ID")
    private String fileId;
    
    /**
     * 视频宽度（像素）
     */
    @JsonProperty("video_width")
    @Schema(description = "视频宽度（像素）")
    private Integer videoWidth;
    
    /**
     * 视频高度（像素）
     */
    @JsonProperty("video_height")
    @Schema(description = "视频高度（像素）")
    private Integer videoHeight;
    
    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    @Schema(description = "基础响应信息")
    private BaseResponse baseResp;
    
    /**
     * 基础响应模型
     */
    @Data
    @Schema(description = "基础响应信息")
    public static class BaseResponse {
        /**
         * 状态码
         * 0：请求成功
         * 其他：请求失败
         */
        @JsonProperty("status_code")
        @Schema(description = "状态码")
        private Integer statusCode;
        
        /**
         * 状态消息
         */
        @JsonProperty("status_msg")
        @Schema(description = "状态消息")
        private String statusMsg;
    }
    
    /**
     * 判断是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return "success".equals(status) &&
               baseResp != null && 
               baseResp.getStatusCode() != null && 
               baseResp.getStatusCode() == 0;
    }
    
    /**
     * 判断是否失败
     * 
     * @return true表示失败，false表示未失败
     */
    public boolean isFailed() {
        return "fail".equals(status) ||
               (baseResp != null && 
                baseResp.getStatusCode() != null && 
                baseResp.getStatusCode() != 0);
    }
    
    /**
     * 判断是否处理中
     * 
     * @return true表示处理中，false表示不是处理中
     */
    public boolean isProcessing() {
        return "preparing".equals(status) ||
               "queueing".equals(status) ||
               "processing".equals(status);
    }
    
    /**
     * 获取错误消息
     * 
     * @return 错误消息
     */
    public String getErrorMessage() {
        if (baseResp != null && baseResp.getStatusMsg() != null) {
            return baseResp.getStatusMsg();
        }
        return "MiniMax视频生成失败，状态: " + status;
    }
}
