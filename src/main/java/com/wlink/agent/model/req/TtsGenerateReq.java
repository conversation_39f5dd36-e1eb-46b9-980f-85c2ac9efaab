package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

@Data
@Schema(description = "TTS音频生成请求")
public class TtsGenerateReq {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true, example = "conv_12345")
    private String conversationId;

    @Schema(description = "内容ID  旁白类型传分镜id 对话类型传对话id ", required = true, example = "content_12345")
    private String contentId;

    @Schema(description = "音频内容类型 ", required = true, example = "1")
    private String soundType;

    @NotBlank(message = "音色ID不能为空")
    @Schema(description = "音色ID", required = true, example = "presenter_female")
    private String voiceId;

    @NotBlank(message = "文本内容不能为空")
    @Schema(description = "需要转换的文本", required = true, example = "你好，世界！")
    private String text;


    /**
     * 音频索引 默认为1
     */
    @Schema(description = "音频索引 默认为1", required = false, example = "1")
    private Integer index;

    /**
     * 语
     */
    @Schema(description = "语范围[0.5,2]，默认值为1.0", required = false, example = "1.0")
    private String rate;

    @Schema(description = "音调 范围[-12,12]，默认值为0", required = false, example = "0")
    private Integer pitch;

    @Schema(description = "音量 范围,10]，默认值为1.0", required = false, example = "1.0")
    private String volume;

    private Integer source;
    /**
     * 情绪
     */
    @Schema(description = "[\"happy\", \"sad\", \"angry\", \"fearful\", \"disgusted\", \"surprised\", \"neutral\"]", required = false, example = "disgusted")
    private String emotion;


    @Schema(description = "[\"燕少飞/(yan4)(shao3)(fei1)\",\"达菲/(da2)(fei1)\"，\"omg/oh my god\"]", required = false, example = "child")
    private List<String> tone;

    /**
     * 验证并规范化emotion参数
     * 如果值不在允许的范围内，则设置为null
     */
    public void setEmotion(String emotion) {
        if (emotion != null && !isValidEmotion(emotion)) {
            this.emotion = null;
        } else {
            this.emotion = emotion;
        }
    }

    /**
     * 检查emotion值是否有效
     */
    private boolean isValidEmotion(String emotion) {
        return "happy".equals(emotion) || "sad".equals(emotion) || "angry".equals(emotion) ||
               "fearful".equals(emotion) || "disgusted".equals(emotion) || "surprised".equals(emotion) ||
               "neutral".equals(emotion);
    }
}
