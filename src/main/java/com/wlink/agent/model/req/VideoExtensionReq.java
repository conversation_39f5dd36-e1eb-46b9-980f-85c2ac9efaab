package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 视频延长请求
 */
@Data
@Schema(description = "视频延长请求")
public class VideoExtensionReq {
    
    /**
     * 画布分镜ID
     */
    @NotNull(message = "分镜ID不能为空")
    @Schema(description = "分镜ID", required = true)
    private Long shotId;
    
    /**
     * 当前分镜视频素材的最后一帧图片URL
     */
    @NotBlank(message = "最后一帧图片URL不能为空")
    @Schema(description = "视频最后一帧图片URL", required = true)
    private String lastFrameImageUrl;
}
