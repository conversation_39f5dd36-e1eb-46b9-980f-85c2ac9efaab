package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 添加空白分镜请求
 */
@Data
@Schema(description = "添加空白分镜请求")
public class ShotCreateReq {
    
    /**
     * 画布ID
     */
    @NotNull(message = "画布ID不能为空")
    @Schema(description = "画布ID", required = true)
    private Long canvasId;
    
    /**
     * 插入位置的排序序号
     * 如果为null或0，则添加到末尾
     * 如果指定值，则插入到该位置，原有分镜顺序后移
     */
    @Schema(description = "插入位置的排序序号，为空则添加到末尾")
    private Integer insertPosition;
    
    /**
     * 分镜类型
     */
    @Schema(description = "分镜类型")
    private String type;
    
    /**
     * 构图描述
     */
    @Schema(description = "构图描述")
    private String composition;
    
    /**
     * 运动类型
     */
    @Schema(description = "运动类型")
    private String movement;


    /**
     * 显示类型
     */
    @Schema(description = "显示类型")
    private String displayType;
}
