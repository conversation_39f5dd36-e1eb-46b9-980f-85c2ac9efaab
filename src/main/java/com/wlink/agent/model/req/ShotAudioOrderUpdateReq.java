package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 分镜音频顺序调整请求
 */
@Data
@Schema(description = "分镜音频顺序调整请求")
public class ShotAudioOrderUpdateReq {
    
    /**
     * 分镜ID
     */
    @NotNull(message = "分镜ID不能为空")
    @Schema(description = "分镜ID", required = true)
    private Long shotId;
    
    /**
     * 音频顺序对象集合
     */
    @NotEmpty(message = "音频顺序列表不能为空")
    @Valid
    @Schema(description = "音频顺序对象集合", required = true)
    private List<AudioOrderItem> audioOrders;
    
    /**
     * 音频顺序项
     */
    @Data
    @Schema(description = "音频顺序项")
    public static class AudioOrderItem {
        
        /**
         * 音频资源ID
         */
        @NotNull(message = "音频资源ID不能为空")
        @Schema(description = "音频资源ID", required = true)
        private Long audioId;
        
        /**
         * 排序序号
         */
        @NotNull(message = "排序序号不能为空")
        @Schema(description = "排序序号", required = true)
        private Integer sortOrder;
    }
}
