package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "分镜任务状态请求")
public class ShotTaskStatusReq {
    
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true)
    private String conversationId;
    
    @NotBlank(message = "内容ID不能为空")
    @Schema(description = "内容ID", required = true)
    private String contentId;
    
    @NotNull(message = "内容ID类型不能为空")
    @Schema(description = "内容ID类型(1-chapter 2-scenes 3-shot)", required = true, example = "3")
    private Integer contentIdType;
} 
