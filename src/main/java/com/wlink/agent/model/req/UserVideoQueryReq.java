package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 用户视频记录查询请求
 */
@Data
@Schema(description = "用户视频记录查询请求")
public class UserVideoQueryReq {

    private String userId;

    @Schema(description = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum = 1;

    @Schema(description = "每页记录数", required = true, example = "10")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize = 10;


    //视频类型
    @Schema(description = "视频类型 1-ai生成  2-渲染导出", required = true, example = "1")
    private Integer videoType;

} 