package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;



/**
 * 分镜对口型请求
 */
@Schema(description = "分镜对口型请求")
@Data
public class ShotLipSyncReq {

    /**
     * 分镜ID
     */
    @Schema(description = "分镜ID", required = true, example = "123456789")
    @NotNull(message = "分镜ID不能为空")
    private Long shotId;
}
