package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 章节转画布请求
 */
@Data
@Schema(description = "章节转画布请求")
public class ChapterToCanvasReq {

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true)
    private String sessionId;

    /**
     * 章节ID
     */
    @NotBlank(message = "章节ID不能为空")
    @Schema(description = "章节ID", required = true)
    private String segmentId;
} 