package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 生成 Stable Audio 请求
 */
@Schema(description = "生成 Stable Audio 请求")
@Data
public class GenerateStableAudioReq {

    @Schema(description = "音频生成提示词", required = true, example = "128 BPM tech house drum loop")
    @NotBlank(message = "音频生成提示词不能为空")
    private String prompt;

    @Schema(description = "音频时长（秒）", example = "30", minimum = "1", maximum = "47")
    @Min(value = 1, message = "音频时长最少1秒")
    @Max(value = 47, message = "音频时长最多47秒")
    private Integer duration = 30; // 默认30秒

    @Schema(description = "去噪步数", example = "100", minimum = "1", maximum = "1000")
    @Min(value = 1, message = "去噪步数最少1步")
    @Max(value = 1000, message = "去噪步数最多1000步")
    private Integer steps = 100; // 默认100步

    @Schema(description = "音频开始时间（秒）", example = "0", minimum = "0", maximum = "47")
    @Min(value = 0, message = "音频开始时间最少0秒")
    @Max(value = 47, message = "音频开始时间最多47秒")
    private Integer secondsStart = 0; // 默认从0秒开始

    /**
     * 内容id
     */
    @Schema(description = "内容ID", example = "1234567890")
    private String contentId;

    /**
     * 音频索引
     */
    @Schema(description = "音频索引", example = "1")
    private Integer index;

    /**
     * 会话id
     */
    @Schema(description = "会话ID", example = "1234567890")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;

    /**
     * 验证请求参数
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return prompt != null && !prompt.trim().isEmpty()
                && duration != null && duration >= 1 && duration <= 47
                && (steps == null || (steps >= 1 && steps <= 1000))
                && (secondsStart == null || (secondsStart >= 0 && secondsStart <= 47))
                && conversationId != null && !conversationId.trim().isEmpty();
    }

    /**
     * 获取格式化的描述
     *
     * @return 格式化描述
     */
    public String getDescription() {
        return String.format("Stable Audio 生成: '%s' (%d秒, %d步, 开始时间%d秒)",
                prompt, duration, steps != null ? steps : 100, secondsStart != null ? secondsStart : 0);
    }

    /**
     * 转换为 StableAudioRequest
     *
     * @return StableAudioRequest 对象
     */
    public com.wlink.agent.model.dto.StableAudioRequest toStableAudioRequest() {
        return new com.wlink.agent.model.dto.StableAudioRequest(
                prompt,
                steps != null ? steps : 100,
                duration,
                secondsStart != null ? secondsStart : 0
        );
    }
}
