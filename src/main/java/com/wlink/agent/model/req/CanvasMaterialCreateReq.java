package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增画布素材请求
 */
@Data
@Schema(description = "新增画布素材请求")
public class CanvasMaterialCreateReq {
    
    /**
     * 画布ID
     */
    @NotNull(message = "画布ID不能为空")
    @Schema(description = "画布ID", required = true)
    private Long canvasId;
    
    /**
     * 素材类型(1-图片,2-视频)
     */
    @NotNull(message = "素材类型不能为空")
    @Schema(description = "素材类型(1-图片,2-视频 3-音频)", required = true)
    private Integer materialType;
    
    /**
     * 素材URL
     */
    @NotNull(message = "素材URL不能为空")
    @Schema(description = "素材URL", required = true)
    private String materialUrl;
    
    /**
     * 素材名称
     */
    @Schema(description = "素材名称")
    private String materialName;
    
    /**
     * 素材描述
     */
    @Schema(description = "素材描述")
    private String materialDesc;


    /**
     * 素材时长
     */
    @Schema(description = "素材时长")
    private Long materialDuration;

}
