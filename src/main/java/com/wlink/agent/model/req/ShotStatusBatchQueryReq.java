package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 批量查询分镜状态请求
 */
@Data
@Schema(description = "批量查询分镜状态请求")
public class ShotStatusBatchQueryReq {
    
    /**
     * 分镜ID列表
     */
    @NotEmpty(message = "分镜ID列表不能为空")
    @Schema(description = "分镜ID列表", required = true)
    private List<Long> shotIds;
}
