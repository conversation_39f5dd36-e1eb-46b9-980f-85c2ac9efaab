package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "语音克隆确认请求")
public class VoiceCloneConfirmReq {
    
    @NotNull(message = "克隆记录ID不能为空")
    @Schema(description = "克隆记录ID", required = true, example = "123")
    private Long cloneRecordId;

    //试听文本
    @Schema(description = "试听文本", required = false, example = "你好，世界！")
    @NotBlank(message = "试听文本不能为空")
    private String text;
}
