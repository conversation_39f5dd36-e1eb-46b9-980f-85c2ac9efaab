package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 通用图片生成请求
 */
@Data
@Schema(description = "通用图片生成请求")
public class UniversalImageGenerationRequest {

    /**
     * 模型类型
     */
    @NotBlank(message = "模型类型不能为空")
    @Schema(description = "模型类型(kontext-pro, kontext-max)", required = true, example = "kontext-pro")
    private String modelType;

    /**
     * 提示词
     */
    @NotBlank(message = "提示词不能为空")
    @Schema(description = "生成提示词", required = true, example = "A beautiful sunset over the ocean")
    private String prompt;

    /**
     * 参考图片URL集合
     */
    @Schema(description = "参考图片URL集合", example = "[\"https://example.com/image1.jpg\", \"https://example.com/image2.jpg\"]")
    private List<String> imageUrls;

    /**
     * 生成的图片数量
     */
    @Min(value = 1, message = "生成图片数量不能小于1")
    @Max(value = 4, message = "生成图片数量不能大于4")
    @Schema(description = "生成的图片数量(1-4)", example = "1")
    private Integer numImages = 1;

    /**
     * 安全等级
     */
    @Schema(description = "安全等级(1-6，1最严格，6最宽松)", example = "2")
    private String safetyTolerance = "2";

    /**
     * 随机种子值
     */
    @Schema(description = "随机种子值(可选)", example = "12345")
    private Integer seed;

    /**
     * 图片尺寸比例
     */
    @Schema(description = "图片尺寸比例", example = "16:9")
    private String aspectRatio = "16:9";

    /**
     * 输出格式
     */
    @Schema(description = "输出格式(jpeg或png)", example = "jpeg")
    private String outputFormat = "jpeg";

    /**
     * 同步模式
     */
    @Schema(description = "同步模式(true为同步，false为异步)", example = "false")
    private Boolean syncMode = false;

    /**
     * 引导比例
     */
    @Min(value = 1, message = "引导比例不能小于1")
    @Max(value = 20, message = "引导比例不能大于20")
    @Schema(description = "引导比例(1-20)", example = "3.5")
    private Double guidanceScale = 3.5;
}
