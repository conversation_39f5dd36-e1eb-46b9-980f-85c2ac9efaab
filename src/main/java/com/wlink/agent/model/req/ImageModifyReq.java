package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "图片修改请求参数")
public class ImageModifyReq {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true, example = "sess_12345")
    private String conversationId;

    @NotNull(message = "内容类型不能为空")
    @Schema(description = "内容类型 (2-场景,3-角色,4-分镜)", required = true, example = "2")
    private Integer contentType;
    /**
     * 功能类型：edit（编辑）、redraw（重绘）、instant（一致性生成）
     */
    @NotBlank(message = "功能类型不能为空 edit（编辑）、redraw（重绘）、instant（一致性生成）")
    @Schema(description = "功能类型 edit（编辑）、redraw（重绘）、instant（一致性生成）", required = true, example = "edit")
    private String functionType;

    @NotBlank(message = "一级ID不能为空")
    @Schema(description = "一级ID", required = true, example = "primary_abc")
    private String primaryId;

    @Schema(description = "二级ID (可", example = "secondary_xyz")
    private String secondaryId; // 可

    @Schema(description = "需要修改的图片源URL", required = true, example = "http://example.com/source.jpg")
    private String sourceImageUrl;

    //参考角色图
    @Schema(description = "参考角色图 (可", example = "http://example.com/character1.jpg")
    private String characterImageUrl1;

    @Schema(description = "参考角色图 (可", example = "http://example.com/character2.jpg")
    private String characterImageUrl2;

    //参考场景图
    @Schema(description = "参考场景图 (可", example = "http://example.com/scene1.jpg")
    private String sceneImageUrl1;

    @NotBlank(message = "提示词不能为")
    @Schema(description = "图片修改的提示词", required = true, example = "make the background blue")
    private String prompt;
}
