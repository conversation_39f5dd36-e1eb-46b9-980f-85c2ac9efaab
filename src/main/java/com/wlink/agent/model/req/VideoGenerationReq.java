package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 视频生成请求
 */
@Data
@Schema(description = "视频生成请求")
public class VideoGenerationReq {

    /**
     * 分镜id
     */
    @NotNull(message = "分镜ID不能为空")
    @Schema(description = "分镜ID")
    private Long shotId;

    /**
     * 模型
     */
    @NotBlank(message = "模型不能为空")
    @Schema(description = "模型")
    private String model;

    /**
     * 生成提示词
     */
    @NotBlank(message = "生成提示词不能为空")
    @Schema(description = "生成提示词", required = true)
    private String prompt;
    
    /**
     * 首帧图片URL
     */
    @Schema(description = "首帧图片URL")
    private String firstFrameImage;
    
    /**
     * 尾帧图片URL
     */
    @Schema(description = "尾帧图片URL")
    private String lastFrameImage;
    
    /**
     * 分辨率
     */
    @Schema(description = "分辨率", required = true, example = "720p")
    private String resolution;
    
    /**
     * 视频比例
     */
    @Schema(description = "视频比例(如:16:9)", required = true, example = "16:9")
    private String ratio;
    
    /**
     * 视频时长(秒)
     */
    @Schema(description = "视频时长(秒)", required = true, example = "5")
    private Integer duration;
    
    /**
     * 帧率
     */
    @Schema(description = "帧率", required = true, example = "30")
    private Integer fps;
}
