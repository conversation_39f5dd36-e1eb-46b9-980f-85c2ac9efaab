package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(description = "MiniMax语音克隆上传请求")
public class MinimaxVoiceCloneUploadReq {
    
    @NotBlank(message = "OSS URL不能为空")
    @Schema(description = "OSS文件URL", required = true, example = "https://example.com/audio.mp3")
    private String ossUrl;
} 
