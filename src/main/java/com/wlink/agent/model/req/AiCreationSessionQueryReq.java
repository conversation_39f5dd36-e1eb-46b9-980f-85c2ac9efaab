package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;

@Data
@Schema(description = "AiCreationSessionQueryReq")
public class AiCreationSessionQueryReq {
    
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;
    
    @Schema(description = "状0-进行1-已完", example = "1")
    private Integer status;
} 
