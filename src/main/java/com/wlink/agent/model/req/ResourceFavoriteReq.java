package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 资源收藏请求
 */
@Data
@Schema(description = "资源收藏请求")
public class ResourceFavoriteReq {

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true, example = "session_123456")
    private String sessionId;

    /**
     * 资源ID
     */
    @NotBlank(message = "资源ID不能为空")
    @Schema(description = "资源ID", required = true, example = "resource_123456")
    private String resourceId;

    /**
     * 资源类型
     */
    @NotNull(message = "资源类型不能为空")
    @Schema(description = "资源类型(1-角色,2-图片,3-视频,4-音频,5-文本)", required = true, example = "2")
    private Integer resourceType;

    /**
     * 资源子类
     */
    @Schema(description = "资源子类对图1-角色图片,2-场景图片,3-分镜图片)", example = "1")
    private Integer resourceSubtype;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称", example = "美丽风景")
    private String resourceName;

    /**
     * 资源URL
     */
    @Schema(description = "资源URL", example = "https://example.com/image.jpg")
    private String resourceUrl;

    /**
     * 资源数据(JSON格式)
     */
    @Schema(description = "资源数据(JSON格式)", example = "{\"width\": 1920, \"height\": 1080}")
    private String resourceData;
} 
