package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 豆包图像编辑请求
 */
@Data
@Schema(description = "豆包图像编辑请求")
public class DoubaoImageEditReq {

    @Schema(description = "分镜ID", required = true, example = "123456789")
    @NotNull(message = "分镜ID不能为空")
    private Long shotId;

    @Schema(description = "需要编辑的图像URL或Base64编码", required = true, example = "https://example.com/original.jpg")
    @NotBlank(message = "图像不能为空")
    private String image;

    @Schema(description = "编辑提示词", required = true, example = "把图中的红色汽车改成蓝色")
    @NotBlank(message = "编辑提示词不能为空")
    private String prompt;

    @Schema(description = "模型名称", example = "doubao-seededit-3-0-i2i-250628")
    private String model = "doubao-seededit-3-0-i2i-250628";

    @Schema(description = "响应格式", example = "url")
    private String responseFormat = "url";

    @Schema(description = "图片尺寸", example = "adaptive")
    private String size = "adaptive";

    @Schema(description = "随机种子", example = "-1")
    private Integer seed = -1;

    @Schema(description = "引导尺度", example = "5.5")
    private Double guidanceScale = 5.5;

    @Schema(description = "是否添加水印", example = "true")
    private Boolean watermark = true;
}
