package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量移除资源收藏请求
 */
@Data
@Schema(description = "批量移除资源收藏请求")
public class BatchRemoveFavoriteReq {

    /**
     * 资源编码列表
     */
    @NotEmpty(message = "资源编码列表不能为空")
    @Schema(description = "资源编码列表", required = true, example = "[")
    private List<Long> resourceIds;
} 
