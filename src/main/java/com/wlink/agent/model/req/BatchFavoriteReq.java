package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量资源收藏请求
 */
@Data
@Schema(description = "批量资源收藏请求")
public class BatchFavoriteReq {

    /**
     * 资源ID列表
     */
    @NotEmpty(message = "资源ID列表不能为空")
    @Schema(description = "资源ID列表", required = true, example = "[123, 456, 789]")
    private List<Long> resourceIds;

    /**
     * 资源类型
     */
    @NotNull(message = "资源类型不能为空")
    @Schema(description = "资源类型(2-图片,3-视频)", required = true, example = "2")
    private Integer resourceType;
} 
