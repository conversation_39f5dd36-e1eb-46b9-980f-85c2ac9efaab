package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 更新视觉记录发布状态请求DTO
 */
@Data
@Schema(description = "UpdateVisualRecordStatusReq")
public class UpdateVisualRecordStatusReq {

    @NotNull(message = "视觉记录ID不能为空")
    @Schema(description = "视觉记录ID", required = true, example = "123")
    private Long visualRecordId;

    @NotNull(message = "发布状态不能为空")
    @Schema(description = "发布状态", required = true, example = "true")
    private Boolean publish;

} 
