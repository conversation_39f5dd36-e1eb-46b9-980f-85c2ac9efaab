package com.wlink.agent.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(description = "MiniMax语音克隆请求")
public class MinimaxVoiceCloneReq {
    
    @NotBlank(message = "OSS URL不能为空")
    @Schema(description = "OSS文件URL", required = true, example = "https://example.com/audio.mp3")
    private String ossUrl;
    
    @NotBlank(message = "声音名称")
    @Schema(description = "声音名称", required = true, example = "test1234")
    @JSONField(name = "name")
    private String name;

    //性别
    @Schema(description = "声音性别 1- 2-, required = true, example = ", required = true, example = "1")
    @JSONField(name = "sex")
    private Integer sex;
} 
