package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 生成音效请求
 */
@Schema(description = "生成音效请求")
@Data
public class GenerateSoundEffectReq {

    @Schema(description = "音效描述提示词", required = true, example = "dog barking in the rain")
    @NotBlank(message = "音效描述不能为空")
    private String prompt;

    @Schema(description = "音效时长（秒）", example = "30", minimum = "1", maximum = "30")
    @Min(value = 1, message = "音效时长最少1秒")
    @Max(value = 30, message = "音效时长最多30秒")
    private Integer duration = 30; // 默认30秒


    /**
     * 内容id
     */
    @Schema(description = "内容ID", example = "1234567890")
    private String contentId;


    /**
     * 音频索引
     */
    @Schema(description = "音频索引", example = "1")
    private Integer index;

    /**
     * 会话id
     */
    @Schema(description = "会话ID", example = "1234567890")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;
}
