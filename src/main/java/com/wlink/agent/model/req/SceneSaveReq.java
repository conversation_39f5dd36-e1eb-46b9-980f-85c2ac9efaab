package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/21 14:36
 */
@NoArgsConstructor
@Data
public class SceneSaveReq {

    @NotEmpty(message = "scenes不能为空")
    @JsonProperty("scenes")
    private List<ScenesDTO> scenes;

    @NoArgsConstructor
    @Data
    public static class ScenesDTO {
        @JsonProperty("ID")
        private String id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("image")
        private String image;
        @JsonProperty("description")
        private String description;
    }
}
