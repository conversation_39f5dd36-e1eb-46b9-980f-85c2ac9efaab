package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
@Schema(description = "任务进度保存请求")
public class TaskProgressSaveReq {

    @JsonProperty("project")
    private ProjectDTO project;
    @JsonProperty("todo")
    private List<TodoDTO> todo;
    @JsonProperty("chapters")
    private List<ChaptersDTO> chapters;
    @JsonProperty("note")
    private String note;


    @NoArgsConstructor
    @Data
    public static class ProjectDTO {
        @JsonProperty("taskId")
        private String taskId;
        @JsonProperty("stage")
        private StageDTO stage;

        @NoArgsConstructor
        @Data
        public static class StageDTO {
            @JsonProperty("current")
            private String current;
            @JsonProperty("next")
            private String next;
            @JsonProperty("progress")
            private ProgressDTO progress;

            @NoArgsConstructor
            @Data
            public static class ProgressDTO {
                @JsonProperty("completed")
                private Integer completed;
                @JsonProperty("format")
                private String format;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class TodoDTO {
        @JsonProperty("task")
        private String task;
        @JsonProperty("status")
        private String status;
        @JsonProperty("completedAt")
        private String completedAt;
        @JsonProperty("dependency")
        private String dependency;
    }

    @NoArgsConstructor
    @Data
    public static class ChaptersDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("status")
        private String status;
        @JsonProperty("scenes")
        private List<ScenesDTO> scenes;

        @NoArgsConstructor
        @Data
        public static class ScenesDTO {
            @JsonProperty("id")
            private String id;
            @JsonProperty("status")
            private String status;
            @JsonProperty("shots")
            private List<ShotsDTO> shots;

            @NoArgsConstructor
            @Data
            public static class ShotsDTO {
                @JsonProperty("id")
                private String id;
                @JsonProperty("status")
                private String status;
            }
        }
    }
}
