package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/21 14:36
 */
@NoArgsConstructor
@Data
public class RoleSaveReq {

    @NotEmpty(message = "characters不能为空")
    @JsonProperty("characters")
    private List<CharactersDTO> characters;

    @NoArgsConstructor
    @Data
    public static class CharactersDTO {
        @JsonProperty("name")
        private String name;
        @JsonProperty("image")
        private String image;
        @JsonProperty("charID")
        private String charID;
        @JsonProperty("description")
        private String description;
        @JsonProperty("background")
        private String background;
        @JsonProperty("voice_id")
        private String voiceId;
        //图片生成状
        @JsonProperty("imageStatus")
        private String imageStatus;
    }
}
