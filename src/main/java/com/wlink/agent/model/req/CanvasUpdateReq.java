package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 画布更新请求
 */
@Data
@Schema(description = "画布更新请求")
public class CanvasUpdateReq {
    
    /**
     * 画布ID
     */
    @NotNull(message = "画布ID不能为空")
    @Schema(description = "画布ID", required = true)
    private Long canvasId;
    
    /**
     * 画布名称
     */
    @Schema(description = "画布名称")
    private String canvasName;
    
    /**
     * 画布封面图片URL
     */
    @Schema(description = "画布封面图片URL")
    private String coverImage;


    /**
     * 画布宽高比
     */
    @Schema(description = "画布宽高比")
    private String ratio;
} 