package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 根据会话ID查询声音列表请求
 */
@Schema(description = "根据会话ID查询声音列表请求")
@Data
public class SessionSoundQueryReq {

    @Schema(description = "会话ID", required = true, example = "session_123456")
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;
}
