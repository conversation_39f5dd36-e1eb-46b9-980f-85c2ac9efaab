package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.math.BigDecimal;

/**
 * 分镜音量修改请求
 */
@Schema(description = "分镜音量修改请求")
@Data
public class ShotVolumeUpdateReq {

    /**
     * 分镜ID
     */
    @Schema(description = "分镜ID", required = true, example = "123456789")
    @NotNull(message = "分镜ID不能为空")
    private Long shotId;

    /**
     * 类型：audio-音频，video-视频，soundEffect-音效
     */
    @Schema(description = "类型", required = true, example = "audio", allowableValues = {"audio", "video", "soundEffect"})
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * 音频ID（当type为audio且只修改某一个音频时需要传入）
     */
    @Schema(description = "音频ID（当type为audio且只修改某一个音频时需要传入）", example = "123456789")
    private Long audioId;

    /**
     * 音量值（0.00-1.50）
     */
    @Schema(description = "音量值", required = true, example = "0.80", minimum = "0.00", maximum = "1.50")
    @NotNull(message = "音量值不能为空")
    @DecimalMin(value = "0.00", message = "音量值不能小于0.00")
    @DecimalMax(value = "1.50", message = "音量值不能大于1.50")
    private BigDecimal volume;
}
