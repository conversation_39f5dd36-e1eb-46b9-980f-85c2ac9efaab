package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 修改定制声音名称请求
 */
@Schema(description = "修改定制声音名称请求")
@Data
public class UpdateSoundNameReq {

    @Schema(description = "声音ID", required = true, example = "123456789")
    @NotNull(message = "声音ID不能为空")
    private Long soundId;

    @Schema(description = "新的声音名称", required = true, example = "我的专属声音")
    @NotBlank(message = "声音名称不能为空")
    @Size(max = 100, message = "声音名称长度不能超过100个字符")
    private String name;

}
