package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 画布素材分页查询请求
 */
@Data
@Schema(description = "画布素材分页查询请求")
public class CanvasMaterialPageReq {
    
    /**
     * 画布ID
     */
    @NotNull(message = "画布ID不能为空")
    @Schema(description = "画布ID", required = true)
    private Long canvasId;
    
    /**
     * 素材类型(1-图片,2-视频)
     */
    @Schema(description = "素材类型(1-图片,2-视频)")
    private Integer materialType;
    
    /**
     * 素材来源(1-生成,2-上传)
     */
    @Schema(description = "素材来源(1-生成,2-上传)")
    private Integer materialSource;
    
    /**
     * 素材名称关键词
     */
    @Schema(description = "素材名称关键词")
    private String keyword;
    
    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    private Integer pageSize = 20;
    
    /**
     * 排序字段(create_time-创建时间)
     */
    @Schema(description = "排序字段(create_time-创建时间)")
    private String orderBy = "create_time";
    
    /**
     * 排序方向(asc-升序,desc-降序)
     */
    @Schema(description = "排序方向(asc-升序,desc-降序)")
    private String orderDirection = "desc";
}
