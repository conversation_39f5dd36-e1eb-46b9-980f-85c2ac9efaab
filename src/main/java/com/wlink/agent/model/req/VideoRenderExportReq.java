package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;

/**
 * 视频渲染导出请求
 */
@Schema(description = "视频渲染导出请求")
@Data
public class VideoRenderExportReq {

    @Schema(description = "画布ID", required = true, example = "123456789")
    @NotNull(message = "画布ID不能为空")
    private Long canvasId;

    @Schema(description = "分辨率", required = true, example = "470p", allowableValues = {"470p", "720p", "1080p"})
    @NotBlank(message = "分辨率不能为空")
    private String resolution;

    /**
     * 视频帧率
     */
    @Schema(description = "视频帧率", required = true, example = "24")
    private Integer fps = 24;


    @Schema(description = "是否显示字幕", example = "1", allowableValues = {"0", "1"})
    private Integer showSubtitle = 0; // 默认不显示字幕
}
