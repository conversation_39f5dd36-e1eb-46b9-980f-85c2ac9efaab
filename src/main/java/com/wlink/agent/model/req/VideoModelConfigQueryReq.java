package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 视频模型配置查询请求
 */
@Data
@Schema(description = "视频模型配置查询请求")
public class VideoModelConfigQueryReq {
    
    /**
     * 提供商
     */
    @Schema(description = "提供商(MINIMAX, DOUBAO)", example = "MINIMAX")
    private String provider;
    
    /**
     * 模型类型
     */
    @Schema(description = "模型类型(T2V-文生视频, I2V-图生视频, FLF2V-首尾帧图生视频)", example = "T2V")
    private String modelType;
    
    /**
     * 是否支持首帧
     */
    @Schema(description = "是否支持首帧(true-支持, false-不支持)")
    private Boolean supportFirstFrame;
    
    /**
     * 是否支持尾帧
     */
    @Schema(description = "是否支持尾帧(true-支持, false-不支持)")
    private Boolean supportLastFrame;
    
    /**
     * 最小积分成本
     */
    @Schema(description = "最小积分成本", example = "0")
    private Integer minPointsCost;
    
    /**
     * 最大积分成本
     */
    @Schema(description = "最大积分成本", example = "100")
    private Integer maxPointsCost;
    
    /**
     * 关键词搜索（模型名称或描述）
     */
    @Schema(description = "关键词搜索（模型名称或描述）", example = "海螺")
    private String keyword;
}
