package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 分镜音频新增请求
 */
@Data
@Schema(description = "分镜音频新增请求")
public class ShotAudioCreateReq {
    
    /**
     * 分镜ID
     */
    @NotNull(message = "分镜ID不能为空")
    @Schema(description = "分镜ID", required = true)
    private Long shotId;
    
    /**
     * 音频URL
     */
    @Schema(description = "音频URL")
    private String audioUrl;
    
    /**
     * 音频类型(1-旁白,2-对话,3-背景音乐)
     */
    @Schema(description = "音频类型(1-旁白,2-音效,3-背景音乐)")
    private Integer audioType;
    
    /**
     * 音频文本，可能是旁白或对话
     */
    @Schema(description = "音频文本")
    private String text;


    /**
     * 音效生成prompt
     */
    @Schema(description = "音效生成prompt")
    private String prompt;
    
    /**
     * 声音ID
     */
    @Schema(description = "声音ID")
    private String voiceId;
    
    /**
     * 音频时长(毫秒)
     */
    @Schema(description = "音频时长(毫秒)")
    private Long audioDuration;
    
    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;


    @Schema(description = "语范围[0.5,2]，默认值为1.0", required = false, example = "1.0")
    private String speed;

    @Schema(description = "音调 范围[-12,12]，默认值为0", required = false, example = "0")
    private Integer pitch;

    @Schema(description = "音量 范围,10]，默认值为1.0", required = false, example = "1.0")
    private String vol;

    @Schema(description = "[\"happy\", \"sad\", \"angry\", \"fearful\", \"disgusted\", \"surprised\", \"neutral\"]", required = false, example = "disgusted")
    private String emotion;

    @Schema(description = "[\"燕少飞/(yan4)(shao3)(fei1)\",\"达菲/(da2)(fei1)\"，\"omg/oh my god\"]", required = false, example = "[\"燕少飞/(yan4)(shao3)(fei1)\",\"达菲/(da2)(fei1)\"，\"omg/oh my god\"]")
    private List<String> tone;

    /**
     * 验证并规范化emotion参数
     * 如果值不在允许的范围内，则设置为null
     */
    public void setEmotion(String emotion) {
        if (emotion != null && !isValidEmotion(emotion)) {
            this.emotion = null;
        } else {
            this.emotion = emotion;
        }
    }

    /**
     * 检查emotion值是否有效
     */
    private boolean isValidEmotion(String emotion) {
        return "happy".equals(emotion) || "sad".equals(emotion) || "angry".equals(emotion) ||
               "fearful".equals(emotion) || "disgusted".equals(emotion) || "surprised".equals(emotion) ||
               "neutral".equals(emotion);
    }

}
