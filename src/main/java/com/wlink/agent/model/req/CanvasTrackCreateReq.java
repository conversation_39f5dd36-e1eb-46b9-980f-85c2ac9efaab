package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建画布音轨请求
 */
@Schema(description = "创建画布音轨请求")
@Data
public class CanvasTrackCreateReq {

    @Schema(description = "画布ID", required = true, example = "123456789")
    @NotNull(message = "画布ID不能为空")
    private Long canvasId;

    @Schema(description = "音轨名称", required = true, example = "背景音乐")
    @NotBlank(message = "音轨名称不能为空")
    private String trackName;

    @Schema(description = "音轨开始时间（毫秒）", example = "0")
    private Long startTime = 0L;

    @Schema(description = "音轨结束时间（毫秒）", example = "30000")
    private Long endTime;

    @Schema(description = "音轨类型", example = "1", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "音轨类型最小为1")
    @Max(value = 4, message = "音轨类型最大为4")
    private Integer trackType = 1; // 1-背景音乐，2-音效，3-旁白，4-对话

    @Schema(description = "音轨音量（0-100）", example = "100")
    @Min(value = 0, message = "音量最小为0")
    @Max(value = 100, message = "音量最大为100")
    private Integer volume = 100;

    @Schema(description = "音轨描述", example = "轻松的背景音乐")
    private String description;
}
