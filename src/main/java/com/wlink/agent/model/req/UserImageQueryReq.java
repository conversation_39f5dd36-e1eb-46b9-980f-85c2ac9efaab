package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 用户图片记录查询请求
 */
@Data
@Schema(description = "用户图片记录查询请求")
public class UserImageQueryReq {

    @Schema(description = "类型: 1-全部 2-收藏的图3-角色 4-分镜", required = true, example = "1")
    @NotNull(message = "类型不能为空")
    @Min(value = 1, message = "类型值最小为1")
    @Max(value = 4, message = "类型值最大为4")
    private Integer type;

    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    private String userId;

    @Schema(description = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum = 1;

    @Schema(description = "每页记录, required = true, example = ", required = true, example = "10")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize = 10;
} 
