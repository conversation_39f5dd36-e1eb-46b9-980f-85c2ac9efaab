package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 分镜顺序更新请求
 */
@Data
@Schema(description = "分镜顺序更新请求")
public class ShotOrderUpdateReq {
    
    /**
     * 画布ID
     */
    @NotNull(message = "画布ID不能为空")
    @Schema(description = "画布ID", required = true)
    private Long canvasId;
    
    /**
     * 分镜顺序对象集合
     */
    @NotEmpty(message = "分镜顺序列表不能为空")
    @Valid
    @Schema(description = "分镜顺序对象集合", required = true)
    private List<ShotOrderItem> shotOrders;
    
    /**
     * 分镜顺序项
     */
    @Data
    @Schema(description = "分镜顺序项")
    public static class ShotOrderItem {
        
        /**
         * 分镜ID
         */
        @NotNull(message = "分镜ID不能为空")
        @Schema(description = "分镜ID", required = true)
        private Long id;
        
        /**
         * 分镜顺序索引
         */
        @NotNull(message = "分镜顺序索引不能为空")
        @Schema(description = "分镜顺序索引", required = true)
        private Integer sortOrder;
    }
}
