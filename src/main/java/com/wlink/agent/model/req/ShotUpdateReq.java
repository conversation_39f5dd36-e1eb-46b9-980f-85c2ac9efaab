package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 分镜更新请求
 */
@Data
@Schema(description = "分镜更新请求")
public class ShotUpdateReq {


    @JsonProperty("shotId")
    private Long shotId;
    @JsonProperty("id")
    private String id;
    @JsonProperty("type")
    private String type;
    @JsonProperty("movement")
    private String movement;
    @JsonProperty("composition")
    private String composition;
    @JsonProperty("image")
    private String image;
    @JsonProperty("imageStatus")
    private String imageStatus;
    @JsonProperty("voice")
    private String voice;
    //音频时长
    @JsonProperty("duration")
    private Long duration;
    @JsonProperty("characters")
    private List<String> characters;
    @JsonProperty("lines")
    private List<String> lines;
    @JsonProperty("narration")
    private String narration;

} 
