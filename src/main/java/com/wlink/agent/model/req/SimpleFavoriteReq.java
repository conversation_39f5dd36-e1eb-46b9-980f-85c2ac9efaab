package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 简化的资源收藏请求
 */
@Data
@Schema(description = "简化的资源收藏请求")
public class SimpleFavoriteReq {

    /**
     * 资源ID
     */
    @NotNull(message = "资源ID不能为空")
    @Schema(description = "资源ID", required = true, example = "123")
    private Long resourceId;

    /**
     * 资源类型
     */
    @NotNull(message = "资源类型不能为空")
    @Schema(description = "资源类型(2-图片,3-视频)", required = true, example = "2")
    private Integer resourceType;
} 
