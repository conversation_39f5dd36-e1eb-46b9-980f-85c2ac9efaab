package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 根据章节ID查询会话设计内容请求
 */
@Schema(description = "根据章节ID查询会话设计内容请求")
@Data
public class ChapterDesignContentQueryReq {

    @Schema(description = "章节ID", required = true, example = "123456789")
    @NotNull(message = "章节ID不能为空")
    private Long chapterId;
}
