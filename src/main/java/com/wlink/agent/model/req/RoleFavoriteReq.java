package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 角色收藏请求
 */
@Data
@Schema(description = "角色收藏请求")
public class RoleFavoriteReq {

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true, example = "session_123456")
    private String sessionId;

    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    @Schema(description = "角色ID", required = true, example = "role_123456")
    private String roleId;
} 
