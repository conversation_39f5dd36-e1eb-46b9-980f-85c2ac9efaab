package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 图片生成请求
 */
@Data
@Schema(description = "图片生成请求")
public class ImageGenerateReq {
    
    /**
     * 生成提示词
     */
    @NotBlank(message = "生成提示词不能为空")
    @Schema(description = "生成提示词", required = true)
    private String prompt;
    
    /**
     * 参考图片URL
     */
    @Schema(description = "参考图片URL")
    private String referenceImageUrl;
    
    /**
     * 图片比例
     */
    @Schema(description = "图片比例", example = "16:9")
    private String aspectRatio = "16:9";
    
    /**
     * 生成强度(0.1-1.0)
     */
    @Schema(description = "生成强度(1-10)", example = "7.5")
    private Float strength =7.5f;

    /**
     * 分镜ID
     */
    @Schema(description = "分镜ID")
    private Long shotId;
}
