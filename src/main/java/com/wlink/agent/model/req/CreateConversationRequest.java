package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Schema(description = "创建AI创作会话请求") // 添加类注
@Data
public class CreateConversationRequest {
    @NotBlank(message = "prompt不能为空")
    @Schema(description = "会话提示语", required = true, example = "请生成一个关于“AI创作”的图片")
    private String prompt;

    @Schema(description = "关联的声音ID (可", example = "123456789")
    private Long soundId;

    //图片风格id
    @Schema(description = "图片风格ID", example = "123456789")
    private Long imageStyleId;

    //图片尺寸
    @Schema(description = "图片尺寸", example = "9:16")
    private String imageSize;


    //图片模型
    @Schema(description = "图片模型  DOUBAO，FLUX", example = "DOUBAO")
    private String imageModel;

    //图片URL集合
    @Schema(description = "图片URL集合", example = "[\"https://example.com/image1.jpg\", \"https://example.com/image2.jpg\"]")
    private List<String> imageUrls;

}
