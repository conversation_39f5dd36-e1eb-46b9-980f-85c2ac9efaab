package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 分镜图片编辑请求
 */
@Schema(description = "分镜图片编辑请求")
@Data
public class ShotImageEditReq {

    @Schema(description = "分镜ID", required = true, example = "123456789")
    @NotNull(message = "分镜ID不能为空")
    private Long shotId;

    @Schema(description = "原图URL", required = true, example = "https://example.com/original.jpg")
    @NotBlank(message = "原图URL不能为空")
    private String originalImageUrl;

    @Schema(description = "遮罩图URL", required = true, example = "https://example.com/mask.jpg")
    @NotBlank(message = "遮罩图URL不能为空")
    private String maskImageUrl;

    @Schema(description = "编辑提示词", required = true, example = "把图中红色部位替换成一顶嘻哈头巾和一副蓝色半透明眼镜，与背景融合，光影协调")
    @NotBlank(message = "编辑提示词不能为空")
    private String prompt;
}
