package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 内容更新请求实体
 */
@Data
@Schema(description = "内容更新请求")
public class ContentUpdateReq {

    @Schema(description = "会话ID", required = true)
    private String conversationId;

    @Schema( required = true, example = "1", allowableValues = "1,2,3,4,5,10,11")
    private Integer contentType;

    @Schema(description = "内容数据", required = true)
    private Object contentData;
} 
