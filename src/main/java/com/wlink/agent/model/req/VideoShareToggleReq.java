package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 视频分享切换请求
 */
@Data
@Schema(description = "视频分享切换请求")
public class VideoShareToggleReq {

    @Schema(description = "任务ID", required = true, example = "123")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    @Schema(description = "分享状态：true-分享，false-取消分享", required = true, example = "true")
    @NotNull(message = "分享状态不能为空")
    private Boolean share;
}
