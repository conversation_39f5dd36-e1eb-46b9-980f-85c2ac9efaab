package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/21 14:36
 */
@NoArgsConstructor
@Data
public class ShotSaveReq {

    @NotEmpty(message = "shotGroups不能为空")
    @JsonProperty("shotGroups")
    private List<ShotGroupsDTO> shotGroups;


    @NoArgsConstructor
    @Data
    public static class ShotGroupsDTO {
        @JsonProperty("scene_id")
        private String sceneId;
        @JsonProperty("sceneName")
        private String sceneName;
        @JsonProperty("segment_id")
        private String segmentId;
        @JsonProperty("segment_name")
        private String segmentName;
        @JsonProperty("totalShots")
        private Integer totalShots;
        @JsonProperty("shots")
        private List<ShotsDTO> shots;

        @NoArgsConstructor
        @Data
        public static class ShotsDTO {
            @JsonProperty("shotId")
            private Long shotId;
            @JsonProperty("id")
            private String id;
            @JsonProperty("type")
            private String type;
            @JsonProperty("movement")
            private String movement;
            @JsonProperty("composition")
            private String composition;
            @JsonProperty("image")
            private String image;
            @JsonProperty("imageStatus")
            private String imageStatus;
            @JsonProperty("voice")
            private String voice;
            //音频时长
            @JsonProperty("duration")
            private Long duration;
            @JsonProperty("characters")
            private List<String> characters;
            @JsonProperty("line_list")
            private List<ShotLines> lineList;
            @JsonProperty("narration")
            private String narration;
            @JsonProperty("image_info")
            private String imageInfo;
            @JsonProperty("queue")
            private String queue;
        }
    }
    @NoArgsConstructor
    @Data
    public static class ShotLines {
        @JsonProperty("name")
        private String name;
        @JsonProperty("line")
        private String line;
        @JsonProperty("charID")
        private String charID;
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("voice")
        private String voice;
    }
}
