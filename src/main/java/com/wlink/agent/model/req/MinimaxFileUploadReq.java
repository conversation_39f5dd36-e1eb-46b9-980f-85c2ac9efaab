package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(description = "MiniMax文件上传请求")
public class MinimaxFileUploadReq {
    
    @NotBlank(message = "OSS URL不能为空")
    @Schema(description = "OSS文件URL", required = true, example = "https://example.com/file.wav")
    private String ossUrl;
    
    @NotBlank(message = "purpose不能为空")
    @Schema(description = "文件用, required = true, example = ", required = true, example = "retrieval")
    private String purpose;
} 
