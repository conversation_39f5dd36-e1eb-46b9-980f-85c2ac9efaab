package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 画布背景音乐请求
 */
@Schema(description = "画布背景音乐请求")
@Data
public class CanvasBackgroundMusicReq {

    @Schema(description = "画布ID", required = true, example = "123456789")
    @NotNull(message = "画布ID不能为空")
    private Long canvasId;

    @Schema(description = "音频地址", example = "https://example.com/background.mp3")
    private String audioUrl;

    @Schema(description = "音频名称", example = "背景音乐.mp3")
    private String name;

    @Schema(description = "音频时长（毫秒）", example = "180000")
    private Long audioDuration;

    @Schema(description = "开始播放时间（毫秒）", example = "0")
    private Long startTime = 0L;

    @Schema(description = "结束播放时间（毫秒）", example = "180000")
    private Long endTime;

    @Schema(description = "音轨开始时间（毫秒）", example = "0")
    private Long startTrackTime = 0L;

    @Schema(description = "音量（0-1）", example = "0.4")
    private Double volume ;

    @Schema(description = "淡入时间（毫秒）", example = "2000")
    private Long fadeInTime = 0L;

    @Schema(description = "淡出时间（毫秒）", example = "2000")
    private Long fadeOutTime = 0L;

    @Schema(description = "是否循环播放（0-否，1-是）", example = "1")
    @Min(value = 0, message = "循环播放标识最小为0")
    @Max(value = 1, message = "循环播放标识最大为1")
    private Integer isLoop = 0;

    @Schema(description = "音频格式", example = "mp3")
    private String audioFormat;

    @Schema(description = "音频文件大小（字节）", example = "5242880")
    private Long fileSize;

    @Schema(description = "音频来源", example = "1", allowableValues = {"1", "2", "3"})
    @Min(value = 1, message = "音频来源最小为1")
    @Max(value = 3, message = "音频来源最大为3")
    private Integer audioSource = 1; // 1-上传，2-AI生成，3-素材库

    @Schema(description = "音频描述", example = "轻松愉快的背景音乐")
    private String description;
}
