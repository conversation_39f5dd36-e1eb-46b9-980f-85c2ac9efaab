package com.wlink.agent.model.req;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@Schema(description = "聊天消息请求(用于SSE)")
@Data
public class ChatMessageRequest {
    @Schema(description = "输入参数，具体内容取决于应用场景", example = "{\"key\":\"value\"}")
    private JSONObject inputs = new JSONObject();

    @Schema(description = "用户查询语句", required = true, example = "你好")
    private String query;
    
    @NotBlank(message = "response_mode")
    @Schema(description = "响应模式（例如'streaming'）", required = true, example = "streaming")
    private String response_mode;
    
    @NotBlank(message = "conversation_id不能为空")
    @Schema(description = "Dify会话ID (如果已有会话)", example = "conv_12345")
    private String conversation_id = "";

    @Schema(description = "用户标识", required = true, example = "<EMAIL>")
    private String user;

    @Schema(description = "文件集合", example = "[{\"type\":\"image\",\"transfer_method\":\"remote_url\",\"url\":\"https://cloud.dify.ai/logo/logo-site.png\"}]")
    private List<FileInfo> files;

    /**
     * 文件信息内部类
     */
    @Schema(description = "文件信息")
    @Data
    public static class FileInfo {
        @Schema(description = "文件类型", example = "image")
        private String type;

        @JSONField(name = "transfer_method")
        @Schema(description = "传输方式", example = "remote_url")
        private String transferMethod;

        @Schema(description = "文件URL", example = "https://cloud.dify.ai/logo/logo-site.png")
        private String url;
    }
}
