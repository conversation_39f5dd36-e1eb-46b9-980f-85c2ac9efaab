package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建音轨音频请求
 */
@Schema(description = "创建音轨音频请求")
@Data
public class TrackAudioCreateReq {

    @Schema(description = "音轨ID", required = true, example = "123456789")
    @NotNull(message = "音轨ID不能为空")
    private Long trackId;

    @Schema(description = "音频名称", required = true, example = "背景音乐.mp3")
    @NotBlank(message = "音频名称不能为空")
    private String audioName;

    @Schema(description = "音频地址", required = true, example = "https://example.com/audio.mp3")
    @NotBlank(message = "音频地址不能为空")
    private String audioUrl;

    @Schema(description = "音频开始播放时间（毫秒）", example = "0")
    private Long startPlayTime = 0L;

    @Schema(description = "音频结束播放时间（毫秒）", example = "30000")
    private Long endPlayTime;

    @Schema(description = "音频音量（0-100）", example = "100")
    @Min(value = 0, message = "音量最小为0")
    @Max(value = 100, message = "音量最大为100")
    private Integer volume = 100;

    @Schema(description = "音频时长（毫秒）", example = "30000")
    private Long duration;

    @Schema(description = "音频文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "音频格式", example = "mp3")
    private String audioFormat;

    @Schema(description = "淡入时间（毫秒）", example = "1000")
    private Long fadeInTime = 0L;

    @Schema(description = "淡出时间（毫秒）", example = "1000")
    private Long fadeOutTime = 0L;

    @Schema(description = "音频来源", example = "1", allowableValues = {"1", "2", "3"})
    @Min(value = 1, message = "音频来源最小为1")
    @Max(value = 3, message = "音频来源最大为3")
    private Integer audioSource = 1; // 1-上传，2-AI生成，3-素材库

    @Schema(description = "音频描述", example = "轻松的背景音乐")
    private String description;
}
