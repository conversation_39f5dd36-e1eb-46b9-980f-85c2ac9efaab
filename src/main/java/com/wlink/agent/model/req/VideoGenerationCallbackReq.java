package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 视频生成回调请求
 */
@Data
@Schema(description = "视频生成回调请求")
public class VideoGenerationCallbackReq {
    
    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    private String id;
    
    /**
     * 模型
     */
    @Schema(description = "模型")
    private String model;
    
    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;
    
    /**
     * 内容
     */
    @Schema(description = "内容")
    private Content content;
    
    /**
     * 用量
     */
    @Schema(description = "用量")
    private Usage usage;
    
    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    @Schema(description = "创建时间")
    private Long createdAt;
    
    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    @Schema(description = "更新时间")
    private Long updatedAt;
    
    /**
     * 内容
     */
    @Data
    public static class Content {
        /**
         * 视频URL
         */
        @JsonProperty("video_url")
        @Schema(description = "视频URL")
        private String videoUrl;
    }
    
    /**
     * 用量
     */
    @Data
    public static class Usage {
        /**
         * 完成token数
         */
        @JsonProperty("completion_tokens")
        @Schema(description = "完成token数")
        private Integer completionTokens;
        
        /**
         * 总token数
         */
        @JsonProperty("total_tokens")
        @Schema(description = "总token数")
        private Integer totalTokens;
    }
} 