package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "确认直播内容请求")
public class ConfirmLiveContentRequest {

    @NotBlank(message = "房间ID不能为空")
    @Schema(description = "房间ID")
    private String roomId;

    @NotNull(message = "节目单内容不能为空")
    @Schema(description = "节目单内容id")
    private Long id;
} 
