package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 视频渲染回调请求
 */
@Schema(description = "视频渲染回调请求")
@Data
public class VideoRenderCallbackReq {

    @Schema(description = "渲染任务ID", required = true, example = "render_task_12345")
    @NotBlank(message = "渲染任务ID不能为空")
    private String taskId;

    @Schema(description = "渲染状态", required = true, example = "2", allowableValues = {"1", "2", "3"})
    @NotNull(message = "渲染状态不能为空")
    private Integer status;

    @Schema(description = "错误原因", example = "渲染失败：内存不足")
    private String errorMessage;

    @Schema(description = "视频URL", example = "https://example.com/rendered_video.mp4")
    private String videoUrl;

    /**
     * 视频时长
     */
    @Schema(description = "视频时长(毫秒)", example = "60000")
    private Long videoDuration;
}
