package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "图片上传请求参数")
public class ImageUploadReq {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true, example = "sess_12345")
    private String conversationId;

    @NotNull(message = "内容类型不能为空")
    @Schema(description = "内容类型 (2-场景,3-角色,4-分镜)", required = true, example = "2")
    private Integer contentType;

    @NotBlank(message = "内容ID不能为空")
    @Schema(description = "内容ID", required = true, example = "content_abc")
    private String contentId;

    @NotBlank(message = "图片URL不能为空")
    @Schema(description = "上传的图片URL", required = true, example = "http://example.com/image.jpg")
    private String imageUrl;

    @Schema(description = "原始图片", example = "http://example.com/image.jpg")
    private String originalImageUrl;
} 
