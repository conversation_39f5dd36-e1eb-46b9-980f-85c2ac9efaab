package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/21 14:35
 */
@NoArgsConstructor
@Data
public class StorySaveReq {

    @NotEmpty(message = "chapters不能为空")
    @JsonProperty("chapters")
    private List<ChaptersDTO> chapters;

    @NoArgsConstructor
    @Data
    public static class ChaptersDTO {
        @JsonProperty("chapterID")
        private String chapterID;
        @JsonProperty("chapterTitle")
        private String chapterTitle;
        @JsonProperty("totalShots")
        private String totalShots;
        @JsonProperty("scenes")
        private List<ScenesDTO> scenes;

        @NoArgsConstructor
        @Data
        public static class ScenesDTO {
            @JsonProperty("id")
            private String id;
            @JsonProperty("shotsProgression")
            private List<String> shotsProgression;
            @JsonProperty("disc")
            private String disc;
            @JsonProperty("shots")
            private List<ShotsDTO> shots;

            @NoArgsConstructor
            @Data
            public static class ShotsDTO {
                @JsonProperty("shot")
                private Integer shot;
                @JsonProperty("summary")
                private String summary;
                @JsonProperty("line")
                private String line;
                @JsonProperty("narration")
                private String narration;
                @JsonProperty("shotsProgression")
                private String shotsProgression;
            }
        }
    }
}
