package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 音轨音频响应
 */
@Schema(description = "音轨音频响应")
@Data
public class TrackAudioRes {

    @Schema(description = "音频ID", example = "123456789")
    private Long id;

    @Schema(description = "音轨ID", example = "123456789")
    private Long trackId;

    @Schema(description = "音频名称", example = "背景音乐.mp3")
    private String audioName;

    @Schema(description = "音频地址", example = "https://example.com/audio.mp3")
    private String audioUrl;

    @Schema(description = "音频开始播放时间（毫秒）", example = "0")
    private Long startPlayTime;

    @Schema(description = "音频结束播放时间（毫秒）", example = "30000")
    private Long endPlayTime;

    @Schema(description = "音频音量（0-100）", example = "100")
    private Integer volume;

    @Schema(description = "音频时长（毫秒）", example = "30000")
    private Long duration;

    @Schema(description = "音频文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "格式化文件大小", example = "1.0 MB")
    private String fileSizeDesc;

    @Schema(description = "音频格式", example = "mp3")
    private String audioFormat;

    @Schema(description = "音频排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否静音", example = "0")
    private Integer muted;

    @Schema(description = "淡入时间（毫秒）", example = "1000")
    private Long fadeInTime;

    @Schema(description = "淡出时间（毫秒）", example = "1000")
    private Long fadeOutTime;

    @Schema(description = "音频来源", example = "1")
    private Integer audioSource;

    @Schema(description = "音频来源描述", example = "上传")
    private String audioSourceDesc;

    @Schema(description = "音频描述", example = "轻松的背景音乐")
    private String description;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 获取格式化文件大小
     */
    public String getFileSizeDesc() {
        if (fileSize == null || fileSize == 0) {
            return "未知大小";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    /**
     * 获取音频来源描述
     */
    public String getAudioSourceDesc() {
        if (audioSource == null) {
            return "未知";
        }
        return switch (audioSource) {
            case 1 -> "上传";
            case 2 -> "AI生成";
            case 3 -> "素材库";
            default -> "未知";
        };
    }
}
