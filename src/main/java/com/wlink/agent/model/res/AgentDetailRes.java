package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "智能体详情响应")
public class AgentDetailRes {

    @Schema(description = "智能体ID")
    private Long agentId;
    
    @Schema(description = "智能体编码")
    private String agentCode;
    
    @Schema(description = "智能体名称")
    private String name;
    
    @Schema(description = "智能体形象")
    private String imageUrl;

    @Schema(description = "智能体视频")
    private String videoUrl;

    @Schema(description = "声音信息")
    private SoundRes sound;

    @Schema(description = "背景介绍")
    private String background;

    @Schema(description = "说话风格")
    private String speakingStyle;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "性别(1-男，2-女)")
    private Integer gender;

    @Schema(description = "上传图片地址")
    private String inputPhoto;

    @Schema(description = "模板ID")
    private Long templateId;
    
    @Schema(description = "创建时间")
    private String createTime;
} 
