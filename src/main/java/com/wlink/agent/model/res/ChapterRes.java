package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 章节响应
 */
@Data
@Schema(description = "章节响应")
public class ChapterRes {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 章节ID
     */
    @Schema(description = "章节ID")
    private String segmentId;

    /**
     * 章节名称
     */
    @Schema(description = "章节名称")
    private String segmentName;

    /**
     * 场景数量
     */
    @Schema(description = "场景数量")
    private Integer sceneCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    /**
     * 总分镜数
     */
    @Schema(description = "总分镜数")
    private Integer totalShotCount = 0;
    
    /**
     * 已完成图片数
     */
    @Schema(description = "已完成图片数")
    private Integer completedImageCount = 0;
    
    /**
     * 已完成音频数
     */
    @Schema(description = "已完成音频数")
    private Integer completedVoiceCount = 0;
    
    /**
     * 已完成旁白数
     */
    @Schema(description = "已完成旁白数")
    private Integer completedNarrationCount = 0;
    
    /**
     * 是否全部完成（图片、音频、旁白都齐全
     */
    @Schema(description = "是否全部完成")
    private Boolean isAllCompleted = false;
    
    /**
     * 章节第一个分镜的图片
     */
    @Schema(description = "章节第一个分镜的图片")
    private String firstShotImage;
} 
