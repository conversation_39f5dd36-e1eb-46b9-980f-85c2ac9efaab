package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "房间应用信息响应")
public class RoomAppInfoRes {

    @Schema(description = "房间ID")
    private String roomId;

    @Schema(description = "应用信息列表")
    private List<AppDeviceInfo> appDeviceInfos;

    @Data
    @Schema(description = "应用信息")
    public static class AppDeviceInfo {
        @Schema(description = "应用ID")
        private Integer appId;

        @Schema(description = "应用名称")
        private String appName;

        @Schema(description = "应用路径")
        private String appPath;

        @Schema(description = "设备ID列表")
        private List<DeviceInfo> deviceInfos;
    }


    //设备信息
    @Data
    @Schema(description = "设备信息")
    public static class DeviceInfo {
        @Schema(description = "设备ID")
        private Integer Id;
        @Schema(description = "设备名称")
        private String serverName;
        @Schema(description = "设备类型")
        private String deviceType;
        @Schema()
        private String runningStatus;
        @Schema()
        private String onlineStatus;
        @Schema(description = "设备IP")
        private String serverIp;

    }

} 
