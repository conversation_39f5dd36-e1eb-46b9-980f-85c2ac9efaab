package com.wlink.agent.model.res;

import com.wlink.agent.model.res.ConversationMessageItem;
import io.swagger.v3.oas.annotations.media.Schema; // 新增导入
import lombok.Data;

import java.util.List;
@Data
@Schema(description = "Dify会话消息列表响应")
public class ConversationMessageListResponse {
    @Schema(description = "返回数量限制", example = "20")
    private int limit;

    @Schema(description = "是否还有更多数据", example = "false")
    private boolean has_more;

    @Schema(description = "会话消息列表数据")
    private List<ConversationMessageItem> data;

}
