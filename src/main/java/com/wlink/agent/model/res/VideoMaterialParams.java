package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频素材参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "视频素材参数")
public class VideoMaterialParams {
    
    /**
     * 生成提示词
     */
    @Schema(description = "生成提示词")
    private String prompt;
    
    /**
     * 首帧图片URL
     */
    @Schema(description = "首帧图片URL")
    private String firstFrameUrl;
    
    /**
     * 尾帧图片URL
     */
    @Schema(description = "尾帧图片URL")
    private String lastFrameUrl;
    
    /**
     * 视频分辨率
     */
    @Schema(description = "视频分辨率")
    private String resolution;
    
    /**
     * 视频时长(秒)
     */
    @Schema(description = "视频时长(毫秒)")
    private Long duration;
    
    /**
     * 视频比例
     */
    @Schema(description = "视频比例")
    private String ratio;
} 