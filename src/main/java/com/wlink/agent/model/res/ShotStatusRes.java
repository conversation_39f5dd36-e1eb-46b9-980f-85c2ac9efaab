package com.wlink.agent.model.res;

import com.wlink.agent.enums.ShotStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分镜状态响应
 */
@Data
@Schema(description = "分镜状态响应")
public class ShotStatusRes {
    
    /**
     * 分镜ID
     */
    @Schema(description = "分镜ID")
    private Long shotId;
    
    /**
     * 分镜状态(0-初始,1-处理中,2-已完成)
     */
    @Schema(description = "分镜状态(0-初始,1-处理中,2-已完成)")
    private Integer shotStatus;
    
    /**
     * 状态描述
     */
    @Schema(description = "状态描述")
    private String statusDesc;
    
    public ShotStatusRes() {}
    
    public ShotStatusRes(Long shotId, Integer shotStatus) {
        this.shotId = shotId;
        this.shotStatus = shotStatus;
        this.statusDesc = getStatusDescription(shotStatus);
    }
    
    /**
     * 获取状态描述
     */
    private String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知";
        }

        try {
            ShotStatus shotStatus = ShotStatus.fromValue(status);
            return shotStatus.getDescription();
        } catch (IllegalArgumentException e) {
            return "未知";
        }
    }
}
