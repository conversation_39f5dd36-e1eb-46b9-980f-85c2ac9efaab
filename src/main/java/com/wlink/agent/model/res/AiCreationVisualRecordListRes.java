package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * AI创作视觉记录响应DTO
 */
@Data
@Schema(description = "AiCreationVisualRecordRes")
public class AiCreationVisualRecordListRes {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "会话ID")
    private String sessionId;
    @Schema(description = "视觉记录编码")
    private String visualRecordCode;


    @Schema(description = "标题")
    private String title;

    @Schema(description = "封面图片URL")
    private String coverImage;

    @Schema(description = "副标描述")
    private String subtitle2;

    @Schema(description = "token")
    private String token;
    @Schema(description = "头像")
    private String avatar;


    //风格标签
    @Schema(description = "风格标签")
    private String styleName;

    //风格分类
    @Schema(description = "风格分类")
    private String styleCategory;

    //图片比例
    @Schema(description = "图片比例")
    private String imageSize;

    //昵称
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "发布时间")
    private Date updateTime;




} 
