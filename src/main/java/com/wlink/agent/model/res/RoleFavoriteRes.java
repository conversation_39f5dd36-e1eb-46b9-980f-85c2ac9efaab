package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 角色收藏响应
 */
@NoArgsConstructor
@Data
@Schema(description = "角色收藏响应")
public class RoleFavoriteRes {


    /**
     * 角色编码
     */
    @Schema(description = "角色编码", example = "role_123456")
    private String roleCode;
    /**
     * 角色ID
     */
    @Schema(description = "角色ID", example = "role_123456")
    private String roleId;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    /**
     * 收藏时间
     */
    @Schema(description = "收藏时间", example = "2023-06-01 12:00:00")
    private Date createTime;
    @Schema(description = "角色名称", example = "角色名称")
    @JsonProperty("name")
    private String name;
    @Schema(description = "角色图片", example = "角色图片")
    @JsonProperty("image")
    private String image;
    @Schema(description = "角色声音", example = "角色声音")
    @JsonProperty("voice_id")
    private String voiceId;
    @Schema(description = "角色背景", example = "角色背景")
    @JsonProperty("background")
    private String background;
    @Schema(description = "角色描述", example = "角色描述")
    @JsonProperty("description")
    private String description;
    @Schema(description = "角色图片状, example = ")
    @JsonProperty("imageStatus")
    private String imageStatus;
}
