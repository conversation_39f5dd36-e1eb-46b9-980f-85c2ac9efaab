package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "标签信息响应对象")
public class TagRes {

    @Schema(description = "故事名称")
    private String name;

    @Schema(description = "故事提示, example = ")
    private String prompt;

    @Schema(description = "标签描述")
    private String category;

}
