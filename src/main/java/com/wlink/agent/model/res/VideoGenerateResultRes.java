package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "视频生成结果响应")
public class VideoGenerateResultRes {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "任务状 1-生成 2-成功  3-失败")
    private Integer status;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "视频URL")
    private String url;

    @Schema(description = "封面URL")
    private String coverUrl;

} 
