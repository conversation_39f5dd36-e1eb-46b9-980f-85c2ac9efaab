package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 画布背景音乐响应
 */
@Schema(description = "画布背景音乐响应")
@Data
public class CanvasBackgroundMusicRes {

    @Schema(description = "背景音乐ID", example = "123456789")
    private Long id;

    @Schema(description = "画布ID", example = "123456789")
    private Long canvasId;

    @Schema(description = "音频地址", example = "https://example.com/background.mp3")
    private String audioUrl;

    @Schema(description = "音频名称", example = "背景音乐.mp3")
    private String name;

    @Schema(description = "音频时长（毫秒）", example = "180000")
    private Long audioDuration;

    @Schema(description = "格式化音频时长", example = "3分0秒")
    private String audioDurationDesc;

    @Schema(description = "开始播放时间（毫秒）", example = "0")
    private Long startTime;

    @Schema(description = "结束播放时间（毫秒）", example = "180000")
    private Long endTime;

    @Schema(description = "音轨开始时间（毫秒）", example = "0")
    private Long startTrackTime;

    @Schema(description = "音量（0-100）", example = "80")
    private Double volume;

    @Schema(description = "淡入时间（毫秒）", example = "2000")
    private Long fadeInTime;

    @Schema(description = "淡出时间（毫秒）", example = "2000")
    private Long fadeOutTime;

    @Schema(description = "是否循环播放（0-否，1-是）", example = "1")
    private Integer isLoop;

    @Schema(description = "循环播放描述", example = "是")
    private String isLoopDesc;

    @Schema(description = "音频格式", example = "mp3")
    private String audioFormat;

    @Schema(description = "音频文件大小（字节）", example = "5242880")
    private Long fileSize;

    @Schema(description = "格式化文件大小", example = "5.0 MB")
    private String fileSizeDesc;

    @Schema(description = "音频来源", example = "1")
    private Integer audioSource;

    @Schema(description = "音频来源描述", example = "上传")
    private String audioSourceDesc;

    @Schema(description = "音频描述", example = "轻松愉快的背景音乐")
    private String description;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 获取格式化音频时长
     */
    public String getAudioDurationDesc() {
        if (audioDuration == null || audioDuration == 0) {
            return "未知时长";
        }
        
        long seconds = audioDuration / 1000;
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, remainingSeconds);
        } else {
            return String.format("%d秒", remainingSeconds);
        }
    }

    /**
     * 获取格式化文件大小
     */
    public String getFileSizeDesc() {
        if (fileSize == null || fileSize == 0) {
            return "未知大小";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    /**
     * 获取循环播放描述
     */
    public String getIsLoopDesc() {
        if (isLoop == null) {
            return "否";
        }
        return isLoop == 1 ? "是" : "否";
    }

    /**
     * 获取音频来源描述
     */
    public String getAudioSourceDesc() {
        if (audioSource == null) {
            return "未知";
        }
        return switch (audioSource) {
            case 1 -> "上传";
            case 2 -> "AI生成";
            case 3 -> "素材库";
            default -> "未知";
        };
    }
}
