package com.wlink.agent.model.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "MiniMax语音克隆上传响应")
public class MinimaxVoiceCloneUploadRes {

    @Schema(description = "输入是否敏感")
    @JSONField(name = "input_sensitive")
    private Boolean inputSensitive;

    @Schema(description = "输入敏感类型")
    @JSONField(name = "input_sensitive_type")
    private Integer inputSensitiveType;

    @Schema(description = "演示音频")
    @JSONField(name = "demo_audio")
    private String demoAudio;

    @Schema(description = "克隆记录ID")
    @JSONField(name = "clone_record_id")
    private Long cloneRecordId;
}
