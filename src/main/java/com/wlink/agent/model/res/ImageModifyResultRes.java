package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "图片修改结果查询响应")
public class ImageModifyResultRes {

    @Schema(description = "记录ID", example = "12345")
    private Long id;

    @Schema(description = "本次修改操作的唯一编码", example = "snow_flake_id_string")
    private String modifyCode;

    @Schema(description = "会话ID", example = "sess_12345")
    private String sessionId;

    @Schema(description = "内容类型 (2-场景,3-角色,4-分镜)", example = "2")
    private Integer contentType;

    @Schema(description = "一级ID", example = "primary_abc")
    private String primaryId;

    @Schema(description = "二级ID", example = "secondary_xyz")
    private String secondaryId;

    @Schema(description = "用户ID", example = "user_123")
    private String userId;

    @Schema(description = "源图片URL", example = "http://example.com/source.jpg")
    private String sourceImageUrl;

    @Schema(description = "修改后的图片URL", example = "http://example.com/modified.jpg")
    private String modifiedImageUrl;

    // 假设 AiImageModifyRecordPo 中已添加 prompt 字段
    // @Schema(description = "图片修改的提示词", example = "make the background blue")
    // private String prompt;

    @Schema(description = "处理状(0:处理 1:成功, 2:失败)", example = "1")
    private Integer status;

    @Schema(description = "是否为当前使用的图片", example = "true")
    private Boolean isCurrentUsed;

    @Schema(description = "失败原因", example = "API call timeout")
    private String failureReason;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

}
