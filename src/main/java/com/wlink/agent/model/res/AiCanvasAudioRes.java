package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 画布音频资源响应
 */
@Data
@Schema(description = "画布音频资源响应")
public class AiCanvasAudioRes {
    
    /**
     * 音频资源ID
     */
    @Schema(description = "音频资源ID")
    private Long id;
    
    /**
     * 画布ID
     */
    @Schema(description = "画布ID")
    private Long canvasId;
    
    /**
     * 分镜编码
     */
    @Schema(description = "分镜编码")
    private String shotCode;
    
    /**
     * 音频URL
     */
    @Schema(description = "音频URL")
    private String audioUrl;
    
    /**
     * 音频类型(1-旁白,2-对话,3-背景音乐)
     */
    @Schema(description = "音频类型(1-旁白,2-对话,3-背景音乐)")
    private Integer audioType;
    
    /**
     * 音频文本，可能是旁白或对话
     */
    @Schema(description = "音频文本")
    private String text;
    
    /**
     * 声音ID
     */
    @Schema(description = "声音ID")
    private String voiceId;
    
    /**
     * 音频时长(毫秒)
     */
    @Schema(description = "音频时长(毫秒)")
    private Long audioDuration;
    
    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;

    /**
     * 音量(0.00-1.00)
     */
    @Schema(description = "音量(0.00-1.00)", example = "1.00")
    private BigDecimal volume;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
} 