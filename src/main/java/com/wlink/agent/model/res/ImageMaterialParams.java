package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片素材参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "图片素材参数")
public class ImageMaterialParams {
    
    /**
     * 生成提示词
     */
    @Schema(description = "生成提示词")
    private String prompt;
    
    /**
     * 参考图片URL
     */
    @Schema(description = "参考图片URL")
    private String referenceImageUrl;
    
    /**
     * 宽高比
     */
    @Schema(description = "宽高比")
    private String aspectRatio;
    
    /**
     * 生成强度
     */
    @Schema(description = "生成强度")
    private Double strength;
} 