package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户图片记录响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户图片记录响应")
public class UserImageRecordRes {

    @Schema(description = "图片任务ID")
    private Long id;

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "内容类型")
    private Integer contentType;

    @Schema(description = "内容ID")
    private String contentId;

    @Schema()
    private String prompt;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "图片比例")
    private String aspectRatio;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema()
    private Boolean isFavorite;


    /**
     * 收藏编码
     */
    private String favoriteCode;

    /**
     * 收藏时间
     */
    private Date favoriteTime;
} 
