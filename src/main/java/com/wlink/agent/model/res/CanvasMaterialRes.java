package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 画布素材响应
 */
@Data
@Schema(description = "画布素材响应")
public class CanvasMaterialRes {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 画布ID
     */
    @Schema(description = "画布ID")
    private Long canvasId;

    /**
     * 素材类型(1-图片,2-视频)
     */
    @Schema(description = "素材类型(1-图片,2-视频 3-音频)")
    private Integer materialType;

    /**
     * 素材类型描述
     */
    @Schema(description = "素材类型描述")
    private String materialTypeDesc;

    /**
     * 素材来源(1-生成,2-上传)
     */
    @Schema(description = "素材来源(1-生成,2-上传)")
    private Integer materialSource;

    /**
     * 素材来源描述
     */
    @Schema(description = "素材来源描述")
    private String materialSourceDesc;

    /**
     * 素材URL
     */
    @Schema(description = "素材URL")
    private String materialUrl;

    /**
     * 素材名称
     */
    @Schema(description = "素材名称")
    private String materialName;

    /**
     * 素材描述
     */
    @Schema(description = "素材描述")
    private String materialDesc;


    /**
     * 素材时长(毫秒)
     */
    @Schema(description = "素材时长(毫秒)")
    private Long materialDuration;

    /**
     * 生成记录ID(来源为生成时有值)
     */
    @Schema(description = "生成记录ID")
    private Long generationRecordId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    /**
     * 图片素材参数(仅当materialType=1时有值)
     */
    @Schema(description = "图片素材参数(仅当materialType=1时有值)")
    private ImageMaterialParams imageMaterialParams;
    
    /**
     * 视频素材参数(仅当materialType=2时有值)
     */
    @Schema(description = "视频素材参数(仅当materialType=2时有值)")
    private VideoMaterialParams videoMaterialParams;
    
    /**
     * 设置素材类型描述
     */
    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
        this.materialTypeDesc = getMaterialTypeDescription(materialType);
    }
    
    /**
     * 设置素材来源描述
     */
    public void setMaterialSource(Integer materialSource) {
        this.materialSource = materialSource;
        this.materialSourceDesc = getMaterialSourceDescription(materialSource);
    }
    
    /**
     * 获取素材类型描述
     */
    private String getMaterialTypeDescription(Integer type) {
        if (type == null) return "未知";
        switch (type) {
            case 1: return "图片";
            case 2: return "视频";
            default: return "未知";
        }
    }
    
    /**
     * 获取素材来源描述
     */
    private String getMaterialSourceDescription(Integer source) {
        if (source == null) return "未知";
        switch (source) {
            case 1: return "生成";
            case 2: return "上传";
            default: return "未知";
        }
    }
    
}
