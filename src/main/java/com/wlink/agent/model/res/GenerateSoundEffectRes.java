package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 生成音效响应
 */
@Schema(description = "生成音效响应")
@Data
public class GenerateSoundEffectRes {

    @Schema(description = "音频URL地址", example = "https://v3.fal.media/files/panda/FJ56Mbpj1F_MQVuO0UJ9k_generated.wav")
    private String audioUrl;

    @Schema(description = "音效描述", example = "dog barking in the rain")
    private String prompt;

    @Schema(description = "音效时长（秒）", example = "30")
    private Integer duration;

    @Schema(description = "文件名", example = "generated_sound.wav")
    private String fileName;

    @Schema(description = "文件大小", example = "3.2 MB")
    private String fileSize;

    @Schema(description = "MIME类型", example = "audio/wav")
    private String contentType;

    /**
     * 创建响应对象的便捷方法
     */
    public static GenerateSoundEffectRes of(String audioUrl, String prompt, Integer duration) {
        GenerateSoundEffectRes res = new GenerateSoundEffectRes();
        res.setAudioUrl(audioUrl);
        res.setPrompt(prompt);
        res.setDuration(duration);
        return res;
    }

    /**
     * 设置文件信息
     */
    public GenerateSoundEffectRes withFileInfo(String fileName, String fileSize, String contentType) {
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        return this;
    }
}
