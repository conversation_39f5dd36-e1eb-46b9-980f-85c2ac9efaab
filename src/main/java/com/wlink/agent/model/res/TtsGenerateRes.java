package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "TTS音频生成响应")
public class TtsGenerateRes {

    @Schema(description = "生成的音频文件URL", example = "/data/audio/tts_20240729103000_abcdef12.wav")
    private String audioUrl;

    @Schema(description = "TTS记录ID", example = "100")
    private Long recordId;
    /**
     * 音频时长
     */
    @Schema(description = "音频时长", example = "10")
    private Long duration;
}
