package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 生成 Stable Audio 响应
 */
@Schema(description = "生成 Stable Audio 响应")
@Data
public class GenerateStableAudioRes {

    @Schema(description = "音频URL地址", example = "https://v3.fal.media/files/panda/FJ56Mbpj1F_MQVuO0UJ9k_generated.wav")
    private String audioUrl;

    @Schema(description = "音频生成提示词", example = "128 BPM tech house drum loop")
    private String prompt;

    @Schema(description = "音频时长（秒）", example = "30")
    private Integer duration;

    @Schema(description = "去噪步数", example = "100")
    private Integer steps;

    @Schema(description = "音频开始时间（秒）", example = "0")
    private Integer secondsStart;

    @Schema(description = "文件名", example = "generated_stable_audio.wav")
    private String fileName;

    @Schema(description = "文件大小", example = "3.2 MB")
    private String fileSize;

    @Schema(description = "MIME类型", example = "audio/wav")
    private String contentType;

    @Schema(description = "生成状态", example = "SUCCESS")
    private String status;

    @Schema(description = "生成耗时（毫秒）", example = "45000")
    private Long generationTime;

    /**
     * 创建响应对象的便捷方法
     */
    public static GenerateStableAudioRes of(String audioUrl, String prompt, Integer duration) {
        GenerateStableAudioRes res = new GenerateStableAudioRes();
        res.setAudioUrl(audioUrl);
        res.setPrompt(prompt);
        res.setDuration(duration);
        res.setStatus("SUCCESS");
        return res;
    }

    /**
     * 创建完整响应对象的便捷方法
     */
    public static GenerateStableAudioRes of(String audioUrl, String prompt, Integer duration, 
                                          Integer steps, Integer secondsStart) {
        GenerateStableAudioRes res = new GenerateStableAudioRes();
        res.setAudioUrl(audioUrl);
        res.setPrompt(prompt);
        res.setDuration(duration);
        res.setSteps(steps);
        res.setSecondsStart(secondsStart);
        res.setStatus("SUCCESS");
        return res;
    }

    /**
     * 设置文件信息
     */
    public GenerateStableAudioRes withFileInfo(String fileName, String fileSize, String contentType) {
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        return this;
    }

    /**
     * 设置生成信息
     */
    public GenerateStableAudioRes withGenerationInfo(String status, Long generationTime) {
        this.status = status;
        this.generationTime = generationTime;
        return this;
    }

    /**
     * 设置参数信息
     */
    public GenerateStableAudioRes withParameters(Integer steps, Integer secondsStart) {
        this.steps = steps;
        this.secondsStart = secondsStart;
        return this;
    }

    /**
     * 判断是否生成成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status) && audioUrl != null && !audioUrl.trim().isEmpty();
    }

    /**
     * 获取格式化的描述
     */
    public String getDescription() {
        return String.format("Stable Audio: '%s' (%d秒, %d步) - %s",
                prompt, duration, steps != null ? steps : 100, status);
    }

    /**
     * 获取格式化的生成时间
     */
    public String getFormattedGenerationTime() {
        if (generationTime == null || generationTime <= 0) {
            return "未知";
        }

        if (generationTime < 1000) {
            return generationTime + "ms";
        } else if (generationTime < 60000) {
            return String.format("%.1f秒", generationTime / 1000.0);
        } else {
            long minutes = generationTime / 60000;
            long seconds = (generationTime % 60000) / 1000;
            return String.format("%d分%d秒", minutes, seconds);
        }
    }
}
