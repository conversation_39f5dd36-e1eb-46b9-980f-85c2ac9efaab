package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 画布音轨响应
 */
@Schema(description = "画布音轨响应")
@Data
public class CanvasTrackRes {

    @Schema(description = "音轨ID", example = "123456789")
    private Long id;

    @Schema(description = "画布ID", example = "123456789")
    private Long canvasId;

    @Schema(description = "音轨名称", example = "背景音乐")
    private String trackName;

    @Schema(description = "音轨开始时间（毫秒）", example = "0")
    private Long startTime;

    @Schema(description = "音轨结束时间（毫秒）", example = "30000")
    private Long endTime;

    @Schema(description = "音轨排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "音轨类型", example = "1")
    private Integer trackType;

    @Schema(description = "音轨类型描述", example = "背景音乐")
    private String trackTypeDesc;

    @Schema(description = "音轨音量（0-100）", example = "100")
    private Integer volume;

    @Schema(description = "是否静音", example = "0")
    private Integer muted;

    @Schema(description = "是否锁定", example = "0")
    private Integer locked;

    @Schema(description = "音轨描述", example = "轻松的背景音乐")
    private String description;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "音频列表")
    private List<TrackAudioRes> audioList;

    /**
     * 获取音轨类型描述
     */
    public String getTrackTypeDesc() {
        if (trackType == null) {
            return "未知";
        }
        return switch (trackType) {
            case 1 -> "背景音乐";
            case 2 -> "音效";
            case 3 -> "旁白";
            case 4 -> "对话";
            default -> "未知";
        };
    }
}
