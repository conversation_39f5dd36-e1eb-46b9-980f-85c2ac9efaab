package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用图片生成响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "通用图片生成响应")
public class UniversalImageGenerationResponse {

    /**
     * 资源编码
     */
    @Schema(description = "资源编码，用于后续查询生成状态", example = "IMG-ABC12345")
    private String code;

    /**
     * 生成状态
     */
    @Schema(description = "生成状态(PENDING-生成中,SUCCESS-成功,FAILED-失败)", example = "PENDING")
    private String status;

    /**
     * 外部请求ID
     */
    @Schema(description = "外部API请求ID", example = "req_123456789")
    private String externalRequestId;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息", example = "图片生成任务已提交，请使用资源编码查询生成状态")
    private String message;
}
