package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wlink.agent.model.req.ShotSaveReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 分镜响应
 */
@Data
@Schema(description = "分镜响应")
public class ShotRes {
    @Schema(description = "分镜ID")
    @JsonProperty("shotId")
    private Long shotId;

    @JsonProperty("id")
    private String id;
    @Schema(description = "分镜类型")
    @JsonProperty("type")
    private String type;

    @Schema(description = "运动类型")
    @JsonProperty("movement")
    private String movement;

    @Schema(description = "构图描述")
    @JsonProperty("composition")
    private String composition;
    @JsonProperty("image")
    private String image;

    @Schema(description = "图片状态 PENDING、PROCESSING、COMPLETED、FAILED")
    @JsonProperty("imageStatus")
    private String imageStatus;
    @JsonProperty("voice")
    private String voice;

    @Schema(description = "音频状态 0-处理中  1-已完成 2-失败")
    @JsonProperty("voiceStatus")
    private Integer voiceStatus;

    //音频时长
    @Schema(description = "音频时长")
    @JsonProperty("duration")
    private Long duration;


    @Schema(description = "角色列表")
    @JsonProperty("characters")
    private List<String> characters;

    @Schema(description = "台词列表")
    @JsonProperty("lineList")
    private List<ShotLines> lineList;

    @Schema(description = " 旁白")
    @JsonProperty("narration")
    private String narration;

    @Schema(description = "图片信息")
    @JsonProperty("imageInfo")
    private String imageInfo;



    @NoArgsConstructor
    @Data
    public static class ShotLines {
        @Schema(description = "名称")
        @JsonProperty("name")
        private String name;
        @Schema(description = "台词")
        @JsonProperty("line")
        private String line;
        @Schema(description = "角色ID")
        @JsonProperty("charID")
        private String charID;
        @Schema(description = "序号")
        @JsonProperty("id")
        private Integer id;
        @Schema(description = "音频url")
        @JsonProperty("voice")
        private String voice;
        //音频时长
        @Schema(description = "音频时长")
        @JsonProperty("duration")
        private Long duration;
        //音频类型

        @Schema(description = "音频类型  1-音频  2-音效")
        @JsonProperty("voiceType")
        private Integer voiceType;
    }
} 
