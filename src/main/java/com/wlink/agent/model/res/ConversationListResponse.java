package com.wlink.agent.model.res;

import com.wlink.agent.model.vo.ConversationItem;
import io.swagger.v3.oas.annotations.media.Schema; // 新增导入
import lombok.Data;

import java.util.List;
@Data
@Schema(description  = "会话列表响应体") // 添加类注
public class ConversationListResponse {
    @Schema(description = "返回数量限制", example = "20")
    private int limit;

    @Schema(description = "是否还有更多数据", example = "true")
    private boolean has_more;

    @Schema(description = "会话列表数据")
    private List<ConversationItem> data;

}
