package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "Dify会话消息")
@Data
public class ConversationMessageItem {
    @Schema(description = "Dify会话消息ID", example = "msg_123")
    private String id;

    @Schema(description = "所属Dify会话ID", example = "conv_456")
    private String conversation_id;

    @Schema(description = "父消息ID (用于多轮对话)", example = "msg_000")
    private String parent_message_id;

    @Schema(description = "输入参数", example = "{")
    private Map<String, Object> inputs;

    @Schema(description = "用户查询语句", example = "今天天气怎么样？")
    private String query;

    @Schema(description = "模型回答")
    private String answer;

    @Schema(description = "消息相关文件列表")
    private List<Object> message_files;

    @Schema(description = "用户反馈信息")
    private Object feedback;

    @Schema(description = "检索器资源列表")
    private List<Object> retriever_resources;

    @Schema(description = "创建时间(Unix timestamp)", example = "1678886400")
    private long created_at;

    @Schema()
    private List<Object> agent_thoughts;

    @Schema(description = "消息状, example = ", example = "completed")
    private String status;

    @Schema(description = "错误信息 (如果发生错误)")
    private Object error;


}
