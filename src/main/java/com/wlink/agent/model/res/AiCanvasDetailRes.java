package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 画布详情响应
 */
@Data
@Schema(description = "画布详情响应")
public class AiCanvasDetailRes {
    
    /**
     * 画布ID
     */
    @Schema(description = "画布ID")
    private Long id;
    
    /**
     * 画布唯一编码
     */
    @Schema(description = "画布唯一编码")
    private String code;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;
    
    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private String sessionId;
    
    /**
     * 画布名称
     */
    @Schema(description = "画布名称")
    private String canvasName;
    
    /**
     * 画布描述
     */
    @Schema(description = "画布描述")
    private String canvasDesc;
    
    /**
     * 封面图片URL
     */
    @Schema(description = "封面图片URL")
    private String coverImage;

    /**
     * 画布宽高比
     */
    @Schema(description = "画布宽高比")
    private String ratio;
    
    /**
     * 状态(0-草稿,1-已发布)
     */
    @Schema(description = "状态(0-草稿,1-已发布)")
    private Integer status;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    /**
     * 分镜列表
     */
    @Schema(description = "分镜列表")
    private List<AiCanvasShotRes> shots;

    /**
     * 背景音乐信息
     */
    @Schema(description = "背景音乐信息")
    private CanvasBackgroundMusicRes backgroundMusic;
} 