package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分镜任务状态组列表响应")
public class ShotTaskStatusGroupsRes {

    @Schema(description = "分镜任务状态组列表")
    @JsonProperty("shotGroups")
    private List<ChapterGroup> shotGroups;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChapterGroup {
        @Schema(description = "章节ID")
        @JsonProperty("chapterID")
        private String chapterID;
        
        @Schema(description = "章节状已完 未完")
        @JsonProperty("chapterStatus")
        private String chapterStatus;
        
        @Schema(description = "场景列表")
        @JsonProperty("scenes")
        private List<SceneGroup> scenes;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SceneGroup {
        @Schema(description = "场景ID")
        @JsonProperty("sceneId")
        private String sceneId;
        
        @Schema(description = "场景镜头状已完 未完")
        @JsonProperty("shotStatus")
        private String shotStatus;
        
        @Schema(description = "镜头数量")
        @JsonProperty("shotCount")
        private String shotCount;
        
        @Schema(description = "镜头列表")
        @JsonProperty("shots")
        private List<ShotDetail> shots;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShotDetail {
        @Schema(description = "镜头ID")
        @JsonProperty("shotId")
        private String shotId;
        
        @Schema(description = "图片状未完 排队 生成 已完")
        @JsonProperty("imageStatus")
        private String imageStatus;
        
        @Schema(description = "语音状未完 排队 生成 已完")
        @JsonProperty("voiceStatus")
        private String voiceStatus;
        
        @Schema(description = "旁白状未完 排队 生成 已完")
        @JsonProperty("narrationStatus")
        private String narrationStatus;
    }
} 
