package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 旁白统计响应
 */
@Data
@Schema(description = "旁白统计响应")
public class NarrationCountRes {

    @Schema(description = "会话ID")
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "章节统计列表")
    @JsonProperty("chapters")
    private List<ChapterStatInfo> chapters;

    @Schema(description = "总旁白数量")
    @JsonProperty("totalNarrationCount")
    private Integer totalNarrationCount;

    @Schema(description = "总角色音频数量")
    @JsonProperty("totalCharacterCount")
    private Integer totalCharacterCount;

    @Schema(description = "角色统计汇总")
    @JsonProperty("characterSummary")
    private List<CharacterSummary> characterSummary;

    /**
     * 章节旁白信息
     */
    @Data
    @Schema(description = "章节旁白信息")
    public static class ChapterNarrationInfo {

        @Schema(description = "章节ID")
        @JsonProperty("segmentId")
        private String segmentId;

        @Schema(description = "章节名称")
        @JsonProperty("segmentName")
        private String segmentName;

        @Schema(description = "数量（旁白数量或角色音频数量）")
        @JsonProperty("count")
        private Integer count;

        @Schema(description = "详情列表")
        @JsonProperty("details")
        private List<NarrationDetail> details;
    }

    /**
     * 详情信息（旁白详情或角色音频详情）
     */
    @Data
    @Schema(description = "详情信息")
    public static class NarrationDetail {

        @Schema(description = "分镜ID")
        @JsonProperty("shotId")
        private String shotId;

        @Schema(description = "ID（旁白ID或角色音频ID）")
        @JsonProperty("id")
        private Integer id;

        @Schema(description = "内容（旁白内容或角色台词）")
        @JsonProperty("content")
        private String content;

        @Schema(description = "角色ID（仅角色统计时有值）")
        @JsonProperty("characterId")
        private String characterId;

        @Schema(description = "角色名称（仅角色统计时有值）")
        @JsonProperty("characterName")
        private String characterName;
    }
}
