package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageStyleRes {
    @Schema(description = "风格ID")
    private Long id;
    @Schema(description = "风格名称")
    private String styleName;
    @Schema(description = "风格编码")
    private String styleCode;
    @Schema(description = "风格图片URL")
    private String styleUrl;
    /**
     * 风格分类
     */
    @Schema(description = "风格分类")
    private String styleCategory;

    /**
     * prompt
     */
    @Schema(description = "prompt")
    private String prompt;
}
