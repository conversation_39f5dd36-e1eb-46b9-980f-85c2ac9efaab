package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModifyImageUrlInfoRes {


    @Schema(description = "图片描述文本", required = true)
    private String prompt;

    @Schema(description = "图片修改结果列表")
    private List<ImageUrlInfoRes> imageUrlInfoResList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageUrlInfoRes{

        /**
         * 源图URL (带前缀)
         */
        @Schema(description = "图片 URL (带前缀)", required = true, example = "https://example.com/source.jpg")
        private String modifiedImageUrl;

        /**
         * 修改记录的唯一编码
         */
        @Schema(description = "修改记录的唯一编码", required = true, example = "modify_code_123")
        private String modifyCode;

    };


}
