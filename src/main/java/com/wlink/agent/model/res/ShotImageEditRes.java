package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 分镜图片编辑响应
 */
@Schema(description = "分镜图片编辑响应")
@Data
public class ShotImageEditRes {

    @Schema(description = "编辑记录ID", example = "123456789")
    private Long id;

    @Schema(description = "分镜ID", example = "123456789")
    private Long shotId;

    @Schema(description = "任务ID", example = "1904163390028185602")
    private String taskId;

    @Schema(description = "客户端ID", example = "14caa1db2110a81629c101b9bb4cb0ce")
    private String clientId;

    @Schema(description = "WebSocket连接URL", example = "wss://www.runninghub.cn:443/ws/c_instance?...")
    private String netWssUrl;

    @Schema(description = "原图URL", example = "https://example.com/original.jpg")
    private String originalImageUrl;

    @Schema(description = "遮罩图URL", example = "https://example.com/mask.jpg")
    private String maskImageUrl;

    @Schema(description = "编辑提示词", example = "把图中红色部位替换成一顶嘻哈头巾和一副蓝色半透明眼镜")
    private String prompt;

    @Schema(description = "任务状态", example = "RUNNING")
    private String status;

    @Schema(description = "结果图片URL", example = "https://example.com/result.jpg")
    private String resultImageUrl;

    @Schema(description = "错误信息", example = "")
    private String errorMessage;

    @Schema(description = "任务耗时（毫秒）", example = "5000")
    private Long taskCostTime;

    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private Date createTime;

    @Schema(description = "更新时间", example = "2024-01-01T10:05:00")
    private Date updateTime;
}
