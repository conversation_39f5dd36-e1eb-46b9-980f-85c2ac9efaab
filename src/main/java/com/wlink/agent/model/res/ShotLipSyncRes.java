package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 分镜对口型响应
 */
@Schema(description = "分镜对口型响应")
@Data
public class ShotLipSyncRes {

    /**
     * 主键ID
     */
    @Schema(description = "对口型记录ID", example = "123456789")
    private Long id;

    /**
     * 分镜ID
     */
    @Schema(description = "分镜ID", example = "123456789")
    private Long shotId;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", example = "1904163390028185602")
    private String taskId;

    /**
     * 客户端ID
     */
    @Schema(description = "客户端ID", example = "14caa1db2110a81629c101b9bb4cb0ce")
    private String clientId;

    /**
     * 音频地址
     */
    @Schema(description = "音频地址", example = "https://example.com/audio.wav")
    private String audioUrl;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址", example = "https://example.com/image.jpg")
    private String imageUrl;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "RUNNING", allowableValues = {"RUNNING", "COMPLETED", "FAILED"})
    private String status;

    /**
     * 生成的视频地址
     */
    @Schema(description = "生成的视频地址", example = "https://example.com/result.mp4")
    private String resultVideoUrl;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 任务耗时（毫秒）
     */
    @Schema(description = "任务耗时（毫秒）", example = "5000")
    private Long taskCostTime;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "user123")
    private String userId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
