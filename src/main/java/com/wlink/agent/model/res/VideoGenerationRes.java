package com.wlink.agent.model.res;

import com.wlink.agent.enums.VideoGenerationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 视频生成响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "视频生成响应")
public class VideoGenerationRes {
    
    /**
     * 生成记录ID
     */
    @Schema(description = "生成记录ID")
    private Long id;
    
    /**
     * 生成提示词
     */
    @Schema(description = "生成提示词")
    private String prompt;
    
    /**
     * 首帧图片URL
     */
    @Schema(description = "首帧图片URL")
    private String firstFrameImage;
    
    /**
     * 尾帧图片URL
     */
    @Schema(description = "尾帧图片URL")
    private String lastFrameImage;
    
    /**
     * 分辨率
     */
    @Schema(description = "分辨率")
    private String resolution;
    
    /**
     * 视频比例
     */
    @Schema(description = "视频比例")
    private String aspectRatio;
    
    /**
     * 视频时长(秒)
     */
    @Schema(description = "视频时长(秒)")
    private Integer duration;
    
    /**
     * 帧率
     */
    @Schema(description = "帧率")
    private Integer fps;
    
    /**
     * 状态值
     */
    @Schema(description = "状态值")
    private Integer status;
    
    /**
     * 状态描述
     */
    @Schema(description = "状态描述")
    private String statusDesc;
    
    /**
     * 生成的视频URL
     */
    @Schema(description = "生成的视频URL")
    private String videoUrl;
    
    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;
    
    /**
     * 队列位置
     */
    @Schema(description = "队列位置")
    private Integer queuePosition;
    
    /**
     * 开始处理时间
     */
    @Schema(description = "开始处理时间")
    private Date startTime;
    
    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private Date completeTime;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    
    /**
     * 设置状态并自动生成描述
     */
    public void setStatus(Integer status) {
        this.status = status;
        try {
            VideoGenerationStatus statusEnum = VideoGenerationStatus.fromValue(status);
            this.statusDesc = statusEnum.getDescription();
        } catch (IllegalArgumentException e) {
            this.statusDesc = "未知状态";
        }
    }
}
