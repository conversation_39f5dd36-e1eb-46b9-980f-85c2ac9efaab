package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "图片上传响应")
public class ImageUploadRes {

    @Schema(description = "修改编码", example = "1234567890")
    private String modifyCode;

    @Schema(description = "图片URL", example = "http://example.com/image.jpg")
    private String imageUrl;
} 
