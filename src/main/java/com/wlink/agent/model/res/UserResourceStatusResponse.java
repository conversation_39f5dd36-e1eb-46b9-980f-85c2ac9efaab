package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 用户资源状态响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户资源状态响应")
public class UserResourceStatusResponse {

    /**
     * 资源编码
     */
    @Schema(description = "资源编码", example = "IMG-ABC12345")
    private String code;

    /**
     * 资源类型
     */
    @Schema(description = "资源类型(1-图片,2-视频,3-音频)", example = "1")
    private Integer resourceType;

    /**
     * 生成状态
     */
    @Schema(description = "生成状态(PENDING-生成中,SUCCESS-成功,FAILED-失败)", example = "SUCCESS")
    private String generationStatus;

    /**
     * 资源URL集合
     */
    @Schema(description = "资源URL集合", example = "[\"https://example.com/image1.jpg\", \"https://example.com/image2.jpg\"]")
    private List<String> resourceUrls;

    /**
     * 资源大小
     */
    @Schema(description = "资源大小(字节)", example = "1024000")
    private Long resourceSize;

    /**
     * 图片/视频宽度
     */
    @Schema(description = "图片/视频宽度", example = "1920")
    private Integer width;

    /**
     * 图片/视频高度
     */
    @Schema(description = "图片/视频高度", example = "1080")
    private Integer height;

    /**
     * 视频/音频时长
     */
    @Schema(description = "视频/音频时长(秒)", example = "30")
    private Integer duration;

    /**
     * 使用的模型类型
     */
    @Schema(description = "使用的模型类型", example = "kontext-pro")
    private String modelType;

    /**
     * 生成提示词
     */
    @Schema(description = "生成提示词", example = "A beautiful sunset over the ocean")
    private String prompt;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息(仅失败时有值)", example = "生成失败：参数错误")
    private String errorMessage;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-17T10:30:00")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-17T10:35:00")
    private Date updateTime;
}
