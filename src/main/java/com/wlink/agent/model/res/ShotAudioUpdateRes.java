package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分镜音频更新响应")
public class ShotAudioUpdateRes {

    @Schema(description = "分镜ID", example = "shot-123")
    private String shotId;

    @Schema(description = "更新后的音频URL", example = "/data/audio/tts_20240729103000_abcdef12.wav")
    private String audioUrl;
} 
