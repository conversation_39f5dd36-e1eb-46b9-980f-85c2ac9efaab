package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ShotTaskStatusRes")
public class ShotTaskStatusRes {

    @Schema(description = "镜头ID")
    @JsonProperty("shot_id")
    private String shotId;
    
    @Schema(description = "场景ID")
    @JsonProperty("scene_id")
    private String sceneId;
    
    @Schema(description = "图片状NOT_EXIST-不存 IN_PROGRESS-进行 COMPLETED-已完 FAILED-失败)")
    @JsonProperty("image_status")
    private String imageStatus;
    
    @Schema(description = "语音状NOT_EXIST-不存 IN_PROGRESS-进行 COMPLETED-已完 FAILED-失败)")
    @JsonProperty("voice_status")
    private String voiceStatus;
    
    @Schema(description = "旁白状NOT_EXIST-不存 IN_PROGRESS-进行 COMPLETED-已完 FAILED-失败)")
    @JsonProperty("narration_status")
    private String narrationStatus;
} 
