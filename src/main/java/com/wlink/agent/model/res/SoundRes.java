package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "声音信息")
public class SoundRes {

    @Schema(description = "声音ID")
    private Long id;

    @Schema()
    private String supplier;

    @Schema(description = "声音类型-系统 2-定制")
    private Integer type;

    @Schema(description = "声音名称(根据当前语言返回对应的名")
    private String name;

    @Schema(description = "描述")
    private String depict;

    @Schema(description = "语言名称")
    private String languageName;

    @Schema(description = "语言标识")
    private String language;

    @Schema(description = "声音标识")
    private String sound;

    @Schema()
    private String rate;

    @Schema()
    private Integer sex;

    @Schema(description = "音频地址")
    private String audioUrl;
} 
