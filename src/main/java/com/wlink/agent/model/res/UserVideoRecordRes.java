package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户视频记录响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户视频记录响应")
public class UserVideoRecordRes {
    @Schema(description = "视频任务ID")
    private Long id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "分镜ID")
    private Long shotId;

    @Schema(description = "生成任务ID")
    private String generateTaskId;

    @Schema(description = "模型")
    private String model;

    @Schema(description = "生成提示词")
    private String prompt;

    @Schema(description = "首帧图片URL")
    private String firstFrameImage;

    @Schema(description = "尾帧图片URL")
    private String lastFrameImage;

    @Schema(description = "分辨率")
    private String resolution;

    @Schema(description = "视频比例")
    private String ratio;

    @Schema(description = "视频时长(毫秒)")
    private Integer duration;

    @Schema(description = "帧率")
    private Integer fps;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "生成的视频URL")
    private String videoUrl;

    @Schema(description = "任务信息")
    private String taskInfo;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "队列位置")
    private Integer queuePosition;

    @Schema(description = "开始处理时间")
    private Date startTime;

    @Schema(description = "完成时间")
    private Date completeTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "分享状态", example = "0", allowableValues = {"0", "1"})
    private Integer shareStatus;

    @Schema(description = "分享状态描述", example = "未分享")
    private String shareStatusDesc;

    @Schema(description = "分享码", example = "ABC123DEF")
    private String shareCode;

    //分享url
    @Schema(description = "分享URL", example = "https://example.com/share/ABC123DEF")
    private String shareUrl;

    @Schema(description = "分享时间", example = "2024-01-01T12:10:00Z")
    private Date shareTime;

    /**
     * 获取分享状态描述
     */
    public String getShareStatusDesc() {
        if (shareStatus == null) {
            return "未分享";
        }
        return switch (shareStatus) {
            case 0 -> "未分享";
            case 1 -> "已分享";
            default -> "未分享";
        };
    }
}