package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 资源收藏响应
 */
@NoArgsConstructor
@Data
@Schema(description = "资源收藏响应")
public class ResourceFavoriteRes {

    /**
     * 资源编码
     */
    @Schema(description = "资源编码", example = "resource_123456")
    private String resourceCode;

    /**
     * 资源ID
     */
    @Schema(description = "资源ID", example = "resource_123456")
    private String resourceId;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    /**
     * 资源类型
     */
    @Schema(description = "资源类型(1-角色,2-图片,3-视频,4-音频,5-文本)", example = "2")
    private Integer resourceType;

    /**
     * 资源类型描述
     */
    @Schema(description = "资源类型描述", example = "图片")
    private String resourceTypeDesc;

    /**
     * 资源子类
     */
    @Schema(description = "资源子类, example = ", example = "1")
    private Integer resourceSubtype;

    /**
     * 资源子类型描
     */
    @Schema(description = "资源子类型描, example = ", example = "角色图片")
    private String resourceSubtypeDesc;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称", example = "美丽风景")
    private String resourceName;

    /**
     * 资源URL
     */
    @Schema(description = "资源URL", example = "https://example.com/image.jpg")
    private String resourceUrl;

    /**
     * 收藏时间
     */
    @Schema(description = "收藏时间", example = "2023-06-01 12:00:00")
    private Date createTime;

    /**
     * 资源附加数据
     */
    @Schema(description = "资源附加数据")
    private Map<String, Object> extraData;
} 
