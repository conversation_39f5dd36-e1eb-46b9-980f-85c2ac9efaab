package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分镜描述分组查询响应")
public class ShotTaskStatusGroupRes {

    @Schema(description = "章节ID")
    @JsonProperty("chapterID")
    private String chapterID;
    
    @Schema(description = "场景列表")
    @JsonProperty("scenes")
    private List<SceneStatusGroupDto> scenes;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SceneStatusGroupDto {
        @Schema(description = "场景ID")
        @JsonProperty("sceneId")
        private String sceneId;
        
        @Schema(description = "镜头列表")
        @JsonProperty("shots")
        private List<ShotStatusDetailDto> shots;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShotStatusDetailDto {
        @Schema(description = "镜头ID")
        @JsonProperty("shotId")
        private String shotId;
        
        @Schema(description = "图片状未完 进行 已完 失败)")
        @JsonProperty("imageStatus")
        private String imageStatus;
        
        @Schema(description = "语音状未完 进行 已完 失败)")
        @JsonProperty("voiceStatus")
        private String voiceStatus;
        
        @Schema(description = "旁白状未完 进行 已完 失败)")
        @JsonProperty("narrationStatus")
        private String narrationStatus;
    }
} 
