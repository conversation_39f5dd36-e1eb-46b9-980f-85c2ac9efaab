package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "源图片URL列表响应")
public class SourceImageUrlsRes {

    @Schema(description = "源图片URL列表", required = true)
    private List<String> sourceImageUrls;
} 
