package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "节目单智能体关联信息")
public class RundownAgentRes {


    private List<RundownAgentRes.AgentConfig> agentConfigs;


    @Data
    public static class AgentConfig {
        @Schema(description = "智能体ID")
        private Long agentId;

        @Schema()
        private String agentName;

        @Schema(description = "3d形象id")
        private String imageCode;

        @Schema(description = "服装编码")
        private String costumeCode;

        private Integer sort;
    }


} 
