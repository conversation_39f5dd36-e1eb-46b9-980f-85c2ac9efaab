package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
@Schema(description = "STS令牌响应")
@Data
public class StsCredentialsRes {
    @Schema(description = "访问密钥ID")
    private String accessKeyId;
    @Schema(description = "访问密钥")
    private String accessKeySecret;
    @Schema(description = "安全令牌")
    private String securityToken;
    @Schema(description = "令牌过期时间")
    private String expiration;
    @Schema(description = "请求ID")
    private String requestId;
    @Schema()
    private String bucketName;
    @Schema(description = "环境")
    private String env;

} 
