package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 画布图片资源响应
 */
@Data
@Schema(description = "画布图片资源响应")
public class AiCanvasImageRes {
    
    /**
     * 图片资源ID
     */
    @Schema(description = "图片资源ID")
    private Long id;
    
    /**
     * 画布ID
     */
    @Schema(description = "画布ID")
    private Long canvasId;
    
    /**
     * 分镜编码
     */
    @Schema(description = "分镜编码")
    private String shotCode;
    
    /**
     * 图片URL
     */
    @Schema(description = "图片URL")
    private String imageUrl;
    
    /**
     * 图片生成提示词
     */
    @Schema(description = "图片生成提示词")
    private String imagePrompt;
    
    /**
     * 图片描述
     */
    @Schema(description = "图片描述")
    private String imageDesc;
    
    /**
     * 图片宽高比
     */
    @Schema(description = "图片宽高比")
    private String imageAspectRatio;
    
    /**
     * 图片状态
     */
    @Schema(description = "图片状态")
    private String imageStatus;
    
    /**
     * 参考图片URL
     */
    @Schema(description = "参考图片URL")
    private String referenceImage;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
} 