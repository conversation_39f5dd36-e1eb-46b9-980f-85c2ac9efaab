package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "VisualRecordPageRes")
public class VisualRecordPageRes {
    
    @Schema(description = "总记录数")
    private Long total;
    
    @Schema(description = "当前页码")
    private Integer pageNum;
    
    @Schema(description = "每页大小")
    private Integer pageSize;
    
    @Schema()
    private Integer totalPages;
    
    @Schema(description = "数据列表")
    private List<AiCreationVisualRecordListRes> list;
} 
