package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "图片生成详情响应")
public class ImageGenerateDetailRes {
    
    @Schema(description = "生成记录ID")
    private String generateId;
    
    @Schema(description = "生成状态：0-生成1-生成成功 2-生成失败")
    private Integer status;
    
    @Schema(description = "失败原因")
    private String failReason;
    
    @Schema()
    private List<String> imageUrls;
} 
