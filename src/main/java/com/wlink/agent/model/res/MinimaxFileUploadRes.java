package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "MiniMax文件上传响应")
public class MinimaxFileUploadRes {
    
    @Schema(description = "文件ID")
    private String fileId;
    
    @Schema(description = "文件大小(字节)")
    private Long bytes;
    
    @Schema()
    private Long createdAt;
    
    @Schema()
    private String filename;
    
    @Schema()
    private String purpose;
} 
