package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 视频模型配置响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "视频模型配置响应")
public class VideoModelConfigRes {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称", example = "MiniMax-Hailuo-02")
    private String modelName;

    /**
     * 模型显示名称
     */
    @Schema(description = "模型显示名称", example = "MiniMax海螺02")
    private String modelDisplayName;

    /**
     * 模型描述
     */
    @Schema(description = "模型描述", example = "MiniMax最新的海螺模型，支持文生视频和图生视频")
    private String modelDescription;

    /**
     * 提供商
     */
    @Schema(description = "提供商", example = "MINIMAX")
    private String provider;

    /**
     * 提供商名称
     */
    @Schema(description = "提供商名称", example = "MiniMax")
    private String providerName;

    /**
     * 模型类型列表
     */
    @Schema(description = "模型类型列表", example = "[\"T2V\", \"I2V\"]")
    private List<String> modelTypes;

    /**
     * 模型类型名称列表
     */
    @Schema(description = "模型类型名称列表", example = "[\"文生视频\", \"图生视频\"]")
    private List<String> modelTypeNames;

    /**
     * 帧率
     */
    @Schema(description = "帧率", example = "24")
    private Integer fps;

    /**
     * 是否包含水印
     */
    @Schema(description = "是否包含水印", example = "true")
    private Boolean supportWatermark;

    /**
     * 是否支持种子
     */
    @Schema(description = "是否支持种子", example = "true")
    private Boolean supportSeed;

    /**
     * 是否支持固定摄像头
     */
    @Schema(description = "是否支持固定摄像头", example = "true")
    private Boolean supportCameraFixed;

    /**
     * 排序顺序
     */
    @Schema(description = "排序顺序", example = "1")
    private Integer sortOrder;

    /**
     * 尺寸配置列表
     */
    @Schema(description = "尺寸配置列表")
    private List<SizeConfig> sizeConfigs;
    
    /**
     * 尺寸配置详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "尺寸配置详情")
    public static class SizeConfig {

        /**
         * 尺寸配置ID
         */
        @Schema(description = "尺寸配置ID")
        private Long id;

        /**
         * 尺寸名称
         */
        @Schema(description = "尺寸名称", example = "720p")
        private String sizeName;

        /**
         * 尺寸显示名称
         */
        @Schema(description = "尺寸显示名称", example = "720p高清")
        private String sizeDisplayName;

        /**
         * 支持的时长列表（秒）
         */
        @Schema(description = "支持的时长列表（秒）", example = "[5, 10]")
        private List<Integer> supportedDurations;

        /**
         * 图片数量(0-不支持图片,1-支持首帧,2-支持首尾帧)
         */
        @Schema(description = "图片数量", example = "1")
        private Integer imageCount;

        /**
         * 图片支持描述
         */
        @Schema(description = "图片支持描述", example = "支持首帧")
        private String imageCountDesc;

        /**
         * 该尺寸生成需要的积分
         */
        @Schema(description = "该尺寸生成需要的积分", example = "80")
        private Integer pointsCost;

        /**
         * 排序顺序
         */
        @Schema(description = "排序顺序", example = "1")
        private Integer sortOrder;

        /**
         * 分辨率配置列表
         */
        @Schema(description = "分辨率配置列表")
        private List<ResolutionConfig> resolutionConfigs;
    }

    /**
     * 分辨率配置详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分辨率配置详情")
    public static class ResolutionConfig {

        /**
         * 分辨率配置ID
         */
        @Schema(description = "分辨率配置ID")
        private Long id;

        /**
         * 宽高比
         */
        @Schema(description = "宽高比", example = "16:9")
        private String ratio;

        /**
         * 宽高比显示名称
         */
        @Schema(description = "宽高比显示名称", example = "横屏16:9")
        private String ratioDisplayName;

        /**
         * 宽度
         */
        @Schema(description = "宽度", example = "1280")
        private Integer width;

        /**
         * 高度
         */
        @Schema(description = "高度", example = "720")
        private Integer height;

        /**
         * 实际像素尺寸
         */
        @Schema(description = "实际像素尺寸", example = "1280×720")
        private String pixelSize;

        /**
         * 该分辨率生成需要的积分
         */
        @Schema(description = "该分辨率生成需要的积分", example = "5")
        private Integer pointsCost;

        /**
         * 排序顺序
         */
        @Schema(description = "排序顺序", example = "1")
        private Integer sortOrder;
    }
}
