package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * AI创作视觉记录响应DTO
 */
@Data
@Schema(description = "AiCreationVisualRecordRes")
public class AiCreationVisualRecordRes {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "会话ID")
    private String sessionId;
    @Schema(description = "视觉记录编码")
    private String visualRecordCode;

    @Schema(description = "视觉数据(JSON格式)")
    private String contentData; // Usually you might parse this further in the client

    @Schema(description = "发布状0-未发1-已发")
    private Integer publishStatus;

    @Schema(description = "分享地址url")
    private String shareUrl;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "封面图片URL")
    private String coverImage;

    @Schema(description = "副标描述")
    private String subtitle2;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "发布时间")
    private Date publishTime;

    @Schema(description = "创建用户ID")
    private String userId;

    //头像
    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "视频生成状0: 未生1: 生成2: 生成完成 3: 生成失败", example = "2")
    private Integer videoState;

    @Schema(description = "视频生成进度", example = "100")
    private Integer videoProgress;

    @Schema(description = "视频地址")
    private String videoUrl;

    //图片大小
    @Schema(description = "图片比例")
    private String imageSize;

} 
