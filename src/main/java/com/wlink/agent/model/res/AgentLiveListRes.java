package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "直播列表")
public class AgentLiveListRes {

    @Schema(description = "直播Id")
    private String liveId;
    @Schema(description = "直播名称")
    private String liveName;
    @Schema()
    private Integer liveStatus;
    List<AgentLiveAppRes> agentLiveApps;
    List<AgentInfo> agents;

    @Data
    public static class AgentLiveAppRes {
        @Schema(description = "应用Id")
        private Integer appId;
        @Schema(description = "应用名称")
        private String appName;
    }

    @Data
    public static class AgentInfo {
        @Schema(description = "智能体ID")
        private Long id;

        @Schema()
        private String name;

    }

}
