package com.wlink.agent.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DelayTask {
    
    /**
     * 任务类型
     */
    private String type;
    
    /**
     * 房间ID
     */
    private String roomId;
    
    /**
     * 任务数据
     */
    private String data;
    
    /**
     * 创建时间
     */
    private long createTime;
    
    public static class TaskType {
        public static final String AI_REPLY = "AI_REPLY";
    }
} 
