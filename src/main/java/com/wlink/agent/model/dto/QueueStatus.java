package com.wlink.agent.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * 队列状态
 * 使用 JDK 17 的 record 特性
 */
@Schema(description = "队列状态")
public record QueueStatus(
        
        @Schema(description = "状态", example = "IN_QUEUE", allowableValues = {"IN_QUEUE", "IN_PROGRESS", "COMPLETED"})
        @JSONField(name = "status")
        String status,
        
        @Schema(description = "请求ID", example = "req_123456")
        @JSONField(name = "request_id")
        String requestId,
        
        @Schema(description = "响应URL", example = "https://queue.fal.run/cassetteai/sound-effects-generator/requests/req_123456")
        @JSONField(name = "response_url")
        String responseUrl,
        
        @Schema(description = "状态查询URL", example = "https://queue.fal.run/cassetteai/sound-effects-generator/requests/req_123456/status")
        @JSONField(name = "status_url")
        String statusUrl,
        
        @Schema(description = "取消URL", example = "https://queue.fal.run/cassetteai/sound-effects-generator/requests/req_123456/cancel")
        @JSONField(name = "cancel_url")
        String cancelUrl,
        
        @Schema(description = "日志信息")
        @JSONField(name = "logs")
        Map<String, Object> logs,
        
        @Schema(description = "指标信息")
        @JSONField(name = "metrics")
        Map<String, Object> metrics,
        
        @Schema(description = "队列位置", example = "5")
        @JSONField(name = "queue_position")
        Integer queuePosition
) {
    
    /**
     * 判断是否在队列中
     *
     * @return 是否在队列中
     */
    public boolean isInQueue() {
        return "IN_QUEUE".equals(status);
    }
    
    /**
     * 判断是否正在处理
     *
     * @return 是否正在处理
     */
    public boolean isInProgress() {
        return "IN_PROGRESS".equals(status);
    }
    
    /**
     * 判断是否已完成
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }
    
    /**
     * 判断是否为终态
     *
     * @return 是否为终态
     */
    public boolean isFinalState() {
        return isCompleted();
    }
    
    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        return switch (status) {
            case "IN_QUEUE" -> "排队中" + (queuePosition != null ? " (位置: " + queuePosition + ")" : "");
            case "IN_PROGRESS" -> "处理中";
            case "COMPLETED" -> "已完成";
            default -> "未知状态: " + status;
        };
    }
    
    /**
     * 获取格式化的状态信息
     *
     * @return 格式化状态信息
     */
    public String getFormattedStatus() {
        return String.format("请求ID: %s, 状态: %s", requestId, getStatusDescription());
    }
}
