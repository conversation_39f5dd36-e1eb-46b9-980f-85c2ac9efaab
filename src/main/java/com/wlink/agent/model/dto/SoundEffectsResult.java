package com.wlink.agent.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 音效生成结果
 * 使用 JDK 17 的 record 特性
 */
@Schema(description = "音效生成结果")
public record SoundEffectsResult(
        
        @Schema(description = "音频文件信息")
        @JSONField(name = "audio_file")
        AudioFile audioFile
) {
    
    /**
     * 音频文件信息
     */
    @Schema(description = "音频文件信息")
    public record AudioFile(
            
            @Schema(description = "文件URL", example = "https://v3.fal.media/files/panda/FJ56Mbpj1F_MQVuO0UJ9k_generated.wav")
            @JSONField(name = "url")
            String url,
            
            @Schema(description = "文件大小（字节）", example = "4404019")
            @JSONField(name = "file_size")
            Long fileSize,
            
            @Schema(description = "文件名", example = "generated_sound.wav")
            @JSONField(name = "file_name")
            String fileName,
            
            @Schema(description = "MIME类型", example = "audio/wav")
            @JSONField(name = "content_type")
            String contentType
    ) {
        
        /**
         * 判断是否为有效的音频文件
         *
         * @return 是否有效
         */
        public boolean isValid() {
            return url != null && !url.trim().isEmpty();
        }
        
        /**
         * 获取文件扩展名
         *
         * @return 文件扩展名
         */
        public String getFileExtension() {
            if (fileName == null) {
                return null;
            }
            int lastDotIndex = fileName.lastIndexOf('.');
            return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : null;
        }
        
        /**
         * 获取格式化的文件大小
         *
         * @return 格式化文件大小
         */
        public String getFormattedFileSize() {
            if (fileSize == null) {
                return "未知大小";
            }
            
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            }
        }
        
        /**
         * 判断是否为音频文件
         *
         * @return 是否为音频文件
         */
        public boolean isAudioFile() {
            return contentType != null && contentType.startsWith("audio/");
        }
    }
    
    /**
     * 判断结果是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return audioFile != null && audioFile.isValid();
    }
    
    /**
     * 获取音频URL
     *
     * @return 音频URL
     */
    public String getAudioUrl() {
        return audioFile != null ? audioFile.url() : null;
    }
    
    /**
     * 获取格式化的结果描述
     *
     * @return 格式化描述
     */
    public String getDescription() {
        if (!isValid()) {
            return "无效的音效生成结果";
        }
        
        return String.format("音效文件: %s (%s)", 
                audioFile.fileName() != null ? audioFile.fileName() : "generated_audio",
                audioFile.getFormattedFileSize());
    }
}
