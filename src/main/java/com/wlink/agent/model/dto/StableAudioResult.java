package com.wlink.agent.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Stable Audio 生成结果
 * 使用 JDK 17 的 record 特性
 */
@Schema(description = "Stable Audio 生成结果")
public record StableAudioResult(
        
        @Schema(description = "音频文件信息")
        @JSONField(name = "audio_file")
        AudioFile audioFile
) {
    
    /**
     * 音频文件信息
     */
    @Schema(description = "音频文件信息")
    public record AudioFile(
            
            @Schema(description = "文件URL", example = "https://v3.fal.media/files/panda/FJ56Mbpj1F_MQVuO0UJ9k_generated.wav")
            @JSONField(name = "url")
            String url,
            
            @Schema(description = "文件大小（字节）", example = "4404019")
            @JSONField(name = "file_size")
            Long fileSize,
            
            @Schema(description = "文件名", example = "generated_audio.wav")
            @JSONField(name = "file_name")
            String fileName,
            
            @Schema(description = "MIME类型", example = "audio/wav")
            @JSONField(name = "content_type")
            String contentType,
            
            @Schema(description = "文件数据（二进制）")
            @JSONField(name = "file_data")
            String fileData
    ) {
        
        /**
         * 判断是否为有效的音频文件
         *
         * @return 是否有效
         */
        public boolean isValid() {
            return url != null && !url.trim().isEmpty();
        }
        
        /**
         * 获取格式化的文件大小
         *
         * @return 格式化的文件大小
         */
        public String getFormattedFileSize() {
            if (fileSize == null || fileSize <= 0) {
                return "未知大小";
            }
            
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            }
        }
        
        /**
         * 获取文件扩展名
         *
         * @return 文件扩展名
         */
        public String getFileExtension() {
            if (fileName == null || fileName.isEmpty()) {
                return "wav"; // 默认扩展名
            }
            
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
                return fileName.substring(lastDotIndex + 1).toLowerCase();
            }
            
            return "wav"; // 默认扩展名
        }
        
        /**
         * 判断是否为音频文件
         *
         * @return 是否为音频文件
         */
        public boolean isAudioFile() {
            if (contentType != null && contentType.startsWith("audio/")) {
                return true;
            }
            
            String extension = getFileExtension();
            return java.util.Set.of("wav", "mp3", "ogg", "m4a", "flac", "aac").contains(extension);
        }
        
        /**
         * 获取格式化的文件信息
         *
         * @return 格式化文件信息
         */
        public String getFormattedInfo() {
            return String.format("%s (%s, %s)", 
                    fileName != null ? fileName : "generated_audio.wav",
                    getFormattedFileSize(),
                    contentType != null ? contentType : "audio/wav");
        }
    }
    
    /**
     * 判断结果是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return audioFile != null && audioFile.isValid();
    }
    
    /**
     * 获取音频URL
     *
     * @return 音频URL
     */
    public String getAudioUrl() {
        return audioFile != null ? audioFile.url() : null;
    }
    
    /**
     * 获取格式化的结果描述
     *
     * @return 格式化描述
     */
    public String getDescription() {
        if (!isValid()) {
            return "无效的 Stable Audio 生成结果";
        }
        
        return String.format("Stable Audio 文件: %s", audioFile.getFormattedInfo());
    }
    
    /**
     * 创建新的结果对象（用于替换URL）
     *
     * @param newUrl 新的URL
     * @return 新的结果对象
     */
    public StableAudioResult withNewUrl(String newUrl) {
        if (audioFile == null) {
            return this;
        }
        
        var newAudioFile = new AudioFile(
                newUrl,
                audioFile.fileSize(),
                audioFile.fileName(),
                audioFile.contentType(),
                audioFile.fileData()
        );
        
        return new StableAudioResult(newAudioFile);
    }
}
