package com.wlink.agent.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * SSE完成事件DTO
 */
@Data
public class SseCompletionEvent {
    
    /**
     * 事件类型
     */
    private String event;
    
    /**
     * 消息内容
     */
    private String message;
    
    /**
     * 时间
     */
    private long timestamp;
    
    /**
     * 是否是错
     */
    @JsonProperty("is_error")
    private boolean isError;
    
    /**
     * 创建完成事件
     * @param message 消息
     * @return 完成事件
     */
    public static SseCompletionEvent completion(String message) {
        SseCompletionEvent event = new SseCompletionEvent();
        event.setEvent("completion");
        event.setMessage(message);
        event.setTimestamp(System.currentTimeMillis());
        event.setError(false);
        return event;
    }
    
    /**
     * 创建错误事件
     * @param message 错误消息
     * @return 错误事件
     */
    public static SseCompletionEvent error(String message) {
        SseCompletionEvent event = new SseCompletionEvent();
        event.setEvent("error");
        event.setMessage(message);
        event.setTimestamp(System.currentTimeMillis());
        event.setError(true);
        return event;
    }
    
    /**
     * 创建连接关闭事件
     * @param message 消息
     * @return 连接关闭事件
     */
    public static SseCompletionEvent connectionClosed(String message) {
        SseCompletionEvent event = new SseCompletionEvent();
        event.setEvent("connection_closed");
        event.setMessage(message);
        event.setTimestamp(System.currentTimeMillis());
        event.setError(false);
        return event;
    }
} 
