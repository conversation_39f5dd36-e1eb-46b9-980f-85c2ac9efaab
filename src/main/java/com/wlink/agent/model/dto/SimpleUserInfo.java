package com.wlink.agent.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/24 18:16
 */
@Data
public class SimpleUserInfo {
    private String userId;
    @Schema()
    private String nickName;
    @Schema()
    private String userTel;
    @Schema()
    private String userTelPlaintext;
    @Schema(description = "账户类型")
    private Integer userType;
    @Schema()
    private String parentUsername;
    @Schema(description = "租户id")
    private Long tenantId;
    @Schema()
    private Integer activeStatus;

}
