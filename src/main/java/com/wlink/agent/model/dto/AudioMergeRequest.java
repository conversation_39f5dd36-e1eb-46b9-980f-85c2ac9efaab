package com.wlink.agent.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 音频合成请求
 */
@Data
public class AudioMergeRequest {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 音频URL列表（按顺序）
     */
    private List<String> audioUrls;

    /**
     * 输出文件名（不含扩展名）
     */
    private String outputFileName;

    /**
     * 音频格式（默认wav）
     */
    private String outputFormat = "wav";

    /**
     * 采样率（默认44100）
     */
    private Integer sampleRate = 44100;

    /**
     * 比特率（默认128k）
     */
    private String bitRate = "128k";

    /**
     * 声道数（默认2）
     */
    private Integer channels = 2;

    /**
     * 音频间隔时间（毫秒，默认0）
     */
    private Long intervalMs = 0L;

    /**
     * 是否标准化音量（默认true）
     */
    private Boolean normalizeVolume = true;

    /**
     * 最大处理时间（秒，默认300秒）
     */
    private Integer maxProcessingTimeSeconds = 300;
}
