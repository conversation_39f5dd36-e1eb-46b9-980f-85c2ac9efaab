package com.wlink.agent.model.dto;

import lombok.Data;

/**
 * 音频合成结果
 */
@Data
public class AudioMergeResult {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 合成后的音频URL
     */
    private String mergedAudioUrl;

    /**
     * 合成后的音频时长（毫秒）
     */
    private Long totalDurationMs;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理开始时间
     */
    private Long startTime;

    /**
     * 处理结束时间
     */
    private Long endTime;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 临时文件路径（用于清理）
     */
    private String tempFilePath;

    /**
     * 创建成功结果
     */
    public static AudioMergeResult success(String taskId, String mergedAudioUrl, Long totalDurationMs) {
        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setSuccess(true);
        result.setMergedAudioUrl(mergedAudioUrl);
        result.setTotalDurationMs(totalDurationMs);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static AudioMergeResult failure(String taskId, String errorMessage) {
        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
