package com.wlink.agent.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 音频合成请求
 */
@Data
public class AudioSynthesisRequest {

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 音频URL集合（最多7个）
     */
    private List<String> audioUrls;

    /**
     * ComfyUI WebApp ID（默认值）
     */
    private String webappId = "1950797978980253697";

    /**
     * ComfyUI API Key（默认值）
     */
    private String apiKey = "264fec3cd17144c59ec690b37a016972";

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (shotId == null || shotId <= 0) {
            return false;
        }
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        if (audioUrls == null || audioUrls.isEmpty()) {
            return false;
        }
        if (audioUrls.size() > 7) {
            return false; // 最多支持7个音频
        }
        // 检查音频URL是否有效
        for (String url : audioUrls) {
            if (url == null || url.trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }
}
