package com.wlink.agent.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 音效生成请求
 * 使用 JDK 17 的 record 特性
 */
@Schema(description = "音效生成请求")
public record SoundEffectsRequest(
        
        @Schema(description = "音效描述提示词", required = true, example = "dog barking in the rain")
        @NotBlank(message = "音效描述不能为空")
        @JSONField(name = "prompt")
        String prompt,
        
        @Schema(description = "音效时长（秒）", required = true, example = "30", minimum = "1", maximum = "30")
        @NotNull(message = "音效时长不能为空")
        @Min(value = 1, message = "音效时长最少1秒")
        @Max(value = 30, message = "音效时长最多30秒")
        @JSONField(name = "duration")
        Integer duration
) {
    
    /**
     * 创建音效生成请求的便捷方法
     *
     * @param prompt 音效描述
     * @param duration 时长（秒）
     * @return 音效生成请求
     */
    public static SoundEffectsRequest of(String prompt, int duration) {
        return new SoundEffectsRequest(prompt, duration);
    }
    
    /**
     * 创建默认30秒时长的音效生成请求
     *
     * @param prompt 音效描述
     * @return 音效生成请求
     */
    public static SoundEffectsRequest of(String prompt) {
        return new SoundEffectsRequest(prompt, 30);
    }
    
    /**
     * 验证请求参数
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return prompt != null && !prompt.trim().isEmpty() 
                && duration != null && duration >= 1 && duration <= 30;
    }
    
    /**
     * 获取格式化的描述
     *
     * @return 格式化描述
     */
    public String getDescription() {
        return String.format("音效生成: '%s' (%d秒)", prompt, duration);
    }
}
