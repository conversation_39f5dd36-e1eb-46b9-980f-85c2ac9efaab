package com.wlink.agent.model.dto;

import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageTaskStatusDTO {
    
    private Long taskId;
    private String sessionId;
    private String taskType;
    private String taskStatus;
    private Integer sessionQueuePosition;
    private Integer globalQueuePosition;
    private Date createdAt;
    private Date updatedAt;
    private String errorReason;
    
    // Calculated estimated wait time in seconds (if in queue)
    private Long estimatedWaitTimeSeconds;
    
    // Helper method to build from ImageTaskQueue entity
    public static ImageTaskStatusDTO fromEntity(AiImageTaskQueuePo entity) {
        return ImageTaskStatusDTO.builder()
                .taskId(entity.getId())
                .sessionId(entity.getSessionId())
                .taskType(entity.getTaskType())
                .taskStatus(entity.getTaskStatus())
                .sessionQueuePosition(entity.getSessionQueuePosition())
                .globalQueuePosition(entity.getGlobalQueuePosition())
                .createdAt(entity.getCreateTime())
                .updatedAt(entity.getUpdateTime())
                .errorReason(entity.getErrorReason())
                // Simple estimation: 5 seconds per task if in queue
                .estimatedWaitTimeSeconds(entity.getGlobalQueuePosition() > 0 ? 
                        (long) entity.getGlobalQueuePosition() * 5 : 0L)
                .build();
    }
} 
