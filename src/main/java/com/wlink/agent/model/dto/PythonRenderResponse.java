package com.wlink.agent.model.dto;

import lombok.Data;

/**
 * Python渲染接口响应
 */
@Data
public class PythonRenderResponse {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 时间戳
     */
    private String timestamp;
    
    /**
     * 跟踪ID (作为renderTaskId使用)
     */
    private String traceId;
    
    /**
     * 是否成功
     */
    private Boolean success;
}
