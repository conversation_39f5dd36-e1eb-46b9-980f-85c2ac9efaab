package com.wlink.agent.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/23 14:17
 */
@NoArgsConstructor
@Data
public class VisualRecordDto {


    @JsonProperty("cover")
    private CoverDTO cover;
    @JsonProperty("background_music")
    private BackgroundMusicDTO backgroundMusic;
    @JsonProperty("shots")
    private List<ShotsDTO> shots;

    @NoArgsConstructor
    @Data
    public static class CoverDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("title")
        private String title;
        @JsonProperty("subtitle2")
        private String subtitle2;
        @JsonProperty("image")
        private String image;
        @JsonProperty("subtitle")
        private String subtitle;
    }

    @NoArgsConstructor
    @Data
    public static class BackgroundMusicDTO {
        @JsonProperty("audio_url")
        private String audioUrl;
        @JsonProperty("volume")
        private Double volume;
        @JsonProperty("loop")
        private String loop;
    }

    @NoArgsConstructor
    @Data
    public static class ShotsDTO {
        @JsonProperty("scene_id")
        private String sceneId;
        @JsonProperty("scene_image")
        private String sceneImage;
        @JsonProperty("subtitle")
        private String subtitle;
        @JsonProperty("audio_clip")
        private String audioClip;
        @JsonProperty("camera_effect")
        private String cameraEffect;
        @JsonProperty("duration_seconds")
        private Integer durationSeconds;
        @JsonProperty("id")
        private Integer id;
    }
}
