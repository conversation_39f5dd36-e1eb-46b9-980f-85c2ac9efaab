package com.wlink.agent.model.dto;

import lombok.Data;

/**
 * 任务状态查询响应
 */
@Data
public class TaskStatusResponse {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 任务数据
     */
    private TaskData data;
    
    /**
     * 时间戳
     */
    private String timestamp;
    
    /**
     * 跟踪ID
     */
    private String traceId;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 任务数据
     */
    @Data
    public static class TaskData {
        
        /**
         * 任务ID
         */
        private Long id;
        
        /**
         * 任务名称
         */
        private String name;
        
        /**
         * 任务描述
         */
        private String description;
        
        /**
         * 任务类型
         */
        private String type;
        
        /**
         * 任务状态
         */
        private String status;
        
        /**
         * 优先级
         */
        private Integer priority;
        
        /**
         * 节点ID
         */
        private Integer nodeId;
        
        /**
         * 节点名称
         */
        private String nodeName;
        
        /**
         * 进度
         */
        private Integer progress;
        
        /**
         * 错误信息
         */
        private String errorMessage;
        
        /**
         * 预估时长
         */
        private Integer estimatedDuration;
        
        /**
         * 实际时长
         */
        private Integer actualDuration;
        
        /**
         * 输出文件信息
         */
        private OutputFile outputFile;
        
        /**
         * 创建时间
         */
        private String createdAt;
        
        /**
         * 开始时间
         */
        private String startedAt;
        
        /**
         * 完成时间
         */
        private String completedAt;
        
        /**
         * 更新时间
         */
        private String updatedAt;
    }
    
    /**
     * 输出文件信息
     */
    @Data
    public static class OutputFile {
        
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 文件大小
         */
        private Long fileSize;
        
        /**
         * 格式化文件大小
         */
        private String fileSizeFormatted;
        
        /**
         * MIME类型
         */
        private String mimeType;
        
        /**
         * 下载URL
         */
        private String downloadUrl;
        
        /**
         * 时长
         */
        private Integer duration;
        
        /**
         * 分辨率
         */
        private String resolution;
        
        /**
         * 比特率
         */
        private Integer bitrate;
        
        /**
         * 帧率
         */
        private Double frameRate;
        
        /**
         * 采样率
         */
        private Integer sampleRate;
        
        /**
         * 声道数
         */
        private Integer channels;
        
        /**
         * 下载次数
         */
        private Integer downloadCount;
        
        /**
         * 过期时间
         */
        private String expiresAt;
    }
}
