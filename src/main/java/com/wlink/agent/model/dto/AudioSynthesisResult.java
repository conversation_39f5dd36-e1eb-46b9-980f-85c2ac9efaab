package com.wlink.agent.model.dto;

import lombok.Data;

/**
 * 音频合成结果
 */
@Data
public class AudioSynthesisResult {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 合成结果音频URL
     */
    private String resultAudioUrl;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 任务耗时（毫秒）
     */
    private Long taskCostTime;

    /**
     * 处理开始时间
     */
    private Long startTime;

    /**
     * 处理结束时间
     */
    private Long endTime;

    /**
     * WebSocket连接URL
     */
    private String netWssUrl;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 创建成功结果
     */
    public static AudioSynthesisResult success(String taskId, Long shotId, String resultAudioUrl) {
        AudioSynthesisResult result = new AudioSynthesisResult();
        result.setTaskId(taskId);
        result.setShotId(shotId);
        result.setSuccess(true);
        result.setStatus("COMPLETED");
        result.setResultAudioUrl(resultAudioUrl);
        return result;
    }

    /**
     * 创建处理中结果
     */
    public static AudioSynthesisResult processing(String taskId, Long shotId, String netWssUrl, String clientId) {
        AudioSynthesisResult result = new AudioSynthesisResult();
        result.setTaskId(taskId);
        result.setShotId(shotId);
        result.setSuccess(null); // 处理中状态
        result.setStatus("PROCESSING");
        result.setNetWssUrl(netWssUrl);
        result.setClientId(clientId);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static AudioSynthesisResult failure(String taskId, Long shotId, String errorMessage) {
        AudioSynthesisResult result = new AudioSynthesisResult();
        result.setTaskId(taskId);
        result.setShotId(shotId);
        result.setSuccess(false);
        result.setStatus("FAILED");
        result.setErrorMessage(errorMessage);
        return result;
    }
}
