package com.wlink.agent.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Stable Audio 生成请求
 * 使用 JDK 17 的 record 特性
 */
@Schema(description = "Stable Audio 生成请求")
public record StableAudioRequest(
        
        @Schema(description = "音频生成提示词", required = true, example = "128 BPM tech house drum loop")
        @NotBlank(message = "音频生成提示词不能为空")
        @JSONField(name = "prompt")
        String prompt,
        
        @Schema(description = "去噪步数", example = "100", minimum = "1", maximum = "1000")
        @Min(value = 1, message = "去噪步数最少1步")
        @Max(value = 1000, message = "去噪步数最多1000步")
        @JSONField(name = "steps")
        Integer steps,
        
        @Schema(description = "音频总时长（秒）", example = "30", minimum = "0", maximum = "47")
        @NotNull(message = "音频总时长不能为空")
        @Min(value = 0, message = "音频总时长最少0秒")
        @Max(value = 47, message = "音频总时长最多47秒")
        @JSONField(name = "seconds_total")
        Integer secondsTotal,
        
        @Schema(description = "音频开始时间（秒）", example = "0", minimum = "0", maximum = "47")
        @Min(value = 0, message = "音频开始时间最少0秒")
        @Max(value = 47, message = "音频开始时间最多47秒")
        @JSONField(name = "seconds_start")
        Integer secondsStart
) {
    
    /**
     * 创建 Stable Audio 生成请求的便捷方法
     *
     * @param prompt 音频生成提示词
     * @param secondsTotal 音频总时长（秒）
     * @return Stable Audio 生成请求
     */
    public static StableAudioRequest of(String prompt, int secondsTotal) {
        return new StableAudioRequest(prompt, 100, secondsTotal, 0);
    }
    
    /**
     * 创建 Stable Audio 生成请求的便捷方法（带自定义步数）
     *
     * @param prompt 音频生成提示词
     * @param secondsTotal 音频总时长（秒）
     * @param steps 去噪步数
     * @return Stable Audio 生成请求
     */
    public static StableAudioRequest of(String prompt, int secondsTotal, int steps) {
        return new StableAudioRequest(prompt, steps, secondsTotal, 0);
    }
    
    /**
     * 创建完整的 Stable Audio 生成请求
     *
     * @param prompt 音频生成提示词
     * @param secondsTotal 音频总时长（秒）
     * @param secondsStart 音频开始时间（秒）
     * @param steps 去噪步数
     * @return Stable Audio 生成请求
     */
    public static StableAudioRequest of(String prompt, int secondsTotal, int secondsStart, int steps) {
        return new StableAudioRequest(prompt, steps, secondsTotal, secondsStart);
    }
    
    /**
     * 创建默认30秒时长的 Stable Audio 生成请求
     *
     * @param prompt 音频生成提示词
     * @return Stable Audio 生成请求
     */
    public static StableAudioRequest of(String prompt) {
        return new StableAudioRequest(prompt, 100, 30, 0);
    }
    
    /**
     * 验证请求参数
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return prompt != null && !prompt.trim().isEmpty() 
                && secondsTotal != null && secondsTotal >= 0 && secondsTotal <= 47
                && (secondsStart == null || (secondsStart >= 0 && secondsStart <= 47))
                && (steps == null || (steps >= 1 && steps <= 1000));
    }
    
    /**
     * 获取格式化的描述
     *
     * @return 格式化描述
     */
    public String getDescription() {
        return String.format("Stable Audio 生成: '%s' (%d秒, %d步)", 
                prompt, secondsTotal, steps != null ? steps : 100);
    }
}
