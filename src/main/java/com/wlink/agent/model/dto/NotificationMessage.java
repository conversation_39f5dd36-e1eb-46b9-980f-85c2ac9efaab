package com.wlink.agent.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DTO for broadcasting operation results via SSE.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMessage implements Serializable {


    @JsonProperty("event")
    private String event = "eventPage";
    @JsonProperty("conversation_id")
    private String conversationId;
    @JsonProperty("eventPage")
    private String eventPage;
}
