package com.wlink.agent.model;

import java.util.List;

/**
 * 图片检测结果封装类
 * 
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */
public class ImageModerationResult {
    
    /**
     * 检测是否成
     */
    private boolean success;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 数据ID
     */
    private String dataId;
    
    /**
     * 最终建
     * pass: 正常内容，建议放
     * review: 可疑内容，建议人工审 
     * block: 违规内容，建议阻
     */
    private String suggestion;
    
    /**
     * 详细检测结
     */
    private List<DetectionDetail> details;
    
    /**
     * 错误信息（当success=false时）
     */
    private String errorMessage;
    
    /**
     * 错误码（当success=false时）
     */
    private String errorCode;

    // 构造函
    public ImageModerationResult() {}

    public ImageModerationResult(boolean success, String requestId) {
        this.success = success;
        this.requestId = requestId;
    }

    // Getter和Setter方法
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }
    
    public String getDataId() { return dataId; }
    public void setDataId(String dataId) { this.dataId = dataId; }
    
    public String getSuggestion() { return suggestion; }
    public void setSuggestion(String suggestion) { this.suggestion = suggestion; }
    
    public List<DetectionDetail> getDetails() { return details; }
    public void setDetails(List<DetectionDetail> details) { this.details = details; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public String getErrorCode() { return errorCode; }
    public void setErrorCode(String errorCode) { this.errorCode = errorCode; }

    /**
     * 判断是否包含违规内容
     */
    public boolean hasViolation() {
        return "block".equals(suggestion);
    }

    /**
     * 判断是否包含可疑内容
     */
    public boolean hasSuspicious() {
        return "review".equals(suggestion);
    }

    /**
     * 判断内容是否正常
     */
    public boolean isNormal() {
        return "pass".equals(suggestion);
    }
} 
