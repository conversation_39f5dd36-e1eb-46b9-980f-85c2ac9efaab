package com.wlink.agent.model;

import jakarta.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信JS-SDK签名请求参数
 */
@Data
@Schema(description = "微信JS-SDK签名请求参数")
public class WxJsapiSignatureRequest {
    
    /**
     * 当前网页的URL，不包含#及其后面部分
     */
    @NotBlank(message = "URL不能为空")
    @Schema(description = "当前网页的URL，不包含#及其后面部分", required = true, example = "http://example.com/page")
    private String url;
} 
