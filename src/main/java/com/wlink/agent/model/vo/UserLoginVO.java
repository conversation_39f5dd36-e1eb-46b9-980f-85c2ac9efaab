package com.wlink.agent.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "用户登录返回VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLoginVO implements Serializable {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "主账户用户ID")
    private Long parentUserId;
    /**
     * 用户
     */
    @Schema()
    private String username;

    @Schema()
    private String parentUsername;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "邮箱")
    private String userEmail;

    @Schema()
    private String userTel;

    @Schema()
    private Integer userType;

    @Schema()
    private Integer authStatus;

    @Schema()
    private Boolean needModifyPassword;

    @Schema()
    private Date lastLoginTime;
    /**
     * token
     */
    @Schema(description = "token")
    private String authorization;

    /**
     * 临时token
     */
    @Schema(description = "临时token")
    private String tempAuthorization;

    /**
     * 在线用户
     */
    @Schema()
    private Integer onLineTokenNum = 0;

    @Schema()
    private Integer newUserFlag;

}
