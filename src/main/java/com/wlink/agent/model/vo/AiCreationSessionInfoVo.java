package com.wlink.agent.model.vo;

import com.wlink.agent.model.req.DesignSaveReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * AI创作会话视图对象
 */
@Schema(description = "AI创作会话视图对象")
@Data
public class AiCreationSessionInfoVo {

    /**
     * 会话id (使用 Po 中的 sessionId)
     */
    @Schema(description = "会话ID", example = "sess_123456789")
    private String conversationId;

    /**
     * 图片风格id
     */
    @Schema(description = "风格ID", example = "style_123456789")
    private Long imageStyleId;

    /**
     * 图片风格名称
     */
    @Schema(description = "风格名称", example = "小猫")
    private String imageStyleName;


    /**
     * 创作提示
     */
    @Schema(description = "创作提示, example = ")
    private String prompt;

    /**
     * 音频ID
     */
    @Schema(description = "音频ID", example = "123456789")
    private Long soundId;


    /**
     * 音色名称
     */
    @Schema(description = "音色名称", example = "小猫")
    private String soundName;


    /**
     * 图片大小比例
     */
    @Schema(description = "图片大小比例", example = "16:9")
    private String imageSize;


    @Schema(description = "设计内容列表")
    private List<DesignSaveReq> designList;


    /**
     * 章节提示词列表
     */
    @Schema(description = "章节提示词列表")
    private List<ChapterPrompt> chapterPromptList;


    @Schema(description = "图片列表")
    private List<String> imageList;


    /**
     * 章节提示词对象
     */
    @Data
    @Schema(description = "章节提示词对象")
    public static class ChapterPrompt {
        @Schema(description = "章节ID", required = true, example = "chapter_123456789")
        private String segmentId;

        @Schema(description = "章节提示词", required = true, example = "This is a chapter prompt")
        private String prompt;
    }

}
