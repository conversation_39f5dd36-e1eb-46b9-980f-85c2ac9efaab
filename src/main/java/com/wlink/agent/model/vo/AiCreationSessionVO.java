package com.wlink.agent.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

/**
 * AI创作会话视图对象
 */
@Schema(description = "AI创作会话视图对象")
@Data
public class AiCreationSessionVO {

    /**
     * 会话id (使用 Po 中的 sessionId)
     */
    @Schema(description = "会话ID", example = "sess_123456789")
    private String sessionId;

    /**
     * dify会话id
     */
    @Schema(description = "Dify会话ID", example = "conv_987654321")
    private String conversationId;


    //尺寸
    private String imageSize;

    /**
     * 创作标题
     */
    @Schema(description = "创作标题", example = "春天的诗")
    private String title;

    /**
     * 创作提示
     */
    @Schema(description = "创作提示, example = ")
    private String prompt;

    /**
     * 音频ID
     */
    @Schema(description = "音频ID", example = "123456789")
    private Long soundId;

    /**
     * 图片地址
     */
    @Schema(description = "图片URL", example = "https://example.com/image.jpg")
    private String imageUrl;

    /**
     * 状0-进行1-已完2-已归
     */
    @Schema(description = "会话状0-进行1-已完2-已归", example = "1")
    private Integer status;


    //章节数量
    @Schema(description = "章节数量", example = "3")
    private Integer chapterCount;

    /**
     * Token使用
     */
    @Schema(description = "Token使用, example = ", example = "1000")
    private Integer tokenUsage;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-03-20T10:30:00Z")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-03-20T11:30:00Z")
    private Date updateTime;

}
