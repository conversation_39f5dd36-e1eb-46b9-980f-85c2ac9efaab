package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 火山引擎图像API调用记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ai_image_api_record")
public class AiImageApiRecordPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * API调用类型
     * 1-生成图片(generateImage)
     * 2-增强图片(enhanceImage)
     * 3-种子编辑(seedEditImage)
     * 4-保留角色形象(retainCharacterImage)
     * 5-图像内部编辑(paintingEditImage)
     */
    private Integer apiType;

    /**
     * API方法名称
     */
    private String apiMethod;

    /**
     * 请求参数 JSON 字符
     */
    private String requestParams;

    /**
     * 结果图片URL
     */
    private String resultImageUrl;

    /**
     * 响应数据 JSON 字符
     */
    private String responseData;

    /**
     * 状态码
     */
    private String statusCode;

    /**
     * 状态消
     */
    private String statusMessage;

    /**
     * 调用状态：1-成功-失败
     */
    private Integer status;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 调用耗时(毫秒)
     */
    private Integer costTime;

    /**
     * 是否使用自定义账号：1-是，0-
     */
    private Integer useCustomAccount;

    /**
     * 账号ID（如果使用自定义账号
     */
    private Long accountId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 
