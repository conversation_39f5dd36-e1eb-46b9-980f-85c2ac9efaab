package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 短信发送记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_sms_records")
public class AiSmsRecordPo extends BasePo {

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 接收手机
     */
    private String phoneNumber;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 短信模板编码
     */
    private String templateCode;

    /**
     * 短信模板参数(JSON格式)
     */
    private String templateParam;

    /**
     * 阿里云请求ID
     */
    private String requestId;
    
    /**
     * 阿里云短信发送回执ID
     */
    private String bizId;

    /**
     * 发送状0-发送中 1-发送成2-发送失
     */
    private Integer status;

    /**
     * 错误
     */
    private String errorCode;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 发送时
     */
    private Date sendTime;

    /**
     * 关联用户ID
     */
    private String userId;

    /**
     * 业务类型 1-验证2-通知 3-营销
     */
    private Integer businessType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识 0-正常 1-已删
     */
    private Integer delFlag;
} 
