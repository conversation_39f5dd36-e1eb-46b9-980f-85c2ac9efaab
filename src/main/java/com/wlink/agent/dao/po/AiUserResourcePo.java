package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户资源表
 * @TableName ai_user_resource
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "ai_user_resource")
@Data
public class AiUserResourcePo implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源唯一编码
     */
    private String code;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 资源类型(1-图片,2-视频,3-音频)
     */
    private Integer resourceType;

    /**
     * 生成状态(PENDING-生成中,SUCCESS-成功,FAILED-失败)
     */
    private String generationStatus;

    /**
     * 资源URL集合(JSON格式)
     */
    private String resourceUrls;

    /**
     * 资源大小(字节)
     */
    private Long resourceSize;

    /**
     * 图片/视频宽度
     */
    private Integer width;

    /**
     * 图片/视频高度
     */
    private Integer height;

    /**
     * 视频/音频时长(秒)
     */
    private Integer duration;

    /**
     * 使用的模型类型
     */
    private String modelType;

    /**
     * 生成提示词
     */
    private String prompt;

    /**
     * 参考图片URL集合(JSON格式)
     */
    private String referenceImages;

    /**
     * 生成参数(JSON格式)
     */
    private String generationParams;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 外部API请求ID
     */
    private String externalRequestId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
