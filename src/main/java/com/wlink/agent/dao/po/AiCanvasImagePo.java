package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 画布图片资源表
 * @TableName ai_canvas_image
 */
@TableName(value ="ai_canvas_image")
@Data
public class AiCanvasImagePo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 分镜编码
     */
    private String shotCode;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 图片生成提示词
     */
    private String imagePrompt;

    /**
     * 图片描述
     */
    private String imageDesc;

    /**
     * 图片宽高比
     */
    private String imageAspectRatio;

    /**
     * 图片状态
     */
    private String imageStatus;

    /**
     * 参考图片URL
     */
    private String referenceImage;


    /**
     * 视频生成提示词
     */
    private String videoConvertPrompt;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}