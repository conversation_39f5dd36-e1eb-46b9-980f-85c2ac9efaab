package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 分镜对口型记录表
 */
@Data
@TableName("ai_shot_lip_sync")
public class AiShotLipSyncPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 任务ID（ComfyUI返回的taskId）
     */
    private String taskId;

    /**
     * 客户端ID（ComfyUI返回的clientId）
     */
    private String clientId;

    /**
     * 音频地址
     */
    private String audioUrl;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 任务状态：RUNNING-运行中, COMPLETED-已完成, FAILED-失败
     */
    private String status;

    /**
     * 生成的视频地址
     */
    private String resultVideoUrl;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 任务耗时（毫秒）
     */
    private Long taskCostTime;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Integer delFlag;
}
