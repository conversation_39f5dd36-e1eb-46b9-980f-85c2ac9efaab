package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 支付退款实体类
 */
@Data
@TableName("ai_pay_refund")
public class AiPayRefundPo {

    /**
     * 退款ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 退款单
     */
    private String refundNo;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 退款金额（单位：分
     */
    private Integer amount;

    /**
     * 退款原
     */
    private String reason;

    /**
     * 是否扣除积分-否，1-
     */
    private Integer deductPoints;

    /**
     * 处理状态：0-处理中，1-成功-失败
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识-未删除，1-已删
     */
    @TableLogic
    private Integer delFlag;
} 
