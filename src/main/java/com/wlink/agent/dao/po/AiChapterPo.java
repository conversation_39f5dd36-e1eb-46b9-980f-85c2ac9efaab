package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 章节表实体类
 */
@Data
@TableName("ai_chapter")
public class AiChapterPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 章节ID
     */
    @TableField("segment_id")
    private String segmentId;

    /**
     * 章节名称
     */
    @TableField("segment_name")
    private String segmentName;


    /**
     * 提示词
     */
    @TableField("prompt")
    private String prompt;


    /**
     * 场景数量
     */
    @TableField("scene_count")
    private Integer sceneCount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;
} 
