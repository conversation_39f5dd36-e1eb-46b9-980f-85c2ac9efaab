package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/25 11:34
 */

@Data
public class BasePo implements Serializable {

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     *
     */
    private Date createTime;


    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
