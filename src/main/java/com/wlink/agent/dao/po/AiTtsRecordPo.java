package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TTS调用记录 * @TableName ai_tts_record
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value ="ai_tts_record")
@Data
public class AiTtsRecordPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    private String conversationId;


    private Integer source;

    /**
     * 声音ID (关联 agent_sound
     */
    private Long voiceId;

    /**
     * 语     */
    private String rate;


    private Integer pitch;


    private String volume;


    private String contentId;


    private String type;

    private String tone;

    /**
     * 需要转换的文本内容
     */
    private String text;

    /**
     * 生成状态：0-待处理，1-成功-失败
     */
    private Integer status;


    private String emotion;


    /**
     * 音频索引
     */
    private Integer audioIndex;

    /**
     * 生成的音频文件URL或本地路     */
    private String audioUrl;


    //音频长度
    private Long audioLength;

    //音频大小
    private Long audioSize;

    /**
     * 失败时的错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
