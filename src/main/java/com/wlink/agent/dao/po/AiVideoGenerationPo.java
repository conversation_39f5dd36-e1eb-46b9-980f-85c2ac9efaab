package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频生成记录表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ai_video_generation")
public class AiVideoGenerationPo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private String userId;


    /**
     * 分镜ID
     */
    private Long shotId;


    /**
     * 生成任务ID
     */
    private String generateTaskId;

    /**
     * 模型
     */
    private String model;

    
    /**
     * 生成提示词
     */
    private String prompt;
    
    /**
     * 首帧图片URL
     */
    private String firstFrameImage;
    
    /**
     * 尾帧图片URL
     */
    private String lastFrameImage;
    
    /**
     * 分辨率(如:1920x1080)
     */
    private String resolution;
    
    /**
     * 视频比例(如:16:9)
     */
    private String ratio;
    
    /**
     * 视频时长(秒)
     */
    private Integer duration;
    
    /**
     * 帧率
     */
    private Integer fps;
    
    /**
     * 状态(0-排队中,1-处理中,2-处理完成,3-处理失败)
     */
    private Integer status;
    
    /**
     * 生成的视频URL
     */
    private String videoUrl;

    /**
     * 消耗积分
     */
    private Integer consumePoints;

    /**
     * 任务信息
     */
    private String taskInfo;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 队列位置
     */
    private Integer queuePosition;
    
    /**
     * 开始处理时间
     */
    private Date startTime;
    
    /**
     * 完成时间
     */
    private Date completeTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;
}
