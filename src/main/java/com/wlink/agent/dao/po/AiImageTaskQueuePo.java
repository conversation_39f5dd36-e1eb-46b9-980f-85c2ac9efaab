package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName ai_image_task_queue
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value ="ai_image_task_queue")
@Data
public class AiImageTaskQueuePo implements Serializable {

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     *
     */
    private Date createTime;


    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 会话ID
     */
    private String sessionId;


    private String userId;

    /**
     * 任务类型（GENERATE或RETAIN
     */
    private String taskType;


    private Integer contentType;


    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 请求参数（JSON格式
     */
    private String requestParams;

    /**
     * 任务状态（PENDING、PROCESSING、COMPLETED、FAILED
     */
    private String taskStatus;

    /**
     * 结果（JSON格式
     */
    private String result;


    private String imageResult;

    /**
     * 错误原因
     */
    private String errorReason;

    /**
     * 任务状态信息（JSON格式
     * 用于存储任务处理过程中的状态信
     */
    private String taskInfo;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 会话内队列位
     */
    private Integer sessionQueuePosition;

    /**
     * 全局队列位置
     */
    private Integer globalQueuePosition;

    /**
     * 供应商账号ID
     */
    private Long vendorAccountId;
    
    /**
     * 供应商类
     */
    private String imageModel;
    
    /**
     * 请求ID
     */
    private String requestId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 
