package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频模型分辨率配置表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ai_video_model_resolution_config")
public class AiVideoModelResolutionConfigPo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 尺寸配置ID
     */
    private Long sizeConfigId;
    
    /**
     * 宽高比(16:9, 4:3, 1:1, 3:4, 9:16, 21:9, adaptive, keep_ratio)
     */
    private String ratio;
    
    /**
     * 宽高比显示名称
     */
    private String ratioDisplayName;
    
    /**
     * 宽度(像素)
     */
    private Integer width;
    
    /**
     * 高度(像素)
     */
    private Integer height;
    
    /**
     * 像素尺寸描述
     */
    private String pixelSize;
    
    /**
     * 该分辨率生成需要的积分
     */
    private Integer pointsCost;
    
    /**
     * 状态(0-禁用,1-启用)
     */
    private Integer status;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;
}
