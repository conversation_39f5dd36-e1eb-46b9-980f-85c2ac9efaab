package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * AI创作视觉记录实体
 */
@Data
@TableName("ai_creation_visual_record")
public class AiCreationVisualRecordPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("visual_record_code")
    private String visualRecordCode;


    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;


    @TableField("cover_id")
    private String coverId;


    /**
     * 视觉数据(JSON格式)
     */
    @TableField("content_data")
    private String contentData;

    /**
     * 发布状0-未发1-已发
     */
    @TableField("publish_status")
    private Integer publishStatus;

    /**
     * 分享令牌
     */
    @TableField("share_token")
    private String shareToken;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 封面图片URL
     */
    @TableField("cover_image")
    private String coverImage;

    /**
     * 副标描述
     */
    @TableField("subtitle")
    private String subtitle;

    @Schema(description = "视频生成状0: 未生1: 生成2: 生成完成 3: 生成失败")
    @TableField("video_state")
    private Integer videoState;

    @Schema(description = "视频生成进度")
    @TableField("video_progress")
    private Integer videoProgress;

    @Schema(description = "视频地址")
    @TableField("video_url")
    private String videoUrl;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private Date publishTime;

    /**
     * 创建用户ID
     */
    @TableField("user_id")
    private String userId;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;



    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableField
    private Integer delFlag;
} 
