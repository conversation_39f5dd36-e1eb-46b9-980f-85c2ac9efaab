package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 音频合成任务记录表
 */
@Data
@TableName("ai_audio_synthesis_task")
public class AiAudioSynthesisTaskPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID（ComfyUI返回的taskId）
     */
    private String taskId;

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 音频URL集合（JSON格式）
     */
    private String audioUrls;

    /**
     * 音频文件数量
     */
    private Integer audioCount;

    /**
     * ComfyUI WebApp ID
     */
    private String webappId;

    /**
     * ComfyUI API Key
     */
    private String apiKey;

    /**
     * 客户端ID（ComfyUI返回的clientId）
     */
    private String clientId;

    /**
     * WebSocket连接URL
     */
    private String netWssUrl;

    /**
     * 任务状态：PENDING-等待中, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败, CANCELLED-已取消
     */
    private String status;

    /**
     * 合成结果音频地址
     */
    private String resultAudioUrl;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 任务耗时（毫秒）
     */
    private Long taskCostTime;

    /**
     * 处理开始时间
     */
    private Date processingStartTime;

    /**
     * 处理结束时间
     */
    private Date processingEndTime;

    /**
     * 请求参数（JSON格式）
     */
    private String requestParams;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 任务状态枚举
     */
    public static class Status {
        public static final String PENDING = "PENDING";
        public static final String PROCESSING = "PROCESSING";
        public static final String COMPLETED = "COMPLETED";
        public static final String FAILED = "FAILED";
        public static final String CANCELLED = "CANCELLED";
    }
}
