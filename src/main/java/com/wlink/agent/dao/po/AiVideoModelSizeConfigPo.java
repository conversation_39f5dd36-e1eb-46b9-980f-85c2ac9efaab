package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频模型尺寸配置表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ai_video_model_size_config")
public class AiVideoModelSizeConfigPo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 尺寸名称(480p, 720p, 1080p, 768P, 1080P)
     */
    private String sizeName;
    
    /**
     * 尺寸显示名称
     */
    private String sizeDisplayName;
    
    /**
     * 支持的时长(JSON数组格式,单位秒)
     */
    private String supportedDurations;
    
    /**
     * 图片数量(0-不支持图片,1-支持首帧,2-支持首尾帧)
     */
    private Integer imageCount;
    
    /**
     * 该尺寸生成需要的积分
     */
    private Integer pointsCost;
    
    /**
     * 状态(0-禁用,1-启用)
     */
    private Integer status;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;
}
