package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 分镜表实体类
 */
@Data
@TableName("ai_shot")
public class AiShotPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 章节ID
     */
    @TableField("segment_id")
    private String segmentId;

    /**
     * 场景ID
     */
    @TableField("scene_id")
    private String sceneId;

    /**
     * 场景名称
     */
    @TableField("scene_name")
    private String sceneName;

    /**
     * 分镜ID
     */
    @TableField("shot_id")
    private String shotId;

    /**
     * 分镜JSON数据
     */
    @TableField("shot_data")
    private String shotData;

    @TableField("queue")
    private Integer queue;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;
} 
