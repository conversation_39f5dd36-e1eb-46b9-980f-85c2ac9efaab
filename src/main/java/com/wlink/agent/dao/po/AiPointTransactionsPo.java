package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 积分账单 * @TableName ai_point_transactions
 */
@TableName(value ="ai_point_transactions")
@Data
public class AiPointTransactionsPo implements Serializable {
    /**
     * 交易ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 积分变动数量(正数为增负数为减
     */
    private Integer points;

    /**
     * 变动后余     */
    private Integer balance;

    /**
     * 交易类型-注册奖励-邀请奖励，3-会话消耗，4-系统赠送，5-其他
     */
    private Integer type;

    /**
     * 关联ID(如会话ID、邀请码ID
     */
    private String referenceId;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
