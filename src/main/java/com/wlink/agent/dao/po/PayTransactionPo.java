package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 支付交易记录实体
 */
@Data
@TableName("ai_pay_transaction")
public class PayTransactionPo {

    /**
     * 交易ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交易流水
     */
    private String transactionNo;

    /**
     * 关联订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 支付方式-微信支付
     */
    private Integer paymentType;

    /**
     * 第三方支付平台交易号
     */
    private String tradeNo;

    /**
     * 交易金额（单位：分）
     */
    private Integer amount;

    /**
     * 交易状态：0-创建-成功-失败
     */
    private Integer status;

    /**
     * 交易完成时间
     */
    private Date finishTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    @TableLogic
    private Integer delFlag;
} 
