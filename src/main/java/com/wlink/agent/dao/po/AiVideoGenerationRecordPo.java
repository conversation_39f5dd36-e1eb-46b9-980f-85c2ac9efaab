package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 火山引擎视频生成记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ai_video_generation_record")
public class AiVideoGenerationRecordPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 提示
     */
    private String prompt;

    /**
     * 生成源类text_to_video, image_to_video, first_last_frame)
     */
    private String sourceType;

    /**
     * 图片URL(图生视频时使
     */
    private String imageUrl;

    /**
     * 首帧URL(首尾帧生成时使用)
     */
    private String firstFrameUrl;

    /**
     * 尾帧URL(首尾帧生成时使用)
     */
    private String lastFrameUrl;

    /**
     * 生成的视频URL
     */
    private String videoUrl;

    /**
     * 任务状queued, running, cancelled, succeeded, failed)
     */
    private String status;

    /**
     * 错误
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 生成令牌
     */
    private Integer completionTokens;

    /**
     * 总令牌数
     */
    private Integer totalTokens;

    /**
     * 供应商账号ID
     */
    private Long vendorAccountId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
