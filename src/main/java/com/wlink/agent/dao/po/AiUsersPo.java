package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * 用户基础信息 * @TableName ai_users
 */
@TableName(value ="ai_users")
@Data
public class AiUsersPo implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机     */
    private String phone;


    private String region;



    private String job;



    private String age;


    private String gender;

    /**
     * 密码哈希
     */
    private String passwordHash;

    /**
     * 密码     */
    private String salt;

    /**
     * 状态：0-未激活，1-正常-禁用
     */
    private Integer status;

    /**
     * 头像URL
     */
    private String avatar;


    /**
     * api-key
     */
    private String apiKey;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录时     */
    private Date lastLoginTime;

    /**
     * 可用积分数量
     */
    private Integer points;

    /**
     * 账户激活时     */
    private Date activationTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 微信OpenID
     */
    private String wechatOpenid;
    
    /**
     * 微信UnionID
     */
    private String wechatUnionid;
    
    /**
     * 微信昵称
     */
    private String wechatNickname;
    
    /**
     * 微信头像URL
     */
    private String wechatAvatar;
    
    /**
     * 微信绑定时间
     */
    private Date wechatBindTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
