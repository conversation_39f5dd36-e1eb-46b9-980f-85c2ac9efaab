package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 邀请码 * @TableName ai_invitation_codes
 */
@TableName(value ="ai_invitation_codes")
@Data
public class AiInvitationCodesPo implements Serializable {
    /**
     * 邀请码ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 创建者用户ID
     */
    private String createUserId;

    /**
     * 使用者用户ID
     */
    private String usedUserId;

    /**
     * 状态：0-已使用，1-可用
     */
    private Integer status;

    /**
     * 使用时间
     */
    private Date usedTime;

    /**
     * 过期时间(null表示永不过期)
     */
    private Date expireTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
