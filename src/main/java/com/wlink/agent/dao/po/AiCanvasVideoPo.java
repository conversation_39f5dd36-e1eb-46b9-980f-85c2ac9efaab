package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 画布视频资源表
 * @TableName ai_canvas_video
 */
@TableName(value ="ai_canvas_video")
@Data
public class AiCanvasVideoPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 分镜编码
     */
    private String shotCode;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 视频生成提示词
     */
    private String videoPrompt;

    /**
     * 视频描述
     */
    private String videoDesc;

    /**
     * 视频时长(毫秒)
     */
    private Integer videoDuration;

    /**
     * 视频宽高比
     */
    private String videoAspectRatio;

    /**
     * 视频状态
     */
    private String videoStatus;

    /**
     * 开始帧图片URL
     */
    private String startFrameImage;

    /**
     * 结束帧图片URL
     */
    private String endFrameImage;

    /**
     * 音量(0.00-1.00)
     */
    private java.math.BigDecimal volume;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}