package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName agent_sound
 */
@TableName(value ="ai_sound")
@Data
public class AgentSoundPo extends BasePo {

    /**
     * 声音供应     */
    private String supplier;


    private String userName;

    /**
     * 1-系统  2-定制
     */
    private Integer type;

    /**
     * 语言标识
     */
    private String language;

    /**
     * 声音标识
     */
    private String sound;

    /**
     * 语     */
    private String rate;


    private Integer volume;

    /**
     * 性别 1- 2-     */
    private Integer sex;

    /**
     * 创建     */
    private String createBy;

    /**
     * 更新     */
    private String updateBy;


}
