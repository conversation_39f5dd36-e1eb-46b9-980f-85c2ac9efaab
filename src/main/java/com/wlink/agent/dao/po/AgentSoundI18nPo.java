package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName agent_sound_i18n
 */
@TableName(value ="ai_sound_i18n")
@Data
public class AgentSoundI18nPo extends BasePo {

    /**
     * 声音id
     */
    private Long soundId;

    /**
     * 语言标识，如：zh_CN, en_US
     */
    private String locale;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String depict;

    /**
     * 语言名称
     */
    private String languageName;

    /**
     * 音频地址
     */
    private String audioUrl;

}
