package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * FalAi Flux API请求记录实体
 * 用于记录FalAiFluxClient的API调用详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("ai_fal_flux_request_log")
public class FalAiFluxRequestLog extends BasePo {

    /**
     * 请求类型：TEXT_TO_IMAGE, IMAGE_TO_IMAGE
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 请求提示
     */
    @TableField("prompt")
    private String prompt;

    /**
     * 图像URL（对于图像到图像请求
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * Fal.ai请求ID
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 请求状态：PENDING, IN_QUEUE, IN_PROGRESS, COMPLETED, FAILED, CANCELLED
     */
    @TableField("status")
    private String status;

    /**
     * 队列位置（如果在队列中）
     */
    @TableField("queue_position")
    private Integer queuePosition;

    /**
     * 生成的图像URLs，JSON格式
     */
    @TableField("result_image_urls")
    private String resultImageUrls;

    /**
     * 请求参数，JSON格式
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 响应数据，JSON格式
     */
    @TableField("response_data")
    private String responseData;

    /**
     * 处理开始时
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 处理结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 处理时间（毫秒）
     */
    @TableField("processing_time")
    private Long processingTime;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 错误消息，如果有的话
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 用户ID，如果适用
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 业务关联ID，可用于关联到特定业务实
     */
    @TableField("business_id")
    private String businessId;
} 
