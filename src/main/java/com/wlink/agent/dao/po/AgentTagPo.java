package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 智能体能力关联表
 * @TableName agent_tag
 */
@TableName(value ="agent_tag")
@Data
public class AgentTagPo extends BasePo{


    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 智能体id
     */
    private Long agentId;

}
