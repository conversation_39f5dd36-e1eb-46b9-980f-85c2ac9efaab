package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 资源收藏实体
 */
@Data
@TableName("ai_resource_favorite")
public class AiResourceFavoritePo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 唯一编码
     */
    @TableField("code")
    private String code;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 资源ID
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 资源类型(1-角色,2-图片,3-视频,4-音频,5-文本)
     */
    @TableField("resource_type")
    private Integer resourceType;

    /**
     * 资源子类对图1-角色图片,2-场景图片,3-分镜图片)
     */
    @TableField("resource_subtype")
    private Integer resourceSubtype;

    /**
     * 资源名称
     */
    @TableField("resource_name")
    private String resourceName;

    /**
     * 资源URL
     */
    @TableField("resource_url")
    private String resourceUrl;

    /**
     * 资源数据(JSON格式)
     */
    @TableField("resource_data")
    private String resourceData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;
} 
