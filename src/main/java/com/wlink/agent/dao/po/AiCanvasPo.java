package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 画布表
 * @TableName ai_canvas
 */
@TableName(value ="ai_canvas")
@Data
public class AiCanvasPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布唯一编码
     */
    private String code;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID，如果是从分镜转换则有值，新建则为空
     */
    private String sessionId;


    /**
     * 画布宽高比
     */
    private String ratio;

    /**
     * 章节id
     */
    private String segmentId;

    /**
     * 画布名称
     */
    private String canvasName;

    /**
     * 画布描述
     */
    private String canvasDesc;

    /**
     * 封面图片URL
     */
    private String coverImage;

    /**
     * 状态(0-草稿,1-已发布)
     */
    private Integer status;


    /**
     * 种子整数
     */
    private Integer seed;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}