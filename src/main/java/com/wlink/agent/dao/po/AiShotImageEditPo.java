package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 分镜图片编辑记录表
 */
@Data
@TableName("ai_shot_image_edit")
public class AiShotImageEditPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 任务ID（ComfyUI返回的taskId）
     */
    private String taskId;

    /**
     * 客户端ID（ComfyUI返回的clientId）
     */
    private String clientId;

    /**
     * WebSocket连接URL
     */
    private String netWssUrl;

    /**
     * 原图URL
     */
    private String originalImageUrl;

    /**
     * 遮罩图URL
     */
    private String maskImageUrl;

    /**
     * 编辑提示词
     */
    private String prompt;

    /**
     * 任务状态：RUNNING-运行中, COMPLETED-已完成, FAILED-失败
     */
    private String status;

    /**
     * 结果图片URL
     */
    private String resultImageUrl;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 任务耗时（毫秒）
     */
    private Long taskCostTime;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Integer delFlag;
}
