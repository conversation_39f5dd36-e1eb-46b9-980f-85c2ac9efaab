package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 画布音轨关联表
 * @TableName ai_canvas_track
 */
@TableName(value = "ai_canvas_track")
@Data
public class AiCanvasTrackPo implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 音轨名称
     */
    private String trackName;

    /**
     * 音轨开始时间（毫秒）
     */
    private Long startTime;

    /**
     * 音轨结束时间（毫秒）
     */
    private Long endTime;

    /**
     * 音轨排序序号
     */
    private Integer sortOrder;

    /**
     * 音轨类型（1-背景音乐，2-音效，3-旁白，4-对话）
     */
    private Integer trackType;

    /**
     * 音轨音量（0-100）
     */
    private Integer volume = 100;

    /**
     * 是否静音（0-否，1-是）
     */
    private Integer muted = 0;

    /**
     * 是否锁定（0-否，1-是）
     */
    private Integer locked = 0;

    /**
     * 音轨描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记（0-正常，1-删除）
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
