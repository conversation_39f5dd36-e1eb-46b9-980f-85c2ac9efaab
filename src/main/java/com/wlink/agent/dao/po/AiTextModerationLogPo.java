package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文本审核调用记录
 * 
 * <AUTHOR> Assistant
 * @since 2024-12-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_text_moderation_log")
public class AiTextModerationLogPo {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 文本内容
     */
    private String content;

    /**
     * 检测服务类chat_detection
     */
    private String serviceType;

    /**
     * 审核建议(pass/review/block)
     */
    private String suggestion;

    /**
     * 审核标签（JSON数组
     */
    private String labels;

    /**
     * 审核理由
     */
    private String reason;

    /**
     * 风险分数
     */
    private Double riskScore;

    /**
     * 阿里云请求ID
     */
    private String requestId;

    /**
     * 响应时间(毫秒)
     */
    private Long responseTime;

    /**
     * 是否成功(0:失败,1:成功)
     */
    private Boolean isSuccess;

    /**
     * 错误
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 详细结果JSON
     */
    private String details;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 
