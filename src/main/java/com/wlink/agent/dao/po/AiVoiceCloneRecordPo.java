package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 语音克隆记录表实体类
 * @TableName ai_voice_clone_record
 */
@TableName(value = "ai_voice_clone_record")
@Data
public class AiVoiceCloneRecordPo extends BasePo {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 声音ID（MiniMax返回的voice_id）
     */
    private String voiceId;

    /**
     * 声音名称
     */
    private String name;

    /**
     * 性别：1-男，2-女
     */
    private Integer sex;

    /**
     * 原始音频OSS地址
     */
    private String ossUrl;

    /**
     * 试听音频OSS地址
     */
    private String demoAudio;

    /**
     * 输入是否敏感：0-否，1-是
     */
    private Boolean inputSensitive;

    /**
     * 输入敏感类型
     */
    private Integer inputSensitiveType;

    /**
     * 状态：PENDING-待确认，CONFIRMED-已确认，CANCELLED-已取消
     */
    private String status;

    /**
     * 确认时间
     */
    private Date confirmedTime;
}
