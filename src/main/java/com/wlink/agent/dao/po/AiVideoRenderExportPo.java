package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频渲染导出记录表
 * @TableName ai_video_render_export
 */
@TableName(value = "ai_video_render_export")
@Data
public class AiVideoRenderExportPo implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 比例
     */
    private String ratio;


    /**
     * 帧率
     */
    private Integer fps;

    /**
     * 是否显示字幕(0-不显示,1-显示)
     */
    private Integer showSubtitle;

    /**
     * 渲染任务ID (Python渲染服务返回的任务ID)
     */
    private String renderTaskId;

    /**
     * 渲染状态(0-排队中,1-渲染中,2-已完成,3-失败)
     */
    private Integer status;

    /**
     * 渲染后的视频地址
     */
    private String videoUrl;


    /**
     *视频时长
     */
    private Long videoDuration;


    /**
     * 视频首帧
     */
    private String firstFrameUrl;

    /**
     * 分享状态(0-未分享,1-已分享)
     */
    private Integer shareStatus;

    /**
     * 分享码
     */
    private String shareCode;

    /**
     * 分享时间
     */
    private Date shareTime;

    /**
     * 画布数据JSON，发送给Python渲染接口
     */
    private String canvasDataJson;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 查询失败次数
     */
    private Integer queryFailCount = 0;

    /**
     * 渲染开始时间
     */
    private Date startTime;

    /**
     * 渲染完成时间
     */
    private Date completeTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
