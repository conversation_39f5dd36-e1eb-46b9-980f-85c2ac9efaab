package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 画布背景音乐表
 * @TableName ai_canvas_background_music
 */
@TableName(value = "ai_canvas_background_music")
@Data
public class AiCanvasBackgroundMusicPo implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 音频地址
     */
    private String audioUrl;

    /**
     * 音频名称
     */
    private String audioName;

    /**
     * 音频时长（毫秒）
     */
    private Long audioDuration;

    /**
     * 开始播放时间（毫秒）
     */
    private Long startPlayTime;

    /**
     * 结束播放时间（毫秒）
     */
    private Long endPlayTime;

    /**
     * 音轨开始时间（毫秒）
     */
    private Long startTrackTime;

    /**
     * 音量（0-100）
     */
    private Double volume;

    /**
     * 淡入时间（毫秒）
     */
    private Long fadeInTime = 0L;

    /**
     * 淡出时间（毫秒）
     */
    private Long fadeOutTime = 0L;

    /**
     * 是否循环播放（0-否，1-是）
     */
    private Integer isLoop = 0;

    /**
     * 音频格式（mp3、wav、ogg等）
     */
    private String audioFormat;

    /**
     * 音频文件大小（字节）
     */
    private Long fileSize;

    /**
     * 音频来源（1-上传，2-AI生成，3-素材库）
     */
    private Integer audioSource = 1;

    /**
     * 音频描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记（0-正常，1-删除）
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
