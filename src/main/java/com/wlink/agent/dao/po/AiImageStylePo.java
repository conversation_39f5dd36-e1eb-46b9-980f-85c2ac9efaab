package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName ai_image_style
 */
@TableName(value ="ai_image_style")
@Data
public class AiImageStylePo implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 风格名称
     */
    private String styleName;

    /**
     * 风格编码
     */
    private String styleCode;

    /**
     * 分类
     */
    private String styleCategory;

    /**
     * 风格示例url
     */
    private String styleUrl;

    /**
     * 状 1-启用   0-禁用
     */
    private Integer status;

    /**
     * 描述
     */
    private String styleDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识  0-  1-     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
