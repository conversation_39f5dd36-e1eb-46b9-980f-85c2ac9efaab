package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频模型基础表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ai_video_model")
public class AiVideoModelPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型显示名称
     */
    private String modelDisplayName;

    /**
     * 模型描述
     */
    private String modelDescription;

    /**
     * 提供商(MINIMAX, DOUBAO)
     */
    private String provider;

    /**
     * 模型类型(T2V-文生视频, I2V-图生视频, FLF2V-首尾帧图生视频)
     */
    private String modelType;

    /**
     * 帧率
     */
    private Integer fps;

    /**
     * 是否包含水印(0-不包含,1-包含)
     */
    private Integer supportWatermark;

    /**
     * 是否支持种子(0-不支持,1-支持)
     */
    private Integer supportSeed;

    /**
     * 是否支持固定摄像头(0-不支持,1-支持)
     */
    private Integer supportCameraFixed;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Integer status;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;
}
