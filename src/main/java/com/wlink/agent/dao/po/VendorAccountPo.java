package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 图片服务供应商账号表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ai_vendor_account")
public class VendorAccountPo {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 供应商名( VOLCENGINE, ALIYUN, TENCENT
     */
    private String vendorName;
    
    /**
     * 账号名称
     */
    private String accountName;
    
    /**
     * 账号说明
     */
    private String description;
    
    /**
     * Access Key ID
     */
    private String accessKeyId;
    
    /**
     * Secret Access Key
     */
    private String secretAccessKey;
    
    /**
     * 其他配置信息 (JSON格式，包含额外的配置，如区域、端点等)
     */
    private String configInfo;
    
    /**
     * QPS限制
     */
    private Integer qpsLimit;
    
    /**
     * 每日配额限制
     */
    private Integer dailyQuota;
    
    /**
     * 已使用的每日配额
     */
    private Integer usedDailyQuota;
    
    /**
     * 上次重置配额时间
     */
    private Date lastQuotaResetTime;
    
    /**
     * 账号状态：0-禁用-启用
     */
    private Integer status;
    
    /**
     * 优先级：数字越小优先级越
     */
    private Integer priority;
    
    /**
     * 当前正在执行的任务数
     */
    private Integer runningTaskCount;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
