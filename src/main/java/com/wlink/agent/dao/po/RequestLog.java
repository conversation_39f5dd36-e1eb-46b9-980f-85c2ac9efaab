package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 请求日志实体
 * 用于记录控制器请求和响应的详细信
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ai_content_request_log")
public class RequestLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID，用于关联请
     */
    @TableField("conversation_id")
    private String conversationId;

    /**
     * 请求的URI路径
     */
    @TableField("request_uri")
    private String requestUri;

    /**
     * 请求的HTTP方法
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * 控制器方法名
     */
    @TableField("method_name")
    private String methodName;

    /**
     * 操作描述
     */
    @TableField("description")
    private String description;

    /**
     * 请求参数，JSON格式
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 请求体，JSON格式
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 响应内容，JSON格式
     */
    @TableField("response_body")
    private String responseBody;

    /**
     * 请求状态，true表示成功，false表示失败
     */
    @TableField("status")
    private Boolean status;

    /**
     * 错误消息，如果有的话
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 请求处理时间（毫秒）
     */
    @TableField("processing_time")
    private Long processingTime;

    /**
     * 请求时间
     */
    @TableField("request_time")
    private Date requestTime;

    /**
     * 响应时间
     */
    @TableField("response_time")
    private Date responseTime;
} 
