package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 音效生成记录
 * 
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_sound_effects_record")
public class AiSoundEffectsRecordPo {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 音频索引
     */
    private Integer audioIndex;

    /**
     * 来源(1-故事,2-画布)
     */
    private Integer source;

    /**
     * 第三方请求ID
     */
    private String requestId;

    /**
     * 音效描述提示词
     */
    private String prompt;

    /**
     * 音频时长(秒)
     */
    private Integer duration;

    /**
     * 原始音频URL
     */
    private String originalAudioUrl;

    /**
     * OSS音频URL
     */
    private String ossAudioUrl;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 生成状态(PENDING/PROCESSING/SUCCESS/FAILED)
     */
    private String generationStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 生成耗时(毫秒)
     */
    private Long generationTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}