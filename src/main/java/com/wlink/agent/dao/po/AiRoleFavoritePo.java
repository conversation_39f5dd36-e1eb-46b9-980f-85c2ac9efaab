package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 角色收藏实体 */
@Data
@TableName("ai_role_favorite")
public class AiRoleFavoritePo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 唯一编码
     */
    @TableField("code")
    private String code;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 角色ID
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 角色数据(JSON格式)
     */
    @TableField("role_data")
    private String roleData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableField("del_flag")
    private Integer delFlag;
} 
