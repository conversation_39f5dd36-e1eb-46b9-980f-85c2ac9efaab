package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 音频合成任务记录表
 */
@Data
@TableName("ai_audio_merge_task")
public class AiAudioMergeTaskPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 任务ID（UUID）
     */
    private String taskId;

    /**
     * 分镜ID
     */
    private Long shotId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 原始音频URL列表（JSON格式）
     */
    private String audioUrls;

    /**
     * 音频文件数量
     */
    private Integer audioCount;

    /**
     * 合成后的音频地址
     */
    private String mergedAudioUrl;

    /**
     * 合成后音频总时长（毫秒）
     */
    private Long totalDurationMs;

    /**
     * 任务状态：PENDING-等待中, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败, CANCELLED-已取消
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理开始时间
     */
    private Date processingStartTime;

    /**
     * 处理结束时间
     */
    private Date processingEndTime;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 临时工作目录
     */
    private String tempWorkDir;

    /**
     * 请求参数（JSON格式）
     */
    private String requestParams;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Integer delFlag;

    /**
     * 任务状态枚举
     */
    public static class Status {
        public static final String PENDING = "PENDING";
        public static final String PROCESSING = "PROCESSING";
        public static final String COMPLETED = "COMPLETED";
        public static final String FAILED = "FAILED";
        public static final String CANCELLED = "CANCELLED";
    }
}
