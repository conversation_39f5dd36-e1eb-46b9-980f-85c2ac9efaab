package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 画布音频资源表
 * @TableName ai_canvas_audio
 */
@TableName(value ="ai_canvas_audio")
@Data
public class AiCanvasAudioPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 分镜编码
     */
    private String shotCode;

    /**
     * 音频URL
     */
    private String audioUrl;

    /**
     * 音频类型(1-旁白,2-音效,3-背景音乐)
     */
    private Integer audioType;

    /**
     * 音频文本，可能是旁白或对话
     */
    private String text;

    /**
     * 声音ID
     */
    private String voiceId;

    /**
     * 音频时长(毫秒)
     */
    private Long audioDuration;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 音量(0.00-1.00)
     */
    private java.math.BigDecimal volume;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}