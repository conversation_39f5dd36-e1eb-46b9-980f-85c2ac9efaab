package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * AI图片修改记录 * @TableName ai_image_modify_record
 */
@TableName(value ="ai_image_modify_record")
@Data
public class AiImageModifyRecordPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 修改编码
     */
    private String modifyCode;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 内容类型 (2-场景,3-角色,4-分镜)
     */
    private Integer contentType;



    /**
     * 功能类型：edit（编辑）、redraw（重绘）、instant（一致性生成）
     */
    private String functionType;


    /**
     * 提示     */
    private String prompt;


    /**
     * 一级ID
     */
    private String primaryId;

    /**
     * 二级ID (可
     */
    private String secondaryId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 图片源URL
     */
    private String sourceImageUrl;

    /**
     * 修改后的图片URL
     */
    private String modifiedImageUrl;


    //参考角色图
    private String characterImageUrl1;

    //参考角色图
    private String characterImageUrl2;

    //参考场景图
    private String sceneImageUrl1;

    /**
     * 状(例如: 0: 处理 1: 成功, 2: 失败)
     */
    private Integer status;

    /**
     * 是否为当前使用的图片 (0- 1-
     */
    private Boolean isCurrentUsed;

    /**
     * 失败原因 (status 为失败状态时)
     */
    private String failureReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识 0- 1-     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
