package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 画布素材表
 * @TableName ai_canvas_material
 */
@TableName(value ="ai_canvas_material")
@Data
public class AiCanvasMaterialPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 素材类型(1-图片,2-视频)
     */
    private Integer materialType;

    /**
     * 素材来源(1-生成,2-上传)
     */
    private Integer materialSource;

    /**
     * 素材URL
     */
    private String materialUrl;

    /**
     * 素材名称
     */
    private String materialName;

    /**
     * 素材描述
     */
    private String materialDesc;

    /**
     * 素材时长
     */
    private Long materialDuration;

    /**
     * 生成记录ID(来源为生成时有值)
     */
    private Long generationRecordId;

    /**
     * 首帧图片URL(仅视频)
     */
    private String firstFrameUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
