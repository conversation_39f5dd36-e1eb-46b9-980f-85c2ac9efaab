package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * AI创作内容实体
 */
@Data
@TableName("ai_creation_content")
public class AiCreationContentPo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉)
     */
    @TableField("content_type")
    private Integer contentType;

    /**
     * 序号
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 内容数据(JSON格式)
     */
    @TableField("content_data")
    private String contentData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableField("del_flag")
    private Integer delFlag;
} 
