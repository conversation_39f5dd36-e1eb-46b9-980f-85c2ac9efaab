package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 支付订单实体
 */
@Data
@TableName("ai_pay_order")
public class PayOrderPo {

    /**
     * 订单ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 微信用户openid（用于小程序支付
     */
    private String openid;

    /**
     * 订单金额（单位：分）
     */
    private Integer amount;

    /**
     * 商品标题
     */
    private String subject;

    /**
     * 商品描述
     */
    private String body;

    /**
     * 订单状态：0-未支付，1-支付中，2-已支付，3-已取消，4-已退
     */
    private Integer status;

    /**
     * 支付方式-微信支付
     */
    private Integer payType;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private Integer delFlag;
} 
