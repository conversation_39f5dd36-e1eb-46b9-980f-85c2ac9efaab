package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户活跃度日志表
 * @TableName ai_user_activity_log
 */
@TableName(value ="ai_user_activity")
@Data
public class AiUserActivityPo implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 活跃日期（格式：yyyy-MM-dd
     */
    private String activityDate;

    /**
     * 当日最后活跃时
     */
    private Date lastActiveTime;

    /**
     * 请求次数
     */
    private Integer requestCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 
