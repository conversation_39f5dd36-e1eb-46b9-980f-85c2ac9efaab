package com.wlink.agent.dao.dto;

import lombok.Data;

@Data
public class LiveMessageDTO {

    private String type;

    private MessageData data;

    @Data
    public static class MessageData {

        private String timestamp;
    }

    @Data
    public static class DanmuData extends MessageData {

        private String userId;

        private String nickname;

        private String content;
    }

    @Data
    public static class GiftData extends MessageData {

        private String senderId;

        private String senderName;

        private String giftId;

        private String giftName;

        private Integer quantity;

        private Double totalValue;
    }

    @Data
    public static class AnnouncementData extends MessageData {

        private String broadcasterId;

        private String broadcasterName;

        private String content;

        private String priority;
    }
}
