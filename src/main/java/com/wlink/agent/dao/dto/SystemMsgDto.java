package com.wlink.agent.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/24 15:29
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SystemMsgDto {

    private Integer type;

    private String msg;

    private Date timestamp;
}
