package com.wlink.agent.dao.dto;

import lombok.Data;

import java.util.Date;

@Data
public class AgentShortVideoDTO {
    
    /**
     * 视频ID
     */
    private Long id;

    /**
     * 视频名称
     */
    private String videoName;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 视频预览
     */
    private String videoCover;

    /**
     * 视频大小
     */
    private Long videoSize;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 点赞
     */
    private Integer likeCount;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
