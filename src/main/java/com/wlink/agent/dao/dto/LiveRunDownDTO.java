package com.wlink.agent.dao.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/14 19:45
 */
@NoArgsConstructor
@Data
public class LiveRunDownDTO {

    @JsonProperty("segments")
    private List<Segment> segments;

    @NoArgsConstructor
    @Data
    public static class Segment {
        @JsonProperty("segmentId")
        private Long segmentId;
        @JsonProperty("segmentName")
        private String segmentName;
        @JsonProperty("segmentType")
        private String segmentType;
        @JsonProperty("shots")
        private List<Shot> shots;
        @JsonProperty("danceList")
        private List<Object> danceList;
        @JsonProperty("dances")
        private List<Dance> dances;

    }

    @NoArgsConstructor
    @Data
    public static class Shot {
        @JsonProperty("cameraPosition")
        private String cameraPosition;
        @JsonProperty("soundEffect")
        private String soundEffect;
        @JsonProperty("roleList")
        private List<Role> roleList;
    }

    @NoArgsConstructor
    @Data
    public static class Role {
        @JsonProperty("roleName")
        private String roleName;
        @JsonProperty("roleId")
        private String roleId;
        @JsonProperty("speechContent")
        private String speechContent;
        @JsonProperty("speechAudio")
        private String speechAudio;
        @JsonProperty("audioToFace")
        private String audioToFace;
        @JsonProperty("audioDuration")
        private Long audioDuration;
        @JsonProperty("action")
        private String action;
    }

    @NoArgsConstructor
    @Data
    public static class Dance {
        @JsonProperty("danceName")
        private String danceName;
        @JsonProperty("agentList")
        private List<Agent> agentList;
    }
    @NoArgsConstructor
    @Data
    public static class Agent {
        @JsonProperty("agentId")
        private Long agentId;
        @JsonProperty("clothName")
        private String clothName;
    }

}
