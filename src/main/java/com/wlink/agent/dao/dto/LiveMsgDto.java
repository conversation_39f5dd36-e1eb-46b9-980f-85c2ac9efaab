package com.wlink.agent.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/14 15:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LiveMsgDto {


    private List<BarrageInfoDto> danmus  = new ArrayList<BarrageInfoDto>();


    private List<GiftInfoDto> gifts = new ArrayList<GiftInfoDto>();



}
