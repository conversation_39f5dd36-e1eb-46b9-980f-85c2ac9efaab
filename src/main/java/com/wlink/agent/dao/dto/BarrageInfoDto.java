package com.wlink.agent.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/6 17:22
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BarrageInfoDto {

    private String userId;

    private String username;

    private String danmu;

    private Date timestamp;


}
