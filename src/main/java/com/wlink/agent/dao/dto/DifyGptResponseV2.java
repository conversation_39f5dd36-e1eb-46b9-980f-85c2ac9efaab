package com.wlink.agent.dao.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/21 18:35
 */
@NoArgsConstructor
@Data
public class DifyGptResponseV2 {


    @JsonProperty("event")
    private String event;
    @JsonProperty("conversation_id")
    private String conversationId;
    @JsonProperty("message_id")
    private String messageId;
    @JsonProperty("created_at")
    private Integer createdAt;
    @JsonProperty("task_id")
    private String taskId;
    @JsonProperty("id")
    private String id;
    @JsonProperty("answer")
    private String answer;
    @JsonProperty("from_variable_selector")
    private List<String> fromVariableSelector;
    @JsonProperty("metadata")
    private MetadataDTO metadata;
    @JsonProperty("files")
    private List<FilesDTO> files;

    @NoArgsConstructor
    @Data
    public static class MetadataDTO {
        @JsonProperty("retriever_resources")
        private List<?> retrieverResources;
        @JsonProperty("usage")
        private UsageDTO usage;

        @NoArgsConstructor
        @Data
        public static class UsageDTO {
            @JsonProperty("prompt_tokens")
            private Integer promptTokens;
            @JsonProperty("prompt_unit_price")
            private String promptUnitPrice;
            @JsonProperty("prompt_price_unit")
            private String promptPriceUnit;
            @JsonProperty("prompt_price")
            private String promptPrice;
            @JsonProperty("completion_tokens")
            private Integer completionTokens;
            @JsonProperty("completion_unit_price")
            private String completionUnitPrice;
            @JsonProperty("completion_price_unit")
            private String completionPriceUnit;
            @JsonProperty("completion_price")
            private String completionPrice;
            @JsonProperty("total_tokens")
            private Integer totalTokens;
            @JsonProperty("total_price")
            private String totalPrice;
            @JsonProperty("currency")
            private String currency;
            @JsonProperty("latency")
            private Double latency;
        }
    }

    @NoArgsConstructor
    @Data
    public static class FilesDTO {
        @JsonProperty("dify_model_identity")
        private String difyModelIdentity;
        @JsonProperty("id")
        private Object id;
        @JsonProperty("tenant_id")
        private String tenantId;
        @JsonProperty("type")
        private String type;
        @JsonProperty("transfer_method")
        private String transferMethod;
        @JsonProperty("remote_url")
        private Object remoteUrl;
        @JsonProperty("related_id")
        private String relatedId;
        @JsonProperty("filename")
        private String filename;
        @JsonProperty("extension")
        private String extension;
        @JsonProperty("mime_type")
        private String mimeType;
        @JsonProperty("size")
        private Integer size;
        @JsonProperty("url")
        private String url;
    }
}
