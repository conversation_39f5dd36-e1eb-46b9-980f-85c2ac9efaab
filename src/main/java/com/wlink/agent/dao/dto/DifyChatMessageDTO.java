package com.wlink.agent.dao.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/15 19:18
 */
@NoArgsConstructor
@Data
public class DifyChatMessageDTO {


    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("task_id")
    private String taskId;

    @JsonProperty("event")
    private String event;
    @JsonProperty("message_id")
    private String messageId;
    @JsonProperty("created_at")
    private Integer createdAt;
    @JsonProperty("id")
    private String id;
    @JsonProperty("metadata")
    private MetadataDTO metadata;
    @JsonProperty("files")
    private Object files;

    @NoArgsConstructor
    @Data
    public static class MetadataDTO {
        @JsonProperty("usage")
        private UsageDTO usage;

        @NoArgsConstructor
        @Data
        public static class UsageDTO {
            @JsonProperty("prompt_tokens")
            private Integer promptTokens;
            @JsonProperty("prompt_unit_price")
            private String promptUnitPrice;
            @JsonProperty("prompt_price_unit")
            private String promptPriceUnit;
            @JsonProperty("prompt_price")
            private String promptPrice;
            @JsonProperty("completion_tokens")
            private Integer completionTokens;
            @JsonProperty("completion_unit_price")
            private String completionUnitPrice;
            @JsonProperty("completion_price_unit")
            private String completionPriceUnit;
            @JsonProperty("completion_price")
            private String completionPrice;
            @JsonProperty("total_tokens")
            private Integer totalTokens;
            @JsonProperty("total_price")
            private String totalPrice;
            @JsonProperty("currency")
            private String currency;
            @JsonProperty("latency")
            private Double latency;
            private Integer pointsToDeduct;
            //剩余积分
            private Integer remainingPoints;

        }
    }
}
