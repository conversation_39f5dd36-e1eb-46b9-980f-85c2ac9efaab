package com.wlink.agent.dao.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/15 13:31
 */
@NoArgsConstructor
@Data
public class RunDownRequestDTO {


    @JsonProperty("G1Name")
    private String g1Name;
    @JsonProperty("G2Name")
    private String g2Name;
    @JsonProperty("G3Name")
    private String g3Name;
    @JsonProperty("G4Name")
    private String g4Name;
    @JsonProperty("G5Name")
    private String g5Name;
    @JsonProperty("G1Prompt")
    private String g1Prompt;
    @JsonProperty("G2Prompt")
    private String g2Prompt;
    @JsonProperty("G3Prompt")
    private String g3Prompt;
    @JsonProperty("G4Prompt")
    private String g4Prompt;
    @JsonProperty("G5Prompt")
    private String g5Prompt;
    @JsonProperty("GGScript")
    private String gGScript;
}
