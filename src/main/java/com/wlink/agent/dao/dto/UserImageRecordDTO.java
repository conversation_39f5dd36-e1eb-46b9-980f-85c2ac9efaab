package com.wlink.agent.dao.dto;

import lombok.Data;

import java.util.Date;

/**
 * 用户图片记录数据传输对象
 */
@Data
public class UserImageRecordDTO {
    
    /**
     * 图片任务ID
     */
    private Long id;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 内容类型
     */
    private Integer contentType;
    
    /**
     * 内容ID
     */
    private String contentId;
    
    /**
     * 请求参数（JSON格式
     */
    private String requestParams;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 是否已收
     */
    private Boolean isFavorite;


    /**
     * 收藏编码
     */
    private String favoriteCode;

    /**
     * 收藏时间
     */
    private Date favoriteTime;
} 
