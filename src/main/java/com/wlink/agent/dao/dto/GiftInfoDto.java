package com.wlink.agent.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/14 15:30
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GiftInfoDto {


    private String userId;

    private String name;

    private String giftId;

    private String giftName;

    private Integer giftCount;

    private Integer giftPrice;

    private Date timestamp;


}
