package com.wlink.agent.dao.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class MicrosoftGptRequest {


    @JsonProperty("messages")
    private List<MessagesRequest> messages;
    @JsonProperty("temperature")
    private Double temperature;
    @JSONField(name = "top_p")
    private Double topP;
    @JSONField(name = "frequency_penalty")
    private Integer frequencyPenalty;
    @JSONField(name = "presence_penalty")
    private Integer presencePenalty;
    @JSONField(name = "max_tokens")
    private Integer maxTokens;
    @JsonProperty("stop")
    private Object stop;
    @JsonProperty("stream")
    private Boolean stream;

}
