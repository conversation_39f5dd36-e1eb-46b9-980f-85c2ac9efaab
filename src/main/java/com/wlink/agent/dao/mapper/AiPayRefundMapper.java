package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiPayRefundPo;
import com.wlink.agent.enums.RefundStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 支付退款Mapper接口
 */
@Mapper
public interface AiPayRefundMapper extends BaseMapper<AiPayRefundPo> {

    /**
     * 根据退款单号查询退款记
     *
     * @param refundNo 退款单
     * @return 退款记
     */
    @Select("SELECT * FROM ai_pay_refund WHERE refund_no = #{refundNo} AND del_flag = 0")
    AiPayRefundPo selectByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 根据订单编号查询退款记
     *
     * @param orderNo 订单编号
     * @return 退款记
     */
    @Select("SELECT * FROM ai_pay_refund WHERE order_no = #{orderNo} AND del_flag = 0")
    AiPayRefundPo selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 更新退款状
     *
     * @param refundNo 退款单
     * @param status   退款状
     * @return 影响行数
     */
    @Update("UPDATE ai_pay_refund SET status = #{status}, update_time = NOW() WHERE refund_no = #{refundNo} AND del_flag = 0")
    int updateStatusByRefundNo(@Param("refundNo") String refundNo, @Param("status") Integer status);

    /**
     * 查询处理中的退款记
     *
     * @param limit 限制数量
     * @return 退款记录列
     */
    @Select("SELECT * FROM ai_pay_refund WHERE status = #{status} AND del_flag = 0 ORDER BY create_time ASC LIMIT #{limit}")
    List<AiPayRefundPo> selectPendingRefunds(@Param("status") Integer status, @Param("limit") int limit);
} 
