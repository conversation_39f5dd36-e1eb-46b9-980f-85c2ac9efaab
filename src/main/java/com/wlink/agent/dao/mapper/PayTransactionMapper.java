package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.PayTransactionPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 支付交易Mapper接口
 */
@Mapper
public interface PayTransactionMapper extends BaseMapper<PayTransactionPo> {

    /**
     * 根据交易流水号查询交易记
     *
     * @param transactionNo 交易流水
     * @return 交易记录
     */
    @Select("SELECT * FROM ai_pay_transaction WHERE transaction_no = #{transactionNo}")
    PayTransactionPo selectByTransactionNo(@Param("transactionNo") String transactionNo);

    /**
     * 根据订单编号查询交易记录
     *
     * @param orderNo 订单编号
     * @return 交易记录
     */
    @Select("SELECT * FROM ai_pay_transaction WHERE order_no = #{orderNo}")
    PayTransactionPo selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据第三方交易号查询交易记录
     *
     * @param tradeNo 第三方交易号
     * @return 交易记录
     */
    @Select("SELECT * FROM ai_pay_transaction WHERE trade_no = #{tradeNo}")
    PayTransactionPo selectByTradeNo(@Param("tradeNo") String tradeNo);

    /**
     * 更新交易状
     *
     * @param transactionNo 交易流水
     * @param status        交易状
     * @return 影响行数
     */
    @Update("UPDATE ai_pay_transaction SET status = #{status}, update_time = NOW() WHERE transaction_no = #{transactionNo}")
    int updateStatusByTransactionNo(@Param("transactionNo") String transactionNo, @Param("status") Integer status);

    /**
     * 更新交易成功
     *
     * @param transactionNo 交易流水
     * @param tradeNo       第三方交易号
     * @param status        交易状
     * @return 影响行数
     */
    @Update("UPDATE ai_pay_transaction SET status = #{status}, trade_no = #{tradeNo}, finish_time = NOW(), update_time = NOW() WHERE transaction_no = #{transactionNo}")
    int updatePaySuccess(@Param("transactionNo") String transactionNo, @Param("tradeNo") String tradeNo, @Param("status") Integer status);
} 
