package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiAudioMergeTaskPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 音频合成任务记录表Mapper接口
 */
@Mapper
public interface AiAudioMergeTaskMapper extends BaseMapper<AiAudioMergeTaskPo> {

    /**
     * 查询正在处理中的任务数量
     *
     * @return 处理中的任务数量
     */
    int countProcessingTasks();

    /**
     * 查询用户的音频合成任务列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 任务列表
     */
    List<AiAudioMergeTaskPo> selectUserTasks(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 查询超时的处理中任务
     *
     * @param timeoutMinutes 超时分钟数
     * @return 超时任务列表
     */
    List<AiAudioMergeTaskPo> selectTimeoutProcessingTasks(@Param("timeoutMinutes") Integer timeoutMinutes);
}
