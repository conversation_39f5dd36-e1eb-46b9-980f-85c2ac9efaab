package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiResourceFavoritePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源收藏Mapper接口
 */
@Mapper
public interface AiResourceFavoriteMapper extends BaseMapper<AiResourceFavoritePo> {
    
    /**
     * 根据用户ID和资源类型查询收藏列
     *
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @return 收藏列表
     */
    List<AiResourceFavoritePo> selectByUserIdAndType(@Param("userId") String userId, @Param("resourceType") Integer resourceType);
    
    /**
     * 根据用户ID、资源类型和子类型查询收藏列
     *
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @param resourceSubtype 资源子类
     * @return 收藏列表
     */
    List<AiResourceFavoritePo> selectByUserIdAndTypeAndSubtype(@Param("userId") String userId, 
                                                              @Param("resourceType") Integer resourceType, 
                                                              @Param("resourceSubtype") Integer resourceSubtype);
} 
