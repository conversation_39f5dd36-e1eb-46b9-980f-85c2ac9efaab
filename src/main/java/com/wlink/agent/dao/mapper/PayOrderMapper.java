package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.PayOrderPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 支付订单Mapper接口
 */
@Mapper
public interface PayOrderMapper extends BaseMapper<PayOrderPo> {

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo 订单编号
     * @return 订单信息
     */
    @Select("SELECT * FROM ai_pay_order WHERE order_no = #{orderNo}")
    PayOrderPo selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 更新订单状
     *
     * @param orderNo 订单编号
     * @param status  订单状
     * @return 影响行数
     */
    @Update("UPDATE ai_pay_order SET status = #{status}, update_time = NOW() WHERE order_no = #{orderNo}")
    int updateStatusByOrderNo(@Param("orderNo") String orderNo, @Param("status") Integer status);

    /**
     * 更新支付成功的订
     *
     * @param orderNo 订单编号
     * @param status  订单状
     * @return 影响行数
     */
    @Update("UPDATE ai_pay_order SET status = #{status}, pay_time = NOW(), update_time = NOW() WHERE order_no = #{orderNo} AND status = 1")
    int updatePaySuccess(@Param("orderNo") String orderNo, @Param("status") Integer status);
} 
