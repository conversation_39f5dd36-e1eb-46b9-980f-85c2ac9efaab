package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiVideoModelPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频模型基础表Mapper接口
 */
@Mapper
public interface AiVideoModelMapper extends BaseMapper<AiVideoModelPo> {

    /**
     * 根据提供商查询启用的模型
     *
     * @param provider 提供商
     * @return 模型列表
     */
    List<AiVideoModelPo> selectByProvider(@Param("provider") String provider);

    /**
     * 根据模型类型查询启用的模型
     *
     * @param modelType 模型类型
     * @return 模型列表
     */
    List<AiVideoModelPo> selectByModelType(@Param("modelType") String modelType);

    /**
     * 根据提供商和模型类型查询启用的模型
     *
     * @param provider 提供商
     * @param modelType 模型类型
     * @return 模型列表
     */
    List<AiVideoModelPo> selectByProviderAndModelType(@Param("provider") String provider,
                                                      @Param("modelType") String modelType);

    /**
     * 查询所有启用的模型，按排序顺序排列
     *
     * @return 模型列表
     */
    List<AiVideoModelPo> selectAllEnabled();
}
