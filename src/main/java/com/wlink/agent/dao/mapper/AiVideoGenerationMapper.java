package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 视频生成记录Mapper接口
 */
@Mapper
public interface AiVideoGenerationMapper extends BaseMapper<AiVideoGenerationPo> {
    
    /**
     * 获取排队中的任务数量
     */
    @Select("SELECT COUNT(*) FROM ai_video_generation WHERE status = 0 AND del_flag = 0")
    int getQueuedCount();
    
    /**
     * 获取处理中的任务数量
     */
    @Select("SELECT COUNT(*) FROM ai_video_generation WHERE status = 1 AND del_flag = 0")
    int getProcessingCount();
    
    /**
     * 获取下一个待处理的任务
     */
    @Select("SELECT * FROM ai_video_generation WHERE status = 0 AND del_flag = 0 ORDER BY create_time ASC LIMIT 1")
    AiVideoGenerationPo getNextQueuedTask();
    
    /**
     * 批量更新队列位置
     */
    @Update("UPDATE ai_video_generation SET queue_position = queue_position - 1, update_time = NOW() " +
            "WHERE status = 0 AND del_flag = 0 AND queue_position > #{position}")
    int updateQueuePositions(@Param("position") Integer position);
    
    /**
     * 获取用户的视频生成记录
     */
    @Select("SELECT * FROM ai_video_generation WHERE user_id = #{userId} AND del_flag = 0 ORDER BY create_time DESC")
    List<AiVideoGenerationPo> getUserVideoGenerations(@Param("userId") String userId);

    /**
     * 获取指定数量的排队中任务
     */
    @Select("SELECT * FROM ai_video_generation WHERE status = 0 AND del_flag = 0 ORDER BY create_time ASC LIMIT #{limit}")
    List<AiVideoGenerationPo> getQueuedTasks(@Param("limit") int limit);

    /**
     * 获取所有处理中且开始时间超过指定时间的任务
     */
    @Select("SELECT * FROM ai_video_generation WHERE status = 1 AND del_flag = 0 AND start_time < #{timeoutBefore}")
    List<AiVideoGenerationPo> getTimeoutTasks(@Param("timeoutBefore") Date timeoutBefore);

    /**
     * 查询用户指定状态的视频记录总数
     */
    @Select("SELECT COUNT(*) FROM ai_video_generation WHERE user_id = #{userId} AND status = #{status} AND del_flag = 0")
    long countUserVideoRecordsByStatus(@Param("userId") String userId, @Param("status") int status);

    /**
     * 分页查询用户指定状态的视频记录
     */
    @Select("SELECT * FROM ai_video_generation WHERE user_id = #{userId} AND status = #{status} AND del_flag = 0 ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<AiVideoGenerationPo> findUserVideoRecordsByStatus(@Param("userId") String userId, @Param("status") int status, @Param("offset") int offset, @Param("limit") int limit);
}
