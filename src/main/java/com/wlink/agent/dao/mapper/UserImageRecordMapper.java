package com.wlink.agent.dao.mapper;

import com.wlink.agent.dao.dto.UserImageRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户图片记录查询Mapper
 */
@Mapper
public interface UserImageRecordMapper {
    
    /**
     * 查询用户图片记录列表
     *
     * @param userId 用户ID
     * @param type 类型(1-全部 2-收藏角色 3-角色 4-分镜)
     * @param sessionId 会话ID (可选)
     * @param offset 分页偏移
     * @param limit 分页大小
     * @return 图片记录列表
     */
    List<UserImageRecordDTO> findUserImageRecords(@Param("userId") String userId,
                                                 @Param("type") Integer type,
                                                 @Param("sessionId") String sessionId,
                                                 @Param("offset") Integer offset,
                                                 @Param("limit") Integer limit);
    
    /**
     * 统计用户图片记录总数
     *
     * @param userId 用户ID
     * @param type 类型(1-全部 2-收藏角色 3-角色 4-分镜)
     * @param sessionId 会话ID (可选)
     * @return 记录总数
     */
    long countUserImageRecords(@Param("userId") String userId, @Param("type") Integer type, @Param("sessionId") String sessionId);
} 
