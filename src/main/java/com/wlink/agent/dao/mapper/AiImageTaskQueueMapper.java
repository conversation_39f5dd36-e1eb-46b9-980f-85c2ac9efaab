package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ai_image_task_queue】的数据库操作Mapper
 * @createDate 2024-12-30 11:33:34
 * @Entity com.wlink.agent.dao.po.AiImageTaskQueuePo
 */
@Mapper
public interface AiImageTaskQueueMapper extends BaseMapper<AiImageTaskQueuePo> {
    
    /**
     * 统计正在处理的任务数
     * 
     * @return 处理中的任务数量
     */
    int countProcessingTasks();
    
    /**
     * 查找下一个待处理的任
     * 
     * @return 下一个待处理的任
     */
    AiImageTaskQueuePo findNextPendingTask();
    
    /**
     * 根据会话ID和任务状态查询任务列
     * 
     * @param sessionId 会话ID
     * @param taskStatus 任务状
     * @return 任务列表
     */
    List<AiImageTaskQueuePo> findBySessionIdAndTaskStatus(@Param("sessionId") String sessionId, @Param("taskStatus") String taskStatus);
    
    /**
     * 查询所有任务的最大全局队列位置
     * 
     * @return 最大全局队列位置
     */
    Integer findMaxGlobalQueuePosition();
    
    /**
     * 查询指定会话的最大会话队列位
     * 
     * @param sessionId 会话ID
     * @return 最大会话队列位
     */
    Integer findMaxSessionQueuePosition(@Param("sessionId") String sessionId);
} 
