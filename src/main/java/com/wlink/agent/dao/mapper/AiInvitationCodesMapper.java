package com.wlink.agent.dao.mapper;

import com.wlink.agent.dao.po.AiInvitationCodesPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_invitation_codes(邀请码】的数据库操作Mapper
* @createDate 2025-04-25 14:10:01
* @Entity com.wlink.agent.dao.po.AiInvitationCodesPo
*/
@Mapper
public interface AiInvitationCodesMapper extends BaseMapper<AiInvitationCodesPo> {


    /**
     * 批量插入指定列（需要自定义SQL实现
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    int insertBatchSomeColumn(@Param("list") List<AiInvitationCodesPo> entityList);

}




