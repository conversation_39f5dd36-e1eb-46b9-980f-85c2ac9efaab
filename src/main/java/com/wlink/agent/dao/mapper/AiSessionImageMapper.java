package com.wlink.agent.dao.mapper;

import com.wlink.agent.dao.po.AiSessionImagePo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ai_session_image(会话图片关联表)】的数据库操作Mapper
 * @Entity com.wlink.agent.dao.po.AiSessionImagePo
 */
@Mapper
public interface AiSessionImageMapper extends BaseMapper<AiSessionImagePo> {

    /**
     * 批量插入会话图片关联记录
     * @param sessionImageList 会话图片关联记录列表
     * @return 插入成功的记录数
     */
    int insertBatch(List<AiSessionImagePo> sessionImageList);

}
