package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.VendorAccountPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 图片服务供应商账号表 Mapper
 */
@Mapper
public interface VendorAccountMapper extends BaseMapper<VendorAccountPo> {

    /**
     * 获取所有可用账号列表（按优先级排序
     *
     * @param vendorName 供应商名
     * @return 可用账号列表
     */
    @Select("SELECT * FROM ai_vendor_account WHERE vendor_name = #{vendorName} AND status = 1 ORDER BY priority ASC, id ASC")
    List<VendorAccountPo> findAvailableAccounts(@Param("vendorName") String vendorName);
    
    /**
     * 增加已使用的每日配额
     *
     * @param id 账号ID
     * @param count 增加数量
     * @return 影响行数
     */
    @Update("UPDATE ai_vendor_account SET used_daily_quota = used_daily_quota + #{count} WHERE id = #{id}")
    int increaseUsedQuota(@Param("id") Long id, @Param("count") int count);
    
    /**
     * 重置已使用的每日配额
     *
     * @param id 账号ID
     * @return 影响行数
     */
    @Update("UPDATE ai_vendor_account SET used_daily_quota = 0, last_quota_reset_time = NOW() WHERE id = #{id}")
    int resetUsedQuota(@Param("id") Long id);
    
    /**
     * 原子递增正在执行的任务数
     * 仅当running_task_count < 2时才递增，确保任务数不超
     *
     * @param id 账号ID
     * @return 影响行数表示递增成功表示已达到最大任务数
     */
    @Update("UPDATE ai_vendor_account SET running_task_count = running_task_count + 1 WHERE id = #{id} AND running_task_count < qps_limit")
    int increaseRunningTaskCount(@Param("id") Long id);
    
    /**
     * 原子递减正在执行的任务数
     * 防止running_task_count小于0
     *
     * @param id 账号ID
     * @return 影响行数
     */
    @Update("UPDATE ai_vendor_account SET running_task_count = GREATEST(running_task_count - 1, 0) WHERE id = #{id}")
    int decreaseRunningTaskCount(@Param("id") Long id);
    
    /**
     * 获取所有可用且未达到最大任务数的账号列表（按优先级排序
     *
     * @param vendorName 供应商名
     * @return 可用且未达到最大任务数的账号列
     */
    @Select("SELECT * FROM ai_vendor_account WHERE vendor_name = #{vendorName} AND status = 1 AND running_task_count < qps_limit ORDER BY priority ASC, id ASC")
    List<VendorAccountPo> findAvailableAccountsWithCapacity(@Param("vendorName") String vendorName);
} 
