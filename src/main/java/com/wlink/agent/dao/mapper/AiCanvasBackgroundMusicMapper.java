package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiCanvasBackgroundMusicPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 画布背景音乐表Mapper接口
 */
@Mapper
public interface AiCanvasBackgroundMusicMapper extends BaseMapper<AiCanvasBackgroundMusicPo> {
    
    /**
     * 根据画布ID查询背景音乐
     *
     * @param canvasId 画布ID
     * @return 背景音乐信息
     */
    AiCanvasBackgroundMusicPo selectByCanvasId(@Param("canvasId") Long canvasId);
    
    /**
     * 根据画布ID删除背景音乐
     *
     * @param canvasId 画布ID
     * @return 删除数量
     */
    int deleteByCanvasId(@Param("canvasId") Long canvasId);
}
