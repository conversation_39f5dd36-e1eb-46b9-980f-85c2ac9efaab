package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiUserActivityPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户活跃度日志Mapper接口
 */
@Mapper
public interface AiUserActivityMapper extends BaseMapper<AiUserActivityPo> {
    
    /**
     * 统计指定日期范围内的每日活跃用户
     * @param startDate 开始日
     * @param endDate 结束日期
     * @return 每日活跃用户数列
     */
    List<AiUserActivityPo> countDailyActiveUsers(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 统计指定日期的活跃用户数
     * @param activityDate 活动日期（格式：yyyy-MM-dd
     * @return 活跃用户
     */
    Integer countActiveUsersByDate(@Param("activityDate") String activityDate);
} 
