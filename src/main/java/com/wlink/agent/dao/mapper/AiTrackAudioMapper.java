package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiTrackAudioPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 音轨音频关联表Mapper接口
 */
@Mapper
public interface AiTrackAudioMapper extends BaseMapper<AiTrackAudioPo> {
    
    /**
     * 根据音轨ID查询音频列表
     *
     * @param trackId 音轨ID
     * @return 音频列表
     */
    List<AiTrackAudioPo> selectByTrackId(@Param("trackId") Long trackId);
    
    /**
     * 根据音轨ID删除音频
     *
     * @param trackId 音轨ID
     * @return 删除数量
     */
    int deleteByTrackId(@Param("trackId") Long trackId);
    
    /**
     * 根据音轨ID列表批量删除音频
     *
     * @param trackIds 音轨ID列表
     * @return 删除数量
     */
    int deleteByTrackIds(@Param("trackIds") List<Long> trackIds);
    
    /**
     * 获取音轨下音频的最大排序号
     *
     * @param trackId 音轨ID
     * @return 最大排序号
     */
    Integer getMaxSortOrder(@Param("trackId") Long trackId);
}
