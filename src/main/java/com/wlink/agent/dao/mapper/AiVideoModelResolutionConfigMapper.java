package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiVideoModelResolutionConfigPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频模型分辨率配置表Mapper接口
 */
@Mapper
public interface AiVideoModelResolutionConfigMapper extends BaseMapper<AiVideoModelResolutionConfigPo> {
    
    /**
     * 根据尺寸配置ID查询启用的分辨率配置
     * 
     * @param sizeConfigId 尺寸配置ID
     * @return 分辨率配置列表
     */
    List<AiVideoModelResolutionConfigPo> selectBySizeConfigId(@Param("sizeConfigId") Long sizeConfigId);
    
    /**
     * 根据尺寸配置ID列表查询启用的分辨率配置
     * 
     * @param sizeConfigIds 尺寸配置ID列表
     * @return 分辨率配置列表
     */
    List<AiVideoModelResolutionConfigPo> selectBySizeConfigIds(@Param("sizeConfigIds") List<Long> sizeConfigIds);
    
    /**
     * 根据宽高比查询启用的分辨率配置
     * 
     * @param ratio 宽高比
     * @return 分辨率配置列表
     */
    List<AiVideoModelResolutionConfigPo> selectByRatio(@Param("ratio") String ratio);
    
    /**
     * 根据积分成本范围查询启用的分辨率配置
     * 
     * @param minPointsCost 最小积分成本
     * @param maxPointsCost 最大积分成本
     * @return 分辨率配置列表
     */
    List<AiVideoModelResolutionConfigPo> selectByPointsCostRange(@Param("minPointsCost") Integer minPointsCost, 
                                                                 @Param("maxPointsCost") Integer maxPointsCost);
}
