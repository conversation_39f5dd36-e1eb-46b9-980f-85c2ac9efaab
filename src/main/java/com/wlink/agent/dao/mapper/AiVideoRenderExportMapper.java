package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiVideoRenderExportPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 视频渲染导出记录表Mapper接口
 */
@Mapper
public interface AiVideoRenderExportMapper extends BaseMapper<AiVideoRenderExportPo> {

    /**
     * 根据分享码查询视频导出记录
     *
     * @param shareCode 分享码
     * @return 视频导出记录
     */
    AiVideoRenderExportPo selectByShareCode(@Param("shareCode") String shareCode);
}
