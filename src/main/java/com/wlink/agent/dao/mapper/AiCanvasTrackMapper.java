package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiCanvasTrackPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 画布音轨关联表Mapper接口
 */
@Mapper
public interface AiCanvasTrackMapper extends BaseMapper<AiCanvasTrackPo> {
    
    /**
     * 根据画布ID查询音轨列表
     *
     * @param canvasId 画布ID
     * @return 音轨列表
     */
    List<AiCanvasTrackPo> selectByCanvasId(@Param("canvasId") Long canvasId);
    
    /**
     * 根据画布ID删除音轨
     *
     * @param canvasId 画布ID
     * @return 删除数量
     */
    int deleteByCanvasId(@Param("canvasId") Long canvasId);
    
    /**
     * 获取画布下音轨的最大排序号
     *
     * @param canvasId 画布ID
     * @return 最大排序号
     */
    Integer getMaxSortOrder(@Param("canvasId") Long canvasId);
}
