package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiVideoModelSizeConfigPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频模型尺寸配置表Mapper接口
 */
@Mapper
public interface AiVideoModelSizeConfigMapper extends BaseMapper<AiVideoModelSizeConfigPo> {
    
    /**
     * 根据模型ID查询启用的尺寸配置
     * 
     * @param modelId 模型ID
     * @return 尺寸配置列表
     */
    List<AiVideoModelSizeConfigPo> selectByModelId(@Param("modelId") Long modelId);
    
    /**
     * 根据模型ID列表查询启用的尺寸配置
     * 
     * @param modelIds 模型ID列表
     * @return 尺寸配置列表
     */
    List<AiVideoModelSizeConfigPo> selectByModelIds(@Param("modelIds") List<Long> modelIds);
    
    /**
     * 根据图片数量查询启用的尺寸配置
     * 
     * @param imageCount 图片数量
     * @return 尺寸配置列表
     */
    List<AiVideoModelSizeConfigPo> selectByImageCount(@Param("imageCount") Integer imageCount);
    
    /**
     * 根据积分成本范围查询启用的尺寸配置
     * 
     * @param minPointsCost 最小积分成本
     * @param maxPointsCost 最大积分成本
     * @return 尺寸配置列表
     */
    List<AiVideoModelSizeConfigPo> selectByPointsCostRange(@Param("minPointsCost") Integer minPointsCost, 
                                                           @Param("maxPointsCost") Integer maxPointsCost);
}
