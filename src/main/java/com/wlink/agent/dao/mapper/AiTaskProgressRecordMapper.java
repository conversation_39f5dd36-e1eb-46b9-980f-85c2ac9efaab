package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiTaskProgressRecordPo;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI任务进度记录Mapper 接口
 *
 * <AUTHOR>
 * @since <current_date> // Replace with actual date
 */
@Mapper
public interface AiTaskProgressRecordMapper extends BaseMapper<AiTaskProgressRecordPo> {

    /**
     * Select the latest task progress record for a given session ID.
     *
     * @param sessionId The session ID (conversationId).
     * @return The latest AiTaskProgressRecordPo, or null if not found.
     */
    default AiTaskProgressRecordPo selectLatestBySessionId(String sessionId) {
        LambdaQueryWrapper<AiTaskProgressRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiTaskProgressRecordPo::getSessionId, sessionId)
               .orderByDesc(AiTaskProgressRecordPo::getCreateTime)
               .last("LIMIT 1");
        return this.selectOne(wrapper);
    }
}




