package com.wlink.agent.dao.mapper;

import com.wlink.agent.dao.po.AiImageModifyRecordPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.model.res.ModifyImageUrlInfoRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_image_modify_record(AI图片修改记录】的数据库操作Mapper
* @createDate 2025-04-28 14:54:51
* @Entity com.wlink.agent.dao.po.AiImageModifyRecordPo
*/
@Mapper
public interface AiImageModifyRecordMapper extends BaseMapper<AiImageModifyRecordPo> {

    /**
     * 根据会话ID、一级ID和二级ID查询源图片URL列表
     *
     * @param sessionId 会话ID
     * @param primaryId 一级ID
     * @param secondaryId 二级ID
     * @return 源图片URL列表
     */
    @Select("SELECT modified_image_url FROM ai_image_modify_record WHERE session_id = #{sessionId} AND primary_id = #{primaryId} AND secondary_id = #{secondaryId} AND del_flag = 0")
    List<String> selectSourceImageUrlsBySessionAndIds(@Param("sessionId") String sessionId,
                                                      @Param("primaryId") String primaryId,
                                                      @Param("secondaryId") String secondaryId);

   /**
    * 根据会话ID、一级ID和二级ID查询源图片URL和修改编码列
    *
    * @param sessionId 会话ID
    * @param primaryId 一级ID
    * @param secondaryId 二级ID
    * @return 包含源图片URL和修改编码的对象列表
    */
   List<ModifyImageUrlInfoRes> selectSourceImageUrlInfosBySessionAndIds(@Param("sessionId") String sessionId,
                                                                        @Param("primaryId") String primaryId,
                                                                        @Param("secondaryId") String secondaryId);
}




