package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiUserResourcePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户资源Mapper接口
 * <AUTHOR> Assistant
 */
@Mapper
public interface AiUserResourceMapper extends BaseMapper<AiUserResourcePo> {

    /**
     * 根据用户ID和资源类型查询资源列表
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @param limit 限制数量
     * @return 资源列表
     */
    List<AiUserResourcePo> selectByUserIdAndType(@Param("userId") String userId, 
                                                 @Param("resourceType") Integer resourceType, 
                                                 @Param("limit") Integer limit);

    /**
     * 根据用户ID查询资源列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 资源列表
     */
    List<AiUserResourcePo> selectByUserId(@Param("userId") String userId, 
                                         @Param("limit") Integer limit);

    /**
     * 根据状态查询资源列表
     * @param generationStatus 生成状态
     * @param limit 限制数量
     * @return 资源列表
     */
    List<AiUserResourcePo> selectByStatus(@Param("generationStatus") String generationStatus, 
                                         @Param("limit") Integer limit);

    /**
     * 根据编码查询资源
     * @param code 资源编码
     * @return 资源信息
     */
    AiUserResourcePo selectByCode(@Param("code") String code);

    /**
     * 批量根据编码查询资源
     * @param codes 资源编码列表
     * @return 资源列表
     */
    List<AiUserResourcePo> selectByCodes(@Param("codes") List<String> codes);
}
