package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 火山引擎视频生成记录表Mapper
 */
@Mapper
public interface AiVideoGenerationRecordMapper extends BaseMapper<AiVideoGenerationRecordPo> {
    /**
     * 查询指定模型的未完成任务（状态不为succeeded或failed）
     *
     * @param model 模型名称
     * @param timeoutBefore 超时时间点，查询创建时间在此之前的任务
     * @return 未完成的任务列表
     */
    @Select("SELECT * FROM ai_video_generation_record WHERE model = #{model} " +
            "AND status NOT IN ('succeeded', 'failed') " +
            "AND create_time < #{timeoutBefore} " +
            "ORDER BY create_time ASC")
    List<AiVideoGenerationRecordPo> getUncompletedTasksByModel(@Param("model") String model,
                                                               @Param("timeoutBefore") LocalDateTime timeoutBefore);





}
