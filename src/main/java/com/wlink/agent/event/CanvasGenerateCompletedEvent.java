package com.wlink.agent.event;

import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 画布生成完成事件
 */
@Getter
public class CanvasGenerateCompletedEvent extends ApplicationEvent {
    
    /**
     * 任务信息
     */
    private final AiImageTaskQueuePo task;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime eventTime;
    
    /**
     * 事件类型
     */
    private final String eventType;
    
    /**
     * 额外信息
     */
    private final String message;
    
    public CanvasGenerateCompletedEvent(Object source, AiImageTaskQueuePo task) {
        this(source, task, "CANVAS_GENERATE_COMPLETED", "画布生成任务完成");
    }
    
    public CanvasGenerateCompletedEvent(Object source, AiImageTaskQueuePo task, String eventType, String message) {
        super(source);
        this.task = task;
        this.eventTime = LocalDateTime.now();
        this.eventType = eventType;
        this.message = message;
    }
    
    /**
     * 获取任务ID
     */
    public Long getTaskId() {
        return task != null ? task.getId() : null;
    }


    /**
     * 获取图片结果
     */
    public String getImageResult() {
        return task != null ? task.getImageResult() : null;
    }
    /**
     * 获取用户ID
     */
    public String getUserId() {
        return task != null ? task.getUserId() : null;
    }
    
    /**
     * 获取内容ID
     */
    public String getContentId() {
        return task != null ? task.getContentId() : null;
    }
    
    /**
     * 获取任务状态
     */
    public String getTaskStatus() {
        return task != null ? task.getTaskStatus() : null;
    }
    
    @Override
    public String toString() {
        return "CanvasGenerateCompletedEvent{" +
                "taskId=" + getTaskId() +
                ", userId='" + getUserId() + '\'' +
                ", contentId='" + getContentId() + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventTime=" + eventTime +
                ", message='" + message + '\'' +
                '}';
    }
}
