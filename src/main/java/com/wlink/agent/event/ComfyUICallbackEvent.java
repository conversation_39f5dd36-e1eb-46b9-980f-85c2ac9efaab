package com.wlink.agent.event;

import com.wlink.agent.client.model.comfyui.ComfyUICallbackRequest;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * ComfyUI 回调事件
 */
@Getter
public class ComfyUICallbackEvent extends ApplicationEvent {
    
    /**
     * 回调请求数据
     */
    private final ComfyUICallbackRequest request;
    
    /**
     * 构造函数
     * 
     * @param source 事件源
     * @param request 回调请求数据
     */
    public ComfyUICallbackEvent(Object source, ComfyUICallbackRequest request) {
        super(source);
        this.request = request;
    }
}
