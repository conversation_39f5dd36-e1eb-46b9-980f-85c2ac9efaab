package com.wlink.agent.event.listener;

import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.event.VideoGenerationCallbackEvent;
import com.wlink.agent.exception.VideoGenerationCallbackException;
import com.wlink.agent.model.req.VideoGenerationCallbackReq;
import com.wlink.agent.service.VideoGenerationQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 视频生成回调事件监听器
 * 异步处理视频生成回调事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoGenerationCallbackListener {
    
    private final VideoGenerationQueueService queueService;
    
    /**
     * 处理视频生成回调事件
     * 使用@Async注解实现异步处理，指定使用videoExecutor线程池
     * 
     * @param event 视频生成回调事件
     */
    @Async("videoExecutor")
    @EventListener
    public void handleVideoGenerationCallback(VideoGenerationCallbackEvent event) {
        VideoGenerationCallbackReq req = event.getRequest();
        log.info("异步处理视频生成回调事件: taskId={}, status={}", req.getId(), req.getStatus());
        
        try {
            // 查找内部任务
            AiVideoGenerationPo task = queueService.findTaskByExternalId(req.getId());
            if (task == null) {
                log.error("未找到对应的内部任务: externalId={}", req.getId());
                throw new VideoGenerationCallbackException("未找到对应的内部任务: " + req.getId());
            }
            
            Long internalTaskId = task.getId();
            log.info("找到对应的内部任务: externalId={}, internalId={}", req.getId(), internalTaskId);
            
            // 判断状态是否为成功
            if ("succeeded".equals(req.getStatus())) {
                // 获取视频URL
                String videoUrl = req.getContent() != null ? req.getContent().getVideoUrl() : null;
                if (videoUrl != null) {
                    // 更新任务状态为完成
                    queueService.completeTask(internalTaskId, videoUrl);
                    log.info("视频生成任务已完成: internalId={}, videoUrl={}", internalTaskId, videoUrl);
                } else {
                    log.error("视频生成回调缺少视频URL: taskId={}", req.getId());
                    throw new VideoGenerationCallbackException("视频生成回调缺少视频URL: " + req.getId());
                }
            } else if ("failed".equals(req.getStatus())) {
                // 更新任务状态为失败
                String errorMessage = "视频生成失败，状态: " + req.getStatus();
                queueService.failTask(internalTaskId, errorMessage);
                log.info("视频生成任务已失败: internalId={}, error={}", internalTaskId, errorMessage);
            } else {
                log.warn("未处理的视频生成状态: {}, taskId={}", req.getStatus(), req.getId());
            }
        } catch (Exception e) {
            log.error("异步处理视频生成回调事件异常: taskId={}, error={}", req.getId(), e.getMessage(), e);
        }
    }
} 