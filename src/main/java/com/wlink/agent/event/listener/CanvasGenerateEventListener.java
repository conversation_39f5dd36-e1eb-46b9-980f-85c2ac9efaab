package com.wlink.agent.event.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.enums.ResourceStatus;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.event.CanvasGenerateCompletedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 画布生成事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CanvasGenerateEventListener {

    private final AiImageTaskQueueMapper imageTaskQueueMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final AiCanvasShotMapper canvasShotMapper;
    private final AiCanvasMaterialMapper canvasMaterialMapper;
    
    /**
     * 处理画布生成完成事件
     * 使用@Async注解实现异步处理
     */
    @Async("taskExecutor")
    @EventListener
    public void handleCanvasGenerateCompleted(CanvasGenerateCompletedEvent event) {
        log.info("收到画布生成完成事件: {}", event);
        
        try {
            // 使用Java 17的新特性 - switch表达式和模式匹配
            var result = switch (event.getEventType()) {
                case "CANVAS_GENERATE_COMPLETED" -> processGenerateCompleted(event);
                case "CANVAS_GENERATE_FAILED" -> processGenerateFailed(event);
                default -> {
                    log.warn("未知的事件类型: {}", event.getEventType());
                    yield CompletableFuture.completedFuture("UNKNOWN_EVENT");
                }
            };
            
            // 异步处理结果
            result.thenAccept(this::logProcessResult)
                  .exceptionally(this::handleException);
                  
        } catch (Exception e) {
            log.error("处理画布生成事件时发生异常: {}", event.getTaskId(), e);
        }
    }
    
    /**
     * 处理生成完成事件
     */
    private CompletableFuture<String> processGenerateCompleted(CanvasGenerateCompletedEvent event) {
        return CompletableFuture.supplyAsync(() -> {
            log.info("开始处理画布生成完成事件 - 任务ID: {}, 用户ID: {}",
                    event.getTaskId(), event.getUserId());

            try {
                // 1. 使用taskId查询AiImageTaskQueuePo表的数据
                Long taskId = event.getTaskId();
                if (taskId == null) {
                    log.error("任务ID为空，无法处理生成完成事件");
                    return "TASK_ID_NULL";
                }

                AiImageTaskQueuePo taskPo = imageTaskQueueMapper.selectById(taskId);
                if (taskPo == null) {
                    log.error("未找到任务记录，任务ID: {}", taskId);
                    return "TASK_NOT_FOUND";
                }

                log.info("查询到任务记录: taskId={}, contentId={}, taskStatus={}",
                        taskId, taskPo.getContentId(), taskPo.getTaskStatus());

                // 解析任务结果，获取生成的图片URL等信息

                // 2. 根据contentId获取分镜编码
                String contentId = taskPo.getContentId();
                if (!StringUtils.hasText(contentId)) {
                    log.error("内容ID为空，无法查询分镜信息，任务ID: {}", taskId);
                    return "CONTENT_ID_EMPTY";
                }

                // 查询分镜信息

                AiCanvasShotPo shotPo = canvasShotMapper.selectOne(new LambdaQueryWrapper<AiCanvasShotPo>()
                        .eq(AiCanvasShotPo::getCode, contentId)
                        .last("LIMIT 1"));
                if (shotPo == null) {
                    log.error("未找到分镜记录，分镜ID: {}", contentId);
                    return "SHOT_NOT_FOUND";
                }

                String shotCode = shotPo.getCode();
                log.info("查询到分镜信息: shotId={}, shotCode={}, canvasId={}",
                        contentId, shotCode, shotPo.getCanvasId());

                // 5. 在AiCanvasMaterialPo插入一条数据
                AiCanvasMaterialPo materialPo = new AiCanvasMaterialPo();
                materialPo.setCanvasId(shotPo.getCanvasId());
                materialPo.setMaterialType(1); // 1-图片
                materialPo.setMaterialSource(1); // 1-生成
                materialPo.setMaterialUrl(event.getImageResult());
                materialPo.setGenerationRecordId(taskId); // 使用taskId作为生成记录ID

                materialPo.setCreateTime(new Date());
                materialPo.setUpdateTime(new Date());
                materialPo.setDelFlag(0);

                canvasMaterialMapper.insert(materialPo);
                log.info("插入画布素材记录成功: materialId={}, generationRecordId={}, materialUrl={}",
                        materialPo.getId(), taskId, taskPo.getImageResult());

                canvasImageMapper.update(new AiCanvasImagePo(),new LambdaUpdateWrapper<AiCanvasImagePo>()
                        .eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                        .eq(AiCanvasImagePo::getShotCode, shotCode)
                        .set(AiCanvasImagePo::getImageUrl, event.getImageResult())
                        .set(AiCanvasImagePo::getReferenceImage, event.getImageResult())
                        .set(AiCanvasImagePo::getImageStatus,ResourceStatus.SUCCESS.getValue()));

                // 6. 更新分镜表的状态为成功
                shotPo.setShotStatus(ShotStatus.COMPLETED.getValue());
                shotPo.setType("image");
                shotPo.setUpdateTime(new Date());
                canvasShotMapper.updateById(shotPo);

                log.info("更新分镜状态为已完成: shotId={}, shotCode={}", contentId, shotCode);

                // 使用Java 17的文本块特性记录完成信息
                var completionMessage = """
                        图片生成完成处理成功:
                        - 任务ID: %s
                        - 分镜ID: %s
                        - 分镜编码: %s
                        - 图片URL: %s
                        - 处理时间: %s
                        """.formatted(
                        taskId,
                        contentId,
                        shotCode,
                        taskPo.getImageResult(),
                        new Date()
                );

                log.info(completionMessage);
                return "GENERATE_COMPLETED_PROCESSED";

            } catch (Exception e) {
                log.error("处理图片生成完成事件时发生异常，任务ID: {}", event.getTaskId(), e);

                // 如果处理失败，尝试更新分镜状态为失败
                try {
                    String contentId = event.getContentId();
                    if (StringUtils.hasText(contentId)) {
                        AiCanvasShotPo shotPo = canvasShotMapper.selectById(Long.valueOf(contentId));
                        if (shotPo != null) {
                            shotPo.setShotStatus(ShotStatus.FAILED.getValue());
                            shotPo.setUpdateTime(new Date());
                            canvasShotMapper.updateById(shotPo);
                            log.info("已将分镜状态更新为失败: shotId={}", contentId);
                        }
                    }
                } catch (Exception updateException) {
                    log.error("更新分镜状态为失败时发生异常", updateException);
                }

                return "GENERATE_COMPLETED_ERROR";
            }
        });
    }
    
    /**
     * 处理生成失败事件
     */
    private CompletableFuture<String> processGenerateFailed(CanvasGenerateCompletedEvent event) {
        return CompletableFuture.supplyAsync(() -> {
            log.warn("处理画布生成失败事件 - 任务ID: {}, 用户ID: {}",
                    event.getTaskId(), event.getUserId());

            try {
                // 1. 使用taskId查询AiImageTaskQueuePo表的数据
                Long taskId = event.getTaskId();
                if (taskId == null) {
                    log.error("任务ID为空，无法处理生成失败事件");
                    return "TASK_ID_NULL";
                }

                AiImageTaskQueuePo taskPo = imageTaskQueueMapper.selectById(taskId);
                if (taskPo == null) {
                    log.error("未找到任务记录，任务ID: {}", taskId);
                    return "TASK_NOT_FOUND";
                }

                // 2. 根据contentId获取分镜信息
                String contentId = taskPo.getContentId();
                if (!StringUtils.hasText(contentId)) {
                    log.error("内容ID为空，无法查询分镜信息，任务ID: {}", taskId);
                    return "CONTENT_ID_EMPTY";
                }
                // 查询分镜信息
                AiCanvasShotPo shotPo = canvasShotMapper.selectOne(new LambdaQueryWrapper<AiCanvasShotPo>()
                        .eq(AiCanvasShotPo::getCode, contentId)
                        .last("LIMIT 1"));
                if (shotPo == null) {
                    log.error("未找到分镜记录，分镜ID: {}", contentId);
                    return "SHOT_NOT_FOUND";
                }

                // 3. 更新分镜状态为失败
                shotPo.setShotStatus(ShotStatus.FAILED.getValue());
                shotPo.setUpdateTime(new Date());
                canvasShotMapper.updateById(shotPo);


                canvasImageMapper.update(new AiCanvasImagePo(),new LambdaUpdateWrapper<AiCanvasImagePo>()
                        .eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                        .eq(AiCanvasImagePo::getShotCode, contentId)
                        .set(AiCanvasImagePo::getImageStatus,ResourceStatus.FAILED.getValue()));


                log.info("更新分镜状态为失败: shotId={}, shotCode={}", contentId, shotPo.getCode());

                // 4. 更新图片记录状态为失败（如果存在的话）
                LambdaQueryWrapper<AiCanvasImagePo> imageQueryWrapper = new LambdaQueryWrapper<>();
                imageQueryWrapper.eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                        .eq(AiCanvasImagePo::getShotCode, shotPo.getCode())
                        .eq(AiCanvasImagePo::getDelFlag, 0);

                // 使用Java 17的文本块特性记录失败信息
                var failureMessage = """
                        图片生成失败处理完成:
                        - 任务ID: %s
                        - 分镜ID: %s
                        - 分镜编码: %s
                        - 错误原因: %s
                        - 处理时间: %s
                        """.formatted(
                        taskId,
                        contentId,
                        shotPo.getCode(),
                        taskPo.getErrorReason() != null ? taskPo.getErrorReason() : "未知错误",
                        new Date()
                );

                log.warn(failureMessage);
                return "GENERATE_FAILED_PROCESSED";

            } catch (Exception e) {
                log.error("处理图片生成失败事件时发生异常，任务ID: {}", event.getTaskId(), e);
                return "GENERATE_FAILED_ERROR";
            }
        });
    }
    
    /**
     * 记录处理结果
     */
    private void logProcessResult(String result) {
        log.info("事件处理完成，结果: {}", result);
    }
    
    /**
     * 处理异常
     */
    private Void handleException(Throwable throwable) {
        log.error("异步处理事件时发生异常", throwable);
        return null;
    }
    
    /**
     * 处理其他类型的画布事件
     * 使用Java 17的sealed类特性（如果需要的话）
     */
    @Async("taskExecutor")
    @EventListener
    public void handleCanvasEvent(CanvasGenerateCompletedEvent event) {
        // 使用Java 17的instanceof模式匹配
        if (event.getTask() != null && event.getTask().getTaskStatus() != null) {
            var status = event.getTask().getTaskStatus();
            
            // 使用增强的switch表达式
            var action = switch (status) {
                case "COMPLETED" -> "发送完成通知";
                case "FAILED" -> "发送失败通知";
                case "PROCESSING" -> "更新进度状态";
                default -> "记录状态变更";
            };
            
            log.info("根据任务状态 {} 执行动作: {}", status, action);
        }
    }

    /**
     * 请求参数信息记录类 - 使用Java 17的record特性
     */
    public record RequestParamsInfo(String prompt, String aspectRatio, String referenceImageUrl) {

        /**
         * 创建空的参数信息
         */
        public static RequestParamsInfo empty() {
            return new RequestParamsInfo(null, null, null);
        }
    }

    /**
     * 解析请求参数，根据不同模型格式处理
     */
    private RequestParamsInfo parseRequestParams(String requestParams, String imageModel) {
        if (!StringUtils.hasText(requestParams)) {
            return RequestParamsInfo.empty();
        }

        try {
            Map<String, Object> paramsMap = JSON.parseObject(requestParams, Map.class);

            // 根据模型类型判断参数格式
            String prompt = null;
            String aspectRatio = null;
            String referenceImageUrl = null;

            if ("DOUBAO".equalsIgnoreCase(imageModel)) {
                // 豆包模型格式
                prompt = (String) paramsMap.get("prompt");

                // 从size字段解析宽高比
                String size = (String) paramsMap.get("size");
                if (StringUtils.hasText(size)) {
                    aspectRatio = convertSizeToAspectRatio(size);
                }

                // 豆包模型通常没有参考图，但可以检查是否有相关字段
                referenceImageUrl = (String) paramsMap.get("image_url");

            } else if ("FLUX".equalsIgnoreCase(imageModel)) {
                // FLUX模型格式
                prompt = (String) paramsMap.get("prompt");
                aspectRatio = (String) paramsMap.get("aspect_ratio");

                // FLUX模型的参考图字段可能是image_url或其他
                referenceImageUrl = (String) paramsMap.get("image_url");
                if (!StringUtils.hasText(referenceImageUrl)) {
                    referenceImageUrl = (String) paramsMap.get("imageUrl");
                }

            } else {
                // 未知模型，尝试通用解析
                prompt = (String) paramsMap.get("prompt");
                aspectRatio = (String) paramsMap.get("aspectRatio");
                if (!StringUtils.hasText(aspectRatio)) {
                    aspectRatio = (String) paramsMap.get("aspect_ratio");
                }
                referenceImageUrl = (String) paramsMap.get("referenceImageUrl");
                if (!StringUtils.hasText(referenceImageUrl)) {
                    referenceImageUrl = (String) paramsMap.get("image_url");
                }
            }

            log.debug("解析请求参数成功 - 模型: {}, prompt: {}, aspectRatio: {}, referenceImageUrl: {}",
                    imageModel, prompt, aspectRatio, referenceImageUrl);

            return new RequestParamsInfo(prompt, aspectRatio, referenceImageUrl);

        } catch (Exception e) {
            log.error("解析请求参数失败，模型: {}, 参数: {}", imageModel, requestParams, e);
            return RequestParamsInfo.empty();
        }
    }

    /**
     * 将尺寸转换为宽高比
     * 例如: "1152x2048" -> "9:16"
     */
    private String convertSizeToAspectRatio(String size) {
        if (!StringUtils.hasText(size) || !size.contains("x")) {
            return null;
        }

        try {
            String[] parts = size.split("x");
            if (parts.length != 2) {
                return null;
            }

            int width = Integer.parseInt(parts[0].trim());
            int height = Integer.parseInt(parts[1].trim());

            // 计算最大公约数
            int gcd = gcd(width, height);
            int ratioWidth = width / gcd;
            int ratioHeight = height / gcd;

            return ratioWidth + ":" + ratioHeight;

        } catch (Exception e) {
            log.warn("转换尺寸为宽高比失败: {}", size, e);
            return null;
        }
    }

    /**
     * 计算最大公约数
     */
    private int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }
}
