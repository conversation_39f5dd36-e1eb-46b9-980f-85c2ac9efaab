package com.wlink.agent.event.listener;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackEventData;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackRequest;
import com.wlink.agent.dao.mapper.AiShotImageEditMapper;
import com.wlink.agent.dao.po.AiShotImageEditPo;
import com.wlink.agent.event.ComfyUICallbackEvent;
import com.wlink.agent.service.ShotImageEditService;
import com.wlink.agent.service.ShotLipSyncService;
import com.wlink.agent.service.AudioSynthesisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * ComfyUI 回调事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ComfyUICallbackListener {

    private final ShotImageEditService shotImageEditService;
    private final ShotLipSyncService shotLipSyncService;
    private final AudioSynthesisService audioSynthesisService;
    private final AiShotImageEditMapper aiShotImageEditMapper;

    /**
     * 处理 ComfyUI 回调事件
     * 使用@Async注解实现异步处理
     * 
     * @param event ComfyUI 回调事件
     */
    @Async("taskExecutor")
    @EventListener
    public void handleComfyUICallback(ComfyUICallbackEvent event) {
        ComfyUICallbackRequest request = event.getRequest();
        log.info("异步处理 ComfyUI 回调事件: event={}, taskId={}", request.getEvent(), request.getTaskId());
        
        try {
            // 解析事件数据
            ComfyUICallbackEventData eventData = parseEventData(request.getEventData());
            
            if (eventData == null) {
                log.error("解析事件数据失败: taskId={}, eventData={}", request.getTaskId(), request.getEventData());
                return;
            }
            
            // 根据事件类型处理
            switch (request.getEvent()) {
                case "TASK_END" -> handleTaskEndEvent(request.getTaskId(), eventData);
                case "TASK_START" -> handleTaskStartEvent(request.getTaskId(), eventData);
                case "TASK_PROGRESS" -> handleTaskProgressEvent(request.getTaskId(), eventData);
                default -> log.warn("未处理的事件类型: event={}, taskId={}", request.getEvent(), request.getTaskId());
            }
            
        } catch (Exception e) {
            log.error("异步处理 ComfyUI 回调事件异常: event={}, taskId={}, error={}", 
                    request.getEvent(), request.getTaskId(), e.getMessage(), e);
        }
    }
    
    /**
     * 解析事件数据
     */
    private ComfyUICallbackEventData parseEventData(String eventDataJson) {
        try {
            return JSON.parseObject(eventDataJson, ComfyUICallbackEventData.class);
        } catch (Exception e) {
            log.error("解析事件数据异常: eventData={}", eventDataJson, e);
            return null;
        }
    }
    
    /**
     * 处理任务结束事件
     */
    private void handleTaskEndEvent(String taskId, ComfyUICallbackEventData eventData) {
        log.info("处理任务结束事件: taskId={}, code={}", taskId, eventData.getCode());

        if (eventData.getCode() != null && eventData.getCode() == 0) {
            // 任务成功完成
            List<ComfyUICallbackEventData.ComfyUIResultData> resultDataList = eventData.getResultDataList();
            log.info("任务成功完成: taskId={}, resultCount={}", taskId, resultDataList.size());

            for (ComfyUICallbackEventData.ComfyUIResultData resultData : resultDataList) {
                log.info("任务结果: taskId={}, nodeId={}, fileUrl={}, fileType={}, costTime={}ms",
                        taskId, resultData.getNodeId(), resultData.getFileUrl(),
                        resultData.getFileType(), resultData.getTaskCostTime());

                // 处理任务成功结果
                processTaskResult(taskId, resultData, eventData);
            }

            // 业务表更新逻辑已整合到各自的服务中
        } else {
            // 任务失败
            String errorMessage = buildErrorMessage(eventData);
            log.error("任务执行失败: taskId={}, code={}, msg={}, details={}",
                    taskId, eventData.getCode(), eventData.getMsg(), errorMessage);
            // 处理任务失败
            processTaskFailure(taskId, eventData);

        }
    }
    
    /**
     * 处理任务开始事件
     */
    private void handleTaskStartEvent(String taskId, ComfyUICallbackEventData eventData) {
        log.info("处理任务开始事件: taskId={}", taskId);
        
        // TODO: 添加任务开始的处理逻辑
        // 例如：更新任务状态为进行中
    }
    
    /**
     * 处理任务进度事件
     */
    private void handleTaskProgressEvent(String taskId, ComfyUICallbackEventData eventData) {
        log.info("处理任务进度事件: taskId={}", taskId);
        
        // TODO: 添加任务进度的处理逻辑
        // 例如：更新任务进度信息
    }
    
    /**
     * 处理任务结果
     */
    private void processTaskResult(String taskId, ComfyUICallbackEventData.ComfyUIResultData resultData, ComfyUICallbackEventData eventData) {
        log.info("处理任务结果: taskId={}, fileUrl={}, fileType={}",
                taskId, resultData.getFileUrl(), resultData.getFileType());

        try {
            // 根据任务ID前缀、文件类型和任务记录判断任务类型
            TaskType taskType = determineTaskType(taskId, resultData.getFileType());

            switch (taskType) {
                case IMAGE_EDIT -> {
                    shotImageEditService.updateEditRecordStatus(
                            taskId,
                            "COMPLETED",
                            resultData.getFileUrl(),
                            resultData.getTaskCostTime()
                    );
                    log.info("分镜图片编辑任务完成: taskId={}, resultUrl={}, fileType={}",
                            taskId, resultData.getFileUrl(), resultData.getFileType());
                }
                case LIP_SYNC -> {
                    // 处理对口型回调，需要从多个结果中找到第一个MP4文件
                    shotLipSyncService.handleLipSyncCallback(taskId, eventData);
                    log.info("分镜对口型任务回调处理完成: taskId={}", taskId);
                }
                case AUDIO_SYNTHESIS -> {
                    audioSynthesisService.handleCallback(
                            taskId,
                            true,
                            resultData.getFileUrl(),
                            null,
                            resultData.getTaskCostTime()
                    );
                    log.info("音频合成任务完成: taskId={}, resultUrl={}, fileType={}",
                            taskId, resultData.getFileUrl(), resultData.getFileType());
                }
                case UNKNOWN -> {
                    log.warn("未知任务类型，无法处理回调: taskId={}, fileType={}, fileUrl={}",
                            taskId, resultData.getFileType(), resultData.getFileUrl());
                }
            }

        } catch (Exception e) {
            log.error("处理任务结果异常: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 处理任务失败
     */
    private void processTaskFailure(String taskId, ComfyUICallbackEventData eventData) {
        log.error("处理任务失败: taskId={}, error={}", taskId, eventData.getMsg());

        try {
            // 根据任务记录判断任务类型
            TaskType taskType = determineTaskType(taskId, null);

            switch (taskType) {
                case IMAGE_EDIT -> {
                    shotImageEditService.updateEditRecordToFailed(taskId, eventData.getMsg());
                    log.info("分镜图片编辑任务失败状态已更新: taskId={}", taskId);
                }
                case LIP_SYNC -> {
                    shotLipSyncService.updateLipSyncRecordToFailed(taskId, eventData.getMsg());
                    log.info("分镜对口型任务失败状态已更新: taskId={}", taskId);
                }
                case AUDIO_SYNTHESIS -> {
                    audioSynthesisService.handleCallback(taskId, false, null, eventData.getMsg(), null);
                    log.info("音频合成任务失败状态已更新: taskId={}", taskId);
                }
                case UNKNOWN -> {
                    log.warn("未知任务类型，无法更新失败状态: taskId={}", taskId);
                }
            }

        } catch (Exception e) {
            log.error("处理任务失败异常: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }



    /**
     * 从URL中提取文件名
     */
    private String extractFileName(String url) {
        if (!StringUtils.hasText(url)) {
            return url;
        }

        // 如果URL包含路径分隔符，提取最后一部分作为文件名
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }

        return url;
    }

    /**
     * 构建详细的错误消息
     */
    private String buildErrorMessage(ComfyUICallbackEventData eventData) {
        StringBuilder errorMsg = new StringBuilder();

        // 基本错误信息
        if (StringUtils.hasText(eventData.getMsg())) {
            errorMsg.append("错误: ").append(eventData.getMsg());
        }

        // 详细错误信息
        String failureDetails = eventData.getFailureDetails();
        if (StringUtils.hasText(failureDetails)) {
            try {
                // 尝试解析失败详情
                FailureDetailsDto failureDto = JSON.parseObject(failureDetails, FailureDetailsDto.class);
                if (failureDto != null && StringUtils.hasText(failureDto.getFailedReason())) {
                    // 进一步解析失败原因
                    FailureReasonDto reasonDto = JSON.parseObject(failureDto.getFailedReason(), FailureReasonDto.class);
                    if (reasonDto != null) {
                        if (errorMsg.length() > 0) {
                            errorMsg.append(" | ");
                        }
                        errorMsg.append("节点: ").append(reasonDto.getNodeName());

                        if (StringUtils.hasText(reasonDto.getExceptionMessage())) {
                            errorMsg.append(", 异常: ").append(reasonDto.getExceptionMessage());
                        }
                    }
                }
            } catch (Exception e) {
                // 如果解析失败，直接使用原始错误信息
                if (errorMsg.length() > 0) {
                    errorMsg.append(" | ");
                }
                errorMsg.append("详情: ").append(failureDetails);
            }
        }

        return errorMsg.length() > 0 ? errorMsg.toString() : "未知错误";
    }

    /**
     * 失败详情DTO
     */
    @Data
    private static class FailureDetailsDto {
        @JsonProperty("failedReason")
        private String failedReason;
    }

    /**
     * 失败原因DTO
     */
    @Data
    private static class FailureReasonDto {
        @JsonProperty("node_name")
        private String nodeName;

        @JsonProperty("exception_message")
        private String exceptionMessage;

        @JsonProperty("exception_type")
        private String exceptionType;

        @JsonProperty("node_id")
        private String nodeId;
    }

    /**
     * 任务类型枚举
     */
    private enum TaskType {
        IMAGE_EDIT,      // 图片编辑任务
        LIP_SYNC,        // 对口型任务
        AUDIO_SYNTHESIS, // 音频合成任务
        UNKNOWN          // 未知任务类型
    }

    /**
     * 判断任务类型
     *
     * @param taskId 任务ID（纯数字）
     * @param fileType 文件类型
     * @return 任务类型
     */
    private TaskType determineTaskType(String taskId, String fileType) {
        try {
            // 方法1: 查询数据库记录判断（最优先，最准确）
            TaskType dbTaskType = determineTaskTypeFromDatabase(taskId);
            if (dbTaskType != TaskType.UNKNOWN) {
                return dbTaskType;
            }

            // 方法2: 根据文件类型判断（备用方案）
            if (fileType != null) {
                if (isVideoFileType(fileType)) {
                    log.debug("根据文件类型判断为对口型任务: taskId={}, fileType={}", taskId, fileType);
                    return TaskType.LIP_SYNC;
                } else if (isImageFileType(fileType)) {
                    log.debug("根据文件类型判断为图片编辑任务: taskId={}, fileType={}", taskId, fileType);
                    return TaskType.IMAGE_EDIT;
                } else if (isAudioFileType(fileType)) {
                    log.debug("根据文件类型判断为音频合成任务: taskId={}, fileType={}", taskId, fileType);
                    return TaskType.AUDIO_SYNTHESIS;
                }
            }

            log.warn("无法判断任务类型: taskId={}, fileType={}", taskId, fileType);
            return TaskType.UNKNOWN;

        } catch (Exception e) {
            log.error("判断任务类型异常: taskId={}, fileType={}", taskId, fileType, e);
            return TaskType.UNKNOWN;
        }
    }

    /**
     * 从数据库查询判断任务类型
     *
     * @param taskId 任务ID
     * @return 任务类型
     */
    private TaskType determineTaskTypeFromDatabase(String taskId) {
        try {
            // 并行查询两个表以提高性能
            boolean isImageEdit = false;
            boolean isLipSync = false;

            // 查询图片编辑表
            try {
                isImageEdit = (shotImageEditService.getEditRecordByTaskId(taskId) != null);
            } catch (Exception e) {
                log.debug("查询图片编辑记录失败: taskId={}, error={}", taskId, e.getMessage());
            }

            // 如果已经找到是图片编辑任务，直接返回
            if (isImageEdit) {
                log.debug("根据数据库记录判断为图片编辑任务: taskId={}", taskId);
                return TaskType.IMAGE_EDIT;
            }

            // 查询对口型表
            try {
                isLipSync = (shotLipSyncService.getLipSyncRecordByTaskId(taskId) != null);
            } catch (Exception e) {
                log.debug("查询对口型记录失败: taskId={}, error={}", taskId, e.getMessage());
            }

            if (isLipSync) {
                log.debug("根据数据库记录判断为对口型任务: taskId={}", taskId);
                return TaskType.LIP_SYNC;
            }

            // 查询音频合成表
            boolean isAudioSynthesis = false;
            try {
                isAudioSynthesis = (audioSynthesisService.getTaskStatus(taskId) != null);
            } catch (Exception e) {
                log.debug("查询音频合成记录失败: taskId={}, error={}", taskId, e.getMessage());
            }

            if (isAudioSynthesis) {
                log.debug("根据数据库记录判断为音频合成任务: taskId={}", taskId);
                return TaskType.AUDIO_SYNTHESIS;
            }

            log.debug("数据库中未找到任务记录: taskId={}", taskId);
            return TaskType.UNKNOWN;

        } catch (Exception e) {
            log.error("从数据库判断任务类型异常: taskId={}", taskId, e);
            return TaskType.UNKNOWN;
        }
    }

    /**
     * 判断是否为视频文件类型
     */
    private boolean isVideoFileType(String fileType) {
        if (fileType == null) {
            return false;
        }
        String lowerFileType = fileType.toLowerCase();
        return lowerFileType.contains("video") ||
               lowerFileType.contains("mp4") ||
               lowerFileType.contains("avi") ||
               lowerFileType.contains("mov") ||
               lowerFileType.contains("wmv") ||
               lowerFileType.contains("flv") ||
               lowerFileType.contains("webm");
    }

    /**
     * 判断是否为图片文件类型
     */
    private boolean isImageFileType(String fileType) {
        if (fileType == null) {
            return false;
        }
        String lowerFileType = fileType.toLowerCase();
        return lowerFileType.contains("image") ||
               lowerFileType.contains("jpg") ||
               lowerFileType.contains("jpeg") ||
               lowerFileType.contains("png") ||
               lowerFileType.contains("gif") ||
               lowerFileType.contains("bmp") ||
               lowerFileType.contains("webp");
    }

    /**
     * 判断是否为音频文件类型
     */
    private boolean isAudioFileType(String fileType) {
        if (fileType == null) {
            return false;
        }
        String lowerFileType = fileType.toLowerCase();
        return lowerFileType.contains("audio") ||
               lowerFileType.contains("mp3") ||
               lowerFileType.contains("wav") ||
               lowerFileType.contains("ogg") ||
               lowerFileType.contains("m4a") ||
               lowerFileType.contains("flac") ||
               lowerFileType.contains("aac") ||
               lowerFileType.contains("wma");
    }
}
