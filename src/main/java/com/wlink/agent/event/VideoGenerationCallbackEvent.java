package com.wlink.agent.event;

import com.wlink.agent.model.req.VideoGenerationCallbackReq;
import lombok.Getter;

/**
 * 视频生成回调事件
 */
@Getter
public class VideoGenerationCallbackEvent {
    
    /**
     * 回调请求
     */
    private final VideoGenerationCallbackReq request;
    
    /**
     * 创建时间
     */
    private final long timestamp;
    
    /**
     * 事件源
     */
    private final Object source;
    
    /**
     * 创建视频生成回调事件
     *
     * @param source 事件源
     * @param request 回调请求
     */
    public VideoGenerationCallbackEvent(Object source, VideoGenerationCallbackReq request) {
        this.source = source;
        this.request = request;
        this.timestamp = System.currentTimeMillis();
    }
} 