package com.wlink.agent.exception;

import com.alibaba.cola.dto.SingleResponse;

import com.alibaba.cola.exception.BizException;

import com.wlink.agent.utils.I18nMessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ValidationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalException {

    /**
     * 创建错误响应的通用方法
     * @param code 错误代码
     * @param message 错误消息
     * @return 统一的错误响
     */
    private SingleResponse<?> buildErrorResponse(String code, String message) {
        return SingleResponse.buildFailure(code, message);
    }

    /**
     * 创建基于国际化错误码的响
     * @param errorCodeEnum 错误码枚
     * @return 国际化的错误响应
     */
    private SingleResponse<?> buildI18nErrorResponse(ErrorCodeEnum errorCodeEnum) {
        return buildErrorResponse(
                errorCodeEnum.getCode(),
                I18nMessageUtils.getMessage(errorCodeEnum.getMsg())
        );
    }

    /**
     * 创建基于国际化错误码的响应，带额外参
     * @param errorCodeEnum 错误码枚
     * @param args 国际化消息参
     * @return 国际化的错误响应
     */
    private SingleResponse<?> buildI18nErrorResponse(ErrorCodeEnum errorCodeEnum, Object[] args) {
        return buildErrorResponse(
                errorCodeEnum.getCode(),
                I18nMessageUtils.getMessage(errorCodeEnum.getMsg(), args)
        );
    }

    /**
     * 处理参数验证异常的通用方法
     * @param ex 异常
     * @param request HTTP请求
     * @param logMessage 日志消息
     * @return 包含详细错误信息的响
     */
    private SingleResponse<?> handleValidationException(Exception ex, HttpServletRequest request, String logMessage) {
        log.error("{}. 请求路径: {}, 请求方法: {}", 
                 logMessage, request.getRequestURI(), request.getMethod(), ex);
        
        return buildI18nErrorResponse(ErrorCodeEnum.PARAM_ERROR);
    }

    /**
     * 统一处理各类服务器异
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public SingleResponse<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        // 从异常中获取所有字段错
        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        String errorMessage;
        
        if (!fieldErrors.isEmpty()) {
            // 格式化错误信
            errorMessage = fieldErrors.stream()
                .map(error -> String.format("%s: %s", error.getField(), error.getDefaultMessage()))
                .collect(Collectors.joining("; "));
        } else {
            // 如果没有具体字段错误，则使用通用错误信息
            errorMessage = I18nMessageUtils.getMessage(ErrorCodeEnum.PARAM_ERROR.getMsg());
        }
        
        log.error("请求参数验证失败. 请求路径: {}, 请求方法: {}, 错误详情: {}", 
                 request.getRequestURI(), request.getMethod(), errorMessage, ex);
        
        return buildErrorResponse(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }


    @ExceptionHandler(value = IllegalArgumentException.class)
    public SingleResponse<?> handleIllegalArgumentException(IllegalArgumentException ex, HttpServletRequest request) {
        // 使用异常的实际消息，而非通用错误消息
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : I18nMessageUtils.getMessage(ErrorCodeEnum.PARAM_ERROR.getMsg());
        
        log.error("非法参数. 请求路径: {}, 请求方法: {}, 错误详情: {}", 
                 request.getRequestURI(), request.getMethod(), errorMessage, ex);
        
        return buildErrorResponse(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }

    @ExceptionHandler(value = ValidationException.class)
    public SingleResponse<?> handleValidationException(ValidationException ex, HttpServletRequest request) {
        String errorMessage = I18nMessageUtils.getMessage(ex.getMessage());
        
        log.error("参数验证失败. 请求路径: {}, 请求方法: {}, 错误详情: {}", 
                 request.getRequestURI(), request.getMethod(), errorMessage, ex);
        
        return buildErrorResponse(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public SingleResponse<?> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        String supportedMethods = String.join(", ", ex.getSupportedMethods());
        
        log.error("不支持的请求方式. 请求路径: {}, 请求方法: {}, 支持的方 {}",
                 request.getRequestURI(), request.getMethod(), supportedMethods, ex);
        
        return buildI18nErrorResponse(ErrorCodeEnum.UNSUPPORTED_METHOD);
    }


    @ExceptionHandler(BizException.class)
    public SingleResponse<?> handleBizException(BizException e, HttpServletRequest request) {
        log.error("业务异常. 请求路径: {}, 请求方法: {}, 异常信息: {}", 
                 request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        
        return buildErrorResponse(e.getErrCode(), e.getMessage());
    }

    /**
     * 处理请求参数缺失异常
     */
    @ExceptionHandler(org.springframework.web.bind.MissingServletRequestParameterException.class)
    public SingleResponse<?> handleMissingServletRequestParameter(
            org.springframework.web.bind.MissingServletRequestParameterException ex, 
            HttpServletRequest request) {
        
        String errorMessage = String.format("缺少必要的请求参 %s", ex.getParameterName());
        
        log.error("请求参数缺失. 请求路径: {}, 请求方法: {}, 错误详情: {}", 
                 request.getRequestURI(), request.getMethod(), errorMessage, ex);
        
        return buildErrorResponse(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }
    
    /**
     * 处理请求体缺失异
     */
    @ExceptionHandler(org.springframework.http.converter.HttpMessageNotReadableException.class)
    public SingleResponse<?> handleHttpMessageNotReadable(
            org.springframework.http.converter.HttpMessageNotReadableException ex, 
            HttpServletRequest request) {
        
        String errorMessage = "请求体格式错误或缺失";
        
        log.error("请求体读取失 请求路径: {}, 请求方法: {}, 错误详情: {}",
                 request.getRequestURI(), request.getMethod(), ex.getMessage(), ex);
        
        return buildErrorResponse(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }
    
    /**
     * 处理类型不匹配异
     */
    @ExceptionHandler(org.springframework.beans.TypeMismatchException.class)
    public SingleResponse<?> handleTypeMismatch(
            org.springframework.beans.TypeMismatchException ex, 
            HttpServletRequest request) {
        
        String errorMessage = String.format(
                "参数类型不匹 %s，要求类 %s，实际 %s",
                ex.getPropertyName(), 
                ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "未知", 
                ex.getValue());
        
        log.error("参数类型不匹 请求路径: {}, 请求方法: {}, 错误详情: {}",
                 request.getRequestURI(), request.getMethod(), errorMessage, ex);
        
        return buildErrorResponse(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }
    
    /**
     * 处理资源不存在异
     */
    @ExceptionHandler(org.springframework.web.servlet.NoHandlerFoundException.class)
    public SingleResponse<?> handleNoHandlerFoundException(
            org.springframework.web.servlet.NoHandlerFoundException ex, 
            HttpServletRequest request) {
        
        String errorMessage = String.format("找不到路%s 的处理器", ex.getRequestURL());
        
        log.error("资源不存 请求路径: {}, 请求方法: {}",
                 request.getRequestURI(), request.getMethod(), ex);
        
        return buildErrorResponse("404", errorMessage);
    }
    
    /**
     * 处理请求方法不支持异
     */
    @ExceptionHandler(org.springframework.web.HttpMediaTypeNotSupportedException.class)
    public SingleResponse<?> handleHttpMediaTypeNotSupported(
            org.springframework.web.HttpMediaTypeNotSupportedException ex, 
            HttpServletRequest request) {
        
        String errorMessage = String.format(
                "不支持的媒体类型: %s, 支持的类 %s",
                ex.getContentType(), 
                ex.getSupportedMediaTypes());
        
        log.error("不支持的媒体类型. 请求路径: {}, 请求方法: {}, 错误详情: {}", 
                 request.getRequestURI(), request.getMethod(), errorMessage, ex);
        
        return buildErrorResponse(ErrorCodeEnum.UNSUPPORTED_METHOD.getCode(), errorMessage);
    }

    @ExceptionHandler(Exception.class)
    public SingleResponse<?> handleException(Exception e, HandlerMethod handler, HttpServletRequest request) {
        String methodInfo = "";
        if (handler != null) {
            methodInfo = handler.getBeanType().getSimpleName() + "#" + handler.getMethod().getName();
        }
        
        log.error("未处理异 请求路径: {}, 请求方法: {}, 处理方法: {}, 异常信息: {}",
                 request.getRequestURI(), request.getMethod(), methodInfo, e.getMessage(), e);
        
        return buildI18nErrorResponse(ErrorCodeEnum.SYSTEM_ERROR);
    }

}
