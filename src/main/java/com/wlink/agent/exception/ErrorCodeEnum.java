package com.wlink.agent.exception;

import lombok.Getter;

/**
 * 统一错误码定义 (V3 - Pure Numeric)
 * 编码规则：模块代码 (2位) + 序列号 (2位)
 * 模块代码:
 *   10: System/General (系统/通用)
 *   20: Auth/Security (认证/安全)
 *   30: User (用户)
 *   40: Agent (智能体)
 *   50: LiveRoom (直播间)
 *   60: Template (模板)
 *   70: Sound (声音)
 *   80: Rundown/Segment (节目单/环节)
 *   90: QRCode (二维码)
 *   95: App/Server (应用/服务)
 *
 * <AUTHOR> (Refactored by Trae AI)
 * @version 1.3
 * @description: Refactored Error Code Definitions (Pure Numeric)
 * @date 2024/12/25 14:05 (Original)
 */
@Getter
public enum ErrorCodeEnum {

    // === 10xx: System/General (系统/通用) ===
    PARAM_ERROR("1001", "param.error"), // 参数错误
    UNSUPPORTED_METHOD("1002", "unsupported.method"), // 不支持的请求方法
    SYSTEM_ERROR("1000", "system.error"), // 系统内部错误 (通用)

    // === 20xx: Auth/Security (认证/安全) ===
    UNAUTHORIZED("2000", "unauthorized"), // 未授权/需要登录 (通用)
    LOGIN_FAIL("2001", "login.fail"), // 登录失败
    TOKEN_INVALID("2002", "error.token.invalid"), // 无效的Token (签名、格式错误)
    TOKEN_EXPIRED("2003", "error.token.expired"), // Token已过期
    TOKEN_REVOKED("2004", "error.token.revoked"), // Token已被吊销或已退出登录
    ACCESS_DENIED_IP("2005", "error.access.denied.ip"), // IP地址访问受限
    VERIFICATION_CODE_SEND_FAILED("2006", "error.verification.code.send.failed"), // 验证码发送失败
    VERIFICATION_CODE_EXPIRED("2007", "error.verification.code.expired"), // 验证码已过期
    VERIFICATION_CODE_INVALID("2008", "error.verification.code.invalid"), // 验证码无效
    VERIFICATION_CODE_SEND_FREQUENT("2009", "error.verification.code.send.frequent" ), // 验证码发送过于频繁 (调整了Code和Key)
    //用户还没有激活
    USER_NOT_ACTIVATED("2010", "error.user.not.activated"),
    API_KEY_INVALID("2011", "error.api.key.invalid"), // API Key无效
    AUTH_INTERNAL_ERROR("2091", "error.auth.internal"), // 认证授权过程中发生内部错误
    FILTER_INTERNAL_ERROR("2092", "error.filter.internal"), // 请求过滤器内部错误

    // === 30xx: User (用户) ===
    USER_NOT_FOUND("3001", "error.user.notfound"), // 用户不存在 (USER_NOT_EXIST 也使用此码)
    USER_ALREADY_EXISTS("3002", "error.user.exists"), // 用户已存在
    USER_REGISTRATION_FAILED("3003", "error.user.registration.failed"), // 用户注册失败
    USER_ACCOUNT_DISABLED("3004", "error.user.account.disabled"), // 用户账户已禁用
    USER_ALREADY_ACTIVATED("3005", "error.user.already.activated"), // 用户已激活 (USER_ALREADY_ACTIVE 也使用此码)
    USER_ACTIVATION_FAILED("3006", "error.user.activation.failed"), // 用户激活失败
    USER_STATUS_UNKNOWN("3007", "error.user.status.unknown"), // 用户状态未知
    INVITATION_CODE_INVALID("3008", "error.invitation.code.invalid"), // 邀请码无效
    INVITATION_CODE_USED_OR_EXPIRED("3009", "error.invitation.code.used.or.expired"), // 邀请码已使用或过期
    USER_OPERATION_FAILED("3010", "error.user.operation.failed"), // 用户操作失败
    //停止响应失败
    USER_STOP_RESPONSE_FAILED("3012", "stop.response.failed"),
    //清空上下文失败
    USER_CLEAR_CONTEXT_FAILED("3013", "clear.context.failed"),
    //用户积分不足
    USER_INSUFFICIENT_POINTS("3014", "error.user.insufficient.points"),

    // === 40xx: Agent (智能体) ===
    AGENT_NOT_EXISTS("4001", "agent.not.exists"), // 智能体不存在
    AGENT_IMAGE_RECORD_NOT_EXISTS("4002", "agent.image.record.not.exists"), // 图片记录不存在
    AGENT_IS_LIVING("4003", "agent.is.living"), // 智能体正在直播中 (状态冲突)
    AGENT_ALREADY_ASSOCIATED_LIVE_ROOM("4004", "agent.already.associated.live.room"), // 智能体已关联直播间 (状态冲突)
    AGENT_LIVE_ROOM_IS_LIVING("4005", "agent.live.room.is.living"), // 智能体关联的直播间正在直播 (状态冲突)
    AGENT_ALREADY_ASSOCIATED_WITH_SINGLE_LIVE_ROOM("4006", "agent.already.associated.with.single.live.room"), // 单人直播间只能关联一个智能体 (状态冲突)
    AGENT_IMAGE_RECORD_CREATE_FAIL("4007", "agent.image.record.create.fail"), // 图片记录创建失败
    AGENT_IMAGE_GENERATE_FAIL("4008", "agent.image.generate.fail"), // 图片生成失败
    AGENT_IMAGE_PROMPT_GENERATE_FAIL("4009", "agent.image.prompt.generate.fail"), // Prompt生成失败
    //会话不存在
    CONVERSATION_NOT_FOUND("4010", "conversation.not.found"),
    DATA_CONVERSION_ERROR("4011", "data.conversion.error"), // 数据转换错误
    CONTENT_DATA_FORMAT_ERROR("4012", "content.data.format.error"), // 内容数据格式错误
    UNSUPPORTED_CONTENT_TYPE("4013", "unsupported.content.type"), // 不支持的内容类型
    PARSE_ERROR("4014", "parse.error"), // 解析错误
    VISUAL_RECORD_SAVE_FAILED("4015", "visual.record.save.failed"), // 视觉记录保存失败
    VISUAL_RECORD_NOT_FOUND("4016", "visual.record.not.found"), // 视觉记录不存在
    VISUAL_RECORD_PERMISSION_DENIED("4017", "visual.record.permission.denied"), // 无权操作/访问视觉记录
    VISUAL_RECORD_PUBLISH_UPDATE_FAILED("4018", "visual.record.publish.update.failed"), // 更新发布状态失败
    VISUAL_RECORD_MISSING_CONTENT("4019", "visual.record.missing.content"), // 缺少用于渲染的内容数据
    VISUAL_RECORD_INSUFFICIENT_POINTS("4020", "visual.record.insufficient.points"), // 积分不足，无法生成视频
    VISUAL_RECORD_RENDERING("4021", "visual.record.rendering"), // 视频正在生成中，请等待

    // === 50xx: LiveRoom (直播间) ===
    ROOM_NOT_EXISTS("5001", "agent.live.room.not_exists"), // 直播间不存在 (兼容旧Key)
    LIVE_ROOM_NOT_FOUND("5002", "live.room.not.found"), // 直播间未找到
    LIVE_ROOM_TYPE_NOT_EXISTS("5003", "live.room.type.not.exists"), // 直播类型不存在
    ROOM_ALREADY_EXISTS("5011", "agent.live.room.already_exists"), // 直播间已存在 (兼容旧Key) (状态冲突)
    ROOM_NOT_LIVE("5012", "agent.live.room.not_live"), // 直播间未在直播 (兼容旧Key) (状态冲突)
    ROOM_LIVE("5013", "agent.live.room.live"), // 直播间正在直播 (兼容旧Key) (状态冲突)
    LIVE_ROOM_IS_LIVING("5014", "live.room.is.living"), // 直播间正在直播中 (状态冲突)
    LIVE_ROOM_NOT_LIVE("5015", "live.room.not.live"), // 直播间未在直播 (状态冲突)
    LIVE_ROOM_ALREADY_LIVE("5016", "live.room.already.live"), // 直播间已开播 (状态冲突)
    LIVE_ROOM_STATUS_NOT_ALLOWED("5017", "live.room.status.not.allowed"), // 直播间当前状态不允许操作 (状态冲突)
    LIVE_ROOM_STATUS_CANNOT_LIVE("5018", "live.room.status.cannot.live"), // 直播间状态不能直播 (状态冲突)
    LIVE_ROOM_ALREADY_PUBLISHED("5019", "live.room.already.published"), // 直播间已发布 (状态冲突)
    LIVE_ROOM_NOT_PUBLISHED("5020", "live.room.not.published"), // 直播间未发布 (状态冲突)
    LIVE_ROOM_NOT_STARTED("5021", "live.room.not.started"), // 直播间未启动 (状态冲突)
    LIVE_ROOM_STATUS_NOT_ALLOWED_DISPATCH("5022", "live.room.status.not.allowed.dispatch"), // 直播间状态不允许调度 (状态冲突)
    GENERATE_ROOM_ID_FAILED("5091", "live.room.generate.id.failed"), // 生成直播间ID失败

    // === 60xx: Template (模板) ===
    TEMPLATE_NOT_EXISTS("6001", "agent.template.not.exists"), // 模板不存在 (兼容旧Key)
    TEMPLATE_NOT_FOUND("6002", "live.room.template.not.found"), // 模板未找到
    TEMPLATE_NAME_EXISTS("6011", "live.room.template.name.exists"), // 模板名称已存在 (状态冲突)

    // === 70xx: Sound (声音) ===
    SOUND_NOT_EXISTS("7001", "agent.sound.not.exists"),
    TTS_GENERATION_FAILED( "7002", "tts.generation.failed"),

    // === 80xx: Rundown/Segment (节目单/环节) ===
    SEGMENT_NOT_SAME_RUNDOWN("8001", "环节不属于同一个节目单"), // 环节不属于同一个节目单 (逻辑错误)
    RUN_DOWN_NOT_ALLOWED("8002", "run.down.not.allowed"), // 单人直播不能生成runDown (逻辑限制)
    RUN_DOWN_RECORD_NOT_EXISTS("8003", "run.down.record.not.exists"), // rundown生成记录不存在
    RUNDOWN_NOT_EXISTS("8004", "rundown.not.exists"), // 节目单不存在
    RUN_DOWN_STYLE_NOT_EXISTS("8005", "rundown.style.not.exists"), // Rundown风格不存在
    RUNDOWN_SEGMENT_NOT_EXISTS("8006", "rundown.segment.not.exists"), // 节目单环节不存在
    SEGMENT_NOT_EXISTS("8007", "环节不存在"), // 环节不存在
    RUN_DOWN_PROCESSING("8011", "run.down.processing"), // rundown正在处理中 (状态冲突)
    RUNDOWN_STATUS_NOT_ALLOWED("8012", "rundown.status.not.allowed"), // 节目单状态不允许操作 (状态冲突)
    GENERATE_LIVE_CONTENT_FAILED("8091", "generate.live.content.failed"), // 生成直播内容失败

    // === 90xx: QRCode (二维码) ===
    QR_CODE_NOT_EXISTS("9001", "qr.code.not.exists"), // 二维码不存在
    QR_CODE_STATUS_ERROR("9011", "qr.code.status.error"), // 二维码状态错误 (状态冲突)
    QR_CODE_EXPIRED("9012", "qr.code.expired"), // 二维码已过期 (状态冲突)
    QR_CODE_OPERATION_TOO_FREQUENT("9013", "qr.code.operation.too.frequent"), // 二维码操作过于频繁 (状态冲突)
    QR_CODE_OPERATION_INTERRUPTED("9091", "qr.code.operation.interrupted"), // 二维码操作中断

    // === 95xx: App/Server (应用/服务) ===
    SERVER_NOT_EXIST("9501", "server.dispath.server.not.exist"), // 调度服务不存在
    APP_NOT_EXISTS("9502", "app.not.exists"), // 应用不存在
    APP_NOT_BINDING("9503", "app.not.binding"), // 应用未绑定
    SERVER_NOT_BINDING("9504", "server.dispath.server.nobinding"), // 服务未绑定
    SERVER_BUSY("9505", "server.dispath.server.busy"), // 调度服务繁忙
    APP_ADD_EXCEPTION("9591", "app.add.exception"), // 添加应用异常
    APP_DEL_EXCEPTION("9592", "app.del.exception"),; // 删除应用异常


    private final String code; // 现在是纯数字字符串
    private final String msg; // 对应国际化消息的 key

    ErrorCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    // 根据 code 获取枚举的方法
    public static ErrorCodeEnum getByCode(String code) {
        for (ErrorCodeEnum errorCode : ErrorCodeEnum.values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        // 找不到时返回系统错误，或者可以抛出异常
        return SYSTEM_ERROR;
    }
}
