package com.wlink.agent.exception;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 异常响应增强
 * 用于获取请求相关上下文信息，方便异常日志记录和排
 */
@Component
public class ResponseExceptionEnhancer {

    /**
     * 提取请求上下文信
     * @return 包含请求信息的Map
     */
    public Map<String, Object> extractRequestInfo() {
        Map<String, Object> requestInfo = new HashMap<>();
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                requestInfo.put("URI", request.getRequestURI());
                requestInfo.put("URL", request.getRequestURL().toString());
                requestInfo.put("Method", request.getMethod());
                requestInfo.put("RemoteAddr", request.getRemoteAddr());
                requestInfo.put("UserAgent", request.getHeader("User-Agent"));
                requestInfo.put("ContentType", request.getContentType());
                requestInfo.put("QueryString", request.getQueryString());
            }
        } catch (Exception e) {
            // 提取信息时发生异常不影响主流
        }
        return requestInfo;
    }

    /**
     * 格式化错误消
     * @param code 错误代码
     * @param message 错误消息
     * @param detail 详细信息
     * @return 格式化后的错误信息对
     */
    public ErrorDetail formatErrorDetail(String code, String message, String detail) {
        ErrorDetail errorDetail = new ErrorDetail();
        errorDetail.setCode(code);
        errorDetail.setMessage(message);
        errorDetail.setDetail(detail);
        errorDetail.setTimestamp(System.currentTimeMillis());
        errorDetail.setRequestInfo(extractRequestInfo());
        return errorDetail;
    }

    /**
     * 错误详情内部
     */
    public static class ErrorDetail {
        private String code;
        private String message;
        private String detail;
        private long timestamp;
        private Map<String, Object> requestInfo;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public Map<String, Object> getRequestInfo() {
            return requestInfo;
        }

        public void setRequestInfo(Map<String, Object> requestInfo) {
            this.requestInfo = requestInfo;
        }
    }
} 
