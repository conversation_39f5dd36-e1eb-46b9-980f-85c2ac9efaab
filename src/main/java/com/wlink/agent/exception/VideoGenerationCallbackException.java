package com.wlink.agent.exception;

/**
 * 视频生成回调异常
 */
public class VideoGenerationCallbackException extends RuntimeException {
    
    /**
     * 构造函数
     * 
     * @param message 错误信息
     */
    public VideoGenerationCallbackException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误信息
     * @param cause 原因
     */
    public VideoGenerationCallbackException(String message, Throwable cause) {
        super(message, cause);
    }
} 