package com.wlink.agent.exception;

import lombok.Getter;

/**
 * 视频生成服务错误码枚举
 */
@Getter
public enum VideoGenerateErrorEnum {
    
    BAD_REQUEST(400, "bad request", "不合法的请求"),
    FIELD_LACKING(400, "field is missing or empty", "缺少必要字段"),
    FIELD_UNWANTED(400, "unwanted field", "包含不需要的字段"),
    FIELD_COUNT_OUT_OF_RANGE(400, "field item count out of range", "字段数量超出范围"),
    PAGE_SIZE_OUT_OF_RANGE(400, "page size out of range", "图像尺寸不符合要求"),
    OPERATION_IN_PROCESS(400, "operation in process", "请求处理中"),
    PROMPT_POLICY_VIOLATION(400, "prompt policy violation", "提示词违反政策"),
    IMAGE_FORMAT_INVALID(400, "invalid image format", "图像格式不符合要求"),
    AUDIT_SUBMIT_ILLEGAL(400, "submit is illegal", "输入未通过安全审核"),
    CREDIT_INSUFFICIENT(400, "insufficient credits", "积分不足"),
    CREATION_POLICY_VIOLATION(400, "creation policy violation", "生成内容违反政策"),
    
    UNAUTHORIZED(401, "unauthorized", "未授权"),
    FORBIDDEN(403, "forbidden", "无权限访问"),
    
    TASK_NOT_FOUND(404, "task not found", "任务不存在"),
    CREATION_NOT_FOUND(404, "creation not found", "生成内容不存在"),
    
    CONFLICT(409, "resource conflict", "资源冲突"),
    
    QUOTA_EXCEEDED(429, "quota exceeded", "超过并发限制"),
    TOO_MANY_REQUESTS(429, "too many requests", "请求过于频繁"),
    SYSTEM_THROTTLING(429, "system is throttling", "系统限流"),
    
    CLIENT_CLOSED_REQUEST(499, "request canceled by client", "客户端取消请求"),
    
    SYSTEM_ERROR(500, "system error", "系统内部错误");

    private final int code;
    private final String msg;
    private final String desc;

    VideoGenerateErrorEnum(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }

    /**
     * 根据HTTP状态码获取对应的错误枚举
     */
    public static VideoGenerateErrorEnum getByHttpCode(int httpCode) {
        for (VideoGenerateErrorEnum error : values()) {
            if (error.getCode() == httpCode) {
                return error;
            }
        }
        return SYSTEM_ERROR;
    }
} 