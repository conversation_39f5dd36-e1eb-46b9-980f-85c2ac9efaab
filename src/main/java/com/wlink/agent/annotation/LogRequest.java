package com.wlink.agent.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于标记需要记录请求日志的方法
 * 添加此注解的方法将被记录请求和响应信息到数据
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogRequest {
    
    /*** @return 默认为true
     */
    boolean logRequestBody() default true;
    
    /**
     * 是否记录响应
     * @return 默认为true
     */
    boolean logResponseBody() default true;
    
    /**
     * 描述信息
     * @return 默认为空
     */
    String description() default "";
} 
