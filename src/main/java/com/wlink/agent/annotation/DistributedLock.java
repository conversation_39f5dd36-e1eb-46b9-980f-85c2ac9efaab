package com.wlink.agent.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {
    /**
     * 锁的key前缀
     */
    String prefix() default "distributed_lock:";
    
    /**
     * 锁的key，支持SpEL表达
     */
    String key();
    
    /**
     * 等待获取锁的最长时间，默认3
     */
    long waitTime() default 3000L;
    
    /**
     * 持有锁的最长时间，默认30
     */
    long leaseTime() default 30000L;
    
    /**
     * 时间单位，默认毫
     */
    TimeUnit timeUnit() default TimeUnit.MILLISECONDS;
    
    /**
     * 获取锁失败时的提示信
     */
    String failMessage() default "请求频繁，请稍后再试";
} 
