package com.wlink.agent;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class AgentApplication {

    public static void main(String[] args) throws InterruptedException {
        SpringApplication.run(AgentApplication.class, args);
        log.info("smart-agent application start success!");
    }

}
