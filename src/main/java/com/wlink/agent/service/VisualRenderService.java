package com.wlink.agent.service;

import com.wlink.agent.model.dto.VisualRecordDto;
import reactor.core.publisher.Mono;

/**
 * Service for interacting with the visual rendering SSE endpoint.
 */
public interface VisualRenderService {

    /**
     * Initiates the rendering process via SSE, updates the corresponding visual record in the database
     * with progress and final URL/status.
     *
     * @param visualRecordCode The session ID associated with the visual record to update.
     * @param request   The rendering request details.
     * @return A Mono<Void> that completes when the SSE stream finishes processing (successfully or with error handling).
     */
    Mono<Void> renderVisual(String visualRecordCode, VisualRecordDto request);

}