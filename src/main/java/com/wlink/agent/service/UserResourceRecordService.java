package com.wlink.agent.service;

import com.alibaba.cola.dto.PageResponse;
import com.wlink.agent.model.req.UserImageQueryReq;
import com.wlink.agent.model.res.UserImageRecordRes;
import com.wlink.agent.model.req.UserVideoQueryReq;
import com.wlink.agent.model.res.UserVideoRecordRes;

/**
 * 用户图片记录服务接口
 */
public interface UserResourceRecordService {

    /**
     * 分页查询用户图片记录
     *
     * @param req 查询请求
     * @return 分页结果
     */
    PageResponse<UserImageRecordRes> queryUserImageRecords(UserImageQueryReq req);

    /**
     * 分页查询用户视频记录
     *
     * @param req 查询请求
     * @return 分页结果
     */
    PageResponse<UserVideoRecordRes> queryUserVideoRecords(UserVideoQueryReq req);
} 