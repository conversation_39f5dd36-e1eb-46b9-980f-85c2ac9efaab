package com.wlink.agent.service;

import com.wlink.agent.model.dto.AudioSynthesisRequest;
import com.wlink.agent.model.dto.AudioSynthesisResult;

/**
 * 音频合成服务接口
 */
public interface AudioSynthesisService {

    /**
     * 提交音频合成任务
     *
     * @param request 音频合成请求
     * @return 音频合成结果
     */
    AudioSynthesisResult submitSynthesisTask(AudioSynthesisRequest request);

    /**
     * 查询音频合成任务状态
     *
     * @param taskId 任务ID
     * @return 音频合成结果，如果任务不存在返回null
     */
    AudioSynthesisResult getTaskStatus(String taskId);

    /**
     * 根据分镜ID查询最新的音频合成任务
     *
     * @param shotId 分镜ID
     * @return 音频合成结果，如果不存在返回null
     */
    AudioSynthesisResult getLatestTaskByShot(Long shotId);

    /**
     * 取消音频合成任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelTask(String taskId);

    /**
     * 处理ComfyUI回调结果
     *
     * @param taskId 任务ID
     * @param success 是否成功
     * @param resultAudioUrl 结果音频URL（成功时）
     * @param errorMessage 错误信息（失败时）
     * @param taskCostTime 任务耗时（毫秒）
     */
    void handleCallback(String taskId, boolean success, String resultAudioUrl, String errorMessage, Long taskCostTime);

    /**
     * 音频合成完成后触发对口型处理
     *
     * @param shotId 分镜ID
     * @param resultAudioUrl 合成后的音频URL
     */
    void triggerLipSyncAfterAudioSynthesis(Long shotId, String resultAudioUrl);
}
