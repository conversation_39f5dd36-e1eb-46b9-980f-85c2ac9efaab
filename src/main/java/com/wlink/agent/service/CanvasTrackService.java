package com.wlink.agent.service;

import com.wlink.agent.model.req.CanvasTrackCreateReq;
import com.wlink.agent.model.req.TrackAudioCreateReq;
import com.wlink.agent.model.res.CanvasTrackRes;
import com.wlink.agent.model.res.TrackAudioRes;

import java.util.List;

/**
 * 画布音轨服务接口
 */
public interface CanvasTrackService {
    
    /**
     * 创建画布音轨
     *
     * @param req 创建请求
     * @return 音轨ID
     */
    Long createCanvasTrack(CanvasTrackCreateReq req);
    
    /**
     * 获取画布音轨列表
     *
     * @param canvasId 画布ID
     * @return 音轨列表
     */
    List<CanvasTrackRes> getCanvasTracks(Long canvasId);
    
    /**
     * 获取音轨详情
     *
     * @param trackId 音轨ID
     * @return 音轨详情
     */
    CanvasTrackRes getTrackDetail(Long trackId);
    
    /**
     * 更新音轨信息
     *
     * @param trackId 音轨ID
     * @param req 更新请求
     */
    void updateCanvasTrack(Long trackId, CanvasTrackCreateReq req);
    
    /**
     * 删除音轨
     *
     * @param trackId 音轨ID
     */
    void deleteCanvasTrack(Long trackId);
    
    /**
     * 删除画布下所有音轨
     *
     * @param canvasId 画布ID
     */
    void deleteCanvasTracks(Long canvasId);
    
    /**
     * 添加音频到音轨
     *
     * @param req 添加请求
     * @return 音频ID
     */
    Long addTrackAudio(TrackAudioCreateReq req);
    
    /**
     * 获取音轨音频列表
     *
     * @param trackId 音轨ID
     * @return 音频列表
     */
    List<TrackAudioRes> getTrackAudios(Long trackId);
    
    /**
     * 获取音频详情
     *
     * @param audioId 音频ID
     * @return 音频详情
     */
    TrackAudioRes getAudioDetail(Long audioId);
    
    /**
     * 更新音频信息
     *
     * @param audioId 音频ID
     * @param req 更新请求
     */
    void updateTrackAudio(Long audioId, TrackAudioCreateReq req);
    
    /**
     * 删除音频
     *
     * @param audioId 音频ID
     */
    void deleteTrackAudio(Long audioId);
    
    /**
     * 删除音轨下所有音频
     *
     * @param trackId 音轨ID
     */
    void deleteTrackAudios(Long trackId);
}
