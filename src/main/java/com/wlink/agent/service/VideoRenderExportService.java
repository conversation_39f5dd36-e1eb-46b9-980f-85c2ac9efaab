package com.wlink.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.common.dto.PageRes;
import com.wlink.agent.dao.po.AiVideoRenderExportPo;
import com.wlink.agent.model.req.VideoRenderExportReq;
import com.wlink.agent.model.res.VideoRenderExportRes;

import java.util.List;

/**
 * 视频渲染导出服务接口
 */
public interface VideoRenderExportService extends IService<AiVideoRenderExportPo> {

    /**
     * 提交视频渲染导出任务
     *
     * @param req 渲染导出请求
     * @return 渲染任务ID
     */
    Long submitRenderExport(VideoRenderExportReq req);

    /**
     * 获取用户的渲染导出任务列表
     *
     * @param userId 用户ID
     * @param canvasId 画布ID (可选)
     * @return 渲染任务列表
     */
    List<VideoRenderExportRes> getUserRenderExports(String userId, Long canvasId);

    /**
     * 获取渲染导出任务详情
     *
     * @param taskId 任务ID
     * @return 渲染任务详情
     */
    VideoRenderExportRes getRenderExportDetail(Long taskId);

    /**
     * 设置渲染任务ID
     *
     * @param taskId 任务ID
     * @param renderTaskId 渲染任务ID (Python渲染服务返回的任务ID)
     */
    void setRenderTaskId(Long taskId, String renderTaskId);

    /**
     * 更新渲染任务状态为处理中
     *
     * @param taskId 任务ID
     */
    void startProcessing(Long taskId);

    /**
     * 完成渲染任务
     *
     * @param taskId 任务ID
     * @param videoUrl 渲染后的视频地址
     */
    void completeTask(Long taskId, String videoUrl);

    /**
     * 标记渲染任务失败
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    void failTask(Long taskId, String errorMessage);

    /**
     * 处理视频渲染回调
     *
     * @param renderTaskId 渲染任务ID (Python服务返回的任务ID)
     * @param status 渲染状态
     * @param errorMessage 错误信息
     * @param videoUrl 视频URL
     * @return 是否处理成功
     */
    boolean handleRenderCallback(String renderTaskId, Integer status, String errorMessage, String videoUrl,Long videoDuration);

    /**
     * 异步处理视频渲染回调
     *
     * @param renderTaskId 渲染任务ID (Python服务返回的任务ID)
     * @param status 渲染状态
     * @param errorMessage 错误信息
     * @param videoUrl 视频URL
     * @param videoDuration 视频时长
     */
    void handleRenderCallbackAsync(String renderTaskId, Integer status, String errorMessage, String videoUrl, Long videoDuration);

    /**
     * 删除视频渲染导出记录
     * 只能删除状态为成功(2)和失败(3)的记录
     *
     * @param taskId 任务ID
     */
    void deleteRenderExport(Long taskId);

    /**
     * 分享视频
     *
     * @param taskId 任务ID
     * @return 分享码
     */
    String shareVideo(Long taskId);

    /**
     * 取消分享视频
     *
     * @param taskId 任务ID
     */
    void unshareVideo(Long taskId);

    /**
     * 分页查询已分享的视频记录
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageRes<VideoRenderExportRes> getSharedVideos(int pageNum, int pageSize);

    /**
     * 根据分享码查询视频导出记录
     *
     * @param shareCode 分享码
     * @return 视频导出记录
     */
    VideoRenderExportRes getVideoByShareCode(String shareCode);
}
