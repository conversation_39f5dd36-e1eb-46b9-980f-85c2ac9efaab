package com.wlink.agent.service;

import com.wlink.agent.model.req.UniversalImageGenerationRequest;
import com.wlink.agent.model.res.UniversalImageGenerationResponse;
import com.wlink.agent.model.res.UserResourceStatusResponse;

import java.util.List;

/**
 * 通用图片生成服务接口
 */
public interface UniversalImageGenerationService {

    /**
     * 生成图片
     * @param request 图片生成请求
     * @return 图片生成响应
     */
    UniversalImageGenerationResponse generateImage(UniversalImageGenerationRequest request);

    /**
     * 根据资源编码查询生成状态
     * @param code 资源编码
     * @return 资源状态响应
     */
    UserResourceStatusResponse getResourceStatus(String code);

    /**
     * 批量查询资源状态
     * @param codes 资源编码列表
     * @return 资源状态响应列表
     */
    List<UserResourceStatusResponse> getResourceStatusBatch(List<String> codes);

    /**
     * 根据用户ID查询资源列表
     * @param userId 用户ID
     * @param resourceType 资源类型(可选)
     * @param limit 限制数量
     * @return 资源状态响应列表
     */
    List<UserResourceStatusResponse> getUserResources(String userId, Integer resourceType, Integer limit);
}
