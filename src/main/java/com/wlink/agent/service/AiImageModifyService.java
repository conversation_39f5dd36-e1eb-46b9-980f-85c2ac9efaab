package com.wlink.agent.service;

import com.wlink.agent.model.req.ImageModifyReq;
import com.wlink.agent.model.req.ImageUploadReq;
import com.wlink.agent.model.res.ImageModifyRes;
import com.wlink.agent.model.res.ImageModifyResultRes; // 引入新的结果类
import com.wlink.agent.model.res.ImageUploadRes;
import com.wlink.agent.model.res.ModifyImageUrlInfoRes; // Import the new DTO
import java.util.List; // Import List

/**
 * AI图片修改服务接口
 */
public interface AiImageModifyService {

    /**
     * 修改图片并记录
     * @param req 图片修改请求参数
     * @return 创建的记录信息 (包含ID和操作编码)
     */
    ImageModifyRes modifyImage(ImageModifyReq req); // 修改返回类型

    /**
     * 根据 modifyCode 查询图片修改结果
     * @param modifyCode 修改操作的唯一编码
     * @return 图片修改结果详情，如果找不到则返回 null 或抛出异常
     */
    ImageModifyResultRes getModifyResultByCode(String modifyCode); // 新增查询方法

    /**
     * 根据会话ID、一级ID和二级ID查询源图片URL列表
     *
     * @param conversationId 会话ID
     * @param primaryId 一级ID
     * @param secondaryId 二级ID (可选, 可为null或空字符串)
     * @return 包含源图片URL和修改编码的对象列表
     */
    ModifyImageUrlInfoRes getSourceImageUrlsBySessionAndIds(String conversationId, String primaryId, String secondaryId);

    /**
     * 确认图片修改，并将新图片更新到内容记录中
     * @param modifyCode 修改操作的唯一编码
     */
    void confirmImageModification(String modifyCode);
    
    /**
     * 上传图片并记录
     * @param req 图片上传请求参数
     * @return 上传结果信息
     */
    ImageUploadRes uploadImage(ImageUploadReq req);
}