package com.wlink.agent.service;

import com.wlink.agent.dao.po.VendorAccountPo;

import java.util.List;

/**
 * 图片服务供应商账号服务
 */
public interface VendorAccountService {

    /**
     * 根据供应商名称获取可用账号列表
     *
     * @param vendorName 供应商名称
     * @return 可用账号列表
     */
    List<VendorAccountPo> getAvailableAccounts(String vendorName);

    /**
     * 根据供应商名称获取可用且未达到最大任务数的账号列表
     *
     * @param vendorName 供应商名称
     * @return 可用且未达到最大任务数的账号列表
     */
    List<VendorAccountPo> getAvailableAccountsWithCapacity(String vendorName);

    /**
     * 为图片生成任务选择一个合适的账号
     *
     * @param vendorName 供应商名称
     * @return 选择的账号，如果没有可用账号则返回null
     */
    VendorAccountPo selectAccountForTask(String vendorName);

    /**
     * 记录账号使用情况
     *
     * @param accountId 账号ID
     * @param count 使用次数
     */
    void recordAccountUsage(Long accountId, int count);

    /**
     * 重置账号的每日配额
     *
     * @param accountId 账号ID
     */
    void resetAccountQuota(Long accountId);

    /**
     * 添加新账号
     *
     * @param account 账号信息
     * @return 添加后的账号
     */
    VendorAccountPo addAccount(VendorAccountPo account);

    /**
     * 更新账号信息
     *
     * @param account 账号信息
     * @return 更新后的账号
     */
    VendorAccountPo updateAccount(VendorAccountPo account);

    /**
     * 启用/禁用账号
     *
     * @param accountId 账号ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    boolean updateAccountStatus(Long accountId, int status);

    /**
     * 获取账号详情
     *
     * @param accountId 账号ID
     * @return 账号详情
     */
    VendorAccountPo getAccountById(Long accountId);


      /**
     * 锁定账号，标记为正在处理任务
     *
     * @param accountId 账号ID
     * @return 是否成功锁定
     */
    boolean lockAccount(Long accountId);

    /**
     * 解锁账号，标记为可用状态
     *
     * @param accountId 账号ID
     */
    void unlockAccount(Long accountId);

    /**
     * 获取当前未被锁定（未在执行任务）的账号
     *
     * @param vendorName 供应商名称
     * @return 可用的未锁定账号，如果没有则返回null
     */
    VendorAccountPo selectAvailableUnlockedAccount(String vendorName);

    /**
     * 递减账号的正在执行任务数
     *
     * @param accountId 账号ID
     * @return 是否成功递减
     */
    boolean decreaseRunningTaskCount(Long accountId);
} 