package com.wlink.agent.service;

/**
 * 短信服务接口
 */
public interface SmsService {
    
    /**
     * 发送验证码短信
     * @param phoneNumber 手机号
     * @param content 短信内容
     * @return 是否发送成功
     */
    boolean sendSms(String phoneNumber, String content);
    
    /**
     * 使用模板发送短信
     * @param phoneNumber 手机号
     * @param templateCode 模板代码
     * @param templateParam 模板参数JSON
     * @param userId 关联用户ID (可选)
     * @return 是否发送成功
     */
    boolean sendSmsWithTemplate(String phoneNumber, String templateCode, String templateParam, String userId);
} 