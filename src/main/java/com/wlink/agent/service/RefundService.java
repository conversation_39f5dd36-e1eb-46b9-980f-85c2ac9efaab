package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiPayRefundPo;

/**
 * 退款服务接口
 */
public interface RefundService {

    /**
     * 创建退款申请
     *
     * @param orderNo     订单编号
     * @param userId      用户ID
     * @param amount      退款金额（单位：分）
     * @param reason      退款原因
     * @param deductPoints 是否扣除积分：0-否，1-是
     * @return 退款记录
     */
    AiPayRefundPo createRefund(String orderNo, String userId, Integer amount, String reason, Integer deductPoints);

    /**
     * 根据退款单号查询退款记录
     *
     * @param refundNo 退款单号
     * @return 退款记录
     */
    AiPayRefundPo queryRefund(String refundNo);

    /**
     * 根据订单编号查询退款记录
     *
     * @param orderNo 订单编号
     * @return 退款记录
     */
    AiPayRefundPo queryRefundByOrderNo(String orderNo);

    /**
     * 更新退款状态
     *
     * @param refundNo 退款单号
     * @param status   退款状态
     * @return 是否成功
     */
    boolean updateRefundStatus(String refundNo, Integer status);

    /**
     * 生成退款单号
     *
     * @return 退款单号
     */
    String generateRefundNo();
} 