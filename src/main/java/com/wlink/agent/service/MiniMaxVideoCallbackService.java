package com.wlink.agent.service;

import com.wlink.agent.model.req.MiniMaxVideoCallbackReq;

/**
 * MiniMax视频生成回调服务接口
 */
public interface MiniMaxVideoCallbackService {
    
    /**
     * 处理MiniMax视频生成回调
     * 
     * @param req 回调请求
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean handleCallback(MiniMaxVideoCallbackReq req);
    
    /**
     * 异步下载并上传视频文件
     * 
     * @param taskId 任务ID
     * @param fileId 文件ID
     * @return 异步处理结果
     */
    void asyncDownloadAndUploadVideo(String taskId, String fileId);
}
