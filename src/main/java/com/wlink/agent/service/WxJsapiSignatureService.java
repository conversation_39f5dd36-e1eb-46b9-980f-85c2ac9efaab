package com.wlink.agent.service;

import com.wlink.agent.model.WxJsapiSignature;

/**
 * 微信JS-SDK签名服务接口
 */
public interface WxJsapiSignatureService {
    
    /**
     * 获取微信JS-SDK的jsapi_ticket
     * 会自动缓存ticket并处理过期逻辑
     * 
     * @return jsapi_ticket
     */
    String getJsapiTicket();
    
    /**
     * 创建微信JS-SDK签名
     * 
     * @param url 当前网页的URL，不包含#及其后面部分
     * @return 签名结果对象
     */
    WxJsapiSignature createJsapiSignature(String url);
} 