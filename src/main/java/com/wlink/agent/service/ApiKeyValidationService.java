package com.wlink.agent.service;

/**
 * API Key 验证服务接口
 */
public interface ApiKeyValidationService {
    
    /**
     * 验证 API Key 并返回用户ID
     *
     * @param apiKey API Key
     * @return 用户ID，如果验证失败返回null
     */
    String validateApiKeyAndGetUserId(String apiKey);
    
    /**
     * 检查 API Key 是否有效
     *
     * @param apiKey API Key
     * @return 是否有效
     */
    boolean isValidApiKey(String apiKey);
}
