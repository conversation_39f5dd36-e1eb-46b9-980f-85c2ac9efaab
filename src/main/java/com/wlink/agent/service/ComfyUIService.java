package com.wlink.agent.service;

import com.wlink.agent.client.model.comfyui.ComfyUINodeInfo;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;

import java.util.List;

/**
 * ComfyUI 服务接口
 */
public interface ComfyUIService {

    /**
     * 运行 ComfyUI 工作流
     *
     * @param webappId Web应用ID
     * @param apiKey API密钥
     * @param nodeInfoList 节点信息列表
     * @return 运行响应
     */
    ComfyUIRunResponse runWorkflow(Long webappId, String apiKey, List<ComfyUINodeInfo> nodeInfoList);

    /**
     * 运行 ComfyUI 工作流
     *
     * @param request 运行请求
     * @return 运行响应
     */
    ComfyUIRunResponse runWorkflow(ComfyUIRunRequest request);

    /**
     * 创建图像替换工作流
     *
     * @param webappId Web应用ID
     * @param apiKey API密钥
     * @param originalImage 原始图像文件名
     * @param maskImage 遮罩图像文件名
     * @param prompt 替换提示词
     * @return 运行响应
     */
    ComfyUIRunResponse runImageReplaceWorkflow(Long webappId, String apiKey, 
                                               String originalImage, String maskImage, String prompt);
}
