package com.wlink.agent.service;

import com.wechat.pay.java.service.payments.model.Transaction;
import com.wlink.agent.user.model.CreateOrderReq;
import com.wlink.agent.user.model.PayOrderRes;
import com.wlink.agent.dao.po.PayOrderPo;
import jakarta.servlet.http.HttpServletRequest;


/**
 * 支付服务接口
 */
public interface PayService {

    /**
     * 创建支付订单
     *
     * @param request 创建订单请求
     * @return 支付订单响应
     */
    PayOrderRes createOrder(CreateOrderReq request);

    /**
     * 查询支付订单
     *
     * @param orderNo 订单编号
     * @return 支付订单
     */
    PayOrderPo queryOrder(String orderNo);

    /**
     * 关闭订单
     *
     * @param orderNo 订单编号
     * @return 是否成功
     */
    void closeOrder(String orderNo);

    /**
     * 处理支付回调(V2版本，已弃用，但保留兼容性)
     *
     * @param xmlData 微信支付回调数据
     * @return 处理结果（返回给微信的XML）
     * @deprecated 请使用V3版本的handlePayNotify(String notifyData, HttpServletRequest request)
     */
    @Deprecated
    String handlePayNotify(String xmlData);
    
    /**
     * 处理支付回调(V3版本)
     *
     * @param notifyData 微信支付回调JSON数据
     * @param request HTTP请求对象，用于获取签名验证信息
     * @return 处理结果（返回给微信的XML）
     */
    String handlePayNotify(String notifyData, HttpServletRequest request);

    /**
     * 退款
     *
     * @param orderNo 订单编号
     * @param reason  退款原因
     * @return 是否成功
     */
    void refund(String orderNo, String reason,  Integer deductPoints);
    
    /**
     * 主动查询微信支付订单状态
     *
     * @param orderNo 商户订单号
     * @return 微信支付订单信息
     */
    Transaction queryOrderFromWechat(String orderNo);


    /**
     * 通过微信支付订单信息更新本地订单
     *
     * @param transaction 微信支付订单信息
     * @return 更新后的订单信息
     */
    PayOrderPo updateOrderStatusByTransaction(Transaction transaction);

    /**
     * 同步订单状态
     * 通过主动查询微信支付订单状态并更新本地订单
     *
     * @param orderNo 商户订单号
     * @return 更新后的订单信息
     */
    PayOrderPo syncOrderStatus(String orderNo);
} 