package com.wlink.agent.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditRequest;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;

import java.util.List;

public interface ImageTaskQueueService {

    /**
     * Queue a text-to-image generation task
     * 
     * @param sessionId the session ID
     * @param request the image generation request
     * @return the queued task
     */
    AiImageTaskQueuePo queueGenerateTask(String sessionId, VolcengineImageRequest request,String taskType);

    /**
     * Queue a character retention task
     * 
     * @param sessionId the session ID
     * @param request the character retention request
     * @return the queued task
     */
    AiImageTaskQueuePo queueRetainTask(String sessionId, VolcengineCharacterRetentionRequest request);

    /**
     * Queue a seed edit task
     * 
     * @param sessionId the session ID
     * @param request the seed edit request
     * @return the queued task
     */
    AiImageTaskQueuePo queueEditTask(String sessionId, VolcengineSeedEditRequest request);

    /**
     * Process a task from the queue
     * 
     * @param task the task to process
     * @return the processing result
     */
    ImageGenerateRes processTask(AiImageTaskQueuePo task);

    /**
     * Check if a new task can be executed immediately
     * 
     * @return true if a new task can be executed immediately
     */
    boolean canExecuteImmediately();

    /**
     * Get the next pending task from the queue
     * 
     * @return the next pending task
     */
    AiImageTaskQueuePo getNextPendingTask();

    /**
     * Get tasks by session ID and status
     * 
     * @param sessionId the session ID
     * @param status the task status
     * @return the list of tasks
     */
    List<AiImageTaskQueuePo> getTasksBySessionAndStatus(String sessionId, String status);

    /**
     * Update task status
     * 
     * @param taskId the task ID
     * @param status the new status
     * @param result the result (if any)
     * @param errorReason the error reason (if any)
     * @param retryCount the retry count (if any)
     * @return the updated task
     */
    AiImageTaskQueuePo updateTaskStatus(Long taskId, String status, ImageGenerateRes result, String errorReason, Integer retryCount);

    /**
     * Update task status (without retry count)
     * 
     * @param taskId the task ID
     * @param status the new status
     * @param result the result (if any)
     * @param errorReason the error reason (if any)
     * @return the updated task
     * @deprecated Use {@link #updateTaskStatus(Long, String, ImageGenerateRes, String, Integer)} instead
     */
    @Deprecated
    default AiImageTaskQueuePo updateTaskStatus(Long taskId, String status, ImageGenerateRes result, String errorReason) {
        return updateTaskStatus(taskId, status, result, errorReason, null);
    }

    /**
     * Get task by ID
     * 
     * @param taskId the task ID
     * @return the task
     */
    AiImageTaskQueuePo getTaskById(Long taskId);
} 