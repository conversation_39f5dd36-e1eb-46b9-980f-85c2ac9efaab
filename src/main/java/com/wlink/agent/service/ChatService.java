package com.wlink.agent.service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.model.req.ChatMessageRequest;
import java.util.List;

import com.wlink.agent.model.req.UpdateSessionReq;
import com.wlink.agent.model.res.ConversationListResponse;
import com.wlink.agent.model.res.ConversationMessageListResponse;
import com.wlink.agent.model.vo.AiCreationSessionInfoVo;
import com.wlink.agent.model.vo.AiCreationSessionVO; // 新增 VO 导入
import com.wlink.agent.model.req.AiCreationSessionQueryReq; // 新增查询请求导入
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.wlink.agent.model.req.CreateConversationRequest;
import com.wlink.agent.model.vo.ConversationItem;
// 移除 AiCreationSessionPo 导入，因为 Service 接口不再直接返回它

public interface ChatService {

    /**
     * 处理聊天消息请求并返回 SSE Emitter
     * @param request 请求参数
     * @return SseEmitter 用于流式响应
     */
    SseEmitter handleChatMessage(ChatMessageRequest request);
    /**
     * 获取会话列表
     * @param limit 返回数量限制
     * @return ConversationListResponse 会话列表响应
     */
    ConversationListResponse getConversationList(int limit);
    /**
     * 获取会话历史记录
     * @param conversationId 会话ID
     * @param limit 返回数量限制
     * @return ConversationMessageListResponse 会话消息列表响应
     */
    ConversationMessageListResponse getConversationMessages(String conversationId, int limit);

    /**
     * 根据会话ID删除会话
     * @param conversationId 会话ID
     */
    void deleteConversation(String conversationId);
    /**
     * 创建会话
     * @param request 创建会话请求体
     * @param
     * @return ConversationItem 新建会话
     */
    ConversationItem createConversation(CreateConversationRequest request);
    /**
     * 获取当前用户的AI创作会话列表（分页查询）
     * @param queryReq 分页查询请求（包含页码、每页大小、状态查询条件）
     * @return PageResponse<AiCreationSessionVO> 分页的AI创作会话列表
     */
    PageResponse<AiCreationSessionVO> getAiCreationSessionsByUserId(AiCreationSessionQueryReq queryReq);
    
    /**
     * 停止响应内容
     * @param conversationId 会话ID
     * @return 是否成功停止响应
     */
    boolean stopResponse(String conversationId);
    
    /**
     * 清空上下文
     * @param conversationId 会话ID
     * @return 是否成功清空上下文
     */
    boolean clearContext(String conversationId);
    
    /**
     * 根据会话ID查询会话信息
     * @param conversationId 会话ID
     * @return SingleResponse<AiCreationSessionInfoVo> 会话信息
     */
    SingleResponse<AiCreationSessionInfoVo> getSessionInfoById(String conversationId);
    
    /**
     * 公开接口：根据会话ID查询会话信息（无需用户登录）
     * @param conversationId 会话ID
     * @return SingleResponse<AiCreationSessionInfoVo> 会话信息
     */
    SingleResponse<AiCreationSessionInfoVo> getPublicSessionInfoById(String conversationId);
    
    /**
     * 更新会话信息
     * @param request 更新会话请求体
     * @return Response 操作结果
     */
    Response updateSession(UpdateSessionReq request);
    
    /**
     * 公开接口：更新会话信息（无需用户登录）
     * @param request 更新会话请求体
     * @return Response 操作结果
     */
    Response updatePublicSession(UpdateSessionReq request);
}