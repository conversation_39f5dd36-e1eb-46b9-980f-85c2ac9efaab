package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.service.impl.TtsResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.File;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.HashMap;

@RefreshScope
@Slf4j
@Service
public class MinimaxTtsService {

    @Value("${minimax.group-id:1685416442501041}")
    private String groupId;

    @Value("${minimax.api-key:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}")
    private String apiKey;

    @Value("${tts.audio.save-path:/data/audio/}")
    private String audioSavePath;

    //model
    @Value("${tts.model:speech-01-hd}")
    private String model;


    private final OkHttpClient client = new OkHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 非流式调用TTS服务，返回音频文件URL和音频元信息
     * @return 包含文件路径、音频长度和大小的TtsResult对象
     */
    public TtsResult textToSpeechNonStream(TtsGenerateReq req, AgentSoundPo agentSoundPo)  {
        // 生成音频文件名
        String fileName = generateFileName();
        // 确保目录存在
        ensureDirectoryExists();
        // 获取音频数据
        log.info("文本处理前:{}", req.getText());
        String text = cleanText(req.getText());
        log.info("文本处理后:{}", text);

        AudioResult audioResult = callTtsApi(req, agentSoundPo, false);
        byte[] audioData = audioResult.getAudioData();

        String filePath = audioSavePath + fileName;

        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(audioData);
        }catch (IOException e) {
            log.error("Error writing audio data to file", e);
            throw new BizException("Error writing audio data to file");
        }
        
        // 构建返回结果
        TtsResult result = new TtsResult(filePath, audioResult.getAudioLength(), audioResult.getAudioSize());
        
        log.info("TTS转换完成，文件路径: {}, 音频长度: {}ms, 音频大小: {}字节", 
                filePath, audioResult.getAudioLength(), audioResult.getAudioSize());
        
        return result;
    }

    private AudioResult callTtsApi(TtsGenerateReq req, AgentSoundPo agentSoundPo,boolean stream) {
        String url = "https://api.minimax.chat/v1/t2a_v2?GroupId=" + groupId;
        
        // 构建请求体
        TtsRequest ttsRequest = new TtsRequest();
        ttsRequest.setModel(model);
        ttsRequest.setText(req.getText());
        ttsRequest.setStream(stream);
        
        // 设置语音参数
        VoiceSetting voiceSetting = new VoiceSetting();
        voiceSetting.setVoiceId(agentSoundPo.getSound());
        voiceSetting.setVol(StringUtils.isBlank(req.getVolume()) ? 1.0 : Double.parseDouble(req.getVolume()));
        voiceSetting.setSpeed(StringUtils.isBlank(req.getRate()) ? 1.0 : Double.parseDouble(req.getRate()));
        voiceSetting.setPitch(null == req.getPitch() ? 0 : req.getPitch());
        voiceSetting.setEmotion(req.getEmotion());
        ttsRequest.setVoiceSetting(voiceSetting);


        if (CollUtil.isNotEmpty(req.getTone())){
            PronunciationDict pronunciationDict = new PronunciationDict();
            pronunciationDict.setTone(req.getTone());
            ttsRequest.setPronunciationDict(pronunciationDict);
        }
        // 设置音频参数
        AudioSetting audioSetting = new AudioSetting();
        audioSetting.setFormat("wav");
        ttsRequest.setAudioSetting(audioSetting);
        ttsRequest.setSubtitleEnable(false);
        String jsonString = JSON.toJSONString(ttsRequest);
        log.info("TTS接口请求开始------------请求参数:{}", jsonString);
        // 发送请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .post(RequestBody.create(
                        jsonString,
                        MediaType.parse("application/json")))
                .build();

        if (!stream) {
            // 非流式处理
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new BizException("Unexpected response code: " + response);
                }

                String responseBody = response.body().string();
                log.info("TTS接口请求结束------------响应成功");
                TtsResponse ttsResponse = JSON.parseObject(responseBody, TtsResponse.class);
                
                // 检查响应状态
                if (ttsResponse.getBaseResp() == null || ttsResponse.getBaseResp().getStatusCode() != 0) {
                    String errorMsg = ttsResponse.getBaseResp() != null ? 
                        ttsResponse.getBaseResp().getStatusMsg() : "Unknown error";
                    throw new BizException("TTS API call failed: " + errorMsg);
                }

                // 检查音频数据
                if (ttsResponse.getData() == null || ttsResponse.getData().getAudio() == null) {
                    throw new BizException("No audio data in response");
                }
                // 获取并处理音频数据
                byte[] audioData = hexStringToByteArray(ttsResponse.getData().getAudio());
                Long audioLength = null;
                Long audioSize = null;
                String subtitleFile = null;
                
                // 获取extra_info数据
                if (ttsResponse.getExtraInfo() != null) {
                    audioLength = ttsResponse.getExtraInfo().getAudioLength();
                    audioSize = ttsResponse.getExtraInfo().getAudioSize();
                    subtitleFile = ttsResponse.getData().getSubtitleFile();
                    log.info("获取到extra_info数据: audio_length={}, audio_size={},subtitleFile:{}", audioLength, audioSize,subtitleFile);
                } else {
                    log.warn("响应中没有包含extra_info数据");
                }
                
                return new AudioResult(audioData, audioLength, audioSize);

            }catch (IOException e) {
                throw new BizException("Error processing response");
            }
        } else {
            // 流式处理
            ByteArrayOutputStream audioData = new ByteArrayOutputStream();
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful() || response.body() == null) {
                    throw new BizException("Unexpected response " + response);
                }

                try (ResponseBody responseBody = response.body()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    Long audioLength = null;
                    Long audioSize = null;
                    
                    while ((bytesRead = responseBody.byteStream().read(buffer)) != -1) {
                        String chunk = new String(buffer, 0, bytesRead);
                        if (chunk.startsWith("data:")) {
                            String jsonData = chunk.substring(5);
                            Map<String, Object> data = objectMapper.readValue(jsonData, Map.class);
                            
                            // 处理音频数据
                            if (data.containsKey("data") && !data.containsKey("extra_info")) {
                                Map<String, Object> innerData = (Map<String, Object>) data.get("data");
                                if (innerData.containsKey("audio")) {
                                    String audioHex = (String) innerData.get("audio");
                                    byte[] decodedAudio = hexStringToByteArray(audioHex);
                                    audioData.write(decodedAudio);
                                }
                            }
                            
                            // 处理extra_info数据
                            if (data.containsKey("extra_info")) {
                                Map<String, Object> extraInfo = (Map<String, Object>) data.get("extra_info");
                                if (extraInfo.containsKey("audio_length")) {
                                    audioLength = (Long) extraInfo.get("audio_length");
                                }
                                if (extraInfo.containsKey("audio_size")) {
                                    audioSize = (Long) extraInfo.get("audio_size");
                                }

                                log.info("获取到extra_info数据: audio_length={}, audio_size={}", audioLength, audioSize);
                            } else {
                                log.debug("当前数据块中没有extra_info信息");
                            }
                        }
                    }
                    return new AudioResult(audioData.toByteArray(), audioLength, audioSize);
                }
            }catch (IOException e) {
                throw new BizException("Error processing response");
            }
        }
    }

    private String generateFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = UUID.randomUUID().toString().substring(0, 8);
        return String.format("tts_%s_%s.wav", timestamp, random);
    }

    private void ensureDirectoryExists() {
        try {
            Path directory = Paths.get(audioSavePath);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
        } catch (IOException e) {
            log.error("Error creating directory: {}", audioSavePath, e);
            throw new BizException ("Error creating directory: " + audioSavePath);
        }
    }

    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    @Data
    static class TtsRequest {
        private String model;
        private String text;
        private boolean stream;
        @JSONField(name = "subtitle_enable")
        private boolean subtitleEnable;
        @JSONField(name = "voice_setting")
        private VoiceSetting voiceSetting;
        @JSONField(name = "audio_setting")
        private AudioSetting audioSetting;
        @JSONField(name = "pronunciation_dict")
        private PronunciationDict pronunciationDict;
    }

    @Data
    static class VoiceSetting {
        @JSONField(name = "voice_id")
        private String voiceId;

        @JSONField(name = "vol")
        private Double vol;

        @JSONField(name = "speed")
        private Double speed;

        @JSONField(name = "pitch")
        private Integer pitch;

        @JSONField(name = "emotion")
        private String emotion;

    }

    @Data
    static class PronunciationDict{
        @JSONField(name = "tone")
        private List<String> tone;
    }

    @Data
    static class AudioSetting {
        private String format;

    }

    @Data
    static class TtsResponse {
        private TtsData data;
        private ExtraInfo extraInfo;
        private String traceId;
        private BaseResp baseResp;
        
        @JSONField(name = "extra_info")
        public ExtraInfo getExtraInfo() {
            return extraInfo;
        }
        
        @JSONField(name = "base_resp")
        public BaseResp getBaseResp() {
            return baseResp;
        }
    }

    @Data
    static class TtsData {
        private String audio;
        private Integer status;
        private String ced;
        @JSONField(name = "subtitle_file")
        private String subtitleFile;
        
        @JSONField(name = "audio_length")
        private Integer audioLength;
        
        @JSONField(name = "audio_size")
        private Integer audioSize;
    }

    @Data
    static class ExtraInfo {
        @JSONField(name = "audio_length")
        private Long audioLength;
        
        @JSONField(name = "audio_sample_rate")
        private Integer audioSampleRate;
        
        @JSONField(name = "audio_size")
        private Long audioSize;
        
        private Integer bitrate;
        
        @JSONField(name = "word_count")
        private Integer wordCount;
        
        @JSONField(name = "invisible_character_ratio")
        private Integer invisibleCharacterRatio;
        
        @JSONField(name = "usage_characters")
        private Integer usageCharacters;
        
        @JSONField(name = "audio_format")
        private String audioFormat;
        
        @JSONField(name = "audio_channel")
        private Integer audioChannel;
    }

    @Data
    static class BaseResp {
        @JSONField(name = "status_code")
        private Integer statusCode;
        
        @JSONField(name = "status_msg")
        private String statusMsg;
    }
    
    /**
     * 音频结果类，包含音频数据和元信息
     */
    @Data
    static class AudioResult {
        private byte[] audioData;
        private Long audioLength;
        private Long audioSize;
        
        public AudioResult(byte[] audioData, Long audioLength, Long audioSize) {
            this.audioData = audioData;
            this.audioLength = audioLength;
            this.audioSize = audioSize;
        }
    }

    /**
     * 获取音频文件实际时长（毫秒）
     */
    public long getAudioFileDuration(File file) {
        try {
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(file);
            long frames = audioInputStream.getFrameLength();
            float frameRate = audioInputStream.getFormat().getFrameRate();
            long durationInMillis = (long) ((frames * 1000) / frameRate);
            audioInputStream.close();
            return durationInMillis;
        } catch (UnsupportedAudioFileException | IOException e) {
            log.error("Failed to get audio file duration: {}", file.getName(), e);
            // 如果无法获取准确时长，返回一个估算值
            return 3000L;
        }
    }

    /**
     * 清理文本，仅保留常用标点符号
     *
     * @param text 需要清理的文本
     * @return 清理后的文本
     */
    public static String cleanText(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        // 正则表达式说明：
        // - 保留中文字符、数字、空格、英文标点和中文标点
        // - 中文标点：。，、；：""''（）！？【】《》—… 等
        String regex = "[^\\p{L}\\p{N}\\s.,!?;:'\"/-。，、；：！？]";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        // 将匹配到的特殊符号替换为空字符串
        String string = matcher.replaceAll("");
        return StringUtils.isNotBlank(string) ? string : text;
    }
} 