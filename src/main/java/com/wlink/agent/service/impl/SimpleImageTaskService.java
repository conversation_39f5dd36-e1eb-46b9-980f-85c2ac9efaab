package com.wlink.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 简化的图片任务服务
 * 去掉MQ复杂性，直接保存到数据库等待调度
 */
@Slf4j
@Service
public class SimpleImageTaskService {

    @Autowired
    private AiImageTaskQueueMapper taskQueueMapper;
    
    @Autowired
    private AiCreationSessionMapper sessionMapper;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Value("${flux.image.safetyTolerance:5}")
    private String safetyTolerance;

    // 全局队列位置计数器（可以考虑使用数据库序列或Redis计数器）
    private static final AtomicLong globalPositionCounter = new AtomicLong(0);

    /**
     * 提交图片生成任务
     * 
     * @param sessionId 会话ID
     * @param request 请求参数
     * @param taskType 任务类型
     * @return 任务对象
     */
    @Transactional
    public AiImageTaskQueuePo submitImageTask(String sessionId, VolcengineImageRequest request, String taskType) {
        try {
            // 1. 验证会话
            AiCreationSessionPo session = sessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                    .eq(AiCreationSessionPo::getSessionId, sessionId)
                    .last("LIMIT 1")
            );
            
            if (session == null) {
                throw new IllegalArgumentException("会话不存在: " + sessionId);
            }

            // 2. 构建请求参数JSON
            String requestJson = buildRequestJson(request);

            // 3. 生成队列位置
            long globalPosition = globalPositionCounter.incrementAndGet();

            // 4. 创建任务记录
            AiImageTaskQueuePo task = AiImageTaskQueuePo.builder()
                .sessionId(sessionId)
                .userId(session.getUserId())
                .contentType(request.getType())
                .contentId(request.getContentId())
                .taskType(taskType)
                .requestParams(requestJson)
                .taskStatus(TaskStatus.PENDING.getValue())
                .globalQueuePosition((int) globalPosition)
                .sessionQueuePosition(getNextSessionPosition(sessionId))
                .imageModel(getImageModel(request))
                .retryCount(0)
                .createTime(new Date())
                .updateTime(new Date())
                .build();

            // 5. 保存到数据库
            taskQueueMapper.insert(task);

            log.info("图片任务提交成功 - 任务ID: {}, 会话: {}, 类型: {}, 模型: {}, 队列位置: {}", 
                task.getId(), sessionId, taskType, task.getImageModel(), globalPosition);

            return task;

        } catch (Exception e) {
            log.error("提交图片任务失败 - 会话: {}, 类型: {}", sessionId, taskType, e);
            throw new RuntimeException("提交图片任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建请求参数JSON
     */
    private String buildRequestJson(VolcengineImageRequest request) throws Exception {
        String imageModel = getImageModel(request);
        
        if ("DOUBAO".equalsIgnoreCase(imageModel)) {
            // 豆包模型请求
            DoubaoImageGenerationRequest doubaoRequest = new DoubaoImageGenerationRequest();
            doubaoRequest.setModel("doubao-seedream-3-0-t2i-250415");
            doubaoRequest.setPrompt(request.getPrompt());
            doubaoRequest.setSeed(request.getSeed());
            doubaoRequest.setGuidanceScale(Double.valueOf(request.getScale()));
            doubaoRequest.setSize(request.getWidth() + "x" + request.getHeight());
            doubaoRequest.setCnPrompt(request.getPrompt());
            
            return objectMapper.writeValueAsString(doubaoRequest);
            
        } else {
            // FLUX模型请求
            // 这里可以根据需要构建FLUX请求参数
            return objectMapper.writeValueAsString(request);
        }
    }

    /**
     * 获取图片模型类型
     */
    private String getImageModel(VolcengineImageRequest request) {
        // 如果有参考图片，使用FLUX，否则使用DOUBAO
        boolean hasImageUrls = request.getImageUrls() != null && !request.getImageUrls().isEmpty();
        return hasImageUrls ? "FLUX" : "DOUBAO";
    }

    /**
     * 获取会话内的下一个位置
     */
    private int getNextSessionPosition(String sessionId) {
        // 查询该会话的最大位置
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getSessionId, sessionId)
                   .orderByDesc(AiImageTaskQueuePo::getSessionQueuePosition)
                   .last("LIMIT 1");
        
        AiImageTaskQueuePo lastTask = taskQueueMapper.selectOne(queryWrapper);
        
        if (lastTask == null) {
            return 1;
        }
        
        return lastTask.getSessionQueuePosition() + 1;
    }

    /**
     * 批量提交任务
     * 
     * @param sessionId 会话ID
     * @param requests 请求列表
     * @param taskType 任务类型
     * @return 提交的任务数量
     */
    @Transactional
    public int submitBatchImageTasks(String sessionId, java.util.List<VolcengineImageRequest> requests, String taskType) {
        int successCount = 0;
        
        for (VolcengineImageRequest request : requests) {
            try {
                submitImageTask(sessionId, request, taskType);
                successCount++;
            } catch (Exception e) {
                log.error("批量提交任务失败，跳过该任务: {}", request, e);
            }
        }
        
        log.info("批量提交任务完成 - 会话: {}, 总数: {}, 成功: {}", 
            sessionId, requests.size(), successCount);
        
        return successCount;
    }

    /**
     * 获取任务统计信息
     */
    public TaskStatistics getTaskStatistics() {
        LambdaQueryWrapper<AiImageTaskQueuePo> pendingQuery = new LambdaQueryWrapper<>();
        pendingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue());
        long pendingCount = taskQueueMapper.selectCount(pendingQuery);

        LambdaQueryWrapper<AiImageTaskQueuePo> processingQuery = new LambdaQueryWrapper<>();
        processingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue());
        long processingCount = taskQueueMapper.selectCount(processingQuery);

        LambdaQueryWrapper<AiImageTaskQueuePo> completedQuery = new LambdaQueryWrapper<>();
        completedQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.COMPLETED.getValue());
        long completedCount = taskQueueMapper.selectCount(completedQuery);

        LambdaQueryWrapper<AiImageTaskQueuePo> failedQuery = new LambdaQueryWrapper<>();
        failedQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.FAILED.getValue());
        long failedCount = taskQueueMapper.selectCount(failedQuery);

        return new TaskStatistics(pendingCount, processingCount, completedCount, failedCount);
    }

    /**
     * 任务统计信息
     */
    public static class TaskStatistics {
        private final long pendingCount;
        private final long processingCount;
        private final long completedCount;
        private final long failedCount;

        public TaskStatistics(long pendingCount, long processingCount, long completedCount, long failedCount) {
            this.pendingCount = pendingCount;
            this.processingCount = processingCount;
            this.completedCount = completedCount;
            this.failedCount = failedCount;
        }

        public long getPendingCount() { return pendingCount; }
        public long getProcessingCount() { return processingCount; }
        public long getCompletedCount() { return completedCount; }
        public long getFailedCount() { return failedCount; }
        public long getTotalCount() { return pendingCount + processingCount + completedCount + failedCount; }

        @Override
        public String toString() {
            return String.format("TaskStatistics{pending=%d, processing=%d, completed=%d, failed=%d, total=%d}", 
                pendingCount, processingCount, completedCount, failedCount, getTotalCount());
        }
    }
}
