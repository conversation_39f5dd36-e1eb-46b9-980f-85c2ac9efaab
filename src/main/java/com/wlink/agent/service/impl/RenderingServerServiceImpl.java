//package com.wlink.agent.service.impl;
//
//import com.alibaba.cola.exception.BizException;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//
//import com.wlink.agent.dao.mapper.*;
//import com.wlink.agent.dao.po.AgentLiveRoomInfoPo;
//import com.wlink.agent.dao.po.AgentRenderingServerChannelPo;
//import com.wlink.agent.dao.po.AgentRenderingServerDispatchPo;
//import com.wlink.agent.dao.po.AgentRenderingServerPo;
//import com.wlink.agent.enums.LiveStatusEnum;
//import com.wlink.agent.model.dto.SimpleUserInfo;
//import com.wlink.agent.netty.DistributedRoomManager;
//import com.wlink.agent.service.RenderingServerService;
//import com.wlink.agent.utils.UserContext;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.logging.log4j.util.Strings;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.Date;
//import java.util.Objects;
//import java.util.UUID;
//import java.util.concurrent.ExecutorService;
//
//@Slf4j
//@Service
//public class RenderingServerServiceImpl implements RenderingServerService {
//
//    private static final String STATUS_ONLINE = "ONLINE";
//    private static final String STATUS_OFFLINE = "OFFLINE";
//    private static final String STATUS_IDLE = "IDLE";
//    private static final String STATUS_BUSY = "BUSY";
//    private static final String STATUS_DETAIL = "CONFIG";
//    private static final int SERVER_CATEGORY_PUBLIC = 1;
//    private static final int SERVER_CATEGORY_PRIVATE = 2;
//
//    @Resource
//    private AgentRenderingServerMapper serverMapper;
//
//    @Resource
//    private AgentRenderingServerChannelMapper channelMapper;
//
//    @Resource
//    private AgentRenderingServerDispatchMapper dispatchMapper;
//
//    @Resource
//    private AgentRenderingServerWhitelistMapper whitelistMapper;
//    @Autowired
//    private AgentLiveRoomInfoMapper agentLiveRoomInfoMapper;
//    @Resource
//    private ExecutorService executorService;
//    @Autowired
//    private AgentRenderingServerDispatchMapper agentRenderingServerDispatchMapper;
//    @Resource
//    DistributedRoomManager distributedRoomManager;
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Integer registerServer(String serverIp, String serverName, String serverInnerIp, String channelName, String username, String deviceName) {
//        // 参数校验
//        if (StringUtils.isAnyBlank(serverIp, serverName, serverInnerIp, channelName)) {
//            throw new BizException(400, "服务器注册参数不能为空");
//        }
//        Integer serverCategory = SERVER_CATEGORY_PUBLIC;
//        if (StringUtils.isNotBlank(username)) {
//            serverCategory = SERVER_CATEGORY_PRIVATE;
//        }
//
//        // 检查是否已经存在该服务器
//        LambdaQueryWrapper<AgentRenderingServerPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerPo.class)
//                .eq(AgentRenderingServerPo::getServerName, serverName)
//                .eq(AgentRenderingServerPo::getUserName, username)
//                .last("limit 1");
//        AgentRenderingServerPo existingServer = serverMapper.selectOne(wrapper);
//        Integer serverId;
//        if (existingServer == null) {
//            // 首次连接，插入新记录
//            AgentRenderingServerPo newServer = new AgentRenderingServerPo();
//            newServer.setServerIp(serverIp);
//            newServer.setServerName(serverName);
//            newServer.setServerInnerIp(serverInnerIp);
//            newServer.setCurrentChannelName(channelName);
//            newServer.setOnlineStatus(STATUS_ONLINE);
//            newServer.setRunningStatus(STATUS_IDLE);
//            newServer.setServerCategory(serverCategory);
//            newServer.setUserName(username);
//            newServer.setDeviceName(deviceName);
//            serverMapper.insert(newServer);
//
//            AgentRenderingServerChannelPo channel = new AgentRenderingServerChannelPo();
//            channel.setServerId(newServer.getId());
//            channel.setChannelName(channelName);
//            channelMapper.insert(channel);
//            serverId = newServer.getId();
//            log.info("注册新服务器成功: {}", newServer);
//        } else {
//            // 更新现有服务器信息
//            LambdaQueryWrapper<AgentRenderingServerChannelPo> channelWrapper = Wrappers.lambdaQuery(AgentRenderingServerChannelPo.class)
//                    .eq(AgentRenderingServerChannelPo::getServerId, existingServer.getId());
//            channelMapper.delete(channelWrapper);
//
//            existingServer.setOnlineStatus(STATUS_ONLINE);
//            existingServer.setRunningStatus(STATUS_IDLE);
//            existingServer.setCurrentChannelName(channelName);
//            existingServer.setUpdateTime(new Date());
//            existingServer.setUserName(username);
//            existingServer.setDeviceName(deviceName);
//            serverMapper.updateById(existingServer);
//
//            AgentRenderingServerChannelPo channel = new AgentRenderingServerChannelPo();
//            channel.setServerId(existingServer.getId());
//            channel.setChannelName(channelName);
//            channelMapper.insert(channel);
//            serverId = existingServer.getId();
//            log.info("更新服务器信息成功: {}", existingServer);
//        }
//        return serverId;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void releaseServer(String channelName, String userName) {
//        if (StringUtils.isBlank(channelName)) {
//            throw new BizException(400, "channelName不能为空");
//        }
//
//        LambdaQueryWrapper<AgentRenderingServerPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerPo.class)
//                .eq(AgentRenderingServerPo::getCurrentChannelName, channelName)
//                .eq(AgentRenderingServerPo::getUserName, userName)
//                .last("limit 1");
//        AgentRenderingServerPo server = serverMapper.selectOne(wrapper);
//
//        if (server != null) {
//            server.setOnlineStatus(STATUS_OFFLINE);
//            server.setUpdateTime(new Date());
//            serverMapper.updateById(server);
//
//            LambdaQueryWrapper<AgentRenderingServerChannelPo> channelWrapper = Wrappers.lambdaQuery(AgentRenderingServerChannelPo.class)
//                    .eq(AgentRenderingServerChannelPo::getServerId, server.getId());
//            channelMapper.delete(channelWrapper);
//            log.info("释放服务器成功: {}", server);
//        } else {
//            log.warn("未找到channelName为{}的服务器", channelName);
//        }
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateServerStatus(String channelName, String runningStatus, String detailStatus) {
//        if (StringUtils.isAnyBlank(channelName, runningStatus)) {
//            throw new BizException(400, "更新状态参数不能为空");
//        }
//
//        LambdaQueryWrapper<AgentRenderingServerPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerPo.class)
//                .eq(AgentRenderingServerPo::getCurrentChannelName, channelName);
//        AgentRenderingServerPo server = serverMapper.selectOne(wrapper);
//
//        if (server == null) {
//            log.warn("未找到channelName为{}的服务器", channelName);
//            return;
//        }
//
//        if (!Objects.equals(STATUS_DETAIL, detailStatus) && !Objects.equals(server.getRunningStatus(), runningStatus) && server.getRunningStatus().equals(STATUS_BUSY)){
//            executorService.execute(() -> updateLiveRoomStatus(server.getId()));
//        }
//        server.setOnlineStatus(STATUS_ONLINE);
//        server.setRunningStatus(runningStatus);
//        server.setUpdateTime(new Date());
//        serverMapper.updateById(server);
//        log.info("更新服务器状态成功: {}", server);
//    }
//
//
//
//    private void updateLiveRoomStatus(Integer id){
//        log.info("机器释放，检查直播间状态 serverId:{}", id);
//        AgentRenderingServerDispatchPo agentRenderingServerDispatchPo = agentRenderingServerDispatchMapper.selectOne(new LambdaQueryWrapper<AgentRenderingServerDispatchPo>()
//                .eq(AgentRenderingServerDispatchPo::getServerId, id)
//                .eq(AgentRenderingServerDispatchPo::getStatus, 1)
//                .orderByDesc(AgentRenderingServerDispatchPo::getCreateTime)
//                .last("LIMIT 1"));
//        if (agentRenderingServerDispatchPo != null) {
//            AgentLiveRoomInfoPo agentLiveRoomInfoPo = agentLiveRoomInfoMapper.selectOne(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                    .eq(AgentLiveRoomInfoPo::getRoomId, agentRenderingServerDispatchPo.getRoomId()));
//            if (agentLiveRoomInfoPo != null && !Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), 0)) {
//                log.info("机器释放，检查直播间状态 roomId:{},liveStatus:{}", agentRenderingServerDispatchPo.getRoomId(), agentLiveRoomInfoPo.getLiveStatus());
//                agentLiveRoomInfoPo.setLiveStatus(0);
//                agentLiveRoomInfoPo.setUpdateTime(new Date());
//                agentLiveRoomInfoMapper.updateById(agentLiveRoomInfoPo);
//
//                distributedRoomManager.closeRoom(agentLiveRoomInfoPo.getRoomId());
//            }
//        }
//    }
//
//    @Override
//    public void updateDispatchStatus(int dispatchStatus, String sessionId) {
//        if (StringUtils.isBlank(sessionId)) {
//            throw new BizException(400, "sessionId不能为空");
//        }
//
//        LambdaQueryWrapper<AgentRenderingServerDispatchPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerDispatchPo.class)
//                .eq(AgentRenderingServerDispatchPo::getSessionId, sessionId);
//        AgentRenderingServerDispatchPo dispatch = new AgentRenderingServerDispatchPo();
//        dispatch.setStatus(dispatchStatus);
//        dispatchMapper.update(dispatch, wrapper);
//        log.info("更新调度状态成功: sessionId={}, status={}", sessionId, dispatchStatus);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public String dispatch(String roomId, Integer serverId, String token) {
//        if (StringUtils.isEmpty(token) || Objects.isNull(roomId)) {
//            throw new BizException(400, "调度参数不能为空");
//        }
//        String sessionId = UUID.randomUUID().toString().replaceAll("-","");
//        SimpleUserInfo simpleUserInfo = UserContext.getUser();
//        if (simpleUserInfo == null) {
//            throw new BizException(401, "用户未登录");
//        }
//
//        if (UserTypeEnum.MAIN.getCode() == simpleUserInfo.getUserType()) {
//            simpleUserInfo.setParentUsername(simpleUserInfo.getUsername());
//        }
//
//        // 查询所有在线且空闲的服务器
//        LambdaQueryWrapper<AgentRenderingServerPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerPo.class)
//                .eq(AgentRenderingServerPo::getOnlineStatus, STATUS_ONLINE)
//                .eq(AgentRenderingServerPo::getRunningStatus, STATUS_IDLE)
//                .eq(AgentRenderingServerPo::getId, serverId)
//                .last("limit 1");
//
//        AgentRenderingServerPo availableServer = serverMapper.selectOne(wrapper);
//
//        if (availableServer == null) {
//            AgentRenderingServerDispatchPo dispatch = new AgentRenderingServerDispatchPo();
//            dispatch.setSessionId(sessionId);
//            dispatch.setUserName(simpleUserInfo.getUsername());
//            dispatch.setParam(String.format("roomId=%s&token=%s", roomId, token));
//            dispatch.setStatus(0);
//            dispatch.setMsg("调度失败：无可用服务器");
//            dispatchMapper.insert(dispatch);
//            return Strings.EMPTY;
//        }
//
//        // 更新服务器状态为忙碌
//        availableServer.setRunningStatus(STATUS_BUSY);
//        availableServer.setUpdateTime(new Date());
//        serverMapper.updateById(availableServer);
//
//
//        agentLiveRoomInfoMapper.update(new AgentLiveRoomInfoPo(),new LambdaUpdateWrapper<AgentLiveRoomInfoPo>()
//                .eq(AgentLiveRoomInfoPo::getRoomId,roomId)
//                .set(AgentLiveRoomInfoPo::getLiveStatus, LiveStatusEnum.STARTING.getCode())
//                .set(AgentLiveRoomInfoPo::getUpdateTime,new Date())
//                .set(AgentLiveRoomInfoPo::getStartTime,new Date()));
//
//
//        AgentRenderingServerDispatchPo dispatch = new AgentRenderingServerDispatchPo();
//        dispatch.setSessionId(sessionId);
//        dispatch.setUserName(simpleUserInfo.getUsername());
//        dispatch.setServerId(availableServer.getId());
//        dispatch.setParam(String.format("roomId=%s&token=%s", roomId, token));
//        dispatch.setMsg("调度成功");
//        dispatch.setRoomId(roomId);
//        dispatchMapper.insert(dispatch);
//
//        log.info("调度成功: sessionId={}, serverId={}", sessionId, availableServer.getId());
//        return sessionId;
//    }
//
//    @Override
//    public String findChannelNameBySesisonId(String sessionId) {
//        if (StringUtils.isBlank(sessionId)) {
//            return "";
//        }
//
//        LambdaQueryWrapper<AgentRenderingServerDispatchPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerDispatchPo.class)
//                .eq(AgentRenderingServerDispatchPo::getSessionId, sessionId);
//        AgentRenderingServerDispatchPo dispatch = dispatchMapper.selectOne(wrapper);
//
//        if (dispatch != null) {
//            AgentRenderingServerPo server = serverMapper.selectById(dispatch.getServerId());
//            if (server != null) {
//                return server.getCurrentChannelName();
//            }
//        }
//        return "";
//    }
//
//    @Override
//    public AgentRenderingServerDispatchPo findBySessionId(String sessionId) {
//        if (StringUtils.isBlank(sessionId)) {
//            return null;
//        }
//
//        LambdaQueryWrapper<AgentRenderingServerDispatchPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerDispatchPo.class)
//                .eq(AgentRenderingServerDispatchPo::getSessionId, sessionId);
//        return dispatchMapper.selectOne(wrapper);
//    }
//
//    @Override
//    public String findSessionIdByLiveId(String liveId) {
//        if (StringUtils.isBlank(liveId)) {
//            return "";
//        }
//        LambdaQueryWrapper<AgentRenderingServerDispatchPo> wrapper = Wrappers.lambdaQuery(AgentRenderingServerDispatchPo.class)
//                .eq(AgentRenderingServerDispatchPo::getRoomId, liveId)
//                .orderByDesc(AgentRenderingServerDispatchPo::getCreateTime)
//                .last(" limit 1")
//                ;
//        AgentRenderingServerDispatchPo agentRenderingServerDispatchPo = dispatchMapper.selectOne(wrapper);
//        if(Objects.nonNull(agentRenderingServerDispatchPo)){
//            AgentRenderingServerDispatchPo agentRenderingServerDispatchPo1 = dispatchMapper.selectOne(new LambdaQueryWrapper<AgentRenderingServerDispatchPo>()
//                    .eq(AgentRenderingServerDispatchPo::getServerId, agentRenderingServerDispatchPo.getServerId())
//                    .orderByDesc(AgentRenderingServerDispatchPo::getCreateTime)
//                    .last("limit 1"));
//            if(Objects.nonNull(agentRenderingServerDispatchPo1) && Objects.equals(agentRenderingServerDispatchPo1.getRoomId(), liveId)){
//               return agentRenderingServerDispatchPo1.getSessionId();
//            }
//        }
//        return "";
//    }
//}
