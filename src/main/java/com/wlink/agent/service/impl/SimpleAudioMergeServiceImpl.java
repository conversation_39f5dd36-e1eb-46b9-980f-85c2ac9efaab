package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.model.dto.AudioMergeRequest;
import com.wlink.agent.model.dto.AudioMergeResult;
import com.wlink.agent.service.AudioMergeService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 简单的音频合成服务实现
 * 使用内存信号量控制并发，避免数据库操作
 */
@Slf4j
@Service("simpleAudioMergeService")
@Primary
@ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "simple", matchIfMissing = true)
@RequiredArgsConstructor
public class SimpleAudioMergeServiceImpl implements AudioMergeService {

    private final OssUtils ossUtils;

    @Value("${audio.merge.max-concurrent-tasks:2}")
    private int maxConcurrentTasks;

    @Value("${audio.merge.temp-dir:/tmp/audio-merge}")
    private String tempDir;

    @Value("${audio.merge.ffmpeg-path:ffmpeg}")
    private String ffmpegPath;

    @Value("${spring.profiles.active}")
    private String env;

    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    // 使用信号量控制并发
    private final Semaphore semaphore = new Semaphore(2); // 默认2个并发

    // 任务结果缓存
    private final ConcurrentHashMap<String, AudioMergeResult> taskCache = new ConcurrentHashMap<>();

    @Override
    public AudioMergeResult mergeAudios(List<String> audioUrls, String outputFileName) {
        if (audioUrls == null || audioUrls.isEmpty()) {
            throw new BizException("音频URL列表不能为空");
        }

        if (audioUrls.size() == 1) {
            // 只有一个音频，直接返回
            AudioMergeResult result = new AudioMergeResult();
            result.setTaskId(IdUtil.fastSimpleUUID());
            result.setSuccess(true);
            result.setMergedAudioUrl(audioUrls.get(0));
            return result;
        }

        // 尝试获取信号量
        try {
            if (!semaphore.tryAcquire(5, TimeUnit.SECONDS)) {
                throw new BizException("音频合成服务繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("音频合成服务被中断");
        }

        try {
            return processAudioMerge(audioUrls, outputFileName);
        } finally {
            semaphore.release();
        }
    }

    @Override
    public CompletableFuture<AudioMergeResult> mergeAudiosAsync(AudioMergeRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            return mergeAudios(request.getAudioUrls(), request.getOutputFileName());
        });
    }

    @Override
    public AudioMergeResult getTaskResult(String taskId) {
        return taskCache.get(taskId);
    }

    @Override
    public boolean cancelTask(String taskId) {
        AudioMergeResult result = taskCache.get(taskId);
        if (result != null && result.getSuccess() == null) {
            result.setSuccess(false);
            result.setErrorMessage("任务已取消");
            return true;
        }
        return false;
    }

    /**
     * 处理音频合成
     */
    private AudioMergeResult processAudioMerge(List<String> audioUrls, String outputFileName) {
        String taskId = IdUtil.fastSimpleUUID();
        long startTime = System.currentTimeMillis();
        
        log.info("开始音频合成: taskId={}, audioCount={}", taskId, audioUrls.size());

        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setStartTime(startTime);
        
        // 缓存任务状态
        taskCache.put(taskId, result);

        String tempWorkDir = null;
        try {
            // 创建临时工作目录
            tempWorkDir = createTempWorkDir(taskId);
            
            // 下载音频文件
            List<String> localAudioFiles = downloadAudioFiles(audioUrls, tempWorkDir);
            
            // 使用FFmpeg合成音频
            String mergedFilePath = mergeAudioWithFFmpeg(localAudioFiles, tempWorkDir, outputFileName);
            
            // 上传到OSS
            String ossUrl = uploadToOss(mergedFilePath, outputFileName);
            
            // 获取音频时长
            Long duration = getAudioDuration(mergedFilePath);
            
            result.setSuccess(true);
            result.setMergedAudioUrl(ossUrl);
            result.setTotalDurationMs(duration);
            
            log.info("音频合成完成: taskId={}, duration={}ms, outputUrl={}", 
                    taskId, duration, ossUrl);

        } catch (Exception e) {
            log.error("音频合成失败: taskId={}", taskId, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (tempWorkDir != null) {
                cleanupTempFiles(tempWorkDir);
            }
            
            long endTime = System.currentTimeMillis();
            result.setEndTime(endTime);
            result.setProcessingTimeMs(endTime - startTime);
            
            // 更新缓存
            taskCache.put(taskId, result);
            
            // 5分钟后清理缓存
            CompletableFuture.delayedExecutor(5, TimeUnit.MINUTES).execute(() -> {
                taskCache.remove(taskId);
            });
        }

        return result;
    }

    // 以下方法复用原有的实现逻辑...
    
    private String createTempWorkDir(String taskId) throws IOException {
        String workDir = tempDir + File.separator + taskId;

        try {
            Files.createDirectories(Paths.get(workDir));
            log.debug("创建临时工作目录: {}", workDir);

            // 验证目录是否创建成功
            File dir = new File(workDir);
            if (!dir.exists() || !dir.isDirectory()) {
                throw new IOException("临时工作目录创建失败: " + workDir);
            }

            return workDir;
        } catch (IOException e) {
            log.error("创建临时工作目录失败: {}, 错误: {}", workDir, e.getMessage());
            throw new IOException("创建临时工作目录失败: " + workDir, e);
        }
    }

    private List<String> downloadAudioFiles(List<String> audioUrls, String workDir) throws IOException {
        List<String> localFiles = new java.util.ArrayList<>();
        
        for (int i = 0; i < audioUrls.size(); i++) {
            String audioUrl = audioUrls.get(i);
            String fileName = String.format("audio_%03d.wav", i);
            String localPath = workDir + File.separator + fileName;
            
            downloadFile(MediaUrlPrefixUtil.getMediaUrl(audioUrl), localPath);
            localFiles.add(localPath);
        }
        
        return localFiles;
    }

    private void downloadFile(String url, String localPath) throws IOException {
        log.debug("下载音频文件: {} -> {}", url, localPath);

        try (InputStream in = new URL(url).openStream();
             FileOutputStream out = new FileOutputStream(localPath)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytes = 0;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
            }

            log.debug("音频文件下载完成: {} -> {}, 大小: {} bytes", url, localPath, totalBytes);

            // 验证文件是否下载成功
            File file = new File(localPath);
            if (!file.exists() || file.length() == 0) {
                throw new IOException("下载的音频文件为空或不存在: " + localPath);
            }

        } catch (IOException e) {
            log.error("下载音频文件失败: {} -> {}, 错误: {}", url, localPath, e.getMessage());
            throw new IOException("下载音频文件失败: " + url + " -> " + localPath, e);
        }
    }

    private String mergeAudioWithFFmpeg(List<String> audioFiles, String workDir, String outputFileName)
            throws IOException, InterruptedException {

        String outputPath = workDir + File.separator + outputFileName + ".wav";

        try {
            // 首先尝试使用concat demuxer方法（推荐）
            return mergeAudioWithConcatDemuxer(audioFiles, workDir, outputPath);
        } catch (Exception e) {
            log.warn("concat demuxer方法失败，尝试filter_complex方法: {}", e.getMessage());

            try {
                // 如果只有两个文件，使用更简单的方法
                if (audioFiles.size() == 2) {
                    return mergeAudioWithSimpleMethod(audioFiles, outputPath);
                }

                // 多个文件使用filter_complex方法
                return mergeAudioWithFilterComplex(audioFiles, outputPath);
            } catch (Exception e2) {
                log.error("所有音频合成方法都失败了", e2);
                throw e2;
            }
        }
    }

    /**
     * 使用concat demuxer方法合并音频（推荐方法）
     */
    private String mergeAudioWithConcatDemuxer(List<String> audioFiles, String workDir, String outputPath)
            throws IOException, InterruptedException {

        // 创建文件列表
        String listFilePath = workDir + File.separator + "filelist.txt";
        try (FileWriter writer = new FileWriter(listFilePath)) {
            for (String audioFile : audioFiles) {
                // 使用相对路径或绝对路径
                writer.write("file '" + audioFile + "'\n");
            }
        }

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command().add(ffmpegPath);
        processBuilder.command().add("-y");
        processBuilder.command().add("-f");
        processBuilder.command().add("concat");
        processBuilder.command().add("-safe");
        processBuilder.command().add("0");
        processBuilder.command().add("-i");
        processBuilder.command().add(listFilePath);
        processBuilder.command().add("-c");
        processBuilder.command().add("copy");
        processBuilder.command().add(outputPath);

        return executeFFmpegCommand(processBuilder, outputPath);
    }

    /**
     * 使用简单方法合并两个音频文件
     */
    private String mergeAudioWithSimpleMethod(List<String> audioFiles, String outputPath)
            throws IOException, InterruptedException {

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command().add(ffmpegPath);
        processBuilder.command().add("-y");
        processBuilder.command().add("-i");
        processBuilder.command().add(audioFiles.get(0));
        processBuilder.command().add("-i");
        processBuilder.command().add(audioFiles.get(1));
        processBuilder.command().add("-filter_complex");
        processBuilder.command().add("[0:a][1:a]concat=n=2:v=0:a=1[outa]");
        processBuilder.command().add("-map");
        processBuilder.command().add("[outa]");
        processBuilder.command().add(outputPath);

        return executeFFmpegCommand(processBuilder, outputPath);
    }

    /**
     * 使用filter_complex方法合并多个音频文件
     */
    private String mergeAudioWithFilterComplex(List<String> audioFiles, String outputPath)
            throws IOException, InterruptedException {

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command().add(ffmpegPath);
        processBuilder.command().add("-y");

        // 添加输入文件
        for (String audioFile : audioFiles) {
            processBuilder.command().add("-i");
            processBuilder.command().add(audioFile);
        }

        // 音频过滤器：连接所有音频
        // 正确的格式应该是: [0:a][1:a]concat=n=2:v=0:a=1[outa]
        StringBuilder filterComplex = new StringBuilder();

        // 构建输入流标识符，不需要分号分隔
        for (int i = 0; i < audioFiles.size(); i++) {
            filterComplex.append(String.format("[%d:a]", i));
        }

        // 添加concat过滤器
        filterComplex.append("concat=n=").append(audioFiles.size()).append(":v=0:a=1[outa]");

        String filterString = filterComplex.toString();
        log.info("FFmpeg过滤器字符串: {}", filterString);

        processBuilder.command().add("-filter_complex");
        processBuilder.command().add(filterString);
        processBuilder.command().add("-map");
        processBuilder.command().add("[outa]");
        processBuilder.command().add(outputPath);

        return executeFFmpegCommand(processBuilder, outputPath);
    }

    /**
     * 执行FFmpeg命令
     */
    private String executeFFmpegCommand(ProcessBuilder processBuilder, String outputPath)
            throws IOException, InterruptedException {

        log.info("执行FFmpeg命令: {}", String.join(" ", processBuilder.command()));

        Process process = processBuilder.start();

        // 读取错误输出
        StringBuilder errorOutput = new StringBuilder();
        StringBuilder standardOutput = new StringBuilder();

        // 启动线程读取输出流，避免缓冲区满导致进程阻塞
        Thread errorReader = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    errorOutput.append(line).append("\n");
                }
            } catch (IOException e) {
                log.warn("读取FFmpeg错误输出失败", e);
            }
        });

        Thread outputReader = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    standardOutput.append(line).append("\n");
                }
            } catch (IOException e) {
                log.warn("读取FFmpeg标准输出失败", e);
            }
        });

        errorReader.start();
        outputReader.start();

        boolean finished = process.waitFor(300, TimeUnit.SECONDS);

        // 等待输出读取完成
        try {
            errorReader.join(5000);
            outputReader.join(5000);
        } catch (InterruptedException e) {
            log.warn("等待输出读取完成被中断", e);
        }

        if (!finished) {
            process.destroyForcibly();
            log.error("FFmpeg处理超时，命令: {}", String.join(" ", processBuilder.command()));
            throw new RuntimeException("FFmpeg处理超时");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            String errorMsg = String.format("FFmpeg处理失败，退出码: %d\n命令: %s\n错误输出: %s\n标准输出: %s",
                    exitCode,
                    String.join(" ", processBuilder.command()),
                    errorOutput.toString(),
                    standardOutput.toString());
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        log.info("FFmpeg处理成功，输出文件: {}", outputPath);
        return outputPath;
    }

    private Long getAudioDuration(String audioPath) {
        // 简化实现，返回null
        return null;
    }

    private String uploadToOss(String filePath, String fileName) {
        String ossPath = OSS_PATH.replace("{env}", env)
                .replace("{userId}", "system")
                .replace("{type}", "audio");
        
        String finalFileName = fileName + "_" + IdUtil.fastSimpleUUID() + ".wav";
        return ossUtils.uploadFile(filePath, ossPath + finalFileName);
    }

    private void cleanupTempFiles(String workDir) {
        try {
            Path workDirPath = Paths.get(workDir);
            if (Files.exists(workDirPath)) {
                Files.walk(workDirPath)
                        .sorted((a, b) -> b.compareTo(a))
                        .forEach(path -> {
                            try {
                                Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", path, e);
                            }
                        });
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}", workDir, e);
        }
    }
}
