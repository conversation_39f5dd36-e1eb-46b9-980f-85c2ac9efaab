package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.mapper.AgentSoundI18nMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AgentSoundI18nPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.DeleteSoundReq;
import com.wlink.agent.model.req.SessionSoundQueryReq;
import com.wlink.agent.model.req.SoundQueryReq;
import com.wlink.agent.model.req.UpdateSoundNameReq;
import com.wlink.agent.model.res.SoundRes;
import com.wlink.agent.service.AgentSoundService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AgentSoundServiceImpl implements AgentSoundService {

    @Resource
    private AgentSoundMapper agentSoundMapper;
    
    @Resource
    private AgentSoundI18nMapper soundI18nMapper;

    @Resource
    private AiCreationSessionMapper aiCreationSessionMapper;

    @Override
    public List<SoundRes> listSounds(SoundQueryReq req) {
        // 1. 查询主表数据
        List<AgentSoundPo> list = agentSoundMapper.selectList(buildQueryWrapper(req));
        
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        
        // 2. 获取当前语言
        Locale currentLocale = LocaleContextHolder.getLocale();
        String locale = currentLocale.toString();
        
        // 3. 批量查询对应语言的国际化数据
        List<Long> soundIds = list.stream()
                .map(AgentSoundPo::getId)
                .collect(Collectors.toList());
                
        LambdaQueryWrapper<AgentSoundI18nPo> i18nWrapper = new LambdaQueryWrapper<>();
        i18nWrapper.in(AgentSoundI18nPo::getSoundId, soundIds)
                  .eq(AgentSoundI18nPo::getLocale, locale);
        
        List<AgentSoundI18nPo> i18nList = soundI18nMapper.selectList(i18nWrapper);
        
        // 4. 将国际化数据关联到主表数据
        Map<Long, AgentSoundI18nPo> i18nMap = i18nList.stream()
                .collect(Collectors.toMap(AgentSoundI18nPo::getSoundId, v -> v));
                
        // 5. 转换为响应对象
        return list.stream().map(po -> convertToVO(po, i18nMap.get(po.getId())))
                .collect(Collectors.toList());
    }

    private Wrapper<AgentSoundPo> buildQueryWrapper(SoundQueryReq req) {
        log.info("查询声音列表参数:{}", JSON.toJSONString(req));
        String userId;
        SimpleUserInfo simpleUserInfo = UserContext.getUser();
        if (simpleUserInfo != null && StringUtils.isNotBlank(simpleUserInfo.getUserId())) {
            userId = simpleUserInfo.getUserId();
        } else {
            userId = "";
        }
        LambdaQueryWrapper<AgentSoundPo> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        wrapper.and(w -> {
            // 系统声音对所有人可见
            w.eq(AgentSoundPo::getType, 1);
            // 定制声音只对指定用户可见
            w.or(subW -> subW.eq(AgentSoundPo::getType, 2)
                    .eq(AgentSoundPo::getUserName, userId));
        });

        // 添加其他过滤条件
        if (req.getType() != null) {
            wrapper.eq(AgentSoundPo::getType, req.getType());
        }
        if (StringUtils.isNotBlank(req.getLanguage())) {
            wrapper.eq(AgentSoundPo::getLanguage, req.getLanguage());
        }
        if (req.getSex() != null) {
            wrapper.eq(AgentSoundPo::getSex, req.getSex());
        }

        // 按创建时间降序排序
        wrapper.orderByDesc(AgentSoundPo::getCreateTime);
        return wrapper;
    }


    private SoundRes convertToVO(AgentSoundPo po, AgentSoundI18nPo i18n) {
        SoundRes vo = new SoundRes();
        BeanUtils.copyProperties(po, vo);
        
        if (i18n != null) {
            vo.setName(i18n.getName());
            vo.setDepict(i18n.getDepict());
            vo.setLanguageName(i18n.getLanguageName());
            vo.setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(i18n.getAudioUrl()));
        }
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSoundName(UpdateSoundNameReq req) {
        log.info("开始修改定制声音名称: soundId={}, newName={}", req.getSoundId(), req.getName());

        // 1. 查询声音记录
        AgentSoundPo soundPo = agentSoundMapper.selectById(req.getSoundId());
        if (soundPo == null) {
            log.error("声音记录不存在: soundId={}", req.getSoundId());
            throw new BizException("声音记录不存在");
        }

        // 2. 验证权限：只能修改定制声音，且只能修改自己的声音
        if (soundPo.getType() != 2) {
            log.error("只能修改定制声音: soundId={}, type={}", req.getSoundId(), soundPo.getType());
            throw new BizException("只能修改定制声音");
        }

        // 验证用户权限
        SimpleUserInfo userInfo = UserContext.getUser();
        String currentUserId = userInfo != null ? userInfo.getUserId() : "";
        if (!currentUserId.equals(soundPo.getUserName())) {
            log.error("无权限修改此声音: soundId={}, currentUser={}, soundOwner={}",
                    req.getSoundId(), currentUserId, soundPo.getUserName());
            throw new BizException("无权限修改此声音");
        }

        // 3. 查询对应的国际化记录
        LambdaQueryWrapper<AgentSoundI18nPo> i18nWrapper = new LambdaQueryWrapper<>();
        i18nWrapper.eq(AgentSoundI18nPo::getSoundId, req.getSoundId())
                .eq(AgentSoundI18nPo::getLocale, "zh_CN");

        AgentSoundI18nPo i18nPo = soundI18nMapper.selectOne(i18nWrapper);
        if (i18nPo == null) {
            log.error("声音国际化记录不存在: soundId={}, locale={}", req.getSoundId(), "zh_CN");
            throw new BizException("声音国际化记录不存在");
        }

        // 4. 更新名称
        String oldName = i18nPo.getName();
        i18nPo.setName(req.getName());
        i18nPo.setUpdateTime(new Date());

        int updateResult = soundI18nMapper.updateById(i18nPo);
        if (updateResult <= 0) {
            log.error("更新声音名称失败: soundId={}", req.getSoundId());
            throw new BizException("更新声音名称失败");
        }

        log.info("声音名称修改成功: soundId={}, oldName={}, newName={}, locale={}",
                req.getSoundId(), oldName, req.getName(), "zh_CN");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSound(DeleteSoundReq req) {
        log.info("开始删除定制声音: soundId={}", req.getSoundId());

        // 1. 查询声音记录
        AgentSoundPo soundPo = agentSoundMapper.selectById(req.getSoundId());
        if (soundPo == null) {
            log.error("声音记录不存在: soundId={}", req.getSoundId());
            throw new BizException("声音记录不存在");
        }

        // 2. 验证权限：只能删除定制声音，且只能删除自己的声音
        if (soundPo.getType() != 2) {
            log.error("只能删除定制声音: soundId={}, type={}", req.getSoundId(), soundPo.getType());
            throw new BizException("只能删除定制声音");
        }

        // 验证用户权限
        SimpleUserInfo userInfo = UserContext.getUser();
        String currentUserId = userInfo != null ? userInfo.getUserId() : "";
        if (!currentUserId.equals(soundPo.getUserName())) {
            log.error("无权限删除此声音: soundId={}, currentUser={}, soundOwner={}",
                    req.getSoundId(), currentUserId, soundPo.getUserName());
            throw new BizException("无权限删除此声音");
        }

        agentSoundMapper.deleteById(soundPo.getId());

        // 4. 删除对应的国际化记录
        LambdaUpdateWrapper<AgentSoundI18nPo> i18nUpdateWrapper = new LambdaUpdateWrapper<>();
        i18nUpdateWrapper.eq(AgentSoundI18nPo::getSoundId, req.getSoundId());

        int i18nUpdateResult = soundI18nMapper.delete(i18nUpdateWrapper);

        log.info("声音删除成功: soundId={}, sound={}, i18nRecordsUpdated={}",
                req.getSoundId(), soundPo.getSound(), i18nUpdateResult);
    }

    @Override
    public List<SoundRes> listSoundsBySession(SessionSoundQueryReq req) {
        log.info("根据会话ID查询声音列表: sessionId={}", req.getSessionId());

        // 1. 根据会话ID查询ai_creation_session获取userId
        LambdaQueryWrapper<AiCreationSessionPo> sessionWrapper = new LambdaQueryWrapper<>();
        sessionWrapper.eq(AiCreationSessionPo::getSessionId, req.getSessionId())
                .eq(AiCreationSessionPo::getDelFlag, 0);

        AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(sessionWrapper);
        if (sessionPo == null) {
            log.error("会话不存在: sessionId={}", req.getSessionId());
            throw new BizException("会话不存在");
        }

        String userId = sessionPo.getUserId();
        log.debug("查询到会话用户: sessionId={}, userId={}", req.getSessionId(), userId);

        // 2. 根据用户ID查询ai_sound表，获取系统声音和用户定制声音
        LambdaQueryWrapper<AgentSoundPo> soundWrapper = new LambdaQueryWrapper<>();
        soundWrapper.eq(AgentSoundPo::getDelFlag, 0)
                .and(wrapper ->
                        wrapper.eq(AgentSoundPo::getType, 1) // 系统声音
                                .or(subWrapper ->
                                        subWrapper.eq(AgentSoundPo::getType, 2) // 定制声音
                                                .eq(AgentSoundPo::getUserName, userId) // 用户自己的定制声音
                                )
                )
                .orderByAsc(AgentSoundPo::getType) // 系统声音在前
                .orderByDesc(AgentSoundPo::getCreateTime); // 创建时间倒序

        List<AgentSoundPo> soundList = agentSoundMapper.selectList(soundWrapper);

        if (soundList.isEmpty()) {
            log.info("未找到可用的声音: sessionId={}, userId={}", req.getSessionId(), userId);
            return Collections.emptyList();
        }

        log.debug("查询到声音数量: sessionId={}, userId={}, count={}",
                req.getSessionId(), userId, soundList.size());

        // 3. 获取当前语言环境
          String  locale = "zh_CN"; // 默认使用中文


        // 4. 查询所有声音的国际化信息
        List<Long> soundIds = soundList.stream()
                .map(AgentSoundPo::getId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<AgentSoundI18nPo> i18nWrapper = new LambdaQueryWrapper<>();
        i18nWrapper.in(AgentSoundI18nPo::getSoundId, soundIds)
                .eq(AgentSoundI18nPo::getLocale, locale)
                .eq(AgentSoundI18nPo::getDelFlag, 0);

        List<AgentSoundI18nPo> i18nList = soundI18nMapper.selectList(i18nWrapper);

        // 创建声音ID到国际化信息的映射
        Map<Long, AgentSoundI18nPo> i18nMap = i18nList.stream()
                .collect(Collectors.toMap(AgentSoundI18nPo::getSoundId, i18n -> i18n));

        // 5. 构建响应对象
        List<SoundRes> result = new ArrayList<>();
        for (AgentSoundPo soundPo : soundList) {
            SoundRes vo = new SoundRes();
            BeanUtils.copyProperties(soundPo, vo);

            // 设置国际化名称
            AgentSoundI18nPo i18nPo = i18nMap.get(soundPo.getId());
            if (i18nPo != null) {
                vo.setName(i18nPo.getName());
            } else {
                vo.setName(soundPo.getSound()); // 如果没有国际化信息，使用原始声音标识
            }

            result.add(vo);
        }

        log.info("根据会话ID查询声音列表完成: sessionId={}, userId={}, resultCount={}",
                req.getSessionId(), userId, result.size());

        return result;
    }
}