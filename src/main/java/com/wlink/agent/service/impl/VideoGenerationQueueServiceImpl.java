package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.MiniMaxVideoApiClient;
import com.wlink.agent.client.VolcengineVideoApiClient;
import com.wlink.agent.client.model.minimax.MiniMaxVideoGenerationRequest;
import com.wlink.agent.client.model.minimax.MiniMaxVideoGenerationResponse;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiCanvasVideoPo;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import com.wlink.agent.enums.TransactionTypeEnum;
import com.wlink.agent.enums.ResourceStatus;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.enums.VideoGenerationStatus;
import com.wlink.agent.model.req.VideoGenerationReq;
import com.wlink.agent.model.res.VideoGenerationRes;
import com.wlink.agent.service.AiCanvasService;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.service.VideoGenerationQueueService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 视频生成队列服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoGenerationQueueServiceImpl implements VideoGenerationQueueService {

    private final AiVideoGenerationMapper videoGenerationMapper;
    private final AiVideoGenerationRecordMapper videoGenerationRecordMapper;
    private final RedissonClient redissonClient;
    private final VolcengineVideoApiClient volcengineVideoApiClient;
    private final MiniMaxVideoApiClient miniMaxVideoApiClient;
    private final ObjectMapper objectMapper;
    private final AiCanvasShotMapper aiCanvasShotMapper;
    private final AiCanvasVideoMapper aiCanvasVideoMapper;
    private final AiCanvasMaterialMapper aiCanvasMaterialMapper;
    private final UserPointsService userPointsService;
    private final AiCanvasMapper aiCanvasMapper;
    private final OssUtils ossUtils;
    @Value("${spring.profiles.active}")
    String env;
    /**
     * 视频生成积分消耗
     */
    private static final int VIDEO_GENERATION_POINTS_5 = 125;


    /**
     * 视频生成积分消耗
     */
    private static final int VIDEO_GENERATION_POINTS_10 = 250;



    /**
     * 最大并发处理数
     */
    private static final int MAX_CONCURRENT_PROCESSING = 5;

    /**
     * 分布式锁key
     */
    private static final String QUEUE_LOCK_KEY = "video_generation_queue_lock";
    // 重试配置
    private static final int MAX_RETRY_COUNT = 3;        // 最大重试次数（从10改为3）

    // 任务信息键名
    private static final String KEY_VIDEO_TASK_ID = "videoTaskId";  // 火山引擎任务ID
    private static final String KEY_RETRY_COUNT = "retryCount";     // 重试次数
    private static final String KEY_VIDEO_TYPE = "videoType";       // 视频类型

    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    /**
     * 判断是否为MiniMax模型
     *
     * @param model 模型名称
     * @return true表示是MiniMax模型，false表示不是
     */
    private boolean isMiniMaxModel(String model) {
        if (model == null) {
            return false;
        }
        // MiniMax模型名称包含：MiniMax-Hailuo-02, T2V-01-Director, I2V-01-Director, S2V-01, I2V-01, I2V-01-live, T2V-01
        return model.startsWith("MiniMax-");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitVideoGeneration(VideoGenerationReq req) {
        String userId = UserContext.getUser().getUserId();
        log.info("用户 {} 提交视频生成任务: prompt={}, resolution={}, duration={}s",
                userId, req.getPrompt(), req.getResolution(), req.getDuration());

        AiCanvasShotPo aiCanvasShotPo = aiCanvasShotMapper.selectById(req.getShotId());
        if (aiCanvasShotPo == null) {
            log.warn("分镜不存在: shotId={}", req.getShotId());
            throw new BizException("分镜不存在");
        }


        AiCanvasPo aiCanvasPo = aiCanvasMapper.selectById(aiCanvasShotPo.getCanvasId());
        if (aiCanvasPo == null) {
            log.warn("画布不存在: canvasId={}", aiCanvasShotPo.getCanvasId());
            throw new BizException("画布不存在");
        }
        int videoGenerationPoints = 0;
        //视频消耗积分
        if (Objects.equals(req.getModel(), "doubao-seedance-1-0-lite-i2v-250428")){
            videoGenerationPoints = req.getDuration() == 5000 ? VIDEO_GENERATION_POINTS_5 : VIDEO_GENERATION_POINTS_10;
        }else  if(Objects.equals(req.getModel(), "MiniMax-Hailuo-02")){
            videoGenerationPoints = req.getDuration() == 5000 ? VIDEO_GENERATION_POINTS_5 : VIDEO_GENERATION_POINTS_10;
            req.setResolution("768P");
            int i = req.getDuration() == 5000 ? 6000 : 10000;
            req.setDuration(i);
        } else{
            videoGenerationPoints = req.getDuration() == 5000 ? 400 : 800;
             req.setResolution("1080p");
        }
        // 1. 检查用户积分是否足够
        if (!userPointsService.hasEnoughPoints(userId, videoGenerationPoints)) {
            log.warn("用户积分不足: userId={}, requiredPoints={}", userId, videoGenerationPoints);
            throw new BizException("积分不足，视频生成需要" + videoGenerationPoints + "积分");
        }

        // 获取分布式锁
        RLock lock = redissonClient.getLock(QUEUE_LOCK_KEY);
        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    // 2. 扣除用户积分
                    userPointsService.deductUserPoints(
                            userId,
                            videoGenerationPoints,
                            aiCanvasPo.getCode(), // 使用shotId作为关联ID
                            "视频生成消耗" + videoGenerationPoints + "积分",
                            TransactionTypeEnum.VIDEO_GENERATION
                    );
                    log.info("用户积分扣除成功: userId={}, points={}, shotId={}",
                            userId, videoGenerationPoints, req.getShotId());

                    // 获取当前队列位置
                    int queuedCount = videoGenerationMapper.getQueuedCount();
                    // 创建视频生成记录
                    AiVideoGenerationPo videoPo = AiVideoGenerationPo.builder()
                            .shotId(req.getShotId())
                            .model(req.getModel())
                            .userId(userId)
                            .prompt(req.getPrompt())
                            .firstFrameImage(StringUtils.hasText(req.getFirstFrameImage()) ? req.getFirstFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : null)
                            .lastFrameImage(StringUtils.hasText(req.getLastFrameImage()) ? req.getLastFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : null)
                            .resolution(req.getResolution())
                            .ratio(req.getRatio())
                            .duration(req.getDuration()/1000)
                            .consumePoints(videoGenerationPoints)
                            .fps(req.getFps())
                            .status(VideoGenerationStatus.QUEUED.getValue())
                            .queuePosition(queuedCount + 1)
                            .createTime(new Date())
                            .updateTime(new Date())
                            .delFlag(0)
                            .build();
                    videoGenerationMapper.insert(videoPo);
                    log.info("视频生成任务已加入数据库: taskId={}, queuePosition={}",
                            videoPo.getId(), videoPo.getQueuePosition());

                    // 使用分镜编码查询和更新AiCanvasVideoPo表
                    updateCanvasVideoRecord(req);

                    processVideoGenerationTask(videoPo.getId());
                    return videoPo.getId();
                } finally {
                    lock.unlock();
                }
            } else {
                throw new BizException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("提交任务被中断");
        }
    }

    @Override
    public AiVideoGenerationPo getNextTask() {
        // 检查是否可以开始新的处理
        QueueStatus queueStatus = getQueueStatus();
        if (!queueStatus.canStartNewProcessing()) {
            log.debug("当前处理任务已达上限: {}", queueStatus.getStatusDescription());
            return null;
        }

        // 获取下一个待处理的任务
        return videoGenerationMapper.getNextQueuedTask();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startProcessing(Long taskId,String generateTaskId) {
        // 更新任务状态为处理中
        AiVideoGenerationPo videoPo = videoGenerationMapper.selectById(taskId);
        if (videoPo == null || !VideoGenerationStatus.QUEUED.getValue().equals(videoPo.getStatus())) {
            log.warn("任务 {} 不存在或状态不正确", taskId);
            return;
        }
        videoPo.setStatus(VideoGenerationStatus.PROCESSING.getValue());
        videoPo.setGenerateTaskId(generateTaskId);
        videoPo.setStartTime(new Date());
        videoPo.setUpdateTime(new Date());
        videoGenerationMapper.updateById(videoPo);
        // 更新队列位置
        if (videoPo.getQueuePosition() != null) {
            videoGenerationMapper.updateQueuePositions(videoPo.getQueuePosition());
        }
        AiCanvasShotPo aiCanvasShotPo = aiCanvasShotMapper.selectById(videoPo.getId());
        if (aiCanvasShotPo != null) {
            aiCanvasShotPo.setShotStatus(ShotStatus.PROCESSING.getValue());
            aiCanvasShotPo.setUpdateTime(new Date());
            aiCanvasShotMapper.updateById(aiCanvasShotPo);
        }
        log.info("开始处理视频生成任务: taskId={}", taskId);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeTask(Long taskId, String videoUrl) {



        AiVideoGenerationPo videoPo = videoGenerationMapper.selectById(taskId);
        if (videoPo == null) {
            log.error("完成任务时未找到记录: taskId={}", taskId);
            return;
        }
        String video = ossUtils.uploadFile(videoUrl, OSS_PATH.replace("{env}", env)
                .replace("{userId}", videoPo.getUserId())
                .replace("{type}", "video") + IdUtil.fastSimpleUUID() + ".mp4");


        videoPo.setStatus(VideoGenerationStatus.COMPLETED.getValue());
        videoPo.setVideoUrl(video);
        videoPo.setCompleteTime(new Date());
        videoPo.setUpdateTime(new Date());
        videoGenerationMapper.updateById(videoPo);

        AiCanvasShotPo aiCanvasShotPo = aiCanvasShotMapper.selectById(videoPo.getShotId());
        if (aiCanvasShotPo != null) {
            aiCanvasShotPo.setShotStatus(ShotStatus.COMPLETED.getValue());
            aiCanvasShotPo.setType("video");
            aiCanvasShotPo.setUpdateTime(new Date());
            aiCanvasShotMapper.updateById(aiCanvasShotPo);

            aiCanvasVideoMapper.update(new AiCanvasVideoPo(),new LambdaUpdateWrapper<AiCanvasVideoPo>()
                    .eq(AiCanvasVideoPo::getShotCode, aiCanvasShotPo.getCode())
                    .eq(AiCanvasVideoPo::getCanvasId, aiCanvasShotPo.getCanvasId())
                    .set(AiCanvasVideoPo::getVideoStatus, ResourceStatus.SUCCESS.getValue())
                    .set(AiCanvasVideoPo::getUpdateTime, new Date())
                    .set(AiCanvasVideoPo::getVideoUrl,video));

            AiCanvasMaterialPo materialPo = new AiCanvasMaterialPo();
            materialPo.setCanvasId(aiCanvasShotPo.getCanvasId());
            materialPo.setMaterialType(2); // 1-图片
            materialPo.setMaterialSource(1); // 1-生成
            materialPo.setMaterialUrl(video);
            materialPo.setGenerationRecordId(taskId); // 使用taskId作为生成记录ID

            materialPo.setCreateTime(new Date());
            materialPo.setUpdateTime(new Date());
            materialPo.setDelFlag(0);
            aiCanvasMaterialMapper.insert(materialPo);
            log.info("插入画布素材记录成功: materialId={}, generationRecordId={}, materialUrl={}",
                    materialPo.getId(), taskId, videoUrl);
        }
        log.info("视频生成任务完成: taskId={}, videoUrl={}", taskId, videoUrl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void failTask(Long taskId, String errorMessage) {
        AiVideoGenerationPo videoPo = videoGenerationMapper.selectById(taskId);
        if (videoPo == null) {
            log.error("失败任务时未找到记录: taskId={}", taskId);
            return;
        }
        videoPo.setStatus(VideoGenerationStatus.FAILED.getValue());
        videoPo.setErrorMessage(errorMessage);
        videoPo.setCompleteTime(new Date());
        videoPo.setUpdateTime(new Date());
        videoGenerationMapper.updateById(videoPo);

        AiCanvasShotPo aiCanvasShotPo = aiCanvasShotMapper.selectById(videoPo.getShotId());
        if (aiCanvasShotPo != null) {
            aiCanvasShotPo.setShotStatus(ShotStatus.FAILED.getValue());
            aiCanvasShotPo.setUpdateTime(new Date());
            aiCanvasShotMapper.updateById(aiCanvasShotPo);


            aiCanvasVideoMapper.update(new AiCanvasVideoPo(),new LambdaUpdateWrapper<AiCanvasVideoPo>()
                    .eq(AiCanvasVideoPo::getShotCode, aiCanvasShotPo.getCode())
                    .eq(AiCanvasVideoPo::getCanvasId, aiCanvasShotPo.getCanvasId())
                    .set(AiCanvasVideoPo::getVideoStatus, ResourceStatus.FAILED.getValue())
                    .set(AiCanvasVideoPo::getUpdateTime, new Date()));
        }

        AiCanvasPo aiCanvasPo = aiCanvasMapper.selectById(aiCanvasShotPo.getCanvasId());

        // 返还用户积分
        try {
            userPointsService.refundUserPoints(
                    videoPo.getUserId(),
                    videoPo.getConsumePoints(),
                    aiCanvasPo.getCode(), // 使用shotId作为关联ID
                    "视频生成失败返还" + videoPo.getConsumePoints() + "积分",
                    TransactionTypeEnum.VIDEO_REFUND
            );
            log.info("视频生成失败，积分返还成功: userId={}, points={}, taskId={}",
                    videoPo.getUserId(), videoPo.getConsumePoints(), taskId);
        } catch (Exception e) {
            log.error("视频生成失败，积分返还异常: userId={}, points={}, taskId={}, error={}",
                    videoPo.getUserId(), videoPo.getConsumePoints(), taskId, e.getMessage(), e);
            // 积分返还失败不影响主流程，但需要记录日志便于后续处理
        }

        log.error("视频生成任务失败: taskId={}, error={}", taskId, errorMessage);
    }

    @Override
    public QueueStatus getQueueStatus() {
        int queuedCount = videoGenerationMapper.getQueuedCount();
        int processingCount = videoGenerationMapper.getProcessingCount();
        return new QueueStatus(queuedCount, processingCount, MAX_CONCURRENT_PROCESSING);
    }

    @Override
    public List<VideoGenerationRes> getUserVideoGenerations(String userId) {
        List<AiVideoGenerationPo> videoList = videoGenerationMapper.getUserVideoGenerations(userId);
        return videoList.stream()
                .map(this::convertToVideoGenerationRes)
                .collect(Collectors.toList());
    }

    @Override
    public AiVideoGenerationPo getTaskById(Long taskId) {
        return videoGenerationMapper.selectById(taskId);
    }
    
    @Override
    public AiVideoGenerationPo findTaskByExternalId(String externalTaskId) {
        AiVideoGenerationPo task = videoGenerationMapper.selectOne(new LambdaQueryWrapper<AiVideoGenerationPo>()
                .eq(AiVideoGenerationPo::getGenerateTaskId, externalTaskId)
                .last("limit 1"));
        if (task == null) {
            log.warn("未找到外部任务ID对应的内部任务: {}", externalTaskId);
        }
        return task;
    }


    /**
     * 处理火山引擎视频生成任务
     * 只处理提交任务阶段，不再处理结果查询
     *
     * @param taskId 任务ID
     */
    public void processVideoGenerationTask(Long taskId) {
        // 获取任务详情
        AiVideoGenerationPo task = videoGenerationMapper.selectById(taskId);
        if (task == null) {
            log.warn("未找到视频生成任务: {}", taskId);
            return;
        }
        // 解析任务状态信息
        Map<String, Object> taskInfo = parseTaskInfo(task);
        int retryCount = (int) taskInfo.getOrDefault(KEY_RETRY_COUNT, 0);

        log.info("处理视频生成任务: {}, 重试次数: {}", taskId, retryCount);
        try {
            // 检查处理中的任务数量
            int processingCount = videoGenerationMapper.getProcessingCount();
            if (processingCount < 5) {
                // 处理中任务少于5个，可以提交
                submitVideoTask(task, taskInfo);
            }
        } catch (Exception e) {
            log.error("处理视频生成任务失败: {}", taskId, e);
            // 更新重试次数
            retryCount++;
            taskInfo.put(KEY_RETRY_COUNT, retryCount);
            updateTaskInfo(task.getId(), taskInfo);
            throw new BizException("处理视频生成任务失败");
        }
    }

    /**
     * 解析任务状态信息
     *
     * @param task 任务对象
     * @return 任务信息Map
     */
    private Map<String, Object> parseTaskInfo(AiVideoGenerationPo task) {
        Map<String, Object> taskInfo = new HashMap<>();

        // 从taskInfo字段获取任务信息（如果有）
        if (task.getTaskInfo() != null && task.getTaskInfo().startsWith("{")) {
            try {
                taskInfo = objectMapper.readValue(task.getTaskInfo(), Map.class);
            } catch (Exception e) {
                log.warn("解析任务信息失败: {}", task.getTaskInfo(), e);
            }
        }

        // 设置默认值
        if (!taskInfo.containsKey(KEY_RETRY_COUNT)) {
            taskInfo.put(KEY_RETRY_COUNT, 0);
        }

        return taskInfo;
    }

    /**
     * 更新任务信息
     *
     * @param taskId   任务ID
     * @param taskInfo 任务信息Map
     */
    private void updateTaskInfo(Long taskId, Map<String, Object> taskInfo) {
        try {
            String taskInfoJson = objectMapper.writeValueAsString(taskInfo);

            AiVideoGenerationPo updateTask = new AiVideoGenerationPo();
            updateTask.setId(taskId);
            updateTask.setTaskInfo(taskInfoJson);
            updateTask.setUpdateTime(new Date());

            videoGenerationMapper.updateById(updateTask);
        } catch (Exception e) {
            log.error("更新视频生成任务信息失败: {}", taskId, e);
        }
    }

    /**
     * 提交视频生成任务
     *
     * @param task     任务对象
     * @param taskInfo 任务信息Map
     */
    private void submitVideoTask(AiVideoGenerationPo task, Map<String, Object> taskInfo) {
        log.info("提交视频生成任务: {}, 模型: {}", task.getId(), task.getModel());

        try {
            // 判断使用哪个客户端
            if (isMiniMaxModel(task.getModel())) {
                submitMiniMaxVideoTask(task, taskInfo);
            } else {
                submitVolcengineVideoTask(task, taskInfo);
            }
        } catch (Exception e) {
            log.error("提交视频生成任务失败: {}", task.getId(), e);
            throw new BizException("提交视频生成任务失败: " + e.getMessage());
        }
    }

    /**
     * 提交MiniMax视频生成任务
     *
     * @param task     任务对象
     * @param taskInfo 任务信息Map
     */
    private void submitMiniMaxVideoTask(AiVideoGenerationPo task, Map<String, Object> taskInfo) {
        log.info("提交MiniMax视频生成任务: {}", task.getId());

        try {
            String miniMaxTaskId;
            String videoType;
            String errorMessage = null;

            // 构建MiniMax请求
            MiniMaxVideoGenerationRequest request;

            // 根据是否有首帧和尾帧图片选择不同的API方法
            if (StringUtils.hasText(task.getFirstFrameImage()) && StringUtils.hasText(task.getLastFrameImage())) {
                // MiniMax不支持首尾帧生成，使用首帧图生视频
                log.warn("MiniMax不支持首尾帧生成，将使用首帧图生视频: taskId={}", task.getId());
                request = MiniMaxVideoGenerationRequest.buildImageToVideoRequest(
                        task.getModel(),
                        task.getPrompt(),
                        MediaUrlPrefixUtil.getMediaUrl(task.getFirstFrameImage()),
                        task.getDuration(),
                        task.getResolution()

                );
                videoType = "IMAGE_TO_VIDEO";

            } else if (StringUtils.hasText(task.getFirstFrameImage())) {
                // 图生视频
                request = MiniMaxVideoGenerationRequest.buildImageToVideoRequest(
                        task.getModel(),
                        task.getPrompt(),
                        MediaUrlPrefixUtil.getMediaUrl(task.getFirstFrameImage()),
                        task.getDuration(),
                        task.getResolution()
                );
                videoType = "IMAGE_TO_VIDEO";

            } else {
                // 文生视频
                request = MiniMaxVideoGenerationRequest.buildTextToVideoRequest(
                        task.getModel(),
                        task.getPrompt(),
                        task.getDuration(),
                        task.getResolution()
                );
                videoType = "TEXT_TO_VIDEO";
            }

            // 异步提交MiniMax任务
            try {
                MiniMaxVideoGenerationResponse response = miniMaxVideoApiClient.submitVideoGeneration(request).get();

                if (response.isSuccess()) {
                    miniMaxTaskId = response.getTaskId();
                    log.info("MiniMax视频生成任务提交成功，任务ID: {}, 类型: {}", miniMaxTaskId, videoType);
                } else {
                    errorMessage = response.getErrorMessage();
                    throw new BizException("MiniMax任务提交失败: " + errorMessage);
                }

                // 更新任务信息
                taskInfo.put(KEY_VIDEO_TASK_ID, miniMaxTaskId);
                taskInfo.put(KEY_VIDEO_TYPE, videoType);
                updateTaskInfo(task.getId(), taskInfo);
                // 更新任务状态为处理中
                startProcessing(task.getId(), miniMaxTaskId);

            } catch (Exception e) {
                log.error("MiniMax任务提交异常: taskId={}", task.getId(), e);
                throw new BizException("MiniMax任务提交异常: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("提交MiniMax视频生成任务失败: {}", task.getId(), e);
            throw new BizException("提交MiniMax视频生成任务失败: " + e.getMessage());
        }
    }

    /**
     * 提交Volcengine视频生成任务
     *
     * @param task     任务对象
     * @param taskInfo 任务信息Map
     */
    private void submitVolcengineVideoTask(AiVideoGenerationPo task, Map<String, Object> taskInfo) {
        log.info("提交Volcengine视频生成任务: {}", task.getId());

        try {
            String volcengineTaskId;
            String videoType;
            String errorMessage;

            // 获取原始prompt并创建StringBuilder
            String originalPrompt = task.getPrompt();
            StringBuilder fullPrompt = new StringBuilder(originalPrompt);
            if (StringUtils.hasText(task.getFirstFrameImage())){
                fullPrompt.append(" --ratio ").append("adaptive");
            }else {
                // 检查并添加ratio参数
                if (task.getRatio() != null && !task.getRatio().isEmpty()) {
                    fullPrompt.append(" --ratio ").append(task.getRatio());
                }
            }
            // 检查并添加resolution参数
            if (task.getResolution() != null && !task.getResolution().isEmpty()) {
                fullPrompt.append(" --resolution ").append(task.getResolution());
            }

            // 检查并添加duration参数
            if (task.getDuration() != null) {
                fullPrompt.append(" --duration ").append(task.getDuration());
            }

            // 检查并添加fps参数
            if (task.getFps() != null) {
                fullPrompt.append(" --fps ").append(task.getFps());
            }

            // 使用拼接后的prompt替换原始prompt
            String finalPrompt = fullPrompt.toString();
            log.info("拼接参数后的完整prompt: {}", finalPrompt);

            // 根据是否有首帧和尾帧图片选择不同的API方法
            if (StringUtils.hasText(task.getFirstFrameImage()) && StringUtils.hasText(task.getLastFrameImage())) {
                // 首尾帧生成视频
                var response = volcengineVideoApiClient.createFirstLastFrameVideoTask(
                        task.getModel(),
                        finalPrompt,
                        MediaUrlPrefixUtil.getMediaUrl(task.getFirstFrameImage()),
                        MediaUrlPrefixUtil.getMediaUrl(task.getLastFrameImage()),
                        Long.valueOf(task.getUserId()),
                        null
                );
                volcengineTaskId = response.getId();
                videoType = "FIRST_LAST_FRAME";
                errorMessage = response.getErrorMsg();

            } else if (StringUtils.hasText(task.getFirstFrameImage())) {
                // 图生视频
                var response = volcengineVideoApiClient.createImageToVideoTask(
                        task.getModel(),
                        finalPrompt,
                       MediaUrlPrefixUtil.getMediaUrl(task.getFirstFrameImage()),
                        Long.valueOf(task.getUserId()),
                        null
                );
                volcengineTaskId = response.getId();
                videoType = "IMAGE_TO_VIDEO";
                errorMessage = response.getErrorMsg();

            } else {
                // 文生视频
                var response = volcengineVideoApiClient.createTextToVideoTask(
                        task.getModel(),
                        finalPrompt,
                        Long.valueOf(task.getUserId()),
                        null
                );
                volcengineTaskId = response.getId();
                videoType = "TEXT_TO_VIDEO";
                errorMessage = response.getErrorMsg();
            }

            if (volcengineTaskId == null) {
                throw new BizException(errorMessage);
            }
            log.info("Volcengine视频生成任务提交成功，任务ID: {}, 类型: {}", volcengineTaskId, videoType);
            // 更新任务信息
            taskInfo.put(KEY_VIDEO_TASK_ID, volcengineTaskId);
            taskInfo.put(KEY_VIDEO_TYPE, videoType);
            updateTaskInfo(task.getId(), taskInfo);
            // 更新任务状态为处理中
            startProcessing(task.getId(), volcengineTaskId);

        } catch (Exception e) {
            log.error("提交Volcengine视频生成任务失败: {}", task.getId(), e);
            throw new BizException("提交Volcengine视频生成任务失败: " + e.getMessage());
        }
    }



    /**
     * 转换为响应对象
     */
    private VideoGenerationRes convertToVideoGenerationRes(AiVideoGenerationPo videoPo) {
        VideoGenerationRes res = new VideoGenerationRes();
        BeanUtils.copyProperties(videoPo, res);
        res.setStatus(videoPo.getStatus()); // 会自动设置状态描述
        return res;
    }

    /**
     * 使用分镜编码查询和更新AiCanvasVideoPo表
     *
     * @param req 视频生成请求
     */
    private void updateCanvasVideoRecord(VideoGenerationReq req) {
        try {
            // 通过shotId查询分镜信息获取分镜编码
            AiCanvasShotPo shotPo = aiCanvasShotMapper.selectById(req.getShotId());
            if (shotPo == null) {
                log.warn("未找到分镜信息: shotId={}", req.getShotId());
                return;
            }

            shotPo.setShotStatus(ShotStatus.PROCESSING.getValue());
            shotPo.setUpdateTime(new Date());
            aiCanvasShotMapper.updateById(shotPo);

            String shotCode = shotPo.getCode();
            Long canvasId = shotPo.getCanvasId();

            // 使用分镜编码查询AiCanvasVideoPo表中是否有记录
            LambdaQueryWrapper<AiCanvasVideoPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiCanvasVideoPo::getCanvasId, canvasId)
                    .eq(AiCanvasVideoPo::getShotCode, shotCode)
                    .eq(AiCanvasVideoPo::getDelFlag, 0);

            AiCanvasVideoPo videoPo = aiCanvasVideoMapper.selectOne(queryWrapper);

            if (videoPo == null) {
                // 没有记录，创建新的AiCanvasVideoPo记录
                videoPo = new AiCanvasVideoPo();
                videoPo.setCanvasId(canvasId);
                videoPo.setShotCode(shotCode);
                videoPo.setVideoPrompt(req.getPrompt());
                videoPo.setVideoAspectRatio(req.getRatio());
                videoPo.setVideoDuration(req.getDuration());
                videoPo.setVideoStatus(ResourceStatus.GENERATING.getValue());
                videoPo.setStartFrameImage(StringUtils.hasText(req.getFirstFrameImage()) ? req.getFirstFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : null);
                videoPo.setEndFrameImage(StringUtils.hasText(req.getLastFrameImage()) ? req.getLastFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : null );
                videoPo.setVolume(new java.math.BigDecimal("1.00")); // 默认音量为1.00
                videoPo.setCreateTime(new Date());
                videoPo.setUpdateTime(new Date());
                videoPo.setDelFlag(0);
                aiCanvasVideoMapper.insert(videoPo);
                log.info("创建新的视频记录: canvasId={}, shotCode={}", canvasId, shotCode);
            } else {
                // 有记录，更新现有记录的相关信息
                videoPo.setVideoPrompt(req.getPrompt());
                videoPo.setVideoAspectRatio(req.getRatio());
                videoPo.setVideoDuration(req.getDuration());
                videoPo.setVideoStatus(ResourceStatus.GENERATING.getValue());
                videoPo.setStartFrameImage(StringUtils.hasText(req.getFirstFrameImage()) ? req.getFirstFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : "");
                videoPo.setEndFrameImage(StringUtils.hasText(req.getLastFrameImage()) ? req.getLastFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : "");
                videoPo.setUpdateTime(new Date());
                aiCanvasVideoMapper.updateById(videoPo);
                log.info("更新现有视频记录: id={}, canvasId={}, shotCode={}", videoPo.getId(), canvasId, shotCode);
            }
        } catch (Exception e) {
            log.error("更新画布视频记录失败: shotId={}", req.getShotId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 异步更新ai_video_generation_record表
     * 
     * @param externalTaskId 外部任务ID
     * @param status 任务状态
     * @param videoUrl 视频URL，成功时必须提供
     * @param errorMessage 错误信息，失败时提供
     * @param completionTokens 完成token数
     * @param totalTokens 总token数
     */
    @Async("videoExecutor")
    @Override
    public void asyncUpdateVideoGenerationRecord(String externalTaskId, String status, String videoUrl, 
                                              String errorMessage, Integer completionTokens, Integer totalTokens) {
        try {
            log.info("异步更新ai_video_generation_record表: taskId={}, status={}", externalTaskId, status);
            
            // 查询是否存在记录
            AiVideoGenerationRecordPo existingRecord = videoGenerationRecordMapper.selectOne(
                new LambdaQueryWrapper<AiVideoGenerationRecordPo>()
                    .eq(AiVideoGenerationRecordPo::getTaskId, externalTaskId)
                    .last("LIMIT 1")
            );
            
            if (existingRecord == null) {
                log.warn("未找到视频生成记录，无法更新: taskId={}", externalTaskId);
                return;
            }
            
            // 创建更新对象
            AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
            updateRecord.setTaskId(externalTaskId);
            updateRecord.setStatus(status);
            updateRecord.setUpdateTime(LocalDateTime.now());
            
            // 根据状态设置不同的字段
            if ("succeeded".equals(status)) {
                if (!StringUtils.hasText(videoUrl)) {
                    log.warn("成功状态但缺少视频URL: taskId={}", externalTaskId);
                    return;
                }
                updateRecord.setVideoUrl(videoUrl);
                updateRecord.setCompletionTokens(completionTokens);
                updateRecord.setTotalTokens(totalTokens);
            } else if ("failed".equals(status)) {
                updateRecord.setErrorMsg(errorMessage);
            }
            
            // 执行更新
            int updated = videoGenerationRecordMapper.update(updateRecord, 
                new LambdaUpdateWrapper<AiVideoGenerationRecordPo>()
                    .eq(AiVideoGenerationRecordPo::getTaskId, externalTaskId)
            );
            
            log.info("ai_video_generation_record表更新完成: taskId={}, status={}, updated={}", 
                    externalTaskId, status, updated > 0 ? "成功" : "失败");
        } catch (Exception e) {
            log.error("异步更新ai_video_generation_record表异常: taskId={}, error={}", 
                    externalTaskId, e.getMessage(), e);
        }
    }
}
