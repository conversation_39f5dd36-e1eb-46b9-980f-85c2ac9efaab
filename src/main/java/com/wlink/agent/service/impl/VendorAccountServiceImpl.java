package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.dao.mapper.VendorAccountMapper;
import com.wlink.agent.dao.po.VendorAccountPo;
import com.wlink.agent.service.VendorAccountService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 图片服务供应商账号服务实现
 */
@Slf4j
@Service
public class VendorAccountServiceImpl implements VendorAccountService {

    @Autowired
    private VendorAccountMapper vendorAccountMapper;

    @Autowired
    private RedissonClient redisson;

    // 账号轮询索引缓存键前缀
    private static final String ACCOUNT_INDEX_KEY = "ai_vendor:account:index:";

    // 账号使用计数缓存键前缀
    private static final String ACCOUNT_USAGE_KEY = "ai_vendor:account:usage:";

    // 账号锁定缓存键前缀
    private static final String ACCOUNT_LOCK_KEY = "ai_vendor:account:lock:";


    // 内存中的账号轮询索引，用于在Redis不可用时的回退方案
    private final java.util.Map<String, AtomicInteger> vendorAccountIndexMap = new java.util.concurrent.ConcurrentHashMap<>();


    // 账号执行锁键前缀
    private static final String ACCOUNT_EXECUTION_LOCK_KEY = "ai_vendor:account:execution:";


    @PostConstruct
    public void init() {
        // 初始化账号轮询索引
        log.info("Initializing vendor account service...");

        // 获取所有供应商名称
        List<String> vendorNames = getVendorNames();
        for (String vendorName : vendorNames) {
            vendorAccountIndexMap.put(vendorName, new AtomicInteger(0));
            log.info("Initialized account index for vendor: {}", vendorName);
        }

        log.info("Vendor account service initialized successfully");
    }

    /**
     * 获取所有供应商名称
     */
    private List<String> getVendorNames() {
        try {
            return vendorAccountMapper.selectObjs(
                            new LambdaQueryWrapper<VendorAccountPo>()
                                    .select(VendorAccountPo::getVendorName)
                                    .groupBy(VendorAccountPo::getVendorName)
                    ).stream()
                    .map(Object::toString)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get vendor names", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<VendorAccountPo> getAvailableAccounts(String vendorName) {
        return vendorAccountMapper.findAvailableAccounts(vendorName);
    }

    /**
     * 获取所有可用且未达到最大任务数的账号
     */
    @Override
    public List<VendorAccountPo> getAvailableAccountsWithCapacity(String vendorName) {
        return vendorAccountMapper.findAvailableAccountsWithCapacity(vendorName);
    }

    @Override
    public VendorAccountPo selectAccountForTask(String vendorName) {
        // 获取所有可用且未达到最大任务数的账号
        List<VendorAccountPo> availableAccounts = getAvailableAccountsWithCapacity(vendorName);
        if (availableAccounts.isEmpty()) {
            log.warn("No available accounts found for vendor: {}", vendorName);
            return null;
        }
        // 轮询选择账号
        int accountIndex = getNextAccountIndex(vendorName, availableAccounts.size());
        VendorAccountPo selectedAccount = availableAccounts.get(accountIndex);
        // 递增正在执行的任务数
        int updated = vendorAccountMapper.increaseRunningTaskCount(selectedAccount.getId());
        if (updated <= 0) {
            log.warn("Account {} has reached maximum concurrent tasks", selectedAccount.getId());
            // 递归选择下一个账号
            return selectAccountForTask(vendorName);
        }
        
        log.info("Selected account {} for vendor {}, current running tasks: {}", 
                selectedAccount.getId(), vendorName, selectedAccount.getRunningTaskCount() + 1);
        return selectedAccount;
    }

    /**
     * 获取下一个账号索引（轮询算法）
     */
    private int getNextAccountIndex(String vendorName, int accountCount) {
        if (accountCount <= 0) {
            return 0;
        }

        String indexKey = ACCOUNT_INDEX_KEY + vendorName;
        try {
            // 使用Redisson的RAtomicLong进行原子递增
            RAtomicLong atomicLong = redisson.getAtomicLong(indexKey);
            long index = atomicLong.incrementAndGet();

            // 设置过期时间，防止长期占用Redis内存
            atomicLong.expire(24, TimeUnit.HOURS);

            // 返回循环索引
            return (int)((index - 1) % accountCount);
        } catch (Exception e) {
            // Redis操作失败，使用内存缓存
            log.warn("Failed to get account index from Redis, using in-memory fallback", e);
            AtomicInteger index = vendorAccountIndexMap.computeIfAbsent(vendorName, k -> new AtomicInteger(0));
            return index.getAndIncrement() % accountCount;
        }
    }

    @Override
    @Transactional
    public void recordAccountUsage(Long accountId, int count) {
        try {
            vendorAccountMapper.increaseUsedQuota(accountId, count);
            log.info("Recorded usage for account {}: +{}", accountId, count);
        } catch (Exception e) {
            log.error("Failed to record account usage", e);
            throw new BizException("Failed to record account usage: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void resetAccountQuota(Long accountId) {
        try {
            vendorAccountMapper.resetUsedQuota(accountId);
            log.info("Reset quota for account {}", accountId);
        } catch (Exception e) {
            log.error("Failed to reset account quota", e);
            throw new BizException("Failed to reset account quota: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public VendorAccountPo addAccount(VendorAccountPo account) {
        // 设置默认值
        if (account.getStatus() == null) {
            account.setStatus(1); // 默认启用
        }
        if (account.getUsedDailyQuota() == null) {
            account.setUsedDailyQuota(0);
        }
        if (account.getPriority() == null) {
            account.setPriority(10); // 默认优先级
        }

        Date now = new Date();
        account.setCreateTime(now);
        account.setUpdateTime(now);
        account.setLastQuotaResetTime(now);

        try {
            vendorAccountMapper.insert(account);
            log.info("Added new account: {}", account.getId());
            return account;
        } catch (Exception e) {
            log.error("Failed to add account", e);
            throw new BizException("Failed to add account: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public VendorAccountPo updateAccount(VendorAccountPo account) {
        account.setUpdateTime(new Date());

        try {
            vendorAccountMapper.updateById(account);
            log.info("Updated account: {}", account.getId());
            return account;
        } catch (Exception e) {
            log.error("Failed to update account", e);
            throw new BizException("Failed to update account: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateAccountStatus(Long accountId, int status) {
        VendorAccountPo account = getAccountById(accountId);
        if (account == null) {
            log.warn("Account not found: {}", accountId);
            return false;
        }

        account.setStatus(status);
        account.setUpdateTime(new Date());

        try {
            vendorAccountMapper.updateById(account);
            log.info("Updated account status: {} -> {}", accountId, status);
            return true;
        } catch (Exception e) {
            log.error("Failed to update account status", e);
            throw new BizException("Failed to update account status: " + e.getMessage());
        }
    }

    @Override
    public VendorAccountPo getAccountById(Long accountId) {
        try {
            return vendorAccountMapper.selectById(accountId);
        } catch (Exception e) {
            log.error("Failed to get account by ID", e);
            throw new BizException("Failed to get account: " + e.getMessage());
        }
    }


    @Override
    public boolean lockAccount(Long accountId) {
        if (accountId == null) {
            return false;
        }

        String lockKey = ACCOUNT_EXECUTION_LOCK_KEY + accountId;
        try {
            // 使用Redisson的RBucket设置锁，如果已存在则返回false
            RBucket<String> bucket = redisson.getBucket(lockKey);
            boolean result = bucket.trySet("1", 10, TimeUnit.MINUTES);

            if (result) {
                log.info("Locked account for execution: {}", accountId);
                return true;
            } else {
                log.info("Account already locked: {}", accountId);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to lock account: {}", accountId, e);
            return false;
        }
    }

    @Override
    public void unlockAccount(Long accountId) {
        if (accountId == null) {
            return;
        }

        String lockKey = ACCOUNT_EXECUTION_LOCK_KEY + accountId;
        try {
            redisson.getBucket(lockKey).delete();
            log.info("Unlocked account: {}", accountId);
        } catch (Exception e) {
            log.error("Failed to unlock account: {}", accountId, e);
        }
    }

    @Override
    public boolean decreaseRunningTaskCount(Long accountId) {
        if (accountId == null) {
            return false;
        }
        
        try {
            int updated = vendorAccountMapper.decreaseRunningTaskCount(accountId);
            log.info("Decreased running task count for account: {}", accountId);
            return updated > 0;
        } catch (Exception e) {
            log.error("Failed to decrease running task count for account: {}", accountId, e);
            return false;
        }
    }

    @Override
    public VendorAccountPo selectAvailableUnlockedAccount(String vendorName) {
        // 获取所有可用且未达到最大任务数的账号
        List<VendorAccountPo> availableAccounts = getAvailableAccountsWithCapacity(vendorName);
        if (availableAccounts.isEmpty()) {
            log.warn("No available accounts found for vendor: {}", vendorName);
            return null;
        }

        // 检查每个账号是否已锁定
        for (VendorAccountPo account : availableAccounts) {
            String lockKey = ACCOUNT_EXECUTION_LOCK_KEY + account.getId();
            RBucket<String> bucket = redisson.getBucket(lockKey);
            boolean locked = bucket.isExists();

            if (!locked) {
                // 找到未锁定的账号，尝试递增任务数
                int updated = vendorAccountMapper.increaseRunningTaskCount(account.getId());
                if (updated <= 0) {
                    // 任务数递增失败，继续下一个账号
                    continue;
                }
                
                // 找到未锁定的账号，尝试锁定
                if (lockAccount(account.getId())) {
                    log.info("Selected and locked account {} for vendor {}, current running tasks: {}", 
                             account.getId(), vendorName, account.getRunningTaskCount() + 1);
                    return account;
                } else {
                    // 锁定失败，回滚任务数递增
                    vendorAccountMapper.decreaseRunningTaskCount(account.getId());
                }
            }
        }

        log.warn("All accounts are currently locked or at maximum capacity for vendor: {}", vendorName);
        return null;
    }
}