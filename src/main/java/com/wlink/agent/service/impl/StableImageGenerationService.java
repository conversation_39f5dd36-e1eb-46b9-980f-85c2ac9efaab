package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.CompletionApiClient;
import com.wlink.agent.client.DoubaoImageApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.config.ConcurrencyControlConfig;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.service.ImageTaskQueueService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 稳定的图片生成服务
 * 使用数据库并发控制替代Redis信号量
 */
@Slf4j
@Service
public class StableImageGenerationService {

    @Autowired
    private DatabaseConcurrencyControlService concurrencyControlService;

    @Autowired
    private AiImageTaskQueueMapper taskMapper;

    @Autowired
    private DoubaoImageApiClient doubaoImageApiClient;

    @Autowired
    private ImageTaskQueueService imageTaskQueueService;

    @Autowired
    private CompletionApiClient completionApiClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ConcurrencyControlConfig config;

    // 重试相关常量
    private static final int MAX_RETRY_COUNT = 3;
    private static final int MAX_ERROR_CODE_RETRY_COUNT = 3;
    private static final String ERROR_CODE_RETRY_FORMAT = "AI_COMPLETION_RETRY";

    // 特定错误码集合
    private static final Set<String> SPECIFIC_ERROR_CODES = new HashSet<>(Arrays.asList("50411", "50511", "50412", "50512", "50413"));

    /**
     * 处理图片生成任务
     * 使用数据库并发控制确保稳定性
     * 
     * @param taskId 任务ID
     */
    public void processImageTask(String taskId) {
        // 1. 尝试获取任务执行权限
        if (!concurrencyControlService.tryAcquireTask(taskId)) {
            // 获取权限失败，任务保持PENDING状态，等待下次调度
            log.info("任务 {} 获取执行权限失败，等待下次调度", taskId);
            return;
        }

        try {
            // 2. 获取任务详情
            AiImageTaskQueuePo task = taskMapper.selectById(taskId);
            if (task == null) {
                log.warn("未找到任务: {}", taskId);
                concurrencyControlService.releaseTask(taskId, false, "任务不存在");
                return;
            }

            // 3. 检查任务状态
            if (!TaskStatus.PROCESSING.getValue().equals(task.getTaskStatus())) {
                log.warn("任务状态异常: {}, 期望: PROCESSING, 实际: {}", 
                    taskId, task.getTaskStatus());
                return;
            }

            // 4. 执行图片生成
            boolean success = executeImageGeneration(task);
            
            // 5. 释放执行权限
            concurrencyControlService.releaseTask(taskId, success, 
                success ? null : "图片生成失败");

        } catch (Exception e) {
            log.error("处理图片生成任务异常: {}", taskId, e);
            
            // 异常情况下也要释放权限
            concurrencyControlService.releaseTask(taskId, false, 
                "任务处理异常: " + e.getMessage());
            
            // 检查是否需要重试
            handleTaskRetry(taskId, e);
        }
    }

    /**
     * 执行图片生成逻辑，包含完整的重试机制
     *
     * @param task 任务对象
     * @return 是否成功
     */
    private boolean executeImageGeneration(AiImageTaskQueuePo task) {
        return executeImageGenerationWithRetry(task, 0);
    }

    /**
     * 带重试机制的图片生成执行方法
     *
     * @param task 任务对象
     * @param retryCount 当前重试次数
     * @return 是否成功
     */
    private boolean executeImageGenerationWithRetry(AiImageTaskQueuePo task, int retryCount) {
        try {
            log.info("开始执行图片生成任务: {}, 重试次数: {}", task.getId(), retryCount);

            // 解析请求参数
            String requestParams = task.getRequestParams();
            DoubaoImageGenerationRequest apiRequest = JSON.parseObject(requestParams,
                DoubaoImageGenerationRequest.class);

            // 调用图片生成API
            ImageGenerateRes response = doubaoImageApiClient.generateImage(apiRequest);

            // 检查响应
            if (response == null) {
                log.error("图片生成API返回空响应, taskId: {}, 重试次数: {}", task.getId(), retryCount);
                throw new RuntimeException("图片生成API返回空响应");
            }

            // 处理特定错误码的情况
            if (StringUtils.isNotBlank(response.getCode()) && StringUtils.isBlank(response.getImageUrl())) {
                String errorCode = response.getCode();

                // 检查是否为需要特殊处理的错误码
                if (SPECIFIC_ERROR_CODES.contains(errorCode)) {
                    log.info("任务 {} 返回错误码 {}, 尝试调用AI完成API", task.getId(), errorCode);

                    // 处理特定错误码的重试逻辑
                    boolean handled = handleSpecificErrorCode(task, errorCode, apiRequest);
                    if (handled) {
                        log.info("特定错误码处理成功，任务已重新提交: {}", task.getId());
                        return true; // 任务已重新提交，当前处理算成功
                    } else {
                        log.error("特定错误码处理失败: {}", task.getId());
                        return false;
                    }
                } else {
                    // 其他错误码，抛出异常进入重试逻辑
                    throw new RuntimeException("图片生成API返回错误: code=" + errorCode + ", message=" + response.getMessage());
                }
            }

            if (response.getImageUrl() == null || response.getImageUrl().trim().isEmpty()) {
                log.error("图片生成API返回空的图片URL, taskId: {}, 重试次数: {}", task.getId(), retryCount);
                throw new RuntimeException("图片生成API返回空的图片URL");
            }

            // 成功情况，更新任务结果
            imageTaskQueueService.updateTaskStatus(
                task.getId(),
                TaskStatus.COMPLETED.getValue(),
                response,
                null,
                null
            );

            log.info("图片生成任务完成: {}, imageUrl: {}", task.getId(), response.getImageUrl());
            return true;

        } catch (Exception e) {
            log.error("执行图片生成失败, taskId: {}, 重试次数: {}", task.getId(), retryCount, e);

            // 检查是否可以重试
            if (retryCount < MAX_RETRY_COUNT) {
                // 重试前等待1秒
                try {
                    log.info("等待1秒后进行第{}次重试", retryCount + 1);
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("重试等待被中断", ie);
                }
                // 递归重试
                return executeImageGenerationWithRetry(task, retryCount + 1);
            }

            // 达到最大重试次数，更新任务状态为失败
            imageTaskQueueService.updateTaskStatus(
                task.getId(),
                TaskStatus.FAILED.getValue(),
                null,
                "图片生成失败,请优化提示词重试",
                null
            );

            return false;
        }
    }

    /**
     * 处理特定错误码的逻辑
     *
     * @param task 任务对象
     * @param errorCode 错误码
     * @param apiRequest 原始请求
     * @return 是否处理成功
     */
    private boolean handleSpecificErrorCode(AiImageTaskQueuePo task, String errorCode, DoubaoImageGenerationRequest apiRequest) {
        try {
            // 检查当前任务的重试次数
            ErrorCodeRetryInfo retryInfo = parseErrorCodeRetryInfo(task.getErrorReason());
            int currentRetryCount = 0;

            if (retryInfo != null && errorCode.equals(retryInfo.getErrorCode())) {
                currentRetryCount = retryInfo.getRetryCount();
            }

            // 检查是否超过重试限制
            if (currentRetryCount >= MAX_ERROR_CODE_RETRY_COUNT) {
                log.warn("任务 {} 错误码 {} 重试次数已达上限 {}, 标记为失败",
                    task.getId(), errorCode, MAX_ERROR_CODE_RETRY_COUNT);

                String finalErrorReason = String.format("错误码%s重试次数已达上限(%d次)，最终失败",
                    errorCode, MAX_ERROR_CODE_RETRY_COUNT);

                imageTaskQueueService.updateTaskStatus(
                    task.getId(),
                    TaskStatus.FAILED.getValue(),
                    null,
                    finalErrorReason,
                    null
                );

                return false;
            }

            // 调用AI完成API
            String prompt = apiRequest.getPrompt();
            String userId = task.getUserId();
            String aiResponse = completionApiClient.requestCompletion(prompt, userId, null);

            // 如果第一次调用返回null，重试一次
            if (aiResponse == null) {
                log.info("第一次调用AI完成API返回null，重试一次");
                aiResponse = completionApiClient.requestCompletion(prompt, userId, null);
            }

            if (aiResponse != null) {
                log.info("AI完成API调用成功，更新任务prompt。重试次数: {}", currentRetryCount + 1);

                // 更新任务的prompt
                apiRequest.setPrompt(aiResponse);
                String updatedRequestParams = JSON.toJSONString(apiRequest);

                // 更新任务状态为待执行，并更新重试信息
                AiImageTaskQueuePo updateTask = new AiImageTaskQueuePo();
                updateTask.setId(task.getId());
                updateTask.setTaskStatus(TaskStatus.PENDING.getValue());
                updateTask.setRequestParams(updatedRequestParams);
                updateTask.setErrorReason(buildErrorCodeRetryInfo(errorCode, currentRetryCount + 1));
                updateTask.setUpdateTime(new Date());

                taskMapper.updateById(updateTask);

                return true;
            } else {
                // AI完成API失败
                log.warn("AI完成API调用失败，任务: {}, 重试次数: {}", task.getId(), currentRetryCount + 1);

                String errorReason = buildErrorCodeRetryInfo(errorCode, currentRetryCount + 1);
                imageTaskQueueService.updateTaskStatus(
                    task.getId(),
                    TaskStatus.FAILED.getValue(),
                    null,
                    "AI完成API调用失败，错误码：" + errorCode + "，重试次数：" + (currentRetryCount + 1),
                    null
                );

                return false;
            }

        } catch (Exception e) {
            log.error("处理特定错误码失败: taskId={}, errorCode={}", task.getId(), errorCode, e);
            return false;
        }
    }

    /**
     * 解析错误原因中的重试信息
     *
     * @param errorReason 错误原因字段
     * @return 重试信息对象，如果不是重试错误则返回null
     */
    private ErrorCodeRetryInfo parseErrorCodeRetryInfo(String errorReason) {
        if (StringUtils.isBlank(errorReason) || !errorReason.startsWith(ERROR_CODE_RETRY_FORMAT + ":")) {
            return null;
        }

        try {
            String jsonPart = errorReason.substring(ERROR_CODE_RETRY_FORMAT.length() + 1);
            return objectMapper.readValue(jsonPart, ErrorCodeRetryInfo.class);
        } catch (Exception e) {
            log.warn("解析错误码重试信息失败: {}", errorReason, e);
            return null;
        }
    }

    /**
     * 构建错误码重试信息字符串
     *
     * @param errorCode  错误码
     * @param retryCount 重试次数
     * @return 格式化的错误信息字符串
     */
    private String buildErrorCodeRetryInfo(String errorCode, int retryCount) {
        try {
            ErrorCodeRetryInfo retryInfo = new ErrorCodeRetryInfo();
            retryInfo.setErrorCode(errorCode);
            retryInfo.setRetryCount(retryCount);
            retryInfo.setLastRetryTime(new Date());

            String jsonInfo = objectMapper.writeValueAsString(retryInfo);
            return ERROR_CODE_RETRY_FORMAT + ":" + jsonInfo;
        } catch (Exception e) {
            log.error("构建错误码重试信息失败", e);
            return ERROR_CODE_RETRY_FORMAT + ":重试次数" + retryCount + ",错误码" + errorCode;
        }
    }

    /**
     * 处理任务重试逻辑
     * 
     * @param taskId 任务ID
     * @param exception 异常信息
     */
    private void handleTaskRetry(String taskId, Exception exception) {
        try {
            AiImageTaskQueuePo task = taskMapper.selectById(taskId);
            if (task == null) {
                return;
            }
            
            int currentRetryCount = task.getRetryCount() != null ? task.getRetryCount() : 0;

            if (currentRetryCount < config.getMaxRetryCount()) {
                // 增加重试次数
                currentRetryCount++;
                
                // 更新重试次数并重置状态为PENDING
                task.setRetryCount(currentRetryCount);
                task.setTaskStatus(TaskStatus.PENDING.getValue());
                task.setErrorReason(String.format("重试第%d次，原因: %s", 
                    currentRetryCount, exception.getMessage()));
                taskMapper.updateById(task);
                
                log.info("任务已标记为重试，等待调度器处理。重试次数: {}, 任务ID: {}", currentRetryCount, taskId);

            } else {
                log.error("任务重试次数已达上限: {}, taskId: {}", config.getMaxRetryCount(), taskId);

                // 最终失败
                imageTaskQueueService.updateTaskStatus(
                    Long.valueOf(taskId),
                    TaskStatus.FAILED.getValue(),
                    null,
                    "图片提示词包含敏感词,请优化提示词重新生成",
                    currentRetryCount
                );
            }
            
        } catch (Exception e) {
            log.error("处理任务重试逻辑失败: {}", taskId, e);
        }
    }

    /**
     * 错误码重试信息内部类
     */
    public static class ErrorCodeRetryInfo {
        private String errorCode;
        private int retryCount;
        private Date lastRetryTime;

        public ErrorCodeRetryInfo() {
        }

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public Date getLastRetryTime() {
            return lastRetryTime;
        }

        public void setLastRetryTime(Date lastRetryTime) {
            this.lastRetryTime = lastRetryTime;
        }
    }
}
