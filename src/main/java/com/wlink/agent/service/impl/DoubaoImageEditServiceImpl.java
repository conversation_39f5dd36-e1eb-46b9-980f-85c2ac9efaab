package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.wlink.agent.client.DoubaoImageEditApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.model.req.DoubaoImageEditReq;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.service.AiCanvasShotService;
import com.wlink.agent.service.DoubaoImageEditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 豆包图像编辑服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DoubaoImageEditServiceImpl implements DoubaoImageEditService {

    private final DoubaoImageEditApiClient doubaoImageEditApiClient;
    private final AiCanvasShotService aiCanvasShotService;

    @Override
    public ImageGenerateRes editImage(DoubaoImageEditReq req) {
        log.info("开始豆包图像编辑: shotId={}, prompt={}", req.getShotId(), req.getPrompt());

        // 1. 验证分镜是否存在
        if (req.getShotId() != null && aiCanvasShotService.getById(req.getShotId()) == null) {
            log.error("分镜不存在: shotId={}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 2. 构建豆包API请求
        DoubaoImageEditRequest apiRequest = buildDoubaoRequest(req);

        // 3. 调用豆包API
        try {
            ImageGenerateRes result = doubaoImageEditApiClient.editImage(apiRequest);
            log.info("豆包图像编辑完成: shotId={}", req.getShotId());
            return result;
        } catch (Exception e) {
            log.error("豆包图像编辑失败: shotId={}, error={}", req.getShotId(), e.getMessage(), e);
            throw new BizException("图像编辑失败: " + e.getMessage());
        }
    }

    @Override
    public ImageGenerateRes editImageWithApiKey(DoubaoImageEditReq req, String apiKey) {
        log.info("开始豆包图像编辑(指定API密钥): shotId={}, prompt={}", req.getShotId(), req.getPrompt());

        // 1. 验证分镜是否存在
        if (req.getShotId() != null && aiCanvasShotService.getById(req.getShotId()) == null) {
            log.error("分镜不存在: shotId={}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 2. 构建豆包API请求
        DoubaoImageEditRequest apiRequest = buildDoubaoRequest(req);

        // 3. 调用豆包API
        try {
            ImageGenerateRes result = doubaoImageEditApiClient.editImageWithApiKey(apiRequest, apiKey);
            log.info("豆包图像编辑完成(指定API密钥): shotId={}", req.getShotId());
            return result;
        } catch (Exception e) {
            log.error("豆包图像编辑失败(指定API密钥): shotId={}, error={}", req.getShotId(), e.getMessage(), e);
            throw new BizException("图像编辑失败: " + e.getMessage());
        }
    }

    /**
     * 构建豆包API请求
     */
    private DoubaoImageEditRequest buildDoubaoRequest(DoubaoImageEditReq req) {
        DoubaoImageEditRequest apiRequest = new DoubaoImageEditRequest();
        apiRequest.setModel(req.getModel());
        apiRequest.setPrompt(req.getPrompt());
        apiRequest.setImage(req.getImage());
        apiRequest.setResponseFormat(req.getResponseFormat());
        apiRequest.setSize(req.getSize());
        apiRequest.setSeed(req.getSeed());
        apiRequest.setGuidanceScale(req.getGuidanceScale());
        apiRequest.setWatermark(req.getWatermark());
        
        return apiRequest;
    }
}
