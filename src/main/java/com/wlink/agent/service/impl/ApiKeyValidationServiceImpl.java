package com.wlink.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.service.ApiKeyValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * API Key 验证服务实现
 * 
 * 注意：这里使用简单的实现方式，实际生产环境中应该：
 * 1. 创建专门的 API Key 表
 * 2. 实现 API Key 的生成、撤销、过期等功能
 * 3. 添加访问频率限制
 * 4. 记录 API 调用日志
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiKeyValidationServiceImpl implements ApiKeyValidationService {
    
    private final AiUsersMapper aiUsersMapper;
    
    @Override
    public String validateApiKeyAndGetUserId(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return null;
        }
        
        try {
            // 这里使用简单的实现：将用户ID作为API Key
            // 实际生产环境中应该有专门的API Key表和加密机制
            
            // 查询用户是否存在且状态正常
            LambdaQueryWrapper<AiUsersPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiUsersPo::getApiKey, apiKey)
                    .eq(AiUsersPo::getDelFlag, 0)
                    .eq(AiUsersPo::getStatus, 1); // 1-正常状态
            
            AiUsersPo user = aiUsersMapper.selectOne(queryWrapper);
            if (user != null) {
                log.debug("API Key validation successful for userId: {}", apiKey);
                return user.getUserId();
            }
            
            log.warn("API Key validation failed: {}", apiKey);
            return null;
            
        } catch (Exception e) {
            log.error("Error validating API Key: {}", apiKey, e);
            return null;
        }
    }
    
    @Override
    public boolean isValidApiKey(String apiKey) {
        return validateApiKeyAndGetUserId(apiKey) != null;
    }
}
