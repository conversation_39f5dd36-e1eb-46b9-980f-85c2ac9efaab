package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiResourceFavoriteMapper;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiResourceFavoritePo;
import com.wlink.agent.enums.ResourceSubtypeEnum;
import com.wlink.agent.enums.ResourceTypeEnum;
import com.wlink.agent.model.req.BatchFavoriteReq;
import com.wlink.agent.model.req.BatchRemoveFavoriteReq;
import com.wlink.agent.model.req.ResourceFavoriteReq;
import com.wlink.agent.model.req.RoleSaveReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.SimpleFavoriteReq;
import com.wlink.agent.model.res.ResourceFavoriteRes;
import com.wlink.agent.service.AiResourceFavoriteService;
import com.wlink.agent.service.ImageTaskQueueService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 资源收藏服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiResourceFavoriteServiceImpl extends ServiceImpl<AiResourceFavoriteMapper, AiResourceFavoritePo> implements AiResourceFavoriteService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ImageTaskQueueService imageTaskQueueService;
    private final AiCreationContentMapper aiCreationContentMapper;

    /**
     * 添加资源收藏
     *
     * @param userId 用户ID
     * @param req    收藏请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response addFavorite(String userId, ResourceFavoriteReq req) {
        if (Objects.isNull(userId) || Objects.isNull(req)) {
            throw new IllegalArgumentException("参数无效");
        }

        try {
            // 检查资源类型是否有效
            ResourceTypeEnum resourceType = ResourceTypeEnum.getByCode(req.getResourceType());
            if (Objects.isNull(resourceType)) {
                throw new BizException("无效的资源类型");
            }

            // 检查资源子类型是否有效
            if (Objects.nonNull(req.getResourceSubtype())) {
                ResourceSubtypeEnum resourceSubtype = ResourceSubtypeEnum.getByCode(req.getResourceType(), req.getResourceSubtype());
                if (Objects.isNull(resourceSubtype)) {
                    throw new BizException("无效的资源子类型");
                }
            }

            // 检查是否已收藏
            AiResourceFavoritePo existingFavorite = getOne(new LambdaQueryWrapper<AiResourceFavoritePo>()
                    .eq(AiResourceFavoritePo::getUserId, userId)
                    .eq(AiResourceFavoritePo::getResourceId, req.getResourceId())
                    .eq(AiResourceFavoritePo::getResourceType, req.getResourceType())
                    .eq(AiResourceFavoritePo::getDelFlag, 0));

            if (Objects.nonNull(existingFavorite)) {
                throw new BizException("该资源已收藏");
            }

            // 保存收藏记录
            AiResourceFavoritePo favoritePo = new AiResourceFavoritePo();
            // 生成唯一code
            String uniqueCode = IdUtil.fastSimpleUUID();
            favoritePo.setCode(uniqueCode);
            favoritePo.setUserId(userId);
            favoritePo.setSessionId(req.getSessionId());
            favoritePo.setResourceId(req.getResourceId());
            favoritePo.setResourceType(req.getResourceType());
            favoritePo.setResourceSubtype(req.getResourceSubtype());
            favoritePo.setResourceName(req.getResourceName());
            favoritePo.setResourceUrl(req.getResourceUrl());
            favoritePo.setResourceData(req.getResourceData());
            favoritePo.setCreateTime(new Date());
            favoritePo.setDelFlag(0);
            save(favoritePo);
            
            log.info("用户 {} 成功收藏资源，类型: {}, ID: {}, 生成唯一编码: {}", 
                    userId, resourceType.getDesc(), req.getResourceId(), uniqueCode);
            return Response.buildSuccess();
        } catch (Exception e) {
            log.error("收藏资源失败: {}", e.getMessage(), e);
            throw new BizException("收藏资源失败: " + e.getMessage());
        }
    }

    /**
     * 移除资源收藏
     *
     * @param userId       用户ID
     * @param resourceCode 资源编码
     * @return 操作结果
     */
    @Override
    public Response removeFavorite(String userId, String resourceCode) {
        if (Objects.isNull(userId) || Objects.isNull(resourceCode)) {
            throw new IllegalArgumentException("参数无效");
        }
        try {
            // 查询收藏记录
            AiResourceFavoritePo favoritePo = getOne(new LambdaQueryWrapper<AiResourceFavoritePo>()
                    .eq(AiResourceFavoritePo::getUserId, userId)
                    .eq(AiResourceFavoritePo::getCode, resourceCode)
                    .eq(AiResourceFavoritePo::getDelFlag, 0));

            if (Objects.isNull(favoritePo)) {
                throw new BizException("未找到收藏记录");
            }
            // 逻辑删除
            favoritePo.setDelFlag(1);
            favoritePo.setUpdateTime(new Date());
            updateById(favoritePo);
            
            ResourceTypeEnum resourceType = ResourceTypeEnum.getByCode(favoritePo.getResourceType());
            String typeDesc = resourceType != null ? resourceType.getDesc() : "未知类型";
            
            log.info("用户 {} 成功取消收藏资源，类型: {}, 唯一编码: {}", 
                    userId, typeDesc, resourceCode);
            return Response.buildSuccess();
        } catch (Exception e) {
            log.error("取消收藏资源失败: {}", e.getMessage(), e);
            throw new BizException("取消收藏失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户收藏的所有资源列表
     *
     * @param userId 用户ID
     * @return 收藏资源列表
     */
    @Override
    public MultiResponse<ResourceFavoriteRes> listUserFavorites(String userId) {
        if (Objects.isNull(userId)) {
            throw new IllegalArgumentException("参数无效");
        }
        try {
            // 查询用户收藏记录
            List<AiResourceFavoritePo> favoriteList = list(new LambdaQueryWrapper<AiResourceFavoritePo>()
                    .eq(AiResourceFavoritePo::getUserId, userId)
                    .eq(AiResourceFavoritePo::getDelFlag, 0)
                    .orderByDesc(AiResourceFavoritePo::getCreateTime));

            return convertToResponseList(favoriteList);
        } catch (Exception e) {
            log.error("查询用户收藏列表失败: {}", e.getMessage(), e);
            throw new BizException("查询收藏列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户收藏的指定类型资源列表
     *
     * @param userId       用户ID
     * @param resourceType 资源类型
     * @return 收藏资源列表
     */
    @Override
    public MultiResponse<ResourceFavoriteRes> listUserFavoritesByType(String userId, Integer resourceType) {
        if (Objects.isNull(userId) || Objects.isNull(resourceType)) {
            throw new IllegalArgumentException("参数无效");
        }
        try {
            // 检查资源类型是否有效
            ResourceTypeEnum typeEnum = ResourceTypeEnum.getByCode(resourceType);
            if (Objects.isNull(typeEnum)) {
                throw new BizException("无效的资源类型");
            }

            // 查询用户收藏记录
            List<AiResourceFavoritePo> favoriteList = baseMapper.selectByUserIdAndType(userId, resourceType);
            
            return convertToResponseList(favoriteList);
        } catch (Exception e) {
            log.error("查询用户收藏列表失败: {}", e.getMessage(), e);
            throw new BizException("查询收藏列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户收藏的指定类型和子类型资源列表
     *
     * @param userId          用户ID
     * @param resourceType    资源类型
     * @param resourceSubtype 资源子类型
     * @return 收藏资源列表
     */
    @Override
    public MultiResponse<ResourceFavoriteRes> listUserFavoritesByTypeAndSubtype(String userId, Integer resourceType, Integer resourceSubtype) {
        if (Objects.isNull(userId) || Objects.isNull(resourceType) || Objects.isNull(resourceSubtype)) {
            throw new IllegalArgumentException("参数无效");
        }
        try {
            // 检查资源类型和子类型是否有效
            ResourceTypeEnum typeEnum = ResourceTypeEnum.getByCode(resourceType);
            if (Objects.isNull(typeEnum)) {
                throw new BizException("无效的资源类型");
            }
            
            ResourceSubtypeEnum subtypeEnum = ResourceSubtypeEnum.getByCode(resourceType, resourceSubtype);
            if (Objects.isNull(subtypeEnum)) {
                throw new BizException("无效的资源子类型");
            }

            // 查询用户收藏记录
            List<AiResourceFavoritePo> favoriteList = baseMapper.selectByUserIdAndTypeAndSubtype(userId, resourceType, resourceSubtype);
            
            return convertToResponseList(favoriteList);
        } catch (Exception e) {
            log.error("查询用户收藏列表失败: {}", e.getMessage(), e);
            throw new BizException("查询收藏列表失败: " + e.getMessage());
        }
    }

    /**
     * 通过资源ID和类型添加收藏
     * 对于图片类型，会从AiImageTaskQueuePo表中查询详细信息
     *
     * @param userId 用户ID
     * @param req    简化的收藏请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response addFavoriteById(String userId, SimpleFavoriteReq req) {
        if (Objects.isNull(userId) || Objects.isNull(req)) {
            throw new IllegalArgumentException("参数无效");
        }
        try {
            // 检查资源类型是否有效
            ResourceTypeEnum resourceType = ResourceTypeEnum.getByCode(req.getResourceType());
            if (Objects.isNull(resourceType)) {
                throw new BizException("无效的资源类型");
            }
            // 根据资源类型处理
            switch (resourceType) {
                case IMAGE:
                    return addImageFavorite(userId, req.getResourceId());
                case VIDEO:
                    throw new BizException("暂不支持视频收藏");
                default:
                    throw new BizException("不支持的资源类型: " + resourceType.getDesc());
            }
        } catch (Exception e) {
            log.error("收藏资源失败: {}", e.getMessage(), e);
            throw new BizException("收藏资源失败: " + e.getMessage());
        }
    }

    /**
     * 添加图片收藏
     *
     * @param userId 用户ID
     * @param imageTaskId 图片任务ID
     * @return 操作结果
     */
    private Response addImageFavorite(String userId, Long imageTaskId) {
        // 查询图片任务信息
        AiImageTaskQueuePo imageTask = imageTaskQueueService.getTaskById(imageTaskId);
        if (imageTask == null) {
            throw new BizException("未找到图片任务，ID: " + imageTaskId);
        }
        // 检查任务状态
        if (!"COMPLETED".equals(imageTask.getTaskStatus())) {
            throw new BizException("图片任务尚未完成，无法收藏");
        }

        // 构建资源收藏请求
        ResourceFavoriteReq favoriteReq = new ResourceFavoriteReq();
        favoriteReq.setSessionId(imageTask.getSessionId());
        favoriteReq.setResourceId(String.valueOf(imageTask.getId()));
        favoriteReq.setResourceType(ResourceTypeEnum.IMAGE.getCode());
        
        // 设置子类型（如果有）
        if (imageTask.getContentType() != null) {
            favoriteReq.setResourceSubtype(imageTask.getContentType());
        }
        
        // 设置资源名称（可以从任务信息中提取）
        String resourceName = "图片_" + imageTask.getId();
        favoriteReq.setResourceName(resourceName);
        
        // 设置资源URL（从结果中提取）

        favoriteReq.setResourceUrl(imageTask.getImageResult());
        // 设置资源数据
        favoriteReq.setResourceData(imageTask.getResult());

        // 检查是否已收藏
        AiResourceFavoritePo existingFavorite = getOne(new LambdaQueryWrapper<AiResourceFavoritePo>()
                .eq(AiResourceFavoritePo::getUserId, userId)
                .eq(AiResourceFavoritePo::getResourceId, imageTaskId)
                .eq(AiResourceFavoritePo::getResourceType, ResourceTypeEnum.IMAGE.getCode())
                .eq(AiResourceFavoritePo::getDelFlag, 0));

        if (Objects.nonNull(existingFavorite)) {
            throw new BizException("该资源已收藏");
        }
        // 保存收藏记录
        AiResourceFavoritePo favoritePo = new AiResourceFavoritePo();
        // 生成唯一code
        String uniqueCode = IdUtil.fastSimpleUUID();
        favoritePo.setCode(uniqueCode);
        favoritePo.setUserId(userId);
        favoritePo.setSessionId(imageTask.getSessionId());
        favoritePo.setResourceId(String.valueOf(imageTask.getId()));
        favoritePo.setResourceType(ResourceTypeEnum.IMAGE.getCode());
        favoritePo.setResourceSubtype(imageTask.getContentType());
        favoritePo.setResourceUrl(imageTask.getImageResult());
        favoritePo.setCreateTime(new Date());
        favoritePo.setDelFlag(0);
        save(favoritePo);

        log.info("用户 {} 成功收藏资源，ID: {}, 生成唯一编码: {}",
                userId, imageTaskId, uniqueCode);
        return SingleResponse.buildSuccess();
    }

    /**
     * 将实体列表转换为响应对象列表
     *
     * @param favoriteList 收藏记录列表
     * @return 响应对象列表
     */
    private MultiResponse<ResourceFavoriteRes> convertToResponseList(List<AiResourceFavoritePo> favoriteList) {
        if (favoriteList.isEmpty()) {
            return MultiResponse.of(new ArrayList<>());
        }
        
        // 转换为响应对象
        List<ResourceFavoriteRes> resList = new ArrayList<>(favoriteList.size());
        for (AiResourceFavoritePo favoritePo : favoriteList) {
            ResourceFavoriteRes res = new ResourceFavoriteRes();
            res.setResourceCode(favoritePo.getCode());
            res.setResourceId(favoritePo.getResourceId());
            res.setSessionId(favoritePo.getSessionId());
            res.setResourceType(favoritePo.getResourceType());
            res.setResourceSubtype(favoritePo.getResourceSubtype());
            res.setResourceName(favoritePo.getResourceName());
            
            // 获取类型和子类型描述
            ResourceTypeEnum typeEnum = ResourceTypeEnum.getByCode(favoritePo.getResourceType());
            if (typeEnum != null) {
                res.setResourceTypeDesc(typeEnum.getDesc());
            }
            
            if (favoritePo.getResourceSubtype() != null) {
                ResourceSubtypeEnum subtypeEnum = ResourceSubtypeEnum.getByCode(
                        favoritePo.getResourceType(), favoritePo.getResourceSubtype());
                if (subtypeEnum != null) {
                    res.setResourceSubtypeDesc(subtypeEnum.getDesc());
                }
            }
            
            // 处理资源URL
            if (favoritePo.getResourceUrl() != null) {
                res.setResourceUrl(MediaUrlPrefixUtil.getMediaUrl(favoritePo.getResourceUrl()));
            }
            
            res.setCreateTime(favoritePo.getCreateTime());
            
            // 解析资源数据
            if (favoritePo.getResourceData() != null && !favoritePo.getResourceData().isEmpty()) {
                try {
                    Map<String, Object> extraData = JSON.parseObject(favoritePo.getResourceData(), HashMap.class);
                    res.setExtraData(extraData);
                } catch (Exception e) {
                    log.warn("解析资源数据失败: {}", e.getMessage());
                    res.setExtraData(new HashMap<>());
                }
            }
            
            resList.add(res);
        }
        
        return MultiResponse.of(resList);
    }

    /**
     * 批量添加资源收藏
     * 对于图片类型，会从AiImageTaskQueuePo表中查询详细信息
     *
     * @param userId 用户ID
     * @param req    批量收藏请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response batchAddFavoriteById(String userId, BatchFavoriteReq req) {
        if (Objects.isNull(userId) || Objects.isNull(req) || Objects.isNull(req.getResourceIds()) || req.getResourceIds().isEmpty()) {
            throw new IllegalArgumentException("参数无效");
        }

        try {
            // 检查资源类型是否有效
            ResourceTypeEnum resourceType = ResourceTypeEnum.getByCode(req.getResourceType());
            if (Objects.isNull(resourceType)) {
                throw new BizException("无效的资源类型");
            }

            // 根据资源类型处理
            switch (resourceType) {
                case IMAGE:
                    return batchAddImageFavorite(userId, req.getResourceIds());
                case VIDEO:
                    throw new BizException("暂不支持视频批量收藏");
                default:
                    throw new BizException("不支持的资源类型: " + resourceType.getDesc());
            }
        } catch (Exception e) {
            log.error("批量收藏资源失败: {}", e.getMessage(), e);
            throw new BizException("批量收藏资源失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加图片收藏
     *
     * @param userId 用户ID
     * @param imageTaskIds 图片任务ID列表
     * @return 操作结果
     */
    private Response batchAddImageFavorite(String userId, List<Long> imageTaskIds) {
        int successCount = 0;
        int failCount = 0;
        List<String> failReasons = new ArrayList<>();

        for (Long imageTaskId : imageTaskIds) {
            try {
                // 查询图片任务信息
                AiImageTaskQueuePo imageTask = imageTaskQueueService.getTaskById(imageTaskId);
                if (imageTask == null) {
                    failCount++;
                    failReasons.add("未找到图片任务，ID: " + imageTaskId);
                    continue;
                }

                // 检查任务状态
                if (!"COMPLETED".equals(imageTask.getTaskStatus())) {
                    failCount++;
                    failReasons.add("图片任务尚未完成，无法收藏，ID: " + imageTaskId);
                    continue;
                }

                // 检查是否已收藏
                AiResourceFavoritePo existingFavorite = getOne(new LambdaQueryWrapper<AiResourceFavoritePo>()
                        .eq(AiResourceFavoritePo::getUserId, userId)
                        .eq(AiResourceFavoritePo::getResourceId, String.valueOf(imageTaskId))
                        .eq(AiResourceFavoritePo::getResourceType, ResourceTypeEnum.IMAGE.getCode())
                        .eq(AiResourceFavoritePo::getDelFlag, 0));

                if (Objects.nonNull(existingFavorite)) {
                    failCount++;
                    failReasons.add("资源已收藏，ID: " + imageTaskId);
                    continue;
                }
                AtomicReference<String> resourceData = new AtomicReference<>();
                AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<AiCreationContentPo>()
                        .eq(AiCreationContentPo::getSessionId, imageTask.getSessionId())
                        .eq(AiCreationContentPo::getContentType, imageTask.getContentType())
                        .last("Limit 1"));
                if (Objects.nonNull(aiCreationContentPo)) {
                    String contentData = aiCreationContentPo.getContentData();
                    if (Objects.equals(imageTask.getContentType(), 4)){
                        ShotSaveReq shotSaveReq = JSON.parseObject(contentData, ShotSaveReq.class);
                        shotSaveReq.getShotGroups().forEach(shotGroups -> {
                            shotGroups.getShots().forEach(shot -> {
                                if (Objects.equals(shot.getId(), imageTask.getContentId())) {
                                    resourceData.set(JSON.toJSONString(shot));
                                }
                            });
                        });

                    }
                    if(Objects.equals(imageTask.getContentType(), 3)){
                        RoleSaveReq roleSaveReq = JSON.parseObject(contentData, RoleSaveReq.class);
                        assert roleSaveReq != null;
                        roleSaveReq.getCharacters().forEach(character -> {
                            if (Objects.equals(character.getCharID(), imageTask.getContentId())) {
                                resourceData.set(JSON.toJSONString(character));
                            }
                        });
                    }
                }


                // 保存收藏记录
                AiResourceFavoritePo favoritePo = new AiResourceFavoritePo();
                // 生成唯一code
                String uniqueCode = IdUtil.fastSimpleUUID();
                favoritePo.setCode(uniqueCode);
                favoritePo.setUserId(userId);
                favoritePo.setSessionId(imageTask.getSessionId());
                favoritePo.setResourceId(String.valueOf(imageTaskId));
                favoritePo.setResourceType(ResourceTypeEnum.IMAGE.getCode());
                favoritePo.setResourceSubtype(imageTask.getContentType());
                favoritePo.setResourceName("图片_" + imageTaskId);
                favoritePo.setResourceUrl(imageTask.getImageResult());
                favoritePo.setResourceData(resourceData.get());
                favoritePo.setCreateTime(new Date());
                favoritePo.setDelFlag(0);
                save(favoritePo);

                successCount++;
                log.info("用户 {} 成功收藏资源，ID: {}, 生成唯一编码: {}", userId, imageTaskId, uniqueCode);
            } catch (Exception e) {
                failCount++;
                failReasons.add("收藏失败，ID: " + imageTaskId + "，原因: " + e.getMessage());
                log.error("收藏资源失败，ID: {}, 原因: {}", imageTaskId, e.getMessage(), e);
            }
        }

        log.info("用户 {} 批量收藏资源完成，成功: {}，失败: {}", userId, successCount, failCount);
        
        // 如果全部失败，抛出异常
        if (successCount == 0 && failCount > 0) {
            throw new BizException("批量收藏全部失败: " + String.join("; ", failReasons));
        }
        
        // 如果部分成功，返回部分成功的信息
        if (failCount > 0) {
            return SingleResponse.of("部分收藏成功，成功: " + successCount + "，失败: " + failCount);
        }
        
        return SingleResponse.buildSuccess();
    }

    /**
     * 批量移除资源收藏
     *
     * @param userId 用户ID
     * @param req    批量移除请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response batchRemoveFavorite(String userId, BatchRemoveFavoriteReq req) {
        if (Objects.isNull(userId) || Objects.isNull(req) || Objects.isNull(req.getResourceIds()) || req.getResourceIds().isEmpty()) {
            throw new IllegalArgumentException("参数无效");
        }

        int successCount = 0;
        int failCount = 0;
        List<String> failReasons = new ArrayList<>();

        for (Long resourceCode : req.getResourceIds()) {
            try {
                // 查询收藏记录
                AiResourceFavoritePo favoritePo = getOne(new LambdaQueryWrapper<AiResourceFavoritePo>()
                        .eq(AiResourceFavoritePo::getUserId, userId)
                        .eq(AiResourceFavoritePo::getResourceId, resourceCode)
                        .eq(AiResourceFavoritePo::getDelFlag, 0)
                        .last("Limit 1"));

                if (Objects.isNull(favoritePo)) {
                    failCount++;
                    failReasons.add("未找到收藏记录，编码: " + resourceCode);
                    continue;
                }

                // 逻辑删除
                removeById(favoritePo.getId());

                ResourceTypeEnum resourceType = ResourceTypeEnum.getByCode(favoritePo.getResourceType());
                String typeDesc = resourceType != null ? resourceType.getDesc() : "未知类型";

                successCount++;
                log.info("用户 {} 成功取消收藏资源，类型: {}, 唯一编码: {}", userId, typeDesc, resourceCode);
            } catch (Exception e) {
                failCount++;
                failReasons.add("取消收藏失败，编码: " + resourceCode + "，原因: " + e.getMessage());
                log.error("取消收藏资源失败，编码: {}, 原因: {}", resourceCode, e.getMessage(), e);
            }
        }

        log.info("用户 {} 批量取消收藏资源完成，成功: {}，失败: {}", userId, successCount, failCount);
        
        // 如果全部失败，抛出异常
        if (successCount == 0 && failCount > 0) {
            throw new BizException("批量取消收藏全部失败: " + String.join("; ", failReasons));
        }
        
        // 如果部分成功，返回部分成功的信息
        if (failCount > 0) {
            return SingleResponse.of("部分取消收藏成功，成功: " + successCount + "，失败: " + failCount);
        }
        return SingleResponse.buildSuccess();
    }
} 