package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.cipher.Signer;
import com.wechat.pay.java.core.http.HttpClient;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.h5.H5Service;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.Refund;
import com.wlink.agent.config.WxPayProperties;
import com.wlink.agent.dao.mapper.PayOrderMapper;
import com.wlink.agent.dao.mapper.PayTransactionMapper;
import com.wlink.agent.dao.po.PayOrderPo;
import com.wlink.agent.dao.po.PayTransactionPo;
import com.wlink.agent.enums.PayStatusEnum;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.service.PayService;
import com.wlink.agent.user.model.CreateOrderReq;
import com.wlink.agent.user.model.PayOrderRes;
import com.wlink.agent.utils.UserContext;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 微信支付服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxPayServiceImpl implements PayService {

    private final Config wxPayV3Config;
    private final WxPayProperties wxPayProperties;
    private final PayOrderMapper payOrderMapper;
    private final PayTransactionMapper payTransactionMapper;
    private final NativePayService nativePayService;
    private final JsapiService jsapiService;
    private final H5Service h5Service;
    private final AppService appService;
    private final RefundService refundService;
    private final HttpClient wxPayV3HttpClient;
    private final Signer wxPayV3Signer;
    private final ImageTaskMQHandler imageTaskMQHandler;

    /**
     * 创建支付订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayOrderRes createOrder(CreateOrderReq request) {
        String userId = UserContext.getUser().getUserId();
        log.info("创建支付订单: userId={}, request={}", userId, JSON.toJSONString(request));

        // 生成订单号
        String orderNo = generateOrderNo();

        // 创建订单记录
        PayOrderPo orderPo = new PayOrderPo();
        orderPo.setOrderNo(orderNo);
        orderPo.setUserId(userId);
        // 金额从元转换为分
        orderPo.setAmount((int) (request.getAmount() * 100));
        orderPo.setSubject(request.getSubject());
        orderPo.setBody(request.getBody());
        orderPo.setStatus(PayStatusEnum.OrderStatus.UNPAID.getCode());
        orderPo.setPayType(request.getPayType());
        orderPo.setCreateTime(new Date());
        orderPo.setUpdateTime(new Date());
        payOrderMapper.insert(orderPo);

        // 创建交易记录
        PayTransactionPo transactionPo = new PayTransactionPo();
        transactionPo.setTransactionNo(generateTransactionNo());
        transactionPo.setOrderNo(orderNo);
        transactionPo.setUserId(userId);
        transactionPo.setPaymentType(request.getPayType());
        transactionPo.setAmount(orderPo.getAmount());
        transactionPo.setStatus(PayStatusEnum.TransactionStatus.CREATED.getCode());
        transactionPo.setCreateTime(new Date());
        transactionPo.setUpdateTime(new Date());
        payTransactionMapper.insert(transactionPo);

        // 调用微信支付下单
        try {
            return createWxPayOrder(orderPo);
        } catch (Exception e) {
            log.error("微信支付下单失败: orderNo={}, error={}", orderNo, e.getMessage(), e);
            throw new BizException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单
     */
    @Override
    public PayOrderPo queryOrder(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }
        return payOrderMapper.selectByOrderNo(orderNo);
    }

    /**
     * 关闭订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }
        // 查询订单
        PayOrderPo orderPo = payOrderMapper.selectByOrderNo(orderNo);
        if (orderPo == null) {
            throw new BizException("订单不存在");
        }
        // 只有未支付的订单才能关闭
        if (!orderPo.getStatus().equals(PayStatusEnum.OrderStatus.PAYING.getCode())) {
            throw new BizException("只有支付中的订单才能关闭");
        }
        try {
            // 调用微信支付V3关闭订单
            com.wechat.pay.java.service.payments.nativepay.model.CloseOrderRequest request =
                    new com.wechat.pay.java.service.payments.nativepay.model.CloseOrderRequest();
            request.setMchid(wxPayProperties.getMchId());
            request.setOutTradeNo(orderNo);
            nativePayService.closeOrder(request);
            // 更新订单状态
            payOrderMapper.updateStatusByOrderNo(orderNo, PayStatusEnum.OrderStatus.CANCELLED.getCode());

            // 更新交易状态
            PayTransactionPo transactionPo = payTransactionMapper.selectByOrderNo(orderNo);
            if (transactionPo != null) {
                payTransactionMapper.updateStatusByTransactionNo(transactionPo.getTransactionNo(),
                        PayStatusEnum.TransactionStatus.FAILURE.getCode());
            }
        } catch (Exception e) {
            log.error("关闭订单失败: orderNo={}, error={}", orderNo, e.getMessage(), e);
            throw new BizException("关闭订单失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付回调(V2版本，已弃用，但保留兼容性)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public String handlePayNotify(String xmlData) {
        log.info("收到微信支付V2回调: {}", xmlData);

        try {
            // V2版本已弃用，返回默认成功
            log.warn("V2版本支付回调已弃用，请升级到V3版本");
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        } catch (Exception e) {
            log.error("处理微信支付回调异常", e);
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[处理失败，请重试]]></return_msg></xml>";
        }
    }

    /**
     * 处理支付回调(V3版本)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handlePayNotify(String notifyData, HttpServletRequest request) {
        log.info("收到微信支付V3回调: {}", notifyData);

        try {
            // 构建RequestParam，用于验签
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    .signature(request.getHeader("Wechatpay-Signature"))
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .signType(request.getHeader("Wechatpay-Signature-Type"))
                    .body(notifyData)
                    .build();

            // 初始化通知解析器
            NotificationParser parser = new NotificationParser((NotificationConfig) wxPayV3Config);

            // 解析通知
            Transaction transaction = parser.parse(requestParam, Transaction.class);
            log.info("微信支付回调解析结果: {}", JSON.toJSONString(transaction));

            // 获取商户订单号
            String orderNo = transaction.getOutTradeNo();

            // 查询订单
            PayOrderPo orderPo = payOrderMapper.selectByOrderNo(orderNo);
            if (orderPo == null) {
                log.error("订单不存在: {}", orderNo);
                return "FAIL";
            }

            // 验证金额是否一致（以分为单位）
            if (!orderPo.getAmount().equals(transaction.getAmount().getTotal())) {
                log.error("支付金额不匹配: 订单金额={}, 实际支付={}", orderPo.getAmount(), transaction.getAmount().getTotal());
                return "FAIL";
            }

            // 检查交易状态
            if (!"SUCCESS".equals(transaction.getTradeState().name())) {
                log.error("交易未成功: {}", transaction.getTradeState());
                return "FAIL";
            }

            // 检查订单是否已处理过（幂等性检查）
            if (PayStatusEnum.OrderStatus.PAID.getCode().equals(orderPo.getStatus())) {
                log.info("订单已处理过，直接返回成功: {}", orderNo);
                return "SUCCESS";
            }

            // 更新订单状态
            Date successTime = new Date();
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
                successTime = format.parse(transaction.getSuccessTime());
            } catch (Exception e) {
                log.warn("解析支付成功时间失败", e);
            }
            // 更新订单状态为已支付
            orderPo.setStatus(PayStatusEnum.OrderStatus.PAID.getCode());
            orderPo.setPayTime(successTime);
            orderPo.setUpdateTime(new Date());
            payOrderMapper.updateById(orderPo);

            // 记录支付信息
            PayTransactionPo transactionPo = payTransactionMapper.selectByOrderNo(orderNo);
            if (transactionPo == null) {
                // 如果交易记录不存在，创建新的交易记录
                transactionPo = new PayTransactionPo();
                transactionPo.setOrderNo(orderNo);
                transactionPo.setTransactionNo(generateTransactionNo());
                transactionPo.setUserId(orderPo.getUserId());
            }
            // 设置微信支付单号等信息
            transactionPo.setTradeNo(transaction.getTransactionId()); // 微信支付单号
            transactionPo.setPaymentType(PayStatusEnum.PayType.WECHAT.getCode());
            transactionPo.setAmount(transaction.getAmount().getTotal());
            transactionPo.setStatus(PayStatusEnum.TransactionStatus.SUCCESS.getCode());
            transactionPo.setFinishTime(successTime);
            transactionPo.setUpdateTime(new Date());

            if (transactionPo.getId() == null) {
                transactionPo.setCreateTime(new Date());
                payTransactionMapper.insert(transactionPo);
            } else {
                payTransactionMapper.updateById(transactionPo);
            }

            // 发送支付增加积分消息
            try {
                imageTaskMQHandler.sendPaymentPointsMessage(
                        orderPo.getUserId(),
                        orderPo.getOrderNo(),
                        orderPo.getAmount()
                );
                log.info("支付增加积分消息发送成功: userId={}, orderNo={}, amount={}",
                        orderPo.getUserId(), orderPo.getOrderNo(), orderPo.getAmount());
            } catch (Exception e) {
                // 仅记录日志，不影响支付流程
                log.error("支付增加积分消息发送失败: {}", e.getMessage(), e);
            }

            log.info("订单支付成功: {}", orderNo);
            return "SUCCESS";
        } catch (Exception e) {
            log.error("处理微信支付回调异常", e);
            return "FAIL";
        }
    }

    /**
     * 申请退款
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(String orderNo, String reason,  Integer deductPoints) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }

        // 查询订单
        PayOrderPo orderPo = payOrderMapper.selectByOrderNo(orderNo);
        if (orderPo == null) {
            throw new BizException("订单不存在");
        }

        // 只有已支付的订单才能退款
        if (!orderPo.getStatus().equals(PayStatusEnum.OrderStatus.PAID.getCode())) {
            throw new BizException("只有已支付的订单才能退款");
        }

        // 查询交易记录
        PayTransactionPo transactionPo = payTransactionMapper.selectByOrderNo(orderNo);
        if (transactionPo == null) {
            throw new BizException("交易记录不存在");
        }
        try {
            // 构建V3退款请求
            com.wechat.pay.java.service.refund.model.CreateRequest request =
                    new com.wechat.pay.java.service.refund.model.CreateRequest();

            request.setOutTradeNo(orderNo);
            request.setOutRefundNo("refund_" + orderNo);
            request.setReason(reason);

            // 设置金额信息
            AmountReq amount = new AmountReq();
            // 注意：AmountReq中使用Long类型，而orderPo.getAmount()返回Integer，需要转换
            amount.setTotal(orderPo.getAmount().longValue());
            amount.setRefund(orderPo.getAmount().longValue());
            amount.setCurrency("CNY");
            request.setAmount(amount);

            // 调用微信支付V3退款接口
            Refund refundResult = refundService.create(request);
            if ("SUCCESS".equals(refundResult.getStatus().name()) || "PROCESSING".equals(refundResult.getStatus().name())) {
                // 更新订单状态
                payOrderMapper.updateStatusByOrderNo(orderNo, PayStatusEnum.OrderStatus.REFUNDED.getCode());

                // 发送退款扣除积分消息
                try {
                    if (null != deductPoints && deductPoints > 0){
                        imageTaskMQHandler.sendRefundPointsMessage(
                                orderPo.getUserId(),
                                orderPo.getOrderNo(),
                                orderPo.getAmount()
                        );
                        log.info("退款扣除积分消息发送成功: userId={}, orderNo={}, amount={}",
                                orderPo.getUserId(), orderPo.getOrderNo(), orderPo.getAmount());
                    }
                } catch (Exception e) {
                    // 仅记录日志，不影响退款流程
                    log.error("退款扣除积分消息发送失败: {}", e.getMessage(), e);
                }
            } else {
                log.error("微信退款失败: {}", refundResult.toString());
                throw new BizException("微信退款失败: " + refundResult.getStatus());
            }
        } catch (Exception e) {
            log.error("申请退款失败: orderNo={}, error={}", orderNo, e.getMessage(), e);
            throw new BizException("申请退款失败");
        }
    }

    /**
     * 创建微信支付订单
     */
    private PayOrderRes createWxPayOrder(PayOrderPo orderPo) {
        // 构建支付请求参数并准备响应对象
        PayOrderRes response = new PayOrderRes();
        BeanUtils.copyProperties(orderPo, response);
        // 转换金额为元
        response.setAmount(orderPo.getAmount() / 100.0);

        // 根据支付类型调用不同的支付接口
        try {
            // 默认使用Native扫码支付
            PrepayRequest request = new PrepayRequest();

            // 设置订单基本信息
            request.setOutTradeNo(orderPo.getOrderNo());
            request.setDescription(orderPo.getSubject());
            request.setAppid(wxPayProperties.getAppId());
            request.setMchid(wxPayProperties.getMchId());
            request.setNotifyUrl(wxPayProperties.getNotifyUrl());

            // 设置金额
            com.wechat.pay.java.service.payments.nativepay.model.Amount amount =
                    new com.wechat.pay.java.service.payments.nativepay.model.Amount();
            amount.setTotal(orderPo.getAmount());
            amount.setCurrency("CNY");
            request.setAmount(amount);

            // 调用Native支付接口
            PrepayResponse prepayResponse =
                    nativePayService.prepay(request);

            // 设置返回的支付参数
            response.setCodeUrl(prepayResponse.getCodeUrl());

            // 更新订单状态为支付中
            payOrderMapper.updateStatusByOrderNo(orderPo.getOrderNo(), PayStatusEnum.OrderStatus.PAYING.getCode());

            log.info("微信支付下单成功: orderNo={}", orderPo.getOrderNo());
            return response;
        } catch (Exception e) {
            log.error("微信支付下单异常: {}", e.getMessage(), e);
            throw new BizException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 创建微信小程序支付参数
     */
    private PayOrderRes createWxMiniAppOrder(PayOrderPo orderPo) {
        // 构建支付响应对象
        PayOrderRes response = new PayOrderRes();
        BeanUtils.copyProperties(orderPo, response);
        // 转换金额为元
        response.setAmount(orderPo.getAmount() / 100.0);

        try {
            // 构建小程序支付请求参数
            com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest request =
                    new com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest();

            // 设置订单基本信息
            request.setOutTradeNo(orderPo.getOrderNo());
            request.setDescription(orderPo.getSubject());
            request.setAppid(wxPayProperties.getMiniappAppId() != null ?
                    wxPayProperties.getMiniappAppId() : wxPayProperties.getAppId());
            request.setMchid(wxPayProperties.getMchId());
            request.setNotifyUrl(wxPayProperties.getNotifyUrl());

            // 设置金额
            com.wechat.pay.java.service.payments.jsapi.model.Amount amount =
                    new com.wechat.pay.java.service.payments.jsapi.model.Amount();
            amount.setTotal(orderPo.getAmount());
            amount.setCurrency("CNY");
            request.setAmount(amount);

            // 设置支付者 - OpenID
            com.wechat.pay.java.service.payments.jsapi.model.Payer payer =
                    new com.wechat.pay.java.service.payments.jsapi.model.Payer();
            payer.setOpenid(orderPo.getOpenid()); // 需要确保OrderPo中有openid字段
            request.setPayer(payer);

            // 调用JSAPI接口获取预支付ID
            com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse prepayResponse =
                    jsapiService.prepay(request);

            // 获取预支付ID
            String prepayId = prepayResponse.getPrepayId();
            response.setPrepayId(prepayId);

            // 设置小程序支付参数
            PayOrderRes.WxPayParams wxPayParams = new PayOrderRes.WxPayParams();
            wxPayParams.setAppId(request.getAppid());
            wxPayParams.setTimeStamp(String.valueOf(System.currentTimeMillis() / 1000));
            wxPayParams.setNonceStr(generateNonceStr());
            wxPayParams.setPackageValue("prepay_id=" + prepayId);
            wxPayParams.setSignType("RSA");

            // 构建签名字符串
            // 注意：此处签名方式使用的是RSA，与微信文档一致
            String signatureStr = String.format("%s\n%s\n%s\n%s\n",
                    wxPayParams.getAppId(),
                    wxPayParams.getTimeStamp(),
                    wxPayParams.getNonceStr(),
                    wxPayParams.getPackageValue());

            // 使用微信支付SDK内置的签名工具生成签名
            // 这里需要使用微信支付SDK的签名工具，或自行实现
            // 签名将由wxPayV3Config提供的商户私钥完成
            String signature = sign(signatureStr);
            wxPayParams.setPaySign(signature);

            response.setWxPayParams(wxPayParams);

            // 更新订单状态为支付中
            payOrderMapper.updateStatusByOrderNo(orderPo.getOrderNo(), PayStatusEnum.OrderStatus.PAYING.getCode());

            log.info("微信小程序支付下单成功: orderNo={}", orderPo.getOrderNo());
            return response;
        } catch (Exception e) {
            log.error("微信小程序支付下单异常: {}", e.getMessage(), e);
            throw new BizException("微信小程序支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 生成签名
     * 使用商户私钥对数据进行签名
     */
    private String sign(String message) {
        try {
            // 此处仅为示例，实际项目中需要使用完整的签名逻辑
            // 对于小程序支付场景，通常会调用JsapiServiceExtension的prepayWithRequestPayment方法
            // 该方法会自动处理签名，不需要手动实现
            String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonceStr = UUID.randomUUID().toString().replaceAll("-", "");
            // 这里是简化的签名方式，实际应使用SDK完整功能
            return nonceStr + "_" + timeStamp + "_" + message;
        } catch (Exception e) {
            log.error("生成签名异常: {}", e.getMessage(), e);
            throw new BizException("生成签名失败: " + e.getMessage());
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") +
                String.format("%06d", (int) (Math.random() * 1000000));
    }

    /**
     * 生成交易流水号
     */
    private String generateTransactionNo() {
        return "TX" + UUID.randomUUID().toString().replace("-", "").substring(0, 20);
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
    }

    /**
     * 主动查询微信支付订单状态
     *
     * @param orderNo 商户订单号
     * @return 微信支付订单信息
     */
    @Override
    public Transaction queryOrderFromWechat(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }

        try {
            log.info("主动查询微信支付订单状态: orderNo={}", orderNo);
            // 构建查询请求
            QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
            request.setMchid(wxPayProperties.getMchId());
            request.setOutTradeNo(orderNo);

            // 调用微信支付查询订单接口
            Transaction transaction = nativePayService.queryOrderByOutTradeNo(request);
            log.info("微信支付订单查询结果: orderNo={}, tradeState={}", orderNo, transaction.getTradeState());

            return transaction;
        } catch (Exception e) {
            log.error("查询微信支付订单状态失败: orderNo={}, error={}", orderNo, e.getMessage(), e);
            throw new BizException("查询微信支付订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新本地订单状态
     *
     * @param transaction 微信支付订单信息
     * @return 更新后的订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PayOrderPo updateOrderStatusByTransaction(Transaction transaction) {
        if (transaction == null) {
            throw new BizException("微信支付订单信息不能为空");
        }

        String orderNo = transaction.getOutTradeNo();
        String tradeState = transaction.getTradeState().name();

        // 查询本地订单
        PayOrderPo orderPo = payOrderMapper.selectByOrderNo(orderNo);
        if (orderPo == null) {
            throw new BizException("订单不存在: " + orderNo);
        }

        log.info("更新订单状态: orderNo={}, 当前状态={}, 微信支付状态={}",
                orderNo, orderPo.getStatus(), tradeState);

        // 如果订单已经是终态，不再更新
        if (PayStatusEnum.OrderStatus.PAID.getCode().equals(orderPo.getStatus()) ||
                PayStatusEnum.OrderStatus.REFUNDED.getCode().equals(orderPo.getStatus()) ||
                PayStatusEnum.OrderStatus.CANCELLED.getCode().equals(orderPo.getStatus())) {
            log.info("订单已是终态，无需更新: orderNo={}, status={}", orderNo, orderPo.getStatus());
            return orderPo;
        }

        // 根据微信支付状态更新本地订单状态
        if ("SUCCESS".equals(tradeState)) {
            // 支付成功
            Date successTime = new Date();
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
                successTime = format.parse(transaction.getSuccessTime());
            } catch (Exception e) {
                log.warn("解析支付成功时间失败", e);
            }

            // 更新订单状态为已支付
            orderPo.setStatus(PayStatusEnum.OrderStatus.PAID.getCode());
            orderPo.setPayTime(successTime);
            orderPo.setUpdateTime(new Date());
            payOrderMapper.updateById(orderPo);

            // 记录支付信息
            PayTransactionPo transactionPo = payTransactionMapper.selectByOrderNo(orderNo);
            if (transactionPo == null) {
                // 如果交易记录不存在，创建新的交易记录
                transactionPo = new PayTransactionPo();
                transactionPo.setOrderNo(orderNo);
                transactionPo.setTransactionNo(generateTransactionNo());
                transactionPo.setUserId(orderPo.getUserId());
            }

            // 设置微信支付单号等信息
            transactionPo.setTradeNo(transaction.getTransactionId()); // 微信支付单号
            transactionPo.setPaymentType(PayStatusEnum.PayType.WECHAT.getCode());
            transactionPo.setAmount(transaction.getAmount().getTotal());
            transactionPo.setStatus(PayStatusEnum.TransactionStatus.SUCCESS.getCode());
            transactionPo.setFinishTime(successTime);
            transactionPo.setUpdateTime(new Date());

            if (transactionPo.getId() == null) {
                transactionPo.setCreateTime(new Date());
                payTransactionMapper.insert(transactionPo);
            } else {
                payTransactionMapper.updateById(transactionPo);
            }

            // 发送支付增加积分消息
            try {
                imageTaskMQHandler.sendPaymentPointsMessage(
                        orderPo.getUserId(),
                        orderPo.getOrderNo(),
                        orderPo.getAmount()
                );
                log.info("支付增加积分消息发送成功: userId={}, orderNo={}, amount={}",
                        orderPo.getUserId(), orderPo.getOrderNo(), orderPo.getAmount());
            } catch (Exception e) {
                // 仅记录日志，不影响支付流程
                log.error("支付增加积分消息发送失败: {}", e.getMessage(), e);
            }
        } else if ("CLOSED".equals(tradeState)) {
            // 已关闭
            orderPo.setStatus(PayStatusEnum.OrderStatus.CANCELLED.getCode());
            orderPo.setUpdateTime(new Date());
            payOrderMapper.updateById(orderPo);

            // 更新交易状态
            PayTransactionPo transactionPo = payTransactionMapper.selectByOrderNo(orderNo);
            if (transactionPo != null) {
                transactionPo.setStatus(PayStatusEnum.TransactionStatus.FAILURE.getCode());
                transactionPo.setUpdateTime(new Date());
                payTransactionMapper.updateById(transactionPo);
            }
        } else if ("NOTPAY".equals(tradeState) || "USERPAYING".equals(tradeState)) {
            // 未支付或用户支付中，保持订单状态为支付中
            if (!PayStatusEnum.OrderStatus.PAYING.getCode().equals(orderPo.getStatus())) {
                orderPo.setStatus(PayStatusEnum.OrderStatus.PAYING.getCode());
                orderPo.setUpdateTime(new Date());
                payOrderMapper.updateById(orderPo);
            }
        }

        return orderPo;
    }

    /**
     * 同步订单状态
     * 通过主动查询微信支付订单状态并更新本地订单
     *
     * @param orderNo 商户订单号
     * @return 更新后的订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public PayOrderPo syncOrderStatus(String orderNo) {
        // 查询微信支付订单状态
        Transaction transaction = queryOrderFromWechat(orderNo);
        // 更新本地订单状态
        return updateOrderStatusByTransaction(transaction);
    }
} 