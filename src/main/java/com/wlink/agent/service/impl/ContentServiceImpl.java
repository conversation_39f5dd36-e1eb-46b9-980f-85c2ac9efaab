package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wlink.agent.client.ExternalTextApiClient;
import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.dao.mapper.AiImageStyleMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiImageStylePo;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.res.ImageStyleRes;
import com.wlink.agent.model.res.TagRes;
import com.wlink.agent.service.ContentService;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.wlink.agent.client.model.text.TextApiInputs;
import com.wlink.agent.client.model.text.TextApiRequest;
import com.wlink.agent.client.model.text.TextApiResponse;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContentServiceImpl implements ContentService {

    private final AiImageStyleMapper aiImageStyleMapper;
    private final ExternalTextApiClient externalTextApiClient; 
    private final AiUsersMapper aiUsersMapper;
    private final RedissonClient redissonClient;
    private final Executor contentExecutor;

    @Override
    public List<ImageStyleRes> getAllActiveStyles() {
        log.info("Fetching all active image styles from database.");
        QueryWrapper<AiImageStylePo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 查询状态为启用的风格
        List<AiImageStylePo> stylePos = aiImageStyleMapper.selectList(queryWrapper);

        List<ImageStyleRes> styleResList = stylePos.stream()
                .map(po -> new ImageStyleRes(po.getId(), po.getStyleName(), po.getStyleCode(), po.getStyleUrl(),po.getStyleCategory(),po.getStyleDesc()))
                .collect(Collectors.toList());

        log.info("Found {} active image styles.", styleResList.size());
        return styleResList;
    }

    @Override
    public List<TagRes> getAllTags() {
        log.info("开始获取文本标签列表");

        SimpleUserInfo user = UserContext.getUser();
        String userId = user.getUserId();
        
        // 构建 Redis key
        String tagsKey = RedisKeyConstant.getKey(RedisKeyConstant.Content.USER_TAGS, userId);
        RBucket<List<TagRes>> tagsBucket = redissonClient.getBucket(tagsKey);
        
        // 尝试从 Redis 获取标签数据
        List<TagRes> tagResList = tagsBucket.get();
        
        if (tagResList != null && !tagResList.isEmpty()) {
            log.info("从 Redis 缓存中获取到 {} 个标签项", tagResList.size());
            
            // 检查是否需要异步更新数据
            String lastUpdateKey = RedisKeyConstant.getKey(RedisKeyConstant.Content.USER_TAGS_LAST_UPDATE, userId);
            RBucket<Long> lastUpdateBucket = redissonClient.getBucket(lastUpdateKey);
            Long lastUpdateTime = lastUpdateBucket.get();
            long currentTime = System.currentTimeMillis();
            if (lastUpdateTime == null || 
                (currentTime - lastUpdateTime > RedisKeyConstant.Content.TAGS_MIN_UPDATE_INTERVAL)) {
                // 更新最后更新时间
                lastUpdateBucket.set(currentTime);
                // 异步更新 Redis 中的标签数据
                contentExecutor.execute(() -> updateTagsAsync(userId));
                log.info("触发异步更新标签数据，距离上次更新已超过5分钟");
            } else {
                log.info("上次更新时间距离现在不足5分钟，跳过异步更新");
            }
            
            // 随机排序并返回最多5条数据
            Collections.shuffle(tagResList);
            List<TagRes> randomTagsList = tagResList.size() <= 5 ? tagResList : tagResList.subList(0, 5);
            log.info("随机返回 {} 条标签数据", randomTagsList.size());
            return randomTagsList;
        }
        
        log.info("Redis 中无缓存数据，从 API 获取标签数据");
        // Redis 中没有数据，调用 API 获取
        tagResList = getTagsFromApi(userId);
        
        if (tagResList != null && !tagResList.isEmpty()) {
            // 将数据存入 Redis
            tagsBucket.set(tagResList, RedisKeyConstant.Content.TAGS_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("成功将 {} 个标签项存入 Redis 缓存", tagResList.size());
            
            // 记录更新时间
            String lastUpdateKey = RedisKeyConstant.getKey(RedisKeyConstant.Content.USER_TAGS_LAST_UPDATE, userId);
            RBucket<Long> lastUpdateBucket = redissonClient.getBucket(lastUpdateKey);
            lastUpdateBucket.set(System.currentTimeMillis());
            
            // 随机排序并返回最多5条数据
            Collections.shuffle(tagResList);
            List<TagRes> randomTagsList = tagResList.size() <= 5 ? tagResList : tagResList.subList(0, 5);
            log.info("随机返回 {} 条标签数据", randomTagsList.size());
            return randomTagsList;
        }
        
        throw new BizException("无法获取标签数据");
    }
    
    /**
     * 异步更新用户标签数据
     * @param userId 用户ID
     */
    @Override
    @Async("contentExecutor")
    public void updateTagsAsync(String userId) {
        log.info("异步更新用户(ID:{})的标签数据开始，更新间隔为5分钟", userId);
        try {
            // 从 API 获取标签数据
            List<TagRes> tagResList = getTagsFromApi(userId);
            
            if (tagResList != null && !tagResList.isEmpty()) {
                // 构建 Redis key
                String tagsKey = RedisKeyConstant.getKey(RedisKeyConstant.Content.USER_TAGS, userId);
                RBucket<List<TagRes>> tagsBucket = redissonClient.getBucket(tagsKey);
                
                // 更新 Redis 中的数据
                tagsBucket.set(tagResList, RedisKeyConstant.Content.TAGS_EXPIRE_SECONDS, TimeUnit.SECONDS);
                
                // 更新最后更新时间
                String lastUpdateKey = RedisKeyConstant.getKey(RedisKeyConstant.Content.USER_TAGS_LAST_UPDATE, userId);
                RBucket<Long> lastUpdateBucket = redissonClient.getBucket(lastUpdateKey);
                lastUpdateBucket.set(System.currentTimeMillis());
                
                log.info("成功异步更新用户(ID:{})的 {} 个标签项到 Redis 缓存", userId, tagResList.size());
            } else {
                log.warn("异步更新标签数据失败，API 返回空数据");
            }
        } catch (Exception e) {
            log.error("异步更新标签数据时发生异常", e);
        }
    }
    
    /**
     * 从外部 API 获取标签数据
     * @param userId 用户ID
     * @return 标签数据列表
     */
    private List<TagRes> getTagsFromApi(String userId) {
        try {
            AiUsersPo aiUsersPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, userId));
            
            if (aiUsersPo == null) {
                log.warn("用户(ID:{})不存在", userId);
                return null;
            }
            
            // 构建请求参数
            TextApiInputs inputs = TextApiInputs.builder()
                    .location(aiUsersPo.getRegion())
                    .job(aiUsersPo.getJob())
                    .age(aiUsersPo.getAge())
                    .gender(aiUsersPo.getGender())
                    .build();
            
            // 生成随机用户ID，或者这里可以使用固定值
            TextApiRequest request = TextApiRequest.builder()
                    .inputs(inputs)
                    .user(userId)
                    .build();
            log.info("调用外部文本API获取标签数据，请求参数: {}", request);
            // 调用API获取响应
            TextApiResponse response = externalTextApiClient.requestTextGenerationWithOkHttp(request);
            // 检查响应和数据是否有效
            if (response != null && response.getData() != null && 
                response.getData().getOutputs() != null &&
                    CollUtil.isNotEmpty(response.getData().getOutputs().getText())) {
                List<TagRes> tagResList = new ArrayList<>();
                List<TextApiResponse.DataDTO.OutputsDTO.TextDTO> text = response.getData().getOutputs().getText();
                for (TextApiResponse.DataDTO.OutputsDTO.TextDTO item : text) {
                    TagRes tagRes = new TagRes();
                    tagRes.setName(item.getName());
                    tagRes.setPrompt(item.getPrompt());
                    tagRes.setCategory(item.getCategory()); // 使用category作为描述
                    tagResList.add(tagRes);
                }
                log.info("成功转换 {} 个标签项", tagResList.size());
                return tagResList;
            } else {
                log.warn("API响应数据无效或格式不符合预期");
                return null;
            }
        } catch (Exception e) {
            log.error("处理标签数据时发生未预期的异常", e);
            return null;
        }
    }
}