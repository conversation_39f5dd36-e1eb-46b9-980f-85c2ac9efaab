package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.utils.OssUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@RefreshScope
@Service
@RequiredArgsConstructor
public class AudioFaceFeatureService {

    private final OkHttpClient okHttpClient;
    private final OssUtils ossUtils;

    @Value("${audio2face.api.url:http://***************:13621/audio2face}")
    private String apiUrl;

    @Value("${audio2face.api.password:weiling123}")
    private String password;

    @Value("${audio2face.api.ctrl:2}")
    private String ctrl;

    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final int RETRY_DELAY_MS = 1000;

    public String getFaceFeatures(String audioFile,String roomId) {
        // 构建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("password", password)
                .addFormDataPart("audioFile", audioFile,
                        RequestBody.create(MediaType.parse("audio/wav"), new File(audioFile)))
                .addFormDataPart("ctrl", ctrl)
                .build();

        Request request = new Request.Builder()
                .url(apiUrl)
                .post(requestBody)
                .build();

        Exception lastException = null;

        // 重试逻辑
        for (int attempt = 0; attempt < MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                Response response = okHttpClient.newCall(request).execute();
                if (!response.isSuccessful()) {
                    throw new BizException("API call failed with code: " + response.code());
                }
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject jsonResponse = JSON.parseObject(responseBody);

                if (!jsonResponse.getBooleanValue("success")) {
                    throw new BizException("API returned error: " + jsonResponse.getString("errMessage"));
                }
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data == null) {
                    throw new BizException("API response missing data field");
                }
                String features = data.getString("faceFeatures");

                // 生成文件名
                String fileName = generateFileName();

                // 写入临时文件
                Path tempFile = writeFeaturestoFile(features, fileName);

                // 上传到OSS并返回URL
                String ossUrl = uploadToOss(tempFile, fileName,roomId);

                // 删除临时文件
                Files.deleteIfExists(tempFile);

                return ossUrl;

            } catch (Exception e) {
                lastException = e;
                log.error("Failed to get face features, attempt: {},err:{}", attempt + 1, e.getMessage());
                if (attempt < MAX_RETRY_ATTEMPTS - 1) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BizException("Retry interrupted", ie);
                    }
                }
            }
        }

        throw new BizException("Failed after " + MAX_RETRY_ATTEMPTS + " attempts", lastException);
    }

    private String generateFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return "face_features_" + timestamp + ".txt";
    }

    private Path writeFeaturestoFile(String features, String fileName) throws Exception {
        Path tempFile = Files.createTempFile(fileName, null);
        // 使用UTF-8编码写入文件
        try (Writer writer = new OutputStreamWriter(
                Files.newOutputStream(tempFile), StandardCharsets.UTF_8)) {
            writer.write(features);
        }
        return tempFile;
    }

    private String uploadToOss(Path filePath, String fileName,String roomId) throws Exception {
        String audioToFacePath = ossUtils.getAudioToFacePath(roomId);
        String ossPath = audioToFacePath + fileName;
        return ossUtils.uploadStream(Files.newInputStream(filePath), ossPath);
    }
}