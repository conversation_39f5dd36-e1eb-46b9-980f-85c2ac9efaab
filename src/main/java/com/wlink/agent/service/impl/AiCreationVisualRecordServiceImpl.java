package com.wlink.agent.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiCreationVisualRecordMapper;
import com.wlink.agent.dao.mapper.AiImageStyleMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiCreationVisualRecordPo;
import com.wlink.agent.dao.po.AiImageStylePo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.model.dto.VisualRecordDto;
import com.wlink.agent.model.req.RenderTriggerReq;
import com.wlink.agent.model.req.UpdateVisualRecordStatusReq;
import com.wlink.agent.model.req.VisualSaveReq;
import com.wlink.agent.model.res.AiCreationVisualRecordRes;
import com.wlink.agent.model.res.AiCreationVisualRecordListRes;
import com.wlink.agent.service.AiCreationVisualRecordService;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.service.VisualRenderService;
import com.wlink.agent.utils.I18nMessageUtils;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * AI创作视觉记录服务实现类
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class AiCreationVisualRecordServiceImpl extends ServiceImpl<AiCreationVisualRecordMapper, AiCreationVisualRecordPo> implements AiCreationVisualRecordService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Lazy
    private final VisualRenderService visualRenderService;
    private final UserPointsService userPointsService;
    private final RedissonClient redissonClient;
    private final AiUsersMapper aiUsersMapper;
    private final AiImageTaskQueueMapper aiImageTaskQueueMapper;
    private final AiCreationSessionMapper aiCreationSessionMapper;
    private final AiImageStyleMapper aiImageStyleMapper;

    // 视频生成所需的积分
    private static final int VIDEO_GENERATION_POINTS = 300;

    // 视频生成锁前缀
    private static final String VIDEO_RENDERING_LOCK_PREFIX = "video_rendering_lock:";
    // 锁等待时间和释放时间（单位：秒）
    private static final int LOCK_WAIT_TIME = 0; // 不等待，立即返回
    private static final int LOCK_LEASE_TIME = 300; // 锁自动释放时间，5分钟

    //平台地址
    @Value("${platform.url:https://dev.neodomain.cn}")
    private String platformUrl;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveVisualRecord(String conversationId, VisualSaveReq visualData, String userId) {
        try {
            // 将视觉数据转换为JSON字符串
            String contentData = objectMapper.writeValueAsString(visualData);
            // 创建新的视觉记录
            AiCreationVisualRecordPo visualRecord = new AiCreationVisualRecordPo();
            visualRecord.setCoverId(visualData.getCover().getId());
            visualRecord.setVisualRecordCode(IdUtil.fastSimpleUUID());
            visualRecord.setShareToken(IdUtil.fastSimpleUUID());
            visualRecord.setSessionId(conversationId);
            visualRecord.setContentData(contentData);
            visualRecord.setPublishStatus(0); // 默认未发布
            visualRecord.setTitle(visualData.getCover().getTitle());
            visualRecord.setCoverImage(visualData.getCover().getImage());
            visualRecord.setSubtitle(visualData.getCover().getSubtitle2());
            visualRecord.setCreateTime(new Date());
            visualRecord.setUpdateTime(new Date());
            visualRecord.setUserId(userId);
            visualRecord.setDelFlag(0);
            // 保存视觉记录
            save(visualRecord);

            return visualRecord.getId();
        } catch (Exception e) {
            log.error("保存视觉记录失败", e);
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_SAVE_FAILED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_SAVE_FAILED.getMsg()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updatePublishStatus(UpdateVisualRecordStatusReq req) {
        String userId = UserContext.getUser().getUserId();
        Long visualRecordId = req.getVisualRecordId();
        boolean publish = req.getPublish();
        
        // 查询视觉记录
        AiCreationVisualRecordPo visualRecord = getById(visualRecordId);
        if (visualRecord == null) {
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getMsg()));
        }
        
        // 检查权限
        if (visualRecord.getUserId() != null && !visualRecord.getUserId().equals(userId)) {
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_PERMISSION_DENIED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_PERMISSION_DENIED.getMsg()));
        }
        
        String shareToken = null;
        if (publish) {
            // 发布操作
            if (visualRecord.getPublishStatus() == 1) {
                log.info("Visual record {} already published.", visualRecordId);
                return visualRecord.getShareToken(); 
            }
            shareToken = UUID.randomUUID().toString().replace("-", "");
            visualRecord.setPublishStatus(1);
            visualRecord.setShareToken(shareToken);
            visualRecord.setPublishTime(new Date());
            log.info("Publishing visual record {}. New share token: {}", visualRecordId, shareToken);
        } else {
            // 取消发布操作
            if (visualRecord.getPublishStatus() == 0) {
                 log.info("Visual record {} already unpublished.", visualRecordId);
                return null; // 已经是未发布状态
            }
            visualRecord.setPublishStatus(0);
            visualRecord.setShareToken(null); // 清除token
            visualRecord.setPublishTime(null); // 清除发布时间
            log.info("Unpublishing visual record {}", visualRecordId);
        }
        
        visualRecord.setUpdateTime(new Date());
        boolean success = updateById(visualRecord);
        
        if (!success) {
             log.error("Failed to update publish status for visual record {}", visualRecordId);
             throw new BizException(ErrorCodeEnum.VISUAL_RECORD_PUBLISH_UPDATE_FAILED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_PUBLISH_UPDATE_FAILED.getMsg()));
        }
        
        return shareToken; // 返回 token (发布时) 或 null (取消发布时)
    }

    @Override
    public List<AiCreationVisualRecordRes> getVisualRecordsBySession(String conversationId) {
        LambdaQueryWrapper<AiCreationVisualRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCreationVisualRecordPo::getSessionId, conversationId)
               .eq(AiCreationVisualRecordPo::getDelFlag, 0)
               .orderByDesc(AiCreationVisualRecordPo::getCreateTime);
        
        List<AiCreationVisualRecordPo> poList = list(wrapper);
        if (poList == null || poList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // Map PO to Res
        return poList.stream().map(this::mapPoToRes).sorted(Comparator.comparing(AiCreationVisualRecordRes::getSubtitle2)).collect(Collectors.toList());
    }

    @Override
    public AiCreationVisualRecordRes getVisualRecordByShareToken(String shareToken) {
        LambdaQueryWrapper<AiCreationVisualRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCreationVisualRecordPo::getShareToken, shareToken)
               .eq(AiCreationVisualRecordPo::getDelFlag, 0);
        
        AiCreationVisualRecordPo po = getOne(wrapper);
        if (po == null || po.getPublishStatus() != 1) {
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getMsg(), new Object[]{"或未发布"})
            );
        }
        // Map PO to Res
        AiCreationVisualRecordRes aiCreationVisualRecordRes = mapPoToRes(po);

        AiUsersPo aiUsersPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>().eq(AiUsersPo::getUserId, po.getUserId()));
        if (aiUsersPo != null) {
            aiCreationVisualRecordRes.setAvatarUrl(MediaUrlPrefixUtil.getMediaUrl(aiUsersPo.getAvatar()));
            aiCreationVisualRecordRes.setNickname(aiUsersPo.getNickname());
        }
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, po.getSessionId())
                .orderByDesc(AiCreationSessionPo::getCreateTime)
                .last("limit 1"));
        if (null != aiCreationSessionPo){
            aiCreationVisualRecordRes.setImageSize(aiCreationSessionPo.getImageSize());
        }
        return aiCreationVisualRecordRes;
    }

    @Override
    public AiCreationVisualRecordRes getVisualRecordByIdWithCheck(Long visualRecordId) {
        boolean hasPermission = checkAccessPermission(visualRecordId);
        if (!hasPermission) {
            // Need to differentiate between not found and no permission
            AiCreationVisualRecordPo tempRecord = getById(visualRecordId);
            if (tempRecord == null) {
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getMsg()));
            } else {
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_PERMISSION_DENIED.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_PERMISSION_DENIED.getMsg()));
            }
        }
        // If permission ok, fetch and map
        AiCreationVisualRecordPo po = getById(visualRecordId);
         if (po == null) { // Should not happen if checkAccessPermission passed, but defensive check
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getMsg()));
        }
        return mapPoToRes(po);
    }
    @Override
    public List<AiCreationVisualRecordListRes> getPublishedVisualRecordsByCode(String token) {
        // 1. Find the record by code to get the sessionId
        AiCreationVisualRecordPo referenceRecord = lambdaQuery()
                .eq(AiCreationVisualRecordPo::getShareToken, token)
                .eq(AiCreationVisualRecordPo::getDelFlag, 0)
                .last("Limit 1")
                .one();

        if (referenceRecord == null) {
            log.warn("Reference visual record not found for code: {}", token);
            // Depending on requirements, could return empty list or throw exception
            return Collections.emptyList();
            // throw new BizException("视觉记录不存在: " + visualRecordCode);
        }
        String sessionId = referenceRecord.getSessionId();

        // 2. Find all published records for that sessionId
        LambdaQueryWrapper<AiCreationVisualRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCreationVisualRecordPo::getSessionId, sessionId)
                .eq(AiCreationVisualRecordPo::getPublishStatus, 1) // Published status
                .eq(AiCreationVisualRecordPo::getDelFlag, 0)
                .orderByDesc(AiCreationVisualRecordPo::getPublishTime); // Order by publish time

        List<AiCreationVisualRecordPo> poList = list(wrapper);
        if (poList == null || poList.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. Map PO to Res
        return poList.stream().map(this::mapPoToListRes).sorted(Comparator.comparing(AiCreationVisualRecordListRes::getSubtitle2)).collect(Collectors.toList());
    }
    @Override
    public List<AiCreationVisualRecordListRes> getTop10PublishedRecords() {
        log.info("Fetching top 10 published records (grouped by session)");
        
        // 1. Fetch all published, non-deleted records
        LambdaQueryWrapper<AiCreationVisualRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCreationVisualRecordPo::getPublishStatus, 1) // Published status
                .eq(AiCreationVisualRecordPo::getDelFlag, 0)
                .orderByDesc(AiCreationVisualRecordPo::getPublishTime); // Order by publish time
        
        List<AiCreationVisualRecordPo> allPublishedPoList = list(wrapper);
        
        if (allPublishedPoList == null || allPublishedPoList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 2. Group by sessionId
        Map<String, List<AiCreationVisualRecordPo>> groupedBySession = allPublishedPoList.stream()
                .collect(Collectors.groupingBy(AiCreationVisualRecordPo::getSessionId));
        
        // 3. For each group, get the first record sorted by subtitle and the most recent timestamp
        List<AiCreationVisualRecordPo> filteredRecords = new ArrayList<>();
        
        for (Map.Entry<String, List<AiCreationVisualRecordPo>> entry : groupedBySession.entrySet()) {
            List<AiCreationVisualRecordPo> sessionRecords = entry.getValue();
            
            // Sort by subtitle
            sessionRecords.sort(Comparator.comparing(AiCreationVisualRecordPo::getSubtitle));
            
            // Take the first record
            AiCreationVisualRecordPo firstRecord = sessionRecords.get(0);
            
            // Find the record with the most recent publishTime
            AiCreationVisualRecordPo mostRecentRecord = sessionRecords.stream()
                    .max(Comparator.comparing(AiCreationVisualRecordPo::getPublishTime))
                    .orElse(firstRecord);
            
            // Update the first record with the most recent timestamp
            firstRecord.setPublishTime(mostRecentRecord.getPublishTime());
            
            filteredRecords.add(firstRecord);
        }
        
        // 4. Sort by publish time descending and limit to 10
        return filteredRecords.stream()
                .sorted(Comparator.comparing(AiCreationVisualRecordPo::getPublishTime).reversed())
                .limit(10)
                .map(this::mapPoToListRes)
                .collect(Collectors.toList());
    }

    @Override
    public boolean checkAccessPermission(Long visualRecordId) {
        String userId = UserContext.getUser().getUserId();
        AiCreationVisualRecordPo visualRecord = getById(visualRecordId);
        if (visualRecord == null) {
            return false;
        }
        
        // 已发布的记录允许任何用户访问
        if (visualRecord.getPublishStatus() == 1) {
            return true;
        }
        // 未发布的记录只允许创建者访问
        return visualRecord.getUserId() != null && visualRecord.getUserId().equals(userId);
    }

    /**
     * Helper method to map PO to Res DTO.
     * Includes URL prefix logic.
     * @param po The AiCreationVisualRecordPo object.
     * @return The AiCreationVisualRecordRes object, or null if po is null.
     */
    @SneakyThrows
    private AiCreationVisualRecordRes mapPoToRes(AiCreationVisualRecordPo po) {
        if (po == null) {
            return null;
        }
        AiCreationVisualRecordRes res = BeanUtil.copyProperties(po, AiCreationVisualRecordRes.class);
        AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                .eq(AiImageTaskQueuePo::getSessionId, res.getSessionId())
                .eq(AiImageTaskQueuePo::getContentType, 0)
                .eq(StringUtils.isNotBlank(po.getCoverId()),AiImageTaskQueuePo::getContentId, po.getCoverId())
                .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                .last("limit 1"));
        if  (aiImageTaskQueuePo != null) {
            res.setCoverImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
        }else{
            //判断res.getCoverImage()是不是.PNG结尾
            res.setCoverImage(MediaUrlPrefixUtil.getMediaUrl(res.getCoverImage()));

        }
        String contentData = res.getContentData();
        VisualSaveReq visualSaveReq = JSON.parseObject(contentData, VisualSaveReq.class);
        if  (aiImageTaskQueuePo != null) {
            visualSaveReq.getCover().setImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
        }else{
            visualSaveReq.getCover().setImage(MediaUrlPrefixUtil.getMediaUrl(visualSaveReq.getCover().getImage()));
        }
        visualSaveReq.getBackgroundMusic().setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(visualSaveReq.getBackgroundMusic().getAudioUrl()));
        visualSaveReq.getShots().forEach(shot -> {
            shot.setSceneImage(MediaUrlPrefixUtil.getMediaUrl(shot.getSceneImage()));
            shot.setAudioClip(MediaUrlPrefixUtil.getMediaUrl(shot.getAudioClip()));
        });

        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, res.getSessionId())
                .orderByDesc(AiCreationSessionPo::getCreateTime)
                .last("limit 1"));
        if (null != aiCreationSessionPo){
            res.setImageSize(aiCreationSessionPo.getImageSize());
        }

        res.setSubtitle2(po.getSubtitle());
        res.setShareUrl(platformUrl  + "/share?token=" + po.getShareToken());
        String contentDataStr = objectMapper.writeValueAsString(visualSaveReq);
        res.setContentData(contentDataStr);
        return res;
    }
    @SneakyThrows
    private AiCreationVisualRecordListRes mapPoToListRes(AiCreationVisualRecordPo po) {
        if (po == null) {
            return null;
        }
        AiCreationVisualRecordListRes res = BeanUtil.copyProperties(po, AiCreationVisualRecordListRes.class);
        AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                .eq(AiImageTaskQueuePo::getSessionId, res.getSessionId())
                .eq(AiImageTaskQueuePo::getContentType, 0)
                .eq(StringUtils.isNotBlank(po.getCoverId()),AiImageTaskQueuePo::getContentId, po.getCoverId())
                .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                .last("limit 1"));

        if  (aiImageTaskQueuePo != null ) {
            res.setCoverImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
        }else{
            res.setCoverImage(MediaUrlPrefixUtil.getMediaUrl(res.getCoverImage()));
        }

        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, res.getSessionId())
                .orderByDesc(AiCreationSessionPo::getCreateTime)
                .last("limit 1"));
        if (null != aiCreationSessionPo){
            res.setImageSize(aiCreationSessionPo.getImageSize());
            AiImageStylePo aiImageStylePo = aiImageStyleMapper.selectById(aiCreationSessionPo.getImageStyleId());
            if (null != aiImageStylePo){
                res.setStyleName(aiImageStylePo.getStyleName());
                res.setStyleCategory(aiImageStylePo.getStyleCategory());
            }
        }
        res.setSubtitle2(po.getSubtitle());
        res.setToken(po.getShareToken());
        res.setCreateTime(po.getCreateTime());
        res.setUpdateTime(po.getUpdateTime());
        AiUsersPo aiUsersPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>().eq(AiUsersPo::getUserId, po.getUserId()));
        if (aiUsersPo != null) {
            res.setNickname(aiUsersPo.getNickname());
            res.setAvatar(MediaUrlPrefixUtil.getMediaUrl(aiUsersPo.getAvatar()));
        }
        return res;
    }

    @Override
    public void triggerRender(RenderTriggerReq req) {
        String visualRecordCode = req.getVisualRecordCode();
        log.info("Service layer: Triggering render for visualRecordCode : {}", visualRecordCode);
        String username = UserContext.getUser().getUserId();
        
        // 1. Find the record by visualRecordCode
        AiCreationVisualRecordPo record = lambdaQuery()
                .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
                .eq(AiCreationVisualRecordPo::getUserId, username)
                .one();

        if (record == null) {
            log.warn("Visual record not found for visualRecordCode: {}", visualRecordCode);
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                    "Visual record not found for visualRecordCode: " + visualRecordCode);
        }
        
        // 快速检查视频状态，避免不必要的锁获取
        if (record.getVideoState() != null && record.getVideoState() == 1) {
            log.info("Video is already being rendered for visualRecordCode: {}", visualRecordCode);
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_RENDERING.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_RENDERING.getMsg()));
        }

        // 使用分布式锁确保同一时间只有一个进程处理同一个visualRecordCode
        String lockKey = VIDEO_RENDERING_LOCK_PREFIX + visualRecordCode;
        RLock lock = redissonClient.getLock(lockKey);
        boolean lockAcquired = false;
        
        try {
            // 尝试获取锁，不等待（LOCK_WAIT_TIME=0）
            lockAcquired = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                log.info("Failed to acquire lock for visualRecordCode: {}, another process is likely rendering it", visualRecordCode);
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_RENDERING.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_RENDERING.getMsg()));
            }
            
            // 获取锁成功后，再次检查视频状态（双重检查模式）
            record = lambdaQuery()
                    .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
                    .eq(AiCreationVisualRecordPo::getUserId, username)
                    .one();
                    
            if (record == null) {
                log.warn("Visual record not found for visualRecordCode (after lock): {}", visualRecordCode);
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                        "Visual record not found for visualRecordCode: " + visualRecordCode);
            }
            
            if (record.getVideoState() != null && record.getVideoState() == 1) {
                log.info("Video is already being rendered for visualRecordCode (after lock): {}", visualRecordCode);
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_RENDERING.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_RENDERING.getMsg()));
            }
            
            // 2. Get content_data
            String contentData = record.getContentData();
            if (contentData == null || contentData.trim().isEmpty()) {
                log.error("Content data is missing or empty for visual record with visualRecordCode: {}", visualRecordCode);
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_MISSING_CONTENT.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_MISSING_CONTENT.getMsg()));
            }

            // 3. Parse content_data into RenderRequest
            VisualRecordDto renderRequest;
            try {
                renderRequest = objectMapper.readValue(contentData, VisualRecordDto.class);
                renderRequest.getCover().setImage(MediaUrlPrefixUtil.getMediaUrl(renderRequest.getCover().getImage()));
                AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                        .eq(AiImageTaskQueuePo::getSessionId, record.getSessionId())
                        .eq(AiImageTaskQueuePo::getContentType, 0)
                        .eq(AiImageTaskQueuePo::getContentId, renderRequest.getCover().getId())
                        .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                        .last("limit 1"));
                if (null != aiImageTaskQueuePo){
                    renderRequest.getCover().setImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
                }
                renderRequest.getBackgroundMusic().setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(renderRequest.getBackgroundMusic().getAudioUrl()));
                renderRequest.getShots().forEach(shot -> {
                    shot.setSceneImage(MediaUrlPrefixUtil.getMediaUrl(shot.getSceneImage()));
                    shot.setAudioClip(MediaUrlPrefixUtil.getMediaUrl(shot.getAudioClip()));
                });
            } catch (JsonProcessingException e) {
                log.error("Failed to parse content data JSON for visualRecordCode {}: {}", visualRecordCode, e.getMessage(), e);
                throw new BizException(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getCode(),
                         I18nMessageUtils.getMessage(ErrorCodeEnum.CONTENT_DATA_FORMAT_ERROR.getMsg()));
            }

            // 检查并扣除用户积分，每次视频生成需要300积分
            userPointsService.deductUserPoints(username, VIDEO_GENERATION_POINTS, visualRecordCode, "视频生成消耗" + VIDEO_GENERATION_POINTS + "积分");

            // 更新视频状态为"生成中"，在开始渲染前
            record.setVideoState(1);
            record.setVideoProgress(0);
            record.setVideoUrl("");
            record.setUpdateTime(new Date());
            this.updateById(record);

            // 4. Trigger asynchronous rendering
            // The renderVisual method returns a Mono<Void>. Calling subscribe() initiates the
            // reactive stream processing. The VisualRenderServiceImpl already handles
            // running blocking DB operations on a separate scheduler (DB_SCHEDULER).
            // This ensures the current thread (likely the HTTP request thread) is not blocked.
            visualRenderService.renderVisual(visualRecordCode, renderRequest)
                    .doOnError(error -> log.error("Error during renderVisual processing for visualRecordCode {}: {}", visualRecordCode, error.getMessage(), error))
                    // You might add more operators here if needed, e.g., .doOnSuccess(...)
                    .subscribe(); // Initiate the flow asynchronously

            log.info("Service layer: Successfully initiated render request for visualRecordCode: {}", visualRecordCode);
            // No explicit return value needed as the operation is async ("fire and forget" from the caller's perspective)
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while trying to acquire lock for visualRecordCode: {}", visualRecordCode, e);
            throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(),
                    "Interrupted while preparing to render video: " + e.getMessage());
        } finally {
            // 如果成功获取了锁，并且当前线程仍然持有锁，则释放锁
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("Released lock for visualRecordCode: {}", visualRecordCode);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String visualRecordCode, int status) {
        log.debug("Attempting to update status to [{}] for visualRecordCode : {}", status, visualRecordCode);
        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
            .set(AiCreationVisualRecordPo::getVideoState, status)
            .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProgress(String visualRecordCode, int progress) {
        log.debug("Attempting to update progress to [{}%] for visualRecordCode ID: {}", progress, visualRecordCode);
        int boundedProgress = Math.max(0, Math.min(100, progress));

        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
            .set(AiCreationVisualRecordPo::getVideoProgress, boundedProgress)
            .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUrlAndStatus(String visualRecordCode, String url, int status) {
        log.debug("Attempting to update URL to [{}] and status to [{}] for visualRecordCode : {}", url, status, visualRecordCode);
        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
            .set(AiCreationVisualRecordPo::getVideoUrl, url)
            .set(AiCreationVisualRecordPo::getVideoState, status)
            .set(AiCreationVisualRecordPo::getVideoProgress, 100)
            .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        this.update(updateWrapper);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisualRecordByCode(String visualRecordCode) {
        String userId = UserContext.getUser().getUserId();
        log.info("User [{}] attempting to delete visual record visualRecordCode: {}", userId, visualRecordCode);

        // 1. Find the record by session ID
        AiCreationVisualRecordPo record = lambdaQuery()
                .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
                .eq(AiCreationVisualRecordPo::getUserId, userId)
                .one();

        // 2. Check if exists
        if (record == null || record.getDelFlag() == 1) { // Also check if already deleted
            log.warn("Attempted to delete non-existent or already deleted visual record visualRecordCode: {}", visualRecordCode);
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_NOT_FOUND.getMsg()));
        }

        // 3. Check ownership
        if (record.getUserId() == null || !record.getUserId().equals(userId)) {
            log.warn("User [{}] does not have permission to delete visual record visualRecordCode: {}. Owner is [{}].",
                     userId, visualRecordCode, record.getUserId());
            throw new BizException(ErrorCodeEnum.VISUAL_RECORD_PERMISSION_DENIED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.VISUAL_RECORD_PERMISSION_DENIED.getMsg()));
        }
        // 4. Perform logical delete by updating the flag
        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
            .set(AiCreationVisualRecordPo::getDelFlag, 1) // Set flag to 1 for deleted
            .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public PageResponse<AiCreationVisualRecordListRes> getPublishedRecordsByPage(Integer pageNum, Integer pageSize) {
        log.info("Fetching published records with pagination - page: {}, size: {}", pageNum, pageSize);
        
        // 1. Fetch all published, non-deleted records
        LambdaQueryWrapper<AiCreationVisualRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCreationVisualRecordPo::getPublishStatus, 1) // Published status
                .eq(AiCreationVisualRecordPo::getDelFlag, 0)
                .orderByDesc(AiCreationVisualRecordPo::getPublishTime); // Order by publish time
        
        // 2. Get all records for processing
        List<AiCreationVisualRecordPo> allRecords = list(wrapper);
        
        // 3. Group by sessionId and select the first record from each group sorted by subtitle
        Map<String, List<AiCreationVisualRecordPo>> groupedBySession = allRecords.stream()
                .collect(Collectors.groupingBy(AiCreationVisualRecordPo::getSessionId));
        
        // 4. For each group, get the first record sorted by subtitle and the most recent timestamp
        List<AiCreationVisualRecordPo> filteredRecords = new ArrayList<>();
        
        for (Map.Entry<String, List<AiCreationVisualRecordPo>> entry : groupedBySession.entrySet()) {
            List<AiCreationVisualRecordPo> sessionRecords = entry.getValue();
            
            // Sort by subtitle
            sessionRecords.sort(Comparator.comparing(AiCreationVisualRecordPo::getSubtitle));
            
            // Take the first record
            AiCreationVisualRecordPo firstRecord = sessionRecords.get(0);
            
            // Find the record with the most recent publishTime
            AiCreationVisualRecordPo mostRecentRecord = sessionRecords.stream()
                    .max(Comparator.comparing(AiCreationVisualRecordPo::getUpdateTime))
                    .orElse(firstRecord);
            
            // Update the first record with the most recent timestamp
            firstRecord.setPublishTime(mostRecentRecord.getPublishTime());
            
            filteredRecords.add(firstRecord);
        }
        
        // 5. Sort the filtered records by the most recent timestamp
        filteredRecords.sort(Comparator.comparing(AiCreationVisualRecordPo::getPublishTime).reversed());
        
        // 6. Apply pagination to the filtered results
        long total = filteredRecords.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, filteredRecords.size());
        
        List<AiCreationVisualRecordPo> paginatedRecords = 
                (startIndex < total) ? filteredRecords.subList(startIndex, endIndex) : Collections.emptyList();
        
        // 7. Map to response DTOs
        List<AiCreationVisualRecordListRes> records = paginatedRecords.stream()
                .map(this::mapPoToListRes)
                .collect(Collectors.toList());
        
        // 8. Build PageResponse
        return PageResponse.of(records, (int)total, pageNum, pageSize);
    }
} 