package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.wlink.agent.dao.mapper.AiPayRefundMapper;
import com.wlink.agent.dao.mapper.PayOrderMapper;
import com.wlink.agent.dao.po.AiPayRefundPo;
import com.wlink.agent.dao.po.PayOrderPo;
import com.wlink.agent.enums.PayStatusEnum;
import com.wlink.agent.enums.RefundStatusEnum;
import com.wlink.agent.service.RefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.UUID;

/**
 * 退款服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefundServiceImpl implements RefundService {

    private final AiPayRefundMapper aiPayRefundMapper;
    private final PayOrderMapper payOrderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiPayRefundPo createRefund(String orderNo, String userId, Integer amount, String reason, Integer deductPoints) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }

        // 查询订单
        PayOrderPo orderPo = payOrderMapper.selectByOrderNo(orderNo);
        if (orderPo == null) {
            throw new BizException("订单不存在");
        }

        // 只有已支付的订单才能退款
        if (!orderPo.getStatus().equals(PayStatusEnum.OrderStatus.PAID.getCode())) {
            throw new BizException("只有已支付的订单才能退款");
        }

        // 查询是否已存在退款记录
        AiPayRefundPo existRefund = aiPayRefundMapper.selectByOrderNo(orderNo);
        if (existRefund != null) {
            throw new BizException("该订单已有退款记录，请勿重复申请");
        }

        // 创建退款记录
        AiPayRefundPo refundPo = new AiPayRefundPo();
        refundPo.setRefundNo(generateRefundNo());
        refundPo.setOrderNo(orderNo);
        refundPo.setUserId(userId);
        refundPo.setAmount(amount);
        refundPo.setReason(reason);
        refundPo.setDeductPoints(deductPoints);
        refundPo.setStatus(RefundStatusEnum.Status.PROCESSING.getCode());
        refundPo.setCreateTime(new Date());
        refundPo.setUpdateTime(new Date());
        refundPo.setDelFlag(0);

        aiPayRefundMapper.insert(refundPo);
        log.info("创建退款申请成功: refundNo={}, orderNo={}, amount={}", refundPo.getRefundNo(), orderNo, amount);

        return refundPo;
    }

    @Override
    public AiPayRefundPo queryRefund(String refundNo) {
        if (StringUtils.isBlank(refundNo)) {
            throw new BizException("退款单号不能为空");
        }
        return aiPayRefundMapper.selectByRefundNo(refundNo);
    }

    @Override
    public AiPayRefundPo queryRefundByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }
        return aiPayRefundMapper.selectByOrderNo(orderNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRefundStatus(String refundNo, Integer status) {
        if (StringUtils.isBlank(refundNo)) {
            throw new BizException("退款单号不能为空");
        }

        // 查询退款记录
        AiPayRefundPo refundPo = aiPayRefundMapper.selectByRefundNo(refundNo);
        if (refundPo == null) {
            throw new BizException("退款记录不存在");
        }

        // 更新状态
        int result = aiPayRefundMapper.updateStatusByRefundNo(refundNo, status);
        if (result > 0) {
            log.info("更新退款状态成功: refundNo={}, status={}", refundNo, status);
            return true;
        } else {
            log.error("更新退款状态失败: refundNo={}, status={}", refundNo, status);
            return false;
        }
    }

    @Override
    public String generateRefundNo() {
        return "RF" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") +
                String.format("%06d", (int) (Math.random() * 1000000));
    }
} 