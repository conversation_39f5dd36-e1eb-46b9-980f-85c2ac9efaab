package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.dao.mapper.AiCanvasTrackMapper;
import com.wlink.agent.dao.mapper.AiTrackAudioMapper;
import com.wlink.agent.dao.po.AiCanvasTrackPo;
import com.wlink.agent.dao.po.AiTrackAudioPo;
import com.wlink.agent.model.req.CanvasTrackCreateReq;
import com.wlink.agent.model.req.TrackAudioCreateReq;
import com.wlink.agent.model.res.CanvasTrackRes;
import com.wlink.agent.model.res.TrackAudioRes;
import com.wlink.agent.service.CanvasTrackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 画布音轨服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CanvasTrackServiceImpl extends ServiceImpl<AiCanvasTrackMapper, AiCanvasTrackPo> 
        implements CanvasTrackService {
    
    private final AiCanvasTrackMapper canvasTrackMapper;
    private final AiTrackAudioMapper trackAudioMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCanvasTrack(CanvasTrackCreateReq req) {
        log.info("创建画布音轨: canvasId={}, trackName={}", req.getCanvasId(), req.getTrackName());
        
        // 创建音轨对象
        AiCanvasTrackPo trackPo = new AiCanvasTrackPo();
        BeanUtils.copyProperties(req, trackPo);
        
        // 设置排序序号
        Integer maxSortOrder = canvasTrackMapper.getMaxSortOrder(req.getCanvasId());
        trackPo.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        
        // 设置创建时间
        Date now = new Date();
        trackPo.setCreateTime(now);
        trackPo.setUpdateTime(now);
        trackPo.setDelFlag(0);
        
        // 保存到数据库
        this.save(trackPo);
        
        log.info("画布音轨创建成功: trackId={}, canvasId={}", trackPo.getId(), req.getCanvasId());
        return trackPo.getId();
    }
    
    @Override
    public List<CanvasTrackRes> getCanvasTracks(Long canvasId) {
        log.debug("获取画布音轨列表: canvasId={}", canvasId);
        
        LambdaQueryWrapper<AiCanvasTrackPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasTrackPo::getCanvasId, canvasId)
                .eq(AiCanvasTrackPo::getDelFlag, 0)
                .orderByAsc(AiCanvasTrackPo::getSortOrder);
        
        List<AiCanvasTrackPo> trackList = this.list(queryWrapper);
        
        return trackList.stream().map(this::convertToTrackRes).collect(Collectors.toList());
    }
    
    @Override
    public CanvasTrackRes getTrackDetail(Long trackId) {
        log.debug("获取音轨详情: trackId={}", trackId);
        
        AiCanvasTrackPo trackPo = this.getById(trackId);
        if (trackPo == null || trackPo.getDelFlag() == 1) {
            throw new BizException("音轨不存在");
        }
        
        CanvasTrackRes trackRes = convertToTrackRes(trackPo);
        
        // 获取音轨下的音频列表
        List<TrackAudioRes> audioList = getTrackAudios(trackId);
        trackRes.setAudioList(audioList);
        
        return trackRes;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCanvasTrack(Long trackId, CanvasTrackCreateReq req) {
        log.info("更新音轨信息: trackId={}, trackName={}", trackId, req.getTrackName());
        
        AiCanvasTrackPo trackPo = this.getById(trackId);
        if (trackPo == null || trackPo.getDelFlag() == 1) {
            throw new BizException("音轨不存在");
        }
        
        // 更新字段
        trackPo.setTrackName(req.getTrackName());
        trackPo.setStartTime(req.getStartTime());
        trackPo.setEndTime(req.getEndTime());
        trackPo.setTrackType(req.getTrackType());
        trackPo.setVolume(req.getVolume());
        trackPo.setDescription(req.getDescription());
        trackPo.setUpdateTime(new Date());
        
        this.updateById(trackPo);
        
        log.info("音轨信息更新成功: trackId={}", trackId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCanvasTrack(Long trackId) {
        log.info("删除音轨: trackId={}", trackId);
        
        AiCanvasTrackPo trackPo = this.getById(trackId);
        if (trackPo == null || trackPo.getDelFlag() == 1) {
            throw new BizException("音轨不存在");
        }
        
        // 先删除音轨下的所有音频
        deleteTrackAudios(trackId);
        
        // 删除音轨
        trackPo.setDelFlag(1);
        trackPo.setUpdateTime(new Date());
        this.updateById(trackPo);
        
        log.info("音轨删除成功: trackId={}", trackId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCanvasTracks(Long canvasId) {
        log.info("删除画布下所有音轨: canvasId={}", canvasId);
        
        // 获取画布下所有音轨
        List<CanvasTrackRes> tracks = getCanvasTracks(canvasId);
        
        // 删除每个音轨及其音频
        for (CanvasTrackRes track : tracks) {
            deleteCanvasTrack(track.getId());
        }
        
        log.info("画布音轨删除完成: canvasId={}, 删除数量={}", canvasId, tracks.size());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addTrackAudio(TrackAudioCreateReq req) {
        log.info("添加音频到音轨: trackId={}, audioName={}", req.getTrackId(), req.getAudioName());
        
        // 验证音轨是否存在
        AiCanvasTrackPo trackPo = this.getById(req.getTrackId());
        if (trackPo == null || trackPo.getDelFlag() == 1) {
            throw new BizException("音轨不存在");
        }
        
        // 创建音频对象
        AiTrackAudioPo audioPo = new AiTrackAudioPo();
        BeanUtils.copyProperties(req, audioPo);
        
        // 设置排序序号
        Integer maxSortOrder = trackAudioMapper.getMaxSortOrder(req.getTrackId());
        audioPo.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        
        // 设置创建时间
        Date now = new Date();
        audioPo.setCreateTime(now);
        audioPo.setUpdateTime(now);
        audioPo.setDelFlag(0);
        
        // 保存到数据库
        trackAudioMapper.insert(audioPo);
        
        log.info("音频添加成功: audioId={}, trackId={}", audioPo.getId(), req.getTrackId());
        return audioPo.getId();
    }
    
    @Override
    public List<TrackAudioRes> getTrackAudios(Long trackId) {
        log.debug("获取音轨音频列表: trackId={}", trackId);
        
        LambdaQueryWrapper<AiTrackAudioPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTrackAudioPo::getTrackId, trackId)
                .eq(AiTrackAudioPo::getDelFlag, 0)
                .orderByAsc(AiTrackAudioPo::getSortOrder);
        
        List<AiTrackAudioPo> audioList = trackAudioMapper.selectList(queryWrapper);
        
        return audioList.stream().map(this::convertToAudioRes).collect(Collectors.toList());
    }

    @Override
    public TrackAudioRes getAudioDetail(Long audioId) {
        log.debug("获取音频详情: audioId={}", audioId);

        AiTrackAudioPo audioPo = trackAudioMapper.selectById(audioId);
        if (audioPo == null || audioPo.getDelFlag() == 1) {
            throw new BizException("音频不存在");
        }

        return convertToAudioRes(audioPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTrackAudio(Long audioId, TrackAudioCreateReq req) {
        log.info("更新音频信息: audioId={}, audioName={}", audioId, req.getAudioName());

        AiTrackAudioPo audioPo = trackAudioMapper.selectById(audioId);
        if (audioPo == null || audioPo.getDelFlag() == 1) {
            throw new BizException("音频不存在");
        }

        // 更新字段
        audioPo.setAudioName(req.getAudioName());
        audioPo.setAudioUrl(req.getAudioUrl());
        audioPo.setStartPlayTime(req.getStartPlayTime());
        audioPo.setEndPlayTime(req.getEndPlayTime());
        audioPo.setVolume(req.getVolume());
        audioPo.setDuration(req.getDuration());
        audioPo.setFileSize(req.getFileSize());
        audioPo.setAudioFormat(req.getAudioFormat());
        audioPo.setFadeInTime(req.getFadeInTime());
        audioPo.setFadeOutTime(req.getFadeOutTime());
        audioPo.setAudioSource(req.getAudioSource());
        audioPo.setDescription(req.getDescription());
        audioPo.setUpdateTime(new Date());

        trackAudioMapper.updateById(audioPo);

        log.info("音频信息更新成功: audioId={}", audioId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTrackAudio(Long audioId) {
        log.info("删除音频: audioId={}", audioId);

        AiTrackAudioPo audioPo = trackAudioMapper.selectById(audioId);
        if (audioPo == null || audioPo.getDelFlag() == 1) {
            throw new BizException("音频不存在");
        }

        // 逻辑删除
        audioPo.setDelFlag(1);
        audioPo.setUpdateTime(new Date());
        trackAudioMapper.updateById(audioPo);

        log.info("音频删除成功: audioId={}", audioId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTrackAudios(Long trackId) {
        log.info("删除音轨下所有音频: trackId={}", trackId);

        // 获取音轨下所有音频
        List<TrackAudioRes> audios = getTrackAudios(trackId);

        // 删除每个音频
        for (TrackAudioRes audio : audios) {
            deleteTrackAudio(audio.getId());
        }

        log.info("音轨音频删除完成: trackId={}, 删除数量={}", trackId, audios.size());
    }

    /**
     * 转换为音轨响应对象
     */
    private CanvasTrackRes convertToTrackRes(AiCanvasTrackPo trackPo) {
        CanvasTrackRes trackRes = new CanvasTrackRes();
        BeanUtils.copyProperties(trackPo, trackRes);
        return trackRes;
    }

    /**
     * 转换为音频响应对象
     */
    private TrackAudioRes convertToAudioRes(AiTrackAudioPo audioPo) {
        TrackAudioRes audioRes = new TrackAudioRes();
        BeanUtils.copyProperties(audioPo, audioRes);
        return audioRes;
    }
}
