package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.wlink.agent.client.CompletionApiClient;
import com.wlink.agent.client.DoubaoImageApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.service.ImageTaskQueueService;
import com.wlink.agent.utils.ReliableDistributedSemaphore;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/5/26 10:40
 */
@Service
@Slf4j
public class ReliableImageGenerationService {

    @Resource
    private ReliableDistributedSemaphore semaphore;
    @Resource
    private ImageTaskMQHandler imageTaskMQHandler;
    @Resource
    private AiImageTaskQueueMapper taskMapper;
    @Resource
    private DoubaoImageApiClient doubaoImageApiClient;
    @Resource
    private CompletionApiClient completionApiClient;
    @Resource
    ImageTaskQueueService imageTaskQueueService;

    /**
     * 自动释放信号量的资源管理器
     */
    public class SemaphoreResource implements AutoCloseable {
        private final String taskId;
        @Getter
        private final boolean acquired;

        public SemaphoreResource(String taskId) {
            this.taskId = taskId;
            this.acquired = semaphore.tryAcquireWithTracking(taskId);
        }

        @Override
        public void close() {
            if (acquired) {
                try {
                    semaphore.releaseWithTracking(taskId);
                } catch (Exception e) {
                    log.error("释放信号量资源失败, taskId: {}", taskId, e);
                    // 记录到数据库，用于后续修复
                    recordSemaphoreLeakage(taskId, e.getMessage());
                }
            }
        }
    }

    /**
     * 处理图片生成任务 - 使用资源管理确保信号量释放
     */
    public void processImageTask(String taskId) {
        try (SemaphoreResource semaphoreResource = new SemaphoreResource(taskId)) {

            if (!semaphoreResource.isAcquired()) {
                // 获取信号量失败，重新放回队列
                reEnqueueTask(taskId, 30);
                return;
            }

            // 更新任务状态为处理中
            imageTaskQueueService.updateTaskStatus(Long.valueOf(taskId), TaskStatus.PROCESSING.getValue(), null, null, null);

            try {
                // 获取任务详情
                AiImageTaskQueuePo task = taskMapper.selectById(taskId);
                if (task == null) {
                    log.warn("未找到任务: {}", taskId);
                    return;
                }

                // 调用外部接口
                String requestParams = task.getRequestParams();
                DoubaoImageGenerationRequest apiRequest = JSON.parseObject(requestParams, DoubaoImageGenerationRequest.class);
                ImageGenerateRes response;
                try {
                    response = doubaoImageApiClient.generateImage(apiRequest);
                    
                    // 检查是否存在错误码
                    if (response.getCode() != null && !response.getCode().isEmpty()) {
                        log.warn("豆包API返回错误码: {}, 错误信息: {}", response.getCode(), response.getMessage());
                        
                        // 处理敏感内容检测
                        if ("OutputImageSensitiveContentDetected".equals(response.getCode()) || "InputTextSensitiveContentDetected".equals(response.getCode())
                        || "SensitiveContentDetected.Violence".equals(response.getCode()) || "SensitiveContentDetected".equals(response.getCode()) ||
                                "SensitiveContentDetected.SevereViolation".equals(response.getCode())) {
                            log.info("检测到敏感内容，尝试优化提示词");
                            response = handleSensitiveContent(apiRequest, task.getUserId());
                            
                            // 如果优化后仍有错误码，抛出异常以触发重试
                            if (response.getCode() != null && !response.getCode().isEmpty()) {
                                throw new BizException("优化提示词后仍然失败: " + response.getCode() + " - " + response.getMessage());
                            }
                        } else {
                            // 其他错误码，直接抛出异常以触发重试
                            throw new BizException("豆包API返回错误: " + response.getCode() + " - " + response.getMessage());
                        }
                    }
                    
                } catch (Exception e) {
                    log.error("调用豆包API失败: {}", e.getMessage(), e);
                    throw new BizException("调用豆包API失败: " + e.getMessage());
                }
                // 更新任务结果
                imageTaskQueueService.updateTaskStatus(Long.valueOf(taskId), TaskStatus.COMPLETED.getValue(), response, null, null);

                log.info("任务 {} 处理成功", taskId);

            } catch (Exception e) {
                log.error("处理任务失败: {}", taskId, e);
                handleTaskError(taskId, e);
                throw e; // 重新抛出异常，确保事务回滚
            }

        } // try-with-resources自动调用close()方法释放信号量
    }

    /**
     * 处理敏感内容检测，优化提示词并重新生成图像
     * 
     * @param originalRequest 原始请求
     * @param userId 用户ID
     * @return 优化后的图像生成结果
     */
    private ImageGenerateRes handleSensitiveContent(DoubaoImageGenerationRequest originalRequest, String userId) {
        try {
            // 调用AI优化提示词
            String optimizedPrompt = completionApiClient.requestCompletion(originalRequest.getPrompt(), userId,null);
            
            if (optimizedPrompt == null || optimizedPrompt.trim().isEmpty()) {
                log.warn("提示词优化失败，返回空结果");
                return new ImageGenerateRes(null, "PromptOptimizationFailed", "提示词优化失败");
            }
            
            log.info("原始提示词: {}", originalRequest.getPrompt());
            log.info("优化后提示词: {}", optimizedPrompt);
            
            // 使用优化后的提示词创建新请求
            DoubaoImageGenerationRequest optimizedRequest = new DoubaoImageGenerationRequest();
            optimizedRequest.setModel(originalRequest.getModel());
            optimizedRequest.setPrompt(optimizedPrompt);
            optimizedRequest.setResponseFormat(originalRequest.getResponseFormat());
            optimizedRequest.setSize(originalRequest.getSize());
            optimizedRequest.setSeed(originalRequest.getSeed());
            optimizedRequest.setGuidanceScale(originalRequest.getGuidanceScale());
            optimizedRequest.setWatermark(originalRequest.getWatermark());
            // 重新尝试生成图像
            return doubaoImageApiClient.generateImage(optimizedRequest);
            
        } catch (Exception e) {
            log.error("处理敏感内容失败: {}", e.getMessage(), e);
            return new ImageGenerateRes(null, "OptimizationError", "提示词优化过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 记录信号量泄漏情况
     */
    private void recordSemaphoreLeakage(String taskId, String errorMsg) {
        try {
            // 可以记录到专门的表中，用于监控和告警
            log.error("信号量泄漏记录 - 任务ID: {}, 错误: {}", taskId, errorMsg);
            // TODO: 发送告警通知
        } catch (Exception e) {
            log.error("记录信号量泄漏失败", e);
        }
    }

    private void updateTaskStatus(String taskId, TaskStatus status) {
        try {
            AiImageTaskQueuePo task = new AiImageTaskQueuePo();
            task.setId(Long.valueOf(taskId));
            task.setTaskStatus(status.getValue());
            task.setUpdateTime(new Date());
            taskMapper.updateById(task);
        } catch (Exception e) {
            log.error("更新任务状态失败: {}", taskId, e);
            throw e;
        }
    }

    private void handleTaskError(String taskId, Exception e) {
        try {
            AiImageTaskQueuePo task = taskMapper.selectById(taskId);
            if (task != null) {
                // 使用 retryCount 字段
                Integer retryCount = task.getRetryCount();
                if (retryCount == null) {
                    retryCount = 0;
                }
                retryCount++;

                if (retryCount >= 3) {
                    // 超过重试次数，标记为失败
                    task.setTaskStatus(TaskStatus.FAILED.getValue());
                    imageTaskQueueService.updateTaskStatus(Long.valueOf(taskId), TaskStatus.FAILED.getValue(), null, "重试次数已达上限 - " + e.getMessage(), retryCount);
                } else {
                    // 重新放回队列重试
                    task.setTaskStatus(TaskStatus.PENDING.getValue());
                    imageTaskQueueService.updateTaskStatus(Long.valueOf(taskId), TaskStatus.PENDING.getValue(), null, "第 " + retryCount + " 次重试 - " + e.getMessage(), retryCount);
                    reEnqueueTask(taskId, 60); // 1分钟后重试
                }
            }
        } catch (Exception ex) {
            log.error("处理任务错误失败: {}", taskId, ex);
        }
    }

    private void reEnqueueTask(String taskId, int delaySeconds) {
        try {
            // 使用ImageTaskMQHandler发送延迟消息
            imageTaskMQHandler.sendDelayTaskMessage(taskId, getDelayLevel(delaySeconds),null);
            log.info("任务 {} 将在 {} 秒后重试", taskId, delaySeconds);
        } catch (Exception e) {
            log.error("重新入队任务失败: {}", taskId, e);
        }
    }

    private int getDelayLevel(int seconds) {
        if (seconds <= 1) return 1;
        if (seconds <= 5) return 2;
        if (seconds <= 10) return 3;
        if (seconds <= 30) return 4;
        if (seconds <= 60) return 5;
        if (seconds <= 120) return 6;
        return 7;
    }
}
