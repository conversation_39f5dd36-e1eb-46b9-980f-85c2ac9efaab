package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.service.ChatCallback;
import com.wlink.agent.service.StreamChatService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class StreamChatServiceImpl implements StreamChatService {

    @Value("${openai.api-key:d3d7e2b4c9ea43d295d55b5f0c63b977}")
    private String apiKey;

    @Value("${openai.deployment-id:gpt4o}")
    private String deploymentId;

    @Value("${openai.api-version:2024-02-15-preview}")
    private String apiVersion;

    @Value("${openai.resource-name:resopenaiserviceus}")
    private String resourceName;

    private OkHttpClient client;
    private EventSource.Factory eventSourceFactory;

    @PostConstruct
    public void init() {
        client = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
        
        eventSourceFactory = EventSources.createFactory(client);
    }

    @Override
    public void streamChat(String prompt, String question, ChatCallback callback) {
        try {
            // 构建消息
            List<Map<String, String>> messages = new ArrayList<>();
            if (prompt != null && !prompt.isEmpty()) {
                messages.add(createMessage("system", prompt));
            }
            messages.add(createMessage("user", question));

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("messages", messages);
            requestBody.put("stream", true);
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 800);

            // 构建URL
            String url = String.format("https://%s.openai.azure.com/openai/deployments/%s/chat/completions?api-version=%s",
                    resourceName, deploymentId, apiVersion);

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("api-key", apiKey)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(
                            MediaType.parse("application/json"),
                            JSON.toJSONString(requestBody)))
                    .build();

            // 创建StringBuilder用于累积响应
            StringBuilder responseBuilder = new StringBuilder();

            // 创建EventSource
            eventSourceFactory.newEventSource(request, new EventSourceListener() {
                @Override
                public void onOpen(EventSource eventSource, Response response) {
                    if (!response.isSuccessful()) {
                        String message = String.format("Failed to connect to Azure OpenAI. Status: %d", response.code());
                        callback.onError(new RuntimeException(message));
                        eventSource.cancel();
                    }
                }

                @Override
                public void onEvent(EventSource eventSource, String id, String type, String data) {
                    try {
                        if ("[DONE]".equals(data)) {
                            // 发送剩余内容
                            String remaining = responseBuilder.toString();
                            if (!remaining.isEmpty()) {
                                callback.onMessage(remaining);
                                responseBuilder.setLength(0);
                            }
                            return;
                        }

                        JSONObject json = JSON.parseObject(data);
                        String content = json.getJSONArray("choices")
                                .getJSONObject(0)
                                .getJSONObject("delta")
                                .getString("content");

                        if (content != null) {
                            responseBuilder.append(content);
                            
                            // 当累积一定长度或遇到标点符号时发送
                            String accumulated = responseBuilder.toString();
                            if (accumulated.length() >= 50 || 
                                accumulated.matches(".*[。！？；，].*")) {
                                callback.onMessage(accumulated);
                                responseBuilder.setLength(0);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error processing event", e);
                        callback.onError(e);
                        eventSource.cancel();
                    }
                }

                @Override
                public void onClosed(EventSource eventSource) {
                    callback.onComplete();
                }

                @Override
                public void onFailure(EventSource eventSource, Throwable t, Response response) {
                    log.error("Stream connection failed", t);
                    callback.onError(t);
                    eventSource.cancel();
                }
            });

        } catch (Exception e) {
            log.error("Failed to start stream chat", e);
            callback.onError(e);
        }
    }

    private Map<String, String> createMessage(String role, String content) {
        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        return message;
    }
} 