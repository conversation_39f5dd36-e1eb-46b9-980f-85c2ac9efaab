package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.dao.mapper.AiVideoModelMapper;
import com.wlink.agent.dao.mapper.AiVideoModelResolutionConfigMapper;
import com.wlink.agent.dao.mapper.AiVideoModelSizeConfigMapper;
import com.wlink.agent.dao.po.AiVideoModelPo;
import com.wlink.agent.dao.po.AiVideoModelResolutionConfigPo;
import com.wlink.agent.dao.po.AiVideoModelSizeConfigPo;
import com.wlink.agent.enums.VideoModelProvider;
import com.wlink.agent.enums.VideoModelType;
import com.wlink.agent.model.req.VideoModelConfigQueryReq;
import com.wlink.agent.model.res.VideoModelConfigRes;
import com.wlink.agent.service.VideoModelConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频模型配置服务实现类
 */
@Slf4j
@Service
public class VideoModelConfigServiceImpl extends ServiceImpl<AiVideoModelMapper, AiVideoModelPo>
        implements VideoModelConfigService {

    @Resource
    private AiVideoModelSizeConfigMapper sizeConfigMapper;

    @Resource
    private AiVideoModelResolutionConfigMapper resolutionConfigMapper;
    
    @Override
    public List<VideoModelConfigRes> listAllEnabledConfigs() {
        log.info("查询所有启用的视频模型配置（级联查询）");

        List<AiVideoModelPo> modelList = baseMapper.selectAllEnabled();
        return buildCascadeConfigList(modelList);
    }
    
    @Override
    public List<VideoModelConfigRes> listConfigsByCondition(VideoModelConfigQueryReq queryReq) {
        log.info("根据条件查询视频模型配置（级联查询）: {}", queryReq);

        LambdaQueryWrapper<AiVideoModelPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiVideoModelPo::getStatus, 1)
               .eq(AiVideoModelPo::getDelFlag, 0);

        // 提供商条件
        if (StringUtils.isNotBlank(queryReq.getProvider())) {
            wrapper.eq(AiVideoModelPo::getProvider, queryReq.getProvider());
        }

        // 模型类型条件
        if (StringUtils.isNotBlank(queryReq.getModelType())) {
            wrapper.like(AiVideoModelPo::getModelType, queryReq.getModelType());
        }

        // 关键词搜索条件
        if (StringUtils.isNotBlank(queryReq.getKeyword())) {
            wrapper.and(w -> w.like(AiVideoModelPo::getModelName, queryReq.getKeyword())
                            .or()
                            .like(AiVideoModelPo::getModelDisplayName, queryReq.getKeyword())
                            .or()
                            .like(AiVideoModelPo::getModelDescription, queryReq.getKeyword()));
        }

        wrapper.orderByAsc(AiVideoModelPo::getSortOrder)
               .orderByDesc(AiVideoModelPo::getCreateTime);

        List<AiVideoModelPo> modelList = list(wrapper);
        List<VideoModelConfigRes> resultList = buildCascadeConfigList(modelList);

        // 根据级联条件进一步过滤
        return filterByCondition(resultList, queryReq);
    }
    
    @Override
    public List<VideoModelConfigRes> listConfigsByProvider(String provider) {
        log.info("根据提供商查询视频模型配置（级联查询）: provider={}", provider);

        if (StringUtils.isBlank(provider)) {
            return Collections.emptyList();
        }

        List<AiVideoModelPo> modelList = baseMapper.selectByProvider(provider);
        return buildCascadeConfigList(modelList);
    }

    @Override
    public List<VideoModelConfigRes> listConfigsByModelType(String modelType) {
        log.info("根据模型类型查询视频模型配置（级联查询）: modelType={}", modelType);

        if (StringUtils.isBlank(modelType)) {
            return Collections.emptyList();
        }

        List<AiVideoModelPo> modelList = baseMapper.selectByModelType(modelType);
        return buildCascadeConfigList(modelList);
    }

    @Override
    public VideoModelConfigRes getConfigByModelName(String modelName) {
        log.info("根据模型名称获取视频模型配置（级联查询）: modelName={}", modelName);

        if (StringUtils.isBlank(modelName)) {
            return null;
        }

        LambdaQueryWrapper<AiVideoModelPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiVideoModelPo::getModelName, modelName)
               .eq(AiVideoModelPo::getStatus, 1)
               .eq(AiVideoModelPo::getDelFlag, 0);

        AiVideoModelPo model = getOne(wrapper);
        if (model == null) {
            return null;
        }

        List<VideoModelConfigRes> configList = buildCascadeConfigList(Collections.singletonList(model));
        return configList.isEmpty() ? null : configList.get(0);
    }

    @Override
    public List<VideoModelConfigRes> listConfigsByProviderAndModelType(String provider, String modelType) {
        log.info("根据提供商和模型类型查询视频模型配置（级联查询）: provider={}, modelType={}", provider, modelType);

        if (StringUtils.isBlank(provider) || StringUtils.isBlank(modelType)) {
            return Collections.emptyList();
        }

        List<AiVideoModelPo> modelList = baseMapper.selectByProviderAndModelType(provider, modelType);
        return buildCascadeConfigList(modelList);
    }

    @Override
    public List<VideoModelConfigRes> listConfigsByImageCount(Integer imageCount) {
        log.info("根据图片数量查询视频模型配置（级联查询）: imageCount={}", imageCount);

        if (imageCount == null) {
            return Collections.emptyList();
        }

        // 先查询符合图片数量条件的尺寸配置
        List<AiVideoModelSizeConfigPo> sizeConfigs = sizeConfigMapper.selectByImageCount(imageCount);
        if (sizeConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取对应的模型ID列表
        List<Long> modelIds = sizeConfigs.stream()
                .map(AiVideoModelSizeConfigPo::getModelId)
                .distinct()
                .collect(Collectors.toList());

        // 查询对应的模型
        List<AiVideoModelPo> modelList = listByIds(modelIds);
        return buildCascadeConfigList(modelList);
    }
    
    /**
     * 构建级联配置列表
     *
     * @param modelList 模型列表
     * @return 级联配置列表
     */
    private List<VideoModelConfigRes> buildCascadeConfigList(List<AiVideoModelPo> modelList) {
        if (modelList == null || modelList.isEmpty()) {
            return Collections.emptyList();
        }

        List<VideoModelConfigRes> resultList = new ArrayList<>();

        for (AiVideoModelPo model : modelList) {
            VideoModelConfigRes config = buildCascadeConfig(model);
            if (config != null) {
                resultList.add(config);
            }
        }

        return resultList;
    }

    /**
     * 构建单个模型的级联配置
     *
     * @param model 模型PO
     * @return 级联配置
     */
    private VideoModelConfigRes buildCascadeConfig(AiVideoModelPo model) {
        if (model == null) {
            return null;
        }

        // 查询该模型的尺寸配置
        List<AiVideoModelSizeConfigPo> sizeConfigs = sizeConfigMapper.selectByModelId(model.getId());
        if (sizeConfigs.isEmpty()) {
            log.warn("模型 {} 没有配置尺寸信息", model.getModelName());
            return null;
        }

        // 构建尺寸配置列表
        List<VideoModelConfigRes.SizeConfig> sizeConfigList = new ArrayList<>();
        for (AiVideoModelSizeConfigPo sizeConfig : sizeConfigs) {
            VideoModelConfigRes.SizeConfig sizeConfigRes = buildSizeConfig(sizeConfig);
            if (sizeConfigRes != null) {
                sizeConfigList.add(sizeConfigRes);
            }
        }

        // 解析模型类型
        List<String> modelTypes = Arrays.asList(model.getModelType().split(","));
        List<String> modelTypeNames = modelTypes.stream()
                .map(this::getModelTypeName)
                .collect(Collectors.toList());

        // 获取提供商名称
        String providerName = getProviderName(model.getProvider());

        return VideoModelConfigRes.builder()
                .id(model.getId())
                .modelName(model.getModelName())
                .modelDisplayName(model.getModelDisplayName())
                .modelDescription(model.getModelDescription())
                .provider(model.getProvider())
                .providerName(providerName)
                .modelTypes(modelTypes)
                .modelTypeNames(modelTypeNames)
                .fps(model.getFps())
                .supportWatermark(model.getSupportWatermark() == 1)
                .supportSeed(model.getSupportSeed() == 1)
                .supportCameraFixed(model.getSupportCameraFixed() == 1)
                .sortOrder(model.getSortOrder())
                .sizeConfigs(sizeConfigList)
                .build();
    }

    /**
     * 构建尺寸配置
     *
     * @param sizeConfig 尺寸配置PO
     * @return 尺寸配置响应
     */
    private VideoModelConfigRes.SizeConfig buildSizeConfig(AiVideoModelSizeConfigPo sizeConfig) {
        if (sizeConfig == null) {
            return null;
        }

        // 查询该尺寸的分辨率配置
        List<AiVideoModelResolutionConfigPo> resolutionConfigs =
                resolutionConfigMapper.selectBySizeConfigId(sizeConfig.getId());

        // 构建分辨率配置列表
        List<VideoModelConfigRes.ResolutionConfig> resolutionConfigList = new ArrayList<>();
        for (AiVideoModelResolutionConfigPo resolutionConfig : resolutionConfigs) {
            VideoModelConfigRes.ResolutionConfig resolutionConfigRes = buildResolutionConfig(resolutionConfig);
            if (resolutionConfigRes != null) {
                resolutionConfigList.add(resolutionConfigRes);
            }
        }

        // 解析支持的时长
        List<Integer> supportedDurations = parseJsonArrayToInt(sizeConfig.getSupportedDurations());

        // 获取图片数量描述
        String imageCountDesc = getImageCountDesc(sizeConfig.getImageCount());

        return VideoModelConfigRes.SizeConfig.builder()
                .id(sizeConfig.getId())
                .sizeName(sizeConfig.getSizeName())
                .sizeDisplayName(sizeConfig.getSizeDisplayName())
                .supportedDurations(supportedDurations)
                .imageCount(sizeConfig.getImageCount())
                .imageCountDesc(imageCountDesc)
                .pointsCost(sizeConfig.getPointsCost())
                .sortOrder(sizeConfig.getSortOrder())
                .resolutionConfigs(resolutionConfigList)
                .build();
    }

    /**
     * 构建分辨率配置
     *
     * @param resolutionConfig 分辨率配置PO
     * @return 分辨率配置响应
     */
    private VideoModelConfigRes.ResolutionConfig buildResolutionConfig(AiVideoModelResolutionConfigPo resolutionConfig) {
        if (resolutionConfig == null) {
            return null;
        }

        return VideoModelConfigRes.ResolutionConfig.builder()
                .id(resolutionConfig.getId())
                .ratio(resolutionConfig.getRatio())
                .ratioDisplayName(resolutionConfig.getRatioDisplayName())
                .width(resolutionConfig.getWidth())
                .height(resolutionConfig.getHeight())
                .pixelSize(resolutionConfig.getPixelSize())
                .pointsCost(resolutionConfig.getPointsCost())
                .sortOrder(resolutionConfig.getSortOrder())
                .build();
    }

    /**
     * 根据条件过滤结果
     *
     * @param configList 配置列表
     * @param queryReq 查询条件
     * @return 过滤后的配置列表
     */
    private List<VideoModelConfigRes> filterByCondition(List<VideoModelConfigRes> configList,
                                                        VideoModelConfigQueryReq queryReq) {
        if (configList == null || configList.isEmpty()) {
            return Collections.emptyList();
        }

        return configList.stream()
                .filter(config -> matchesCondition(config, queryReq))
                .collect(Collectors.toList());
    }

    /**
     * 判断配置是否匹配条件
     *
     * @param config 配置
     * @param queryReq 查询条件
     * @return 是否匹配
     */
    private boolean matchesCondition(VideoModelConfigRes config, VideoModelConfigQueryReq queryReq) {
        // 首帧支持条件
        if (queryReq.getSupportFirstFrame() != null) {
            boolean hasFirstFrameSupport = config.getSizeConfigs().stream()
                    .anyMatch(sizeConfig -> sizeConfig.getImageCount() >= 1);
            if (queryReq.getSupportFirstFrame() && !hasFirstFrameSupport) {
                return false;
            }
            if (!queryReq.getSupportFirstFrame() && hasFirstFrameSupport) {
                return false;
            }
        }

        // 尾帧支持条件
        if (queryReq.getSupportLastFrame() != null) {
            boolean hasLastFrameSupport = config.getSizeConfigs().stream()
                    .anyMatch(sizeConfig -> sizeConfig.getImageCount() >= 2);
            if (queryReq.getSupportLastFrame() && !hasLastFrameSupport) {
                return false;
            }
            if (!queryReq.getSupportLastFrame() && hasLastFrameSupport) {
                return false;
            }
        }

        // 积分成本范围条件
        if (queryReq.getMinPointsCost() != null || queryReq.getMaxPointsCost() != null) {
            boolean hasMatchingCost = config.getSizeConfigs().stream()
                    .anyMatch(sizeConfig -> {
                        Integer cost = sizeConfig.getPointsCost();
                        if (queryReq.getMinPointsCost() != null && cost < queryReq.getMinPointsCost()) {
                            return false;
                        }
                        if (queryReq.getMaxPointsCost() != null && cost > queryReq.getMaxPointsCost()) {
                            return false;
                        }
                        return true;
                    });
            if (!hasMatchingCost) {
                return false;
            }
        }

        return true;
    }

    /**
     * 解析JSON数组为整数列表
     *
     * @param jsonStr JSON字符串
     * @return 整数列表
     */
    private List<Integer> parseJsonArrayToInt(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyList();
        }

        try {
            return JSON.parseArray(jsonStr, Integer.class);
        } catch (Exception e) {
            log.warn("解析JSON数组为整数失败: {}", jsonStr, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取模型类型名称
     *
     * @param modelTypeCode 模型类型代码
     * @return 模型类型名称
     */
    private String getModelTypeName(String modelTypeCode) {
        try {
            VideoModelType modelType = VideoModelType.fromCode(modelTypeCode.trim());
            return modelType.getName();
        } catch (Exception e) {
            log.warn("未知的模型类型代码: {}", modelTypeCode);
            return modelTypeCode;
        }
    }

    /**
     * 获取提供商名称
     *
     * @param providerCode 提供商代码
     * @return 提供商名称
     */
    private String getProviderName(String providerCode) {
        try {
            VideoModelProvider provider = VideoModelProvider.fromCode(providerCode);
            return provider.getName();
        } catch (Exception e) {
            log.warn("未知的提供商代码: {}", providerCode);
            return providerCode;
        }
    }

    /**
     * 获取图片数量描述
     *
     * @param imageCount 图片数量
     * @return 图片数量描述
     */
    private String getImageCountDesc(Integer imageCount) {
        if (imageCount == null) {
            return "未知";
        }

        return switch (imageCount) {
            case 0 -> "不支持图片";
            case 1 -> "支持首帧";
            case 2 -> "支持首尾帧";
            default -> "支持" + imageCount + "张图片";
        };
    }
    


}
