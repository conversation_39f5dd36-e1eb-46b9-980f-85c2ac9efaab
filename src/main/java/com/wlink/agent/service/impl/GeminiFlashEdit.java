package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Interacts with the fal.run Gemini Flash Edit API using OkHttpClient.
 * @date 2025/4/28 13:55
 */
@Slf4j
@Service
@RequiredArgsConstructor// Make this a Spring service
public class GeminiFlashEdit {

    private static final String API_URL = "https://queue.fal.run/fal-ai/gemini-flash-edit";
    // FAL_KEY should ideally be injected from configuration properties
    private static final String FAL_KEY = "a9636bc0-5690-474c-b4f5-1aa47b2a6c24:0dca19d55cc59551ebb5d5c4f45edd6c";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;



    /**
     * Calls the Gemini Flash Edit API to modify an image based on a prompt and returns the request ID.
     *
     * @param prompt The instruction for image modification (e.g., "Make the car black").
     * @param imageUrl The URL of the image to edit.
     * @return The request ID from the API response.
     * @throws IOException If an I/O error occurs when sending or receiving.
     * @throws RuntimeException If the API call fails, the API key is not set, or the response cannot be parsed.
     */
    public String editImageAndGetRequestId(String prompt, String imageUrl)  {
        if (FAL_KEY == null || FAL_KEY.isEmpty()) {
            log.error("FAL_KEY environment variable not set.");
            throw new RuntimeException("FAL_KEY environment variable not set.");
        }

        // Construct JSON payload using ObjectMapper
        // Use HashMap for Java 8 compatibility
        Map<String, String> payloadMap = new java.util.HashMap<>();
        payloadMap.put("prompt", prompt);
        payloadMap.put("image_url", imageUrl);
        String jsonPayload = null;
        try {
            jsonPayload = objectMapper.writeValueAsString(payloadMap);
        } catch (JsonProcessingException e) {
            log.error("Error converting payload to JSON: {}", e.getMessage(), e);
            throw new RuntimeException("Error converting payload to JSON");
        }

        RequestBody body = RequestBody.create(jsonPayload, JSON);
        Request request = new Request.Builder()
                .url(API_URL)
                .post(body)
                .header("Authorization", "Key " + FAL_KEY)
                .header("Content-Type", "application/json") // Content-Type already set by MediaType
                .build();

        log.info("Sending request to Gemini Flash Edit API: URL={}, Payload Snippet={}", API_URL, jsonPayload.substring(0, Math.min(jsonPayload.length(), 100)));

        try (Response response = httpClient.newCall(request).execute()) {
            String responseBodyString = Objects.requireNonNull(response.body()).string(); // Ensure body is read fully
            log.debug("Received response: Code={}, Body={}", response.code(), responseBodyString);

            if (!response.isSuccessful()) {
                log.error("API call failed: Code={}, Body={}", response.code(), responseBodyString);
                throw new RuntimeException("API call failed with status code: " + response.code() + " and body: " + responseBodyString);
            }

            // Parse response JSON to extract request_id
            JsonNode responseJson = objectMapper.readTree(responseBodyString);
            if (responseJson.has("request_id") && responseJson.get("request_id").isTextual()) {
                String requestId = responseJson.get("request_id").asText();
                log.info("Successfully received request_id: {}", requestId);
                return requestId;
            } else {
                log.error("Could not extract 'request_id' from response: {}", responseBodyString);
                throw new RuntimeException("Could not extract 'request_id' from response: " + responseBodyString);
            }

        } catch (IOException e) {
            log.error("IOException during API call: {}", e.getMessage(), e);
            throw new BizException(""); // Re-throw IOExceptions
        } catch (Exception e) { // Catch other potential exceptions during processing
             log.error("Error processing API request or response: {}", e.getMessage(), e);
             throw new RuntimeException("Error processing API request or response", e);
        }
    }

    // Removed escapeJson method

    // Removed main method (should be tested via Spring context or unit tests)
}
