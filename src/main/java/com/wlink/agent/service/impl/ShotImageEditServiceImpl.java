package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.client.ComfyUIApiClient;
import com.wlink.agent.client.model.comfyui.ComfyUINodeInfo;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.wlink.agent.dao.mapper.AiShotImageEditMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiShotImageEditPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.enums.ResourceStatus;
import com.wlink.agent.utils.OssUtils;
import cn.hutool.core.util.IdUtil;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.ShotImageEditReq;
import com.wlink.agent.model.req.ShotLipSyncReq;
import com.wlink.agent.model.res.ShotImageEditRes;
import com.wlink.agent.model.res.ShotLipSyncRes;
import com.wlink.agent.service.AiCanvasShotService;
import com.wlink.agent.service.ShotImageEditService;
import com.wlink.agent.service.ShotLipSyncService;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分镜图片编辑服务实现
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class ShotImageEditServiceImpl implements ShotImageEditService {

    private final AiShotImageEditMapper shotImageEditMapper;
    private final AiShotMapper aiShotMapper;
    private final ComfyUIApiClient comfyUIApiClient;
    private final AiCanvasShotService aiCanvasShotService;
    private final ShotLipSyncService shotLipSyncService;
    private final AiCanvasImageMapper aiCanvasImageMapper;
    private final AiCanvasMaterialMapper aiCanvasMaterialMapper;
    private final OssUtils ossUtils;

    @Value("${comfyui.webapp-id:1947966253481754626}")
    private Long webappId;

    @Value("${comfyui.api-key:899272847c6746bf86df40a35fa9615c}")
    private String apiKey;

    @Value("${comfyui.webhook-url:https://dev.neodomain.cn/agent/comfyui/callback}")
    private String webhookUrl;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * OSS路径模板
     */
    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShotImageEditRes submitImageEdit(ShotImageEditReq req) {
        log.info("提交分镜图片编辑任务: shotId={}, prompt={}", req.getShotId(), req.getPrompt());

        // 1. 验证分镜是否存在
        AiCanvasShotPo aiCanvasShotPo = aiCanvasShotService.getById(req.getShotId());
        if (aiCanvasShotPo == null) {
            log.error("分镜不存在: shotId={}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 2. 获取当前用户信息
        SimpleUserInfo userInfo = UserContext.getUser();
        String userId = userInfo != null ? userInfo.getUserId() : "";

        // 3. 构建 ComfyUI 请求
        ComfyUIRunRequest comfyUIRequest = buildComfyUIRequest(req);

        // 4. 调用 ComfyUI API
        ComfyUIRunResponse comfyUIResponse;
        try {
            comfyUIResponse = comfyUIApiClient.runWorkflow(comfyUIRequest);
        } catch (Exception e) {
            log.error("调用 ComfyUI API 失败: shotId={}, error={}", req.getShotId(), e.getMessage(), e);
            throw new BizException("调用图片编辑服务失败: " + e.getMessage());
        }

        if (!comfyUIResponse.isSuccess() || comfyUIResponse.getData() == null) {
            log.error("ComfyUI API 返回失败: shotId={}, code={}, msg={}", 
                    req.getShotId(), comfyUIResponse.getCode(), comfyUIResponse.getMsg());
            throw new BizException("图片编辑任务提交失败: " + comfyUIResponse.getMsg());
        }

        aiCanvasShotPo.setShotStatus(ShotStatus.PROCESSING.getValue()); // 处理中
        aiCanvasShotPo.setUpdateTime(new Date());
        aiCanvasShotService.updateById(aiCanvasShotPo);


        // 5. 保存编辑记录到数据库
        AiShotImageEditPo editRecord = new AiShotImageEditPo();
        editRecord.setShotId(req.getShotId());
        editRecord.setTaskId(comfyUIResponse.getData().getTaskId());
        editRecord.setClientId(comfyUIResponse.getData().getClientId());
        editRecord.setNetWssUrl(comfyUIResponse.getData().getNetWssUrl());
        editRecord.setOriginalImageUrl(req.getOriginalImageUrl());
        editRecord.setMaskImageUrl(req.getMaskImageUrl());
        editRecord.setPrompt(req.getPrompt());
        editRecord.setStatus(comfyUIResponse.getData().getTaskStatus());
        editRecord.setUserId(userId);
        editRecord.setCreateTime(new Date());
        editRecord.setUpdateTime(new Date());
        editRecord.setDelFlag(0);

        int insertResult = shotImageEditMapper.insert(editRecord);
        if (insertResult <= 0) {
            log.error("保存编辑记录失败: shotId={}, taskId={}", req.getShotId(), comfyUIResponse.getData().getTaskId());
            throw new BizException("保存编辑记录失败");
        }

        log.info("分镜图片编辑任务提交成功: shotId={}, taskId={}, recordId={}", 
                req.getShotId(), comfyUIResponse.getData().getTaskId(), editRecord.getId());

        // 6. 构建响应
        ShotImageEditRes response = new ShotImageEditRes();
        BeanUtils.copyProperties(editRecord, response);
        return response;
    }

    @Override
    public ShotImageEditRes getEditRecordByTaskId(String taskId) {
        log.info("根据任务ID查询编辑记录: taskId={}", taskId);

        LambdaQueryWrapper<AiShotImageEditPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                .eq(AiShotImageEditPo::getDelFlag, 0);

        AiShotImageEditPo editRecord = shotImageEditMapper.selectOne(queryWrapper);
        if (editRecord == null) {
            log.warn("未找到编辑记录: taskId={}", taskId);
            return null;
        }

        ShotImageEditRes response = new ShotImageEditRes();
        BeanUtils.copyProperties(editRecord, response);
        return response;
    }

    @Override
    public List<ShotImageEditRes> getEditRecordsByShotId(Long shotId) {
        log.info("根据分镜ID查询编辑记录列表: shotId={}", shotId);

        LambdaQueryWrapper<AiShotImageEditPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiShotImageEditPo::getShotId, shotId)
                .eq(AiShotImageEditPo::getDelFlag, 0)
                .orderByDesc(AiShotImageEditPo::getCreateTime);

        List<AiShotImageEditPo> editRecords = shotImageEditMapper.selectList(queryWrapper);

        return editRecords.stream()
                .map(record -> {
                    ShotImageEditRes response = new ShotImageEditRes();
                    BeanUtils.copyProperties(record, response);
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEditRecordStatus(String taskId, String status, String resultImageUrl, Long taskCostTime) {
        log.info("更新编辑记录状态: taskId={}, status={}, resultImageUrl={}, taskCostTime={}",
                taskId, status, resultImageUrl, taskCostTime);

        try {
            // 1. 查询分镜图片编辑记录
            LambdaQueryWrapper<AiShotImageEditPo> editQueryWrapper = new LambdaQueryWrapper<>();
            editQueryWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                    .eq(AiShotImageEditPo::getDelFlag, 0);

            AiShotImageEditPo editRecord = shotImageEditMapper.selectOne(editQueryWrapper);
            if (editRecord == null) {
                log.warn("未找到分镜图片编辑记录: taskId={}", taskId);
                return;
            }

            // 2. 更新编辑记录状态
            LambdaUpdateWrapper<AiShotImageEditPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                    .set(AiShotImageEditPo::getStatus, status)
                    .set(AiShotImageEditPo::getUpdateTime, new Date());

            if (resultImageUrl != null) {
                updateWrapper.set(AiShotImageEditPo::getResultImageUrl, resultImageUrl);
            }

            if (taskCostTime != null) {
                updateWrapper.set(AiShotImageEditPo::getTaskCostTime, taskCostTime);
            }

            int updateResult = shotImageEditMapper.update(null, updateWrapper);
            if (updateResult <= 0) {
                log.error("更新编辑记录状态失败: taskId={}", taskId);
                return;
            }

            log.info("更新编辑记录状态成功: taskId={}, status={}", taskId, status);

            // 3. 如果任务成功完成，更新相关业务表
            if ("COMPLETED".equals(status) && resultImageUrl != null) {
                updateBusinessTablesOnSuccess(editRecord, resultImageUrl);
            }

        } catch (Exception e) {
            log.error("更新编辑记录状态异常: taskId={}, error={}", taskId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEditRecordToFailed(String taskId, String errorMessage) {
        log.info("更新编辑记录为失败状态: taskId={}, errorMessage={}", taskId, errorMessage);

        try {
            // 1. 查询编辑记录获取分镜ID
            LambdaQueryWrapper<AiShotImageEditPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                    .eq(AiShotImageEditPo::getDelFlag, 0);

            AiShotImageEditPo editRecord = shotImageEditMapper.selectOne(queryWrapper);
            if (editRecord == null) {
                log.warn("未找到分镜图片编辑记录: taskId={}", taskId);
                return;
            }

            // 2. 更新编辑记录状态
            LambdaUpdateWrapper<AiShotImageEditPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiShotImageEditPo::getTaskId, taskId)
                    .set(AiShotImageEditPo::getStatus, "FAILED")
                    .set(AiShotImageEditPo::getErrorMessage, errorMessage)
                    .set(AiShotImageEditPo::getUpdateTime, new Date());

            int updateResult = shotImageEditMapper.update(null, updateWrapper);
            if (updateResult <= 0) {
                log.error("更新编辑记录为失败状态失败: taskId={}", taskId);
                return;
            }

            log.info("更新编辑记录为失败状态成功: taskId={}", taskId);

            // 3. 更新分镜状态为失败
            updateCanvasShotStatus(editRecord.getShotId(), ShotStatus.FAILED.getValue());

        } catch (Exception e) {
            log.error("更新编辑记录为失败状态异常: taskId={}, error={}", taskId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 任务成功时更新相关业务表
     */
    private void updateBusinessTablesOnSuccess(AiShotImageEditPo editRecord, String resultImageUrl) {
        log.info("开始更新业务表状态 - 任务成功: taskId={}, shotId={}, resultImageUrl={}",
                editRecord.getTaskId(), editRecord.getShotId(), resultImageUrl);

        try {
            Long shotId = editRecord.getShotId();

            // 1. 根据shotId查询对应的画布分镜记录
            AiCanvasShotPo canvasShotPo = aiCanvasShotService.getById(shotId);
            if (canvasShotPo != null) {
                // 2. 更新或创建画布图片记录
                updateCanvasImage(canvasShotPo, resultImageUrl, editRecord.getPrompt(), editRecord.getUserId());

                // 3. 更新画布分镜状态为已完成
                updateCanvasShotStatus(canvasShotPo.getId(), ShotStatus.COMPLETED.getValue()); // 2-已完成

                log.info("画布相关表更新成功: canvasId={}, shotCode={}",
                        canvasShotPo.getCanvasId(), canvasShotPo.getCode());
            } else {
                log.warn("未找到对应的画布分镜记录: shotId={}", shotId);
            }

        } catch (Exception e) {
            log.error("更新业务表状态异常 - 任务成功: taskId={}, shotId={}, error={}",
                    editRecord.getTaskId(), editRecord.getShotId(), e.getMessage(), e);
        }
    }

    /**
     * 更新或创建画布图片记录
     */
    private void updateCanvasImage(AiCanvasShotPo canvasShotPo, String imageUrl, String prompt, String userId) {
        try {
            // 上传图片到OSS
            String ossImageUrl = ossUtils.uploadFile(imageUrl, OSS_PATH.replace("{env}", env)
                    .replace("{userId}", userId)
                    .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png");

            // 查询是否已存在画布图片记录
            LambdaQueryWrapper<AiCanvasImagePo> imageQueryWrapper = new LambdaQueryWrapper<>();
            imageQueryWrapper.eq(AiCanvasImagePo::getCanvasId, canvasShotPo.getCanvasId())
                    .eq(AiCanvasImagePo::getShotCode, canvasShotPo.getCode())
                    .eq(AiCanvasImagePo::getDelFlag, 0);

            AiCanvasImagePo canvasImagePo = aiCanvasImageMapper.selectOne(imageQueryWrapper);

            if (canvasImagePo == null) {
                // 创建新的画布图片记录
                canvasImagePo = new AiCanvasImagePo();
                canvasImagePo.setCanvasId(canvasShotPo.getCanvasId());
                canvasImagePo.setShotCode(canvasShotPo.getCode());
                canvasImagePo.setCreateTime(new Date());
                canvasImagePo.setDelFlag(0);
            }

            // 更新图片信息
            canvasImagePo.setImageUrl(ossImageUrl);
            canvasImagePo.setImagePrompt(prompt);
            canvasImagePo.setImageStatus(ResourceStatus.SUCCESS.getValue());
            canvasImagePo.setUpdateTime(new Date());

            if (canvasImagePo.getId() == null) {
                // 新增
                int insertResult = aiCanvasImageMapper.insert(canvasImagePo);
                log.info("创建画布图片记录: canvasId={}, shotCode={}, result={}",
                        canvasShotPo.getCanvasId(), canvasShotPo.getCode(), insertResult > 0 ? "成功" : "失败");
            } else {
                // 更新
                int updateResult = aiCanvasImageMapper.updateById(canvasImagePo);
                log.info("更新画布图片记录: id={}, result={}",
                        canvasImagePo.getId(), updateResult > 0 ? "成功" : "失败");
            }

            // 创建画布素材记录
            AiCanvasMaterialPo materialPo = new AiCanvasMaterialPo();
            materialPo.setCanvasId(canvasShotPo.getCanvasId());
            materialPo.setMaterialType(1); // 1-图片
            materialPo.setMaterialSource(1); // 1-生成
            materialPo.setMaterialUrl(ossImageUrl);
            materialPo.setCreateTime(new Date());
            materialPo.setUpdateTime(new Date());
            materialPo.setDelFlag(0);

            aiCanvasMaterialMapper.insert(materialPo);

        } catch (Exception e) {
            log.error("更新画布图片记录异常: canvasId={}, shotCode={}, error={}",
                    canvasShotPo.getCanvasId(), canvasShotPo.getCode(), e.getMessage(), e);
            throw new BizException("更新画布图片记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新画布分镜状态
     */
    private void updateCanvasShotStatus(Long canvasShotId, Integer status) {
        try {
            AiCanvasShotPo shotPo = aiCanvasShotService.getById(canvasShotId);
            if (shotPo == null) {
                log.error("分镜不存在: shotId={}", canvasShotId);
                throw new BizException("分镜不存在");
            }

            shotPo.setShotStatus(status);
            shotPo.setUpdateTime(new Date());

            boolean updateResult = aiCanvasShotService.updateById(shotPo);
            log.info("更新画布分镜状态: id={}, status={}, result={}",
                    canvasShotId, status, updateResult ? "成功" : "失败");

            if (!updateResult) {
                throw new BizException("更新画布分镜状态失败");
            }

        } catch (Exception e) {
            log.error("更新画布分镜状态异常: id={}, status={}, error={}",
                    canvasShotId, status, e.getMessage(), e);
            throw new BizException("更新画布分镜状态失败: " + e.getMessage());
        }
    }

    /**
     * 构建 ComfyUI 请求
     */
    private ComfyUIRunRequest buildComfyUIRequest(ShotImageEditReq req) {
        // 构建节点信息列表
        List<ComfyUINodeInfo> nodeInfoList = new ArrayList<>();

        // 节点199: 原始图像
        ComfyUINodeInfo originalImageNode = new ComfyUINodeInfo();
        originalImageNode.setNodeId("31");
        originalImageNode.setFieldName("value");
        originalImageNode.setFieldValue(req.getOriginalImageUrl());
        nodeInfoList.add(originalImageNode);

        // 节点207: 提示词
        ComfyUINodeInfo promptNode = new ComfyUINodeInfo();
        promptNode.setNodeId("25");
        promptNode.setFieldName("prompt");
        promptNode.setFieldValue(req.getPrompt());
        nodeInfoList.add(promptNode);

        // 节点220: 遮罩图像
        ComfyUINodeInfo maskImageNode = new ComfyUINodeInfo();
        maskImageNode.setNodeId("32");
        maskImageNode.setFieldName("value");
        maskImageNode.setFieldValue(req.getMaskImageUrl());
        nodeInfoList.add(maskImageNode);

        // 构建请求
        ComfyUIRunRequest comfyUIRequest = new ComfyUIRunRequest();
        comfyUIRequest.setWebappId(1947966253481754626L);
        comfyUIRequest.setApiKey(apiKey);
        comfyUIRequest.setNodeInfoList(nodeInfoList);
        comfyUIRequest.setWebhookUrl(webhookUrl);

        return comfyUIRequest;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileName(String url) {
        if (url == null || url.isEmpty()) {
            return url;
        }
        
        // 如果URL包含路径分隔符，提取最后一部分作为文件名
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }
        
        return url;
    }

    @Override
    public ShotLipSyncRes submitLipSync(ShotLipSyncReq req) {
        log.info("提交分镜对口型任务: shotId={}", req.getShotId());
        return shotLipSyncService.submitLipSync(req);
    }
}
