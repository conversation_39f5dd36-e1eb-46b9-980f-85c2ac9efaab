package com.wlink.agent.service.impl;

import com.wlink.agent.client.ComfyUIApiClient;
import com.wlink.agent.client.model.comfyui.ComfyUINodeInfo;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.wlink.agent.service.ComfyUIService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * ComfyUI 服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComfyUIServiceImpl implements ComfyUIService {

    private final ComfyUIApiClient comfyUIApiClient;

    @Override
    public ComfyUIRunResponse runWorkflow(Long webappId, String apiKey, List<ComfyUINodeInfo> nodeInfoList) {
        log.info("运行 ComfyUI 工作流: webappId={}, nodeCount={}", webappId, 
                nodeInfoList != null ? nodeInfoList.size() : 0);

        ComfyUIRunRequest request = new ComfyUIRunRequest();
        request.setWebappId(webappId);
        request.setApiKey(apiKey);
        request.setNodeInfoList(nodeInfoList);

        return comfyUIApiClient.runWorkflow(request);
    }

    @Override
    public ComfyUIRunResponse runWorkflow(ComfyUIRunRequest request) {
        log.info("运行 ComfyUI 工作流: webappId={}, nodeCount={}", 
                request.getWebappId(), 
                request.getNodeInfoList() != null ? request.getNodeInfoList().size() : 0);

        return comfyUIApiClient.runWorkflow(request);
    }

    @Override
    public ComfyUIRunResponse runImageReplaceWorkflow(Long webappId, String apiKey, 
                                                      String originalImage, String maskImage, String prompt) {
        log.info("运行图像替换工作流: webappId={}, originalImage={}, maskImage={}, prompt={}", 
                webappId, originalImage, maskImage, prompt);

        // 构建节点信息列表（基于您提供的示例）
        List<ComfyUINodeInfo> nodeInfoList = new ArrayList<>();

        // 节点199: 原始图像
        ComfyUINodeInfo imageNode = new ComfyUINodeInfo();
        imageNode.setNodeId("227");
        imageNode.setFieldName("image");
        imageNode.setFieldValue(originalImage);
        nodeInfoList.add(imageNode);

        // 节点207: 提示词
        ComfyUINodeInfo promptNode = new ComfyUINodeInfo();
        promptNode.setNodeId("241");
        promptNode.setFieldName("value");
        promptNode.setFieldValue(prompt);
        nodeInfoList.add(promptNode);

        // 节点220: 遮罩图像
        ComfyUINodeInfo maskNode = new ComfyUINodeInfo();
        maskNode.setNodeId("226");
        maskNode.setFieldName("image");
        maskNode.setFieldValue(maskImage);
        nodeInfoList.add(maskNode);

        return runWorkflow(webappId, apiKey, nodeInfoList);
    }
}
