package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.client.MiniMaxFileDownloadClient;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.model.req.MiniMaxVideoCallbackReq;
import com.wlink.agent.service.MiniMaxVideoCallbackService;
import com.wlink.agent.service.VideoGenerationQueueService;
import com.wlink.agent.utils.OssUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;

/**
 * MiniMax视频生成回调服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniMaxVideoCallbackServiceImpl implements MiniMaxVideoCallbackService {
    
    private final VideoGenerationQueueService queueService;
    private final MiniMaxFileDownloadClient downloadClient;
    private final OssUtils ossUtils;
    
    @Value("${spring.profiles.active}")
    private String env;
    
    private static final String OSS_PATH_TEMPLATE = "dify/{env}/{userId}/video/";
    
    @Override
    public boolean handleCallback(MiniMaxVideoCallbackReq req) {
        log.info("处理MiniMax视频生成回调: taskId={}, status={}", req.getTaskId(), req.getStatus());
        
        try {
            // 验证外部任务ID是否存在
            AiVideoGenerationPo task = queueService.findTaskByExternalId(req.getTaskId());
            if (task == null) {
                log.error("未找到对应的内部任务: externalId={}", req.getTaskId());
                throw new BizException("未找到对应的内部任务: " + req.getTaskId());
            }
            
            Long internalTaskId = task.getId();
            log.info("找到对应的内部任务: externalId={}, internalId={}", req.getTaskId(), internalTaskId);
            
            // 根据状态处理
            if (req.isSuccess()) {
                // 成功状态
                if (req.getFileId() != null && !req.getFileId().isEmpty()) {
                    log.info("MiniMax视频生成成功，开始异步下载: taskId={}, fileId={}", 
                            req.getTaskId(), req.getFileId());
                    
                    // 异步下载并上传视频文件
                    asyncDownloadAndUploadVideo(req.getTaskId(), req.getFileId());
                    
                    // 异步更新记录表
                    queueService.asyncUpdateVideoGenerationRecord(
                            req.getTaskId(),
                            "success",
                            null, // 视频URL稍后更新
                            null,
                            null,
                            null
                    );
                    
                    return true;
                } else {
                    log.error("MiniMax视频生成成功但缺少文件ID: taskId={}", req.getTaskId());
                    String errorMsg = "视频生成成功但缺少文件ID";
                    queueService.failTask(internalTaskId, errorMsg);
                    
                    // 异步更新记录表
                    queueService.asyncUpdateVideoGenerationRecord(
                            req.getTaskId(),
                            "failed",
                            null,
                            errorMsg,
                            null,
                            null
                    );
                    
                    return false;
                }
            } else if (req.isFailed()) {
                // 失败状态
                String errorMsg = req.getErrorMessage();
                log.error("MiniMax视频生成失败: taskId={}, error={}", req.getTaskId(), errorMsg);
                queueService.failTask(internalTaskId, errorMsg);
                
                // 异步更新记录表
                queueService.asyncUpdateVideoGenerationRecord(
                        req.getTaskId(),
                        "failed",
                        null,
                        errorMsg,
                        null,
                        null
                );
                
                return false;
            } else if (req.isProcessing()) {
                // 处理中状态，只更新记录表
                log.info("MiniMax视频生成处理中: taskId={}, status={}", req.getTaskId(), req.getStatus());
                
                // 异步更新记录表
                queueService.asyncUpdateVideoGenerationRecord(
                        req.getTaskId(),
                        "processing",
                        null,
                        null,
                        null,
                        null
                );
                
                return true;
            } else {
                log.warn("未知的MiniMax回调状态: taskId={}, status={}", req.getTaskId(), req.getStatus());
                return true;
            }
            
        } catch (Exception e) {
            log.error("处理MiniMax视频生成回调异常: taskId={}", req.getTaskId(), e);
            return false;
        }
    }
    
    @Override
    @Async
    public void asyncDownloadAndUploadVideo(String taskId, String fileId) {
        log.info("开始异步下载并上传MiniMax视频: taskId={}, fileId={}", taskId, fileId);
        
        try {
            // 查找任务信息
            AiVideoGenerationPo task = queueService.findTaskByExternalId(taskId);
            if (task == null) {
                log.error("异步处理时未找到任务: taskId={}", taskId);
                return;
            }
            
            // 下载文件
            String videoUrl = downloadClient.retrieveFileAndUploadToOss(fileId, task.getUserId()).get();
            log.info("MiniMax视频下载成功: taskId={}, fileId={}，videoUrl:{}", taskId, fileId,videoUrl);

            // 更新任务状态为完成
            queueService.completeTask(task.getId(), videoUrl);
            
            // 更新记录表中的视频URL
            queueService.asyncUpdateVideoGenerationRecord(
                    taskId,
                    "success",
                    videoUrl,
                    null,
                    null,
                    null
            );
            
            log.info("MiniMax视频处理完成: taskId={}, fileId={}, videoUrl={}", 
                    taskId, fileId, videoUrl);
            
        } catch (Exception e) {
            log.error("异步下载并上传MiniMax视频失败: taskId={}, fileId={}", taskId, fileId, e);
            
            try {
                // 查找任务信息并标记为失败
                AiVideoGenerationPo task = queueService.findTaskByExternalId(taskId);
                if (task != null) {
                    String errorMsg = "视频下载或上传失败: " + e.getMessage();
                    queueService.failTask(task.getId(), errorMsg);
                    
                    // 更新记录表
                    queueService.asyncUpdateVideoGenerationRecord(
                            taskId,
                            "failed",
                            null,
                            errorMsg,
                            null,
                            null
                    );
                }
            } catch (Exception ex) {
                log.error("标记任务失败时异常: taskId={}", taskId, ex);
            }
        }
    }
}
