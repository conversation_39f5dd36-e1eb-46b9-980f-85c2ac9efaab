package com.wlink.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.dao.mapper.AiCreationVisualRecordMapper;
import com.wlink.agent.dao.po.AiCreationVisualRecordPo;
import com.wlink.agent.model.dto.RenderEvent;
import com.wlink.agent.model.dto.VisualRecordDto;
import com.wlink.agent.service.AiCreationVisualRecordService;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.service.VisualRenderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
@RequiredArgsConstructor // 使用 Lombok 自动注入 final 字段
public class VisualRenderServiceImpl implements VisualRenderService {

    private final WebClient webClient;
    private final AiCreationVisualRecordMapper aiCreationVisualRecordMapper; // 注入数据库服务
    private final UserPointsService userPointsService; // 注入积分服务

    // Define a scheduler for blocking DB operations
    private static final Scheduler DB_SCHEDULER = Schedulers.boundedElastic();

    // 状态常量
    private static final int STATE_SUCCESS = 2;
    private static final int STATE_FAILURE = 3;

    // 从配置文件读取 URL，更灵活
    @Value("${external.render.api.url:http://video.neodomain.com:8000/render}")
    private String renderApiUrl;

    // 定义超时时间，例如 10 分钟
    private static final Duration SSE_TIMEOUT = Duration.ofMinutes(20);

    @Override
    public Mono<Void> renderVisual(String visualRecordCode, VisualRecordDto request) {
        log.info("Initiating visual rendering for visualRecordCode [{}], request to: {}", visualRecordCode, renderApiUrl);

        // 用于跟踪是否已成功处理 "done" 事件
        AtomicBoolean successHandled = new AtomicBoolean(false);
        // 用于跟踪上一次的进度值
        AtomicReference<Integer> lastProgress = new AtomicReference<>(null);

        // The stream processing results in side effects (DB updates), so the final type is Flux<Void>
        Flux<Void> processingStream = webClient.post()
                .uri(renderApiUrl)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(RenderEvent.class) // Still need to parse events
                .timeout(SSE_TIMEOUT)
                .doOnSubscribe(subscription -> log.info("Subscribed to SSE stream for visualRecordCode [{}].", visualRecordCode))
                .concatMap(event -> processEvent(visualRecordCode, event, successHandled, lastProgress)) // Pass lastProgress
                .doOnError(error -> log.error("Error during SSE stream processing for visualRecordCode [{}]: {}", visualRecordCode, error.getMessage(), error))
                .doOnComplete(() -> log.info("SSE stream processing completed for visualRecordCode [{}].", visualRecordCode));

        // Handle stream completion or error to set the final state if not already set successfully
        return processingStream
                .then(Mono.defer(() -> { // Use defer to check the flag *after* the main stream completes
                    // If the stream completed normally but success was never handled (no valid 'done' event)
                    if (!successHandled.get()) {
                        log.warn("SSE stream for visualRecordCode [{}] completed without a successful 'done' event. Marking as failed.", visualRecordCode);
                        // Wrap synchronous DB call
                        return Mono.fromRunnable(() -> {
                            try {
                                // 首先更新视频状态
                                updateStatus(visualRecordCode, STATE_FAILURE);
                                log.info("Updated video status to FAILURE for visualRecordCode [{}]", visualRecordCode);
                            } catch (Exception e) {
                                log.error("Failed to update video status for visualRecordCode [{}]: {}", visualRecordCode, e.getMessage(), e);
                            }
                            
                            try {
                                // 然后尝试返还积分
                                userPointsService.refundPointsForFailedVideoGeneration(visualRecordCode);
                                log.info("Refunding points for failed render: visualRecordCode [{}]", visualRecordCode);
                            } catch (Exception e) {
                                log.error("Failed to refund points for visualRecordCode [{}]: {}", visualRecordCode, e.getMessage(), e);
                            }
                        })
                        .subscribeOn(DB_SCHEDULER) // Run on separate thread pool
                        .doOnError(dbError -> log.error("Failed to update final failure state for visualRecordCode [{}]: {}", visualRecordCode, dbError.getMessage(), dbError))
                        .then(); // Convert Mono<Void> from Runnable to signal completion
                    }
                    return Mono.empty();
                }))
                .onErrorResume(error -> {
                    log.error("SSE stream for visualRecordCode [{}] terminated with error. Marking as failed.", visualRecordCode, error);
                    // Wrap synchronous DB call
                    return Mono.fromRunnable(() -> {
                        try {
                            // 首先更新视频状态
                            updateStatus(visualRecordCode, STATE_FAILURE);
                            log.info("Updated video status to FAILURE for visualRecordCode [{}] after error", visualRecordCode);
                        } catch (Exception e) {
                            log.error("Failed to update video status for visualRecordCode [{}] after error: {}", visualRecordCode, e.getMessage(), e);
                        }
                        
                        try {
                            // 然后尝试返还积分
                            userPointsService.refundPointsForFailedVideoGeneration(visualRecordCode);
                            log.info("Refunding points for failed render due to error: visualRecordCode [{}]", visualRecordCode);
                        } catch (Exception e) {
                            log.error("Failed to refund points for visualRecordCode [{}] after error: {}", visualRecordCode, e.getMessage(), e);
                        }
                    })
                    .subscribeOn(DB_SCHEDULER) // Run on separate thread pool
                    .doOnError(dbError -> log.error("Failed to update failure state after stream error for visualRecordCode [{}]: {}", visualRecordCode, dbError.getMessage(), dbError))
                    .then(Mono.empty()); // Ensure Mono<Void> completion signal even after handling error
                });
    }

    /**
     * Processes a single SSE event, performing DB updates asynchronously.
     */
    private Mono<Void> processEvent(String visualRecordCode, RenderEvent event, AtomicBoolean successHandled, AtomicReference<Integer> lastProgress) {
        log.debug("Processing event for visualRecordCode [{}]: {}", visualRecordCode, event);
        Mono<Void> progressUpdateMono = Mono.empty();
        Mono<Void> completionUpdateMono = Mono.empty();

        // Update progress only if it has changed
        if (event.getProgress() != null) {
            Integer currentProgress = event.getProgress();
            Integer previousProgress = lastProgress.get();

            // Only process if progress has changed
            if (previousProgress == null || !currentProgress.equals(previousProgress)) {
                lastProgress.set(currentProgress); // Update last known progress

                // Use correct getter based on RenderEvent definition
                String taskId = event.getTask_id() != null ? event.getTask_id() : "unknown";
                log.info("Rendering progress changed for visualRecordCode [{}], task [{}]: {}%", visualRecordCode, taskId, currentProgress);

                // Wrap synchronous DB call
                progressUpdateMono = Mono.fromRunnable(() -> updateProgress(visualRecordCode, currentProgress))
                        .subscribeOn(DB_SCHEDULER) // Run on separate thread pool
                        .doOnError(e -> log.error("Failed to update progress for visualRecordCode [{}]: {}", visualRecordCode, e.getMessage(), e))
                        .then(); // Convert Mono<Void> from Runnable
                        // Ignoring progress update errors for now to let the main stream continue
            } else {
                 log.trace("Progress unchanged for visualRecordCode [{}], task [{}]: {}. Skipping update.",
                           visualRecordCode, event.getTask_id() != null ? event.getTask_id() : "unknown", currentProgress);
                 // No update needed, progressUpdateMono remains Mono.empty()
            }
        }

        // Handle completion event
        if ("done".equalsIgnoreCase(event.getStatus())) {
            // Use correct getter based on RenderEvent definition
            String ossUrl = event.getOss_url();
            if (ossUrl != null && !ossUrl.trim().isEmpty()) {
                 // Use correct getter based on RenderEvent definition
                log.info("Rendering task [{}] for visualRecordCode [{}] completed successfully. OSS URL: {}", event.getTask_id(), visualRecordCode, ossUrl);
                successHandled.set(true);

                // Wrap synchronous DB call for final update
                completionUpdateMono = Mono.fromRunnable(() -> updateUrlAndStatus(visualRecordCode, ossUrl, STATE_SUCCESS))
                           .subscribeOn(DB_SCHEDULER) // Run on separate thread pool
                           .doOnError(e -> log.error("Failed to update URL and success status for visualRecordCode [{}]: {}", visualRecordCode, e.getMessage(), e))
                           .then(); // Convert Mono<Void> from Runnable
            } else {
                 // Use correct getter based on RenderEvent definition
                log.warn("Received 'done' event for visualRecordCode [{}] but OSS URL is missing or empty. Task ID: {}", visualRecordCode, event.getTask_id());
                // This case is handled by the .then(Mono.defer(...)) block after the stream completes
            }
        } else if (event.getStatus() != null && !event.getStatus().isEmpty()) {
             // Use correct getter based on RenderEvent definition
             log.debug("Received intermediate event for visualRecordCode [{}]: status={}, progress={}, taskId={}",
                     visualRecordCode, event.getStatus(), event.getProgress(), event.getTask_id());
        }

        // Chain progress update (if any) and completion update (if any) sequentially
        // Ensure the combined operation signals completion as Mono<Void>
        return progressUpdateMono.then(completionUpdateMono);
    }
    private void updateProgress(String visualRecordCode, int progress) {
        log.debug("Attempting to update progress to [{}%] for visualRecordCode ID: {}", progress, visualRecordCode);
        int boundedProgress = Math.max(0, Math.min(100, progress));

        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
                .set(AiCreationVisualRecordPo::getVideoProgress, boundedProgress)
                .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        aiCreationVisualRecordMapper.update(updateWrapper);
    }
    private void updateUrlAndStatus(String visualRecordCode, String url, int status) {
        log.debug("Attempting to update URL to [{}] and status to [{}] for visualRecordCode : {}", url, status, visualRecordCode);
        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
                .set(AiCreationVisualRecordPo::getVideoUrl, url)
                .set(AiCreationVisualRecordPo::getVideoState, status)
                .set(AiCreationVisualRecordPo::getVideoProgress, 100)
                .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        aiCreationVisualRecordMapper.update(updateWrapper);

    }
    private void updateStatus(String visualRecordCode, int status) {
        log.debug("Attempting to update status to [{}] for visualRecordCode : {}", status, visualRecordCode);
        LambdaUpdateWrapper<AiCreationVisualRecordPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(AiCreationVisualRecordPo::getVisualRecordCode, visualRecordCode)
                .set(AiCreationVisualRecordPo::getVideoState, status)
                .set(AiCreationVisualRecordPo::getUpdateTime, new Date());
        aiCreationVisualRecordMapper.update(updateWrapper);
    }



}