package com.wlink.agent.service.impl;


import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.client.CompletionApiClient;
import com.wlink.agent.client.DoubaoImageEditApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.config.ExternalApiConfig;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.enums.ResourceStatus;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.model.req.CanvasMaterialCreateReq;
import com.wlink.agent.model.req.CanvasMaterialPageReq;
import com.wlink.agent.model.req.ImageGenerateReq;
import com.wlink.agent.model.res.CanvasMaterialRes;
import com.wlink.agent.model.res.ImageGenerateRes;
import com.wlink.agent.model.res.ImageMaterialParams;
import com.wlink.agent.model.res.VideoMaterialParams;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.service.AiCanvasMaterialService;
import com.wlink.agent.service.AiCanvasShotService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ai_canvas_material(画布素材表)】的数据库操作Service实现
 * @createDate 2025-06-24 17:39:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiCanvasMaterialServiceImpl extends ServiceImpl<AiCanvasMaterialMapper, AiCanvasMaterialPo>
        implements AiCanvasMaterialService {

    private final AiCanvasMapper canvasMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final AiCanvasShotMapper canvasShotMapper;
    private final AiImageTaskQueueMapper imageTaskQueueMapper;
    private final ImageTaskMQHandler imageTaskMQHandler;
    private final AiCanvasShotService aiCanvasShotService;
    private final CompletionApiClient completionApiClient;
    private final ExternalApiConfig externalApiConfig;
    private final AiVideoGenerationMapper videoGenerationMapper;
    private final DoubaoImageEditApiClient doubaoImageEditApiClient;

    @Override
    public Page<CanvasMaterialRes> pageQueryMaterials(CanvasMaterialPageReq req) {
        if (req.getCanvasId() == null) {
            throw new BizException("画布ID不能为空");
        }

        // 验证画布是否存在且属于当前用户
        AiCanvasPo canvasPo = canvasMapper.selectById(req.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found, id: {}", req.getCanvasId());
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to view canvas materials {}", currentUserId, req.getCanvasId());
            throw new BizException("无权限查看此画布素材");
        }

        // 构建查询条件
        LambdaQueryWrapper<AiCanvasMaterialPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasMaterialPo::getCanvasId, req.getCanvasId())
                .eq(AiCanvasMaterialPo::getDelFlag, 0);

        // 素材类型过滤
        if (req.getMaterialType() != null) {
            queryWrapper.eq(AiCanvasMaterialPo::getMaterialType, req.getMaterialType());
        }

        // 素材来源过滤
        if (req.getMaterialSource() != null) {
            queryWrapper.eq(AiCanvasMaterialPo::getMaterialSource, req.getMaterialSource());
        }

        // 关键词搜索
        if (StringUtils.hasText(req.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(AiCanvasMaterialPo::getMaterialName, req.getKeyword())
                    .or()
                    .like(AiCanvasMaterialPo::getMaterialDesc, req.getKeyword())
            );
        }

        // 排序
        String orderBy = req.getOrderBy();
        String orderDirection = req.getOrderDirection();

        if ("create_time".equals(orderBy)) {
            if ("asc".equals(orderDirection)) {
                queryWrapper.orderByAsc(AiCanvasMaterialPo::getCreateTime);
            } else {
                queryWrapper.orderByDesc(AiCanvasMaterialPo::getCreateTime);
            }
        } else {
            // 默认按创建时间倒序
            queryWrapper.orderByDesc(AiCanvasMaterialPo::getCreateTime);
        }

        // 分页查询
        Page<AiCanvasMaterialPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<AiCanvasMaterialPo> resultPage = this.page(page, queryWrapper);

        // 转换为响应对象
        List<CanvasMaterialRes> materialResList = resultPage.getRecords().stream()
                .map(this::convertToMaterialRes)
                .collect(Collectors.toList());

        // 构建分页响应
        Page<CanvasMaterialRes> responsePage = new Page<>();
        responsePage.setCurrent(resultPage.getCurrent());
        responsePage.setSize(resultPage.getSize());
        responsePage.setTotal(resultPage.getTotal());
        responsePage.setPages(resultPage.getPages());
        responsePage.setRecords(materialResList);

        log.info("Page queried canvas materials for canvas {}, page: {}, size: {}, total: {}",
                req.getCanvasId(), req.getPageNum(), req.getPageSize(), resultPage.getTotal());

        return responsePage;
    }

    /**
     * 将素材PO转换为素材响应DTO
     */
    private CanvasMaterialRes convertToMaterialRes(AiCanvasMaterialPo materialPo) {
        CanvasMaterialRes res = new CanvasMaterialRes();
        BeanUtils.copyProperties(materialPo, res);

        // 设置描述字段（会自动触发setter中的描述生成逻辑）
        res.setMaterialType(materialPo.getMaterialType());
        res.setMaterialSource(materialPo.getMaterialSource());
        res.setMaterialUrl(MediaUrlPrefixUtil.getMediaUrl(materialPo.getMaterialUrl()));
        res.setMaterialDuration(materialPo.getMaterialDuration());
        
        // 如果是生成的素材，查询额外信息
        if (materialPo.getMaterialSource() != null && materialPo.getMaterialSource() == 1) {
            // 生成的素材
            if (materialPo.getMaterialType() != null && materialPo.getMaterialType() == 1) {
                // 生成的图片，查询ai_image_task_queue表
                if (materialPo.getGenerationRecordId() != null) {
                    AiImageTaskQueuePo taskPo = imageTaskQueueMapper.selectById(materialPo.getGenerationRecordId());
                    if (taskPo != null && StringUtils.hasText(taskPo.getRequestParams())) {
                        try {
                            JSONObject params = JSON.parseObject(taskPo.getRequestParams());
                            
                            // 创建图片参数对象
                            ImageMaterialParams imageParams = ImageMaterialParams.builder()
                                    .prompt(params.containsKey("prompt") ? params.getString("prompt") : 
                                           (params.containsKey("cnPrompt") ? params.getString("cnPrompt") : null))
                                    .referenceImageUrl(params.containsKey("imageUrl") ? params.getString("imageUrl") : null)
                                    .strength(params.containsKey("guidanceScale") ? params.getDouble("guidanceScale") : null)
                                    .build();
                            
                            // 处理宽高比
                            if (params.containsKey("aspectRatio")) {
                                imageParams.setAspectRatio(params.getString("aspectRatio"));
                            } else if (params.containsKey("size")) {
                                String size = params.getString("size");
                                if (StringUtils.hasText(size)) {
                                    // 从size解析比例，例如 1024x1024 -> 1:1
                                    String[] dimensions = size.split("x");
                                    if (dimensions.length == 2) {
                                        int width = Integer.parseInt(dimensions[0]);
                                        int height = Integer.parseInt(dimensions[1]);
                                        if (width == height) {
                                            imageParams.setAspectRatio("1:1");
                                        } else if (width > height) {
                                            // 简化比例
                                            if (width / height == 16 / 9) {
                                                imageParams.setAspectRatio("16:9");
                                            } else if (width / height == 4 / 3) {
                                                imageParams.setAspectRatio("4:3");
                                            } else {
                                                imageParams.setAspectRatio(width + ":" + height);
                                            }
                                        } else {
                                            // 简化比例
                                            if (height / width == 16 / 9) {
                                                imageParams.setAspectRatio("9:16");
                                            } else if (height / width == 4 / 3) {
                                                imageParams.setAspectRatio("3:4");
                                            } else {
                                                imageParams.setAspectRatio(width + ":" + height);
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 设置图片参数对象
                            res.setImageMaterialParams(imageParams);
                        } catch (Exception e) {
                            log.error("Failed to parse image task request params: {}", taskPo.getRequestParams(), e);
                        }
                    }
                }
            } else if (materialPo.getMaterialType() != null && materialPo.getMaterialType() == 2) {
                // 生成的视频，查询ai_video_generation_record表
                if (materialPo.getGenerationRecordId() != null) {
                    AiVideoGenerationPo aiVideoGenerationPo = videoGenerationMapper.selectById(materialPo.getGenerationRecordId());
                    if (aiVideoGenerationPo != null) {
                        // 创建视频参数对象
                        VideoMaterialParams videoParams = VideoMaterialParams.builder()
                                .prompt(aiVideoGenerationPo.getPrompt())
                                .firstFrameUrl(MediaUrlPrefixUtil.getMediaUrl(aiVideoGenerationPo.getFirstFrameImage()))
                                .lastFrameUrl(MediaUrlPrefixUtil.getMediaUrl(aiVideoGenerationPo.getLastFrameImage()))
                                .resolution(aiVideoGenerationPo.getResolution())
                                .duration(aiVideoGenerationPo.getDuration() * 1000L)
                                .ratio(aiVideoGenerationPo.getRatio())
                                .build();
                        
                        // 设置视频参数对象
                        res.setVideoMaterialParams(videoParams);
                    }
                }
            }
        }else {

            if (Objects.equals(materialPo.getMaterialType(), 2)){
                // 创建视频参数对象
                VideoMaterialParams videoParams = VideoMaterialParams.builder()
                        .duration(materialPo.getMaterialDuration())
                        .build();

                // 设置视频参数对象
                res.setVideoMaterialParams(videoParams);
            }

        }

        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMaterial(CanvasMaterialCreateReq req) {
        if (req.getCanvasId() == null) {
            throw new BizException("画布ID不能为空");
        }

        if (req.getMaterialType() == null) {
            throw new BizException("素材类型不能为空");
        }

        if (!StringUtils.hasText(req.getMaterialUrl())) {
            throw new BizException("素材URL不能为空");
        }

        // 验证素材类型
        if (req.getMaterialType() != 1 && req.getMaterialType() != 2 && req.getMaterialType() != 3) {
            throw new BizException("素材类型只能是1(图片)或2(视频)");
        }

        // 验证画布是否存在且属于当前用户
        AiCanvasPo canvasPo = canvasMapper.selectById(req.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found, id: {}", req.getCanvasId());
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to add material to canvas {}", currentUserId, req.getCanvasId());
            throw new BizException("无权限向此画布添加素材");
        }

        // 创建素材记录
        AiCanvasMaterialPo materialPo = new AiCanvasMaterialPo();
        materialPo.setCanvasId(req.getCanvasId());
        materialPo.setMaterialType(req.getMaterialType());
        materialPo.setMaterialUrl(req.getMaterialUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX,""));
        materialPo.setMaterialName(req.getMaterialName());
        materialPo.setMaterialDesc(req.getMaterialDesc());
        materialPo.setMaterialDuration(req.getMaterialDuration());
        materialPo.setFirstFrameUrl("");
        materialPo.setMaterialSource(2); // 上传
        materialPo.setCreateTime(new Date());
        materialPo.setUpdateTime(new Date());
        materialPo.setDelFlag(0);

        this.save(materialPo);

        log.info("Created material for canvas {}, material id: {}, type: {}, source: {}",
                req.getCanvasId(), materialPo.getId(), req.getMaterialType(), materialPo.getMaterialSource());

        return materialPo.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImageGenerateRes generateImage(ImageGenerateReq req) {
        log.info("Starting image generation process: {}", JSON.toJSONString(req));

        String userId = UserContext.getUser().getUserId();
        // 参数验证
        if (!StringUtils.hasText(req.getPrompt())) {
            throw new BizException("生成提示词不能为空");
        }

        // 验证分镜是否存在（如果提供了分镜ID）
        AiCanvasShotPo shotPo = canvasShotMapper.selectById(req.getShotId());
        if (shotPo == null || shotPo.getDelFlag() == 1) {
            log.error("Shot not found, id: {}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 验证分镜所属画布的权限
        AiCanvasPo aiCanvasPo = canvasMapper.selectById(shotPo.getCanvasId());
        if (aiCanvasPo == null || aiCanvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        if (!userId.equals(aiCanvasPo.getUserId())) {
            log.error("User {} has no permission to generate image for shot {}", userId, req.getShotId());
            throw new BizException("无权限为此分镜生成图片");
        }

        // 根据是否有参考图判断使用的模型
        String imageModel;
        String mqType = "";
        String taskType;
        if (StringUtils.hasText(req.getReferenceImageUrl())) {
            // 有参考图使用豆包图像编辑API
            log.info("检测到参考图，使用豆包图像编辑API: {}", req.getReferenceImageUrl());
            imageModel = "DOUBAO_EDIT";
        } else {
            // 没有参考图使用DOUBAO模型
            imageModel = "DOUBAO";
            mqType = TaskType.GENERATE_DOUBAO.getValue();
        }

        // 组装请求参数，参考ImageTaskQueueServiceImpl的实现
        String requestParamsJson;
        if ("DOUBAO".equalsIgnoreCase(imageModel)) {
            // 豆包 API 请求参数
            Map<String, Object> doubaoParams = new HashMap<>();
            doubaoParams.put("model", "doubao-seedream-3-0-t2i-250415");
            doubaoParams.put("prompt", req.getPrompt());
            doubaoParams.put("cnPrompt", req.getPrompt());
            doubaoParams.put("seed", aiCanvasPo.getSeed()); // 使用时间戳作为随机种子
            doubaoParams.put("guidanceScale", req.getStrength() != null ? req.getStrength().doubleValue() : 7.5);

            // 根据aspectRatio计算尺寸
            String size = calculateSizeFromAspectRatio(req.getAspectRatio());
            doubaoParams.put("size", size);

            requestParamsJson = JSON.toJSONString(doubaoParams);
            taskType = TaskType.GENERATE_CANVAS.getValue();
        } else {
            DoubaoImageEditRequest editRequest = new DoubaoImageEditRequest();
            editRequest.setModel("doubao-seededit-3-0-i2i-250628");
            editRequest.setPrompt(req.getPrompt());
            editRequest.setImage(MediaUrlPrefixUtil.getMediaUrl(req.getReferenceImageUrl()));
            editRequest.setResponseFormat("url");
            editRequest.setSize("adaptive");
            editRequest.setSeed(aiCanvasPo.getSeed() != null ? aiCanvasPo.getSeed() : -1);
            editRequest.setGuidanceScale(req.getStrength() != null ? req.getStrength().doubleValue() : 5.5);
            editRequest.setWatermark(false);
            requestParamsJson = JSON.toJSONString(editRequest);
            taskType = TaskType.EDIT_CANVAS.getValue();

        }
        // 如果有分镜ID，在ai_canvas_image表中新增一条记录，状态为生成中
        // 更新分镜状态为处理中
        shotPo.setShotStatus(ShotStatus.PROCESSING.getValue()); // 处理中
        shotPo.setUpdateTime(new Date());
        canvasShotMapper.updateById(shotPo);

        log.info("Updated shot {} status to PROCESSING", req.getShotId());
        // 创建任务记录
        AiImageTaskQueuePo taskPo = AiImageTaskQueuePo.builder()
                .userId(userId)
                .sessionId(aiCanvasPo.getCode())
                .contentType(4) // 内容类型为4
                .contentId(shotPo.getCode())
                .taskType(taskType)
                .requestParams(requestParamsJson)
                .taskStatus(TaskStatus.PENDING.getValue())
                .imageModel(imageModel)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        // 保存到数据库
        imageTaskQueueMapper.insert(taskPo);
        Long taskId = taskPo.getId();

        log.info("Created image generation task with ID: {}, model: {}, mqType: {}",
                taskId, imageModel, mqType);
        LambdaQueryWrapper<AiCanvasImagePo> imageQueryWrapper = new LambdaQueryWrapper<>();
        imageQueryWrapper.eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasImagePo::getShotCode, shotPo.getCode())
                .eq(AiCanvasImagePo::getDelFlag, 0);
        AiCanvasImagePo imagePo = canvasImageMapper.selectOne(imageQueryWrapper);

        // 4. 有就更新成最新的数据，没有就插入
        if (imagePo != null) {
            // 更新现有记录
            imagePo.setImageStatus(ResourceStatus.GENERATING.getValue());
            imagePo.setUpdateTime(new Date());
            imagePo.setImagePrompt(req.getPrompt());
            imagePo.setImageAspectRatio(req.getAspectRatio());
            imagePo.setReferenceImage(StringUtils.hasText(req.getReferenceImageUrl()) ? req.getReferenceImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX,"") : "");
            canvasImageMapper.updateById(imagePo);
            log.info("更新图片记录成功: imageId={}, imageUrl={}", imagePo.getId(), taskPo.getImageResult());
        } else {
            // 插入新记录
            imagePo = new AiCanvasImagePo();
            imagePo.setCanvasId(shotPo.getCanvasId());
            imagePo.setShotCode(shotPo.getCode());
            imagePo.setImageUrl(taskPo.getImageResult());
            imagePo.setImageStatus(ResourceStatus.GENERATING.getValue());
            imagePo.setImagePrompt(req.getPrompt());
            imagePo.setImageAspectRatio(req.getAspectRatio());
            imagePo.setReferenceImage(StringUtils.hasText(req.getReferenceImageUrl()) ? req.getReferenceImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX,"") : "");
            imagePo.setCreateTime(new Date());
            imagePo.setUpdateTime(new Date());
            imagePo.setDelFlag(0);
            canvasImageMapper.insert(imagePo);
            log.info("插入新图片记录成功: imageId={}, imageUrl={}", imagePo.getId(), taskPo.getImageResult());
        }
        try {
            if (StringUtils.hasText(mqType)) {
                // 发送图片处理任务
                imageTaskMQHandler.sendTaskMessage(String.valueOf(taskId), mqType);
            }
            log.info("Sent MQ message for task: {}, taskType: {}", taskId, mqType);
        } catch (
                Exception e) {
            log.error("Failed to send MQ message for task: {}", taskId, e);
            throw new BizException("任务提交失败，请稍后重试");
        }

        // 返回响应
        return new ImageGenerateRes(taskId);
    }

    /**
     * 根据宽高比计算尺寸
     */
    private String calculateSizeFromAspectRatio(String aspectRatio) {
        if (!StringUtils.hasText(aspectRatio)) {
            return "2048x2048"; // 默认1:1
        }
        return switch (aspectRatio) {
            case "1:1" -> "2048x2048"; // 1:1比例，最大支持
            case "16:9" -> "1792x1008"; // 16:9比例，保持在2048以下
            case "9:16" -> "1008x1792"; // 9:16比例，保持在2048以下
            case "4:3" -> "1536x1152"; // 4:3比例，保持在2048以下
            case "3:4" -> "1152x1536"; // 3:4比例，保持在2048以下
            default -> "2048x2048"; // 默认1:1
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterial(Long materialId) {
        if (materialId == null) {
            throw new BizException("素材ID不能为空");
        }

        // 查询素材是否存在
        AiCanvasMaterialPo materialPo = this.getById(materialId);
        if (materialPo == null || materialPo.getDelFlag() == 1) {
            log.error("Material not found or already deleted, id: {}", materialId);
            throw new BizException("素材不存在或已被删除");
        }

        // 验证素材所属画布的权限
        AiCanvasPo canvasPo = canvasMapper.selectById(materialPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found for material, materialId: {}, canvasId: {}",
                    materialId, materialPo.getCanvasId());
            throw new BizException("素材所属画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to delete material {} from canvas {}",
                    currentUserId, materialId, materialPo.getCanvasId());
            throw new BizException("无权限删除此素材");
        }

        this.removeById(materialPo.getId());

        log.info("Deleted material: id={}, canvasId={}, type={}, source={}, user={}",
                materialId, materialPo.getCanvasId(), materialPo.getMaterialType(),
                materialPo.getMaterialSource(), currentUserId);
    }
}
