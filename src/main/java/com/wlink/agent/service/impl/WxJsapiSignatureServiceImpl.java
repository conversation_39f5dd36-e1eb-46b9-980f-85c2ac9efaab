package com.wlink.agent.service.impl;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.cola.exception.BizException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.config.WechatProperties;
import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.model.WxJsapiSignature;
import com.wlink.agent.service.WxJsapiSignatureService;


import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 微信JS-SDK签名服务实现
 */
@Slf4j
@Service
public class WxJsapiSignatureServiceImpl implements WxJsapiSignatureService {

    private final WechatProperties wechatProperties;
    private final OkHttpClient okHttpClient;
    private final RedissonClient redissonClient;

    @Autowired
    public WxJsapiSignatureServiceImpl(WechatProperties wechatProperties, OkHttpClient okHttpClient, RedissonClient redissonClient) {
        this.wechatProperties = wechatProperties;
        this.okHttpClient = okHttpClient;
        this.redissonClient = redissonClient;
    }

    @Override
    public String getJsapiTicket() {
        // 1. 先从缓存获取
        RBucket<String> ticketBucket = redissonClient.getBucket(RedisKeyConstant.WECHAT_JSAPI_TICKET);
        String jsapiTicket = ticketBucket.get();
        
        // 2. 缓存不存在或已过期，重新获取
        if (StringUtils.isBlank(jsapiTicket)) {
            String accessToken = getAccessToken();
            
            if (StringUtils.isNotBlank(accessToken)) {
                jsapiTicket = requestJsapiTicket(accessToken);
                
                // 成功获取ticket后缓存
                if (StringUtils.isNotBlank(jsapiTicket)) {
                    ticketBucket.set(jsapiTicket, wechatProperties.getJsapiTicketExpireSeconds(), TimeUnit.SECONDS);
                    log.info("JSAPI ticket refreshed and cached for {} seconds", wechatProperties.getJsapiTicketExpireSeconds());
                }
            }
        }
        
        return jsapiTicket;
    }

    @Override
    public WxJsapiSignature createJsapiSignature(String url) {
        // 1. 获取jsapi_ticket
        String jsapiTicket = getJsapiTicket();
        
        if (StringUtils.isBlank(jsapiTicket)) {
            log.error("Failed to create jsapi signature: jsapi_ticket is empty");
           throw new BizException("Failed to create jsapi signature: jsapi_ticket is empty");
        }
        
        // 2. 准备签名参数
        String nonceStr = generateNonceStr();
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        
        // 3. 按照字典序拼接参数
        String signatureStr = "jsapi_ticket=" + jsapiTicket +
                              "&noncestr=" + nonceStr +
                              "&timestamp=" + timestamp +
                              "&url=" + url;
        
        // 4. 对字符串进行SHA1签名
        String signature = DigestUtils.sha1Hex(signatureStr);
        
        // 5. 创建并返回签名结果对象
        WxJsapiSignature result = new WxJsapiSignature();
        result.setAppId(wechatProperties.getMpAppId());
        result.setTimestamp(timestamp);
        result.setNonceStr(nonceStr);
        result.setSignature(signature);
        result.setUrl(url);
        
        return result;
    }
    
    /**
     * 获取微信access_token
     * 会优先从缓存获取，缓存不存在时重新请求并缓存
     * 
     * @return access_token
     */
    private String getAccessToken() {
        // 1. 先从缓存获取
        RBucket<String> tokenBucket = redissonClient.getBucket(RedisKeyConstant.WECHAT_ACCESS_TOKEN);
        String accessToken = tokenBucket.get();
        
        // 2. 缓存不存在或已过期，重新获取
        if (StringUtils.isBlank(accessToken)) {
            accessToken = requestAccessToken();
            
            // 成功获取token后缓存
            if (StringUtils.isNotBlank(accessToken)) {
                tokenBucket.set(accessToken, wechatProperties.getJsapiTicketExpireSeconds(), TimeUnit.SECONDS);
                log.info("Access token refreshed and cached for {} seconds", wechatProperties.getJsapiTicketExpireSeconds());
            }
        }
        
        return accessToken;
    }
    
    /**
     * 请求微信服务器获取access_token
     * 
     * @return access_token或null（获取失败）
     */
    private String requestAccessToken() {
        try {
            HttpUrl url = HttpUrl.parse("https://api.weixin.qq.com/cgi-bin/token").newBuilder()
                    .addQueryParameter("grant_type", "client_credential")
                    .addQueryParameter("appid", wechatProperties.getMpAppId())
                    .addQueryParameter("secret", wechatProperties.getMpAppSecret())
                    .build();
            
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to get access_token, HTTP code: {}", response.code());
                    throw new BizException("Failed to get access_token: HTTP code " + response.code());
                }
                
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                
                // 检查是否有错误码
                if (jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode") != 0) {
                    log.error("Failed to get access_token, errcode: {}, errmsg: {}", 
                            jsonObject.getInteger("errcode"),
                            jsonObject.getString("errmsg"));
                    throw new BizException("Failed to get access_token: " + jsonObject.getString("errmsg"));
                }
                
                // 返回access_token
                return jsonObject.getString("access_token");
            }
        } catch (IOException e) {
            log.error("Failed to get access_token", e);
            throw new BizException("Failed to get access_token");
        }
    }
    
    /**
     * 使用access_token请求jsapi_ticket
     * 
     * @param accessToken 有效的access_token
     * @return jsapi_ticket或null（获取失败）
     */
    private String requestJsapiTicket(String accessToken) {
        try {
            HttpUrl url = HttpUrl.parse("https://api.weixin.qq.com/cgi-bin/ticket/getticket").newBuilder()
                    .addQueryParameter("access_token", accessToken)
                    .addQueryParameter("type", "jsapi")
                    .build();
            
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to get jsapi_ticket, HTTP code: {}", response.code());
                    throw new BizException("Failed to get jsapi_ticket: HTTP code " + response.code());
                }
                
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                
                // 检查是否有错误码
                if (jsonObject.getIntValue("errcode") != 0) {
                    log.error("Failed to get jsapi_ticket, errcode: {}, errmsg: {}", 
                            jsonObject.getInteger("errcode"),
                            jsonObject.getString("errmsg"));
                    throw new BizException("Failed to get jsapi_ticket: " + jsonObject.getString("errmsg"));
                }
                
                // 返回jsapi_ticket
                return jsonObject.getString("ticket");
            }
        } catch (IOException e) {
            log.error("Failed to get jsapi_ticket", e);
            throw new BizException("Failed to get jsapi_ticket");
        }
    }
    
    /**
     * 生成随机字符串
     * 
     * @return 随机字符串
     */
    private String generateNonceStr() {
        return RandomUtil.randomString(16);
    }
} 