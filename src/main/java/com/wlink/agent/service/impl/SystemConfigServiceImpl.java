package com.wlink.agent.service.impl;

import com.wlink.agent.dao.dto.SystemConfigDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service

public class SystemConfigServiceImpl implements SystemConfigService {

    @Value("${ws.endpoint}")
    String wsEndpoint;
    @Value("${audio2face.endpoint}")
    String audio2faceEndpoint;

    @Override
    public SystemConfigDTO findSystemConfig() {

        return SystemConfigDTO.builder()
                .wsEndpoint(wsEndpoint)
                .audio2faceEndpoint(audio2faceEndpoint)
                .build();
    }
}
