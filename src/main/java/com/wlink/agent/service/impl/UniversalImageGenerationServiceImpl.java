package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.client.FalAiFluxKontextClient;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextTextToImageRequest;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextMaxTextToImageRequest;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextImageToImageRequest;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextMaxImageToImageRequest;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextMaxMultiRequest;
import com.wlink.agent.client.FalAiFluxKontextClient.QueueStatus;
import com.wlink.agent.dao.mapper.AiUserResourceMapper;
import com.wlink.agent.dao.po.AiUserResourcePo;
import com.wlink.agent.enums.UserResourceGenerationStatus;
import com.wlink.agent.enums.UserResourceType;
import com.wlink.agent.model.req.UniversalImageGenerationRequest;
import com.wlink.agent.model.res.UniversalImageGenerationResponse;
import com.wlink.agent.model.res.UserResourceStatusResponse;
import com.wlink.agent.service.UniversalImageGenerationService;
import com.wlink.agent.utils.ResourceCodeGenerator;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用图片生成服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UniversalImageGenerationServiceImpl implements UniversalImageGenerationService {

    private final FalAiFluxKontextClient falAiFluxKontextClient;
    private final AiUserResourceMapper userResourceMapper;
    private final ResourceCodeGenerator resourceCodeGenerator;

    // 支持的模型类型常量
    private static final String MODEL_KONTEXT_PRO = "kontext-pro";
    private static final String MODEL_KONTEXT_MAX = "kontext-max";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UniversalImageGenerationResponse generateImage(UniversalImageGenerationRequest request) {
        log.info("开始处理通用图片生成请求: {}", JSON.toJSONString(request));

        // 获取当前用户ID
        String userId = UserContext.getUser().getUserId();
        if (!StringUtils.hasText(userId)) {
            throw new BizException("用户未登录");
        }

        // 验证请求参数
        validateRequest(request);

        // 生成资源编码
        String resourceCode = resourceCodeGenerator.generateImageCode();

        // 创建用户资源记录
        AiUserResourcePo userResource = createUserResourceRecord(userId, resourceCode, request);

        try {
            // 根据模型类型和参数选择合适的方法
            QueueStatus queueStatus = submitGenerationRequest(request);

            // 更新资源记录的外部请求ID
            userResource.setExternalRequestId(queueStatus.getRequestId());
            userResource.setUpdateTime(new Date());
            userResourceMapper.updateById(userResource);

            log.info("图片生成任务提交成功，资源编码: {}, 外部请求ID: {}", resourceCode, queueStatus.getRequestId());

            return UniversalImageGenerationResponse.builder()
                    .code(resourceCode)
                    .status(UserResourceGenerationStatus.PENDING.getValue())
                    .externalRequestId(queueStatus.getRequestId())
                    .message("图片生成任务已提交，请使用资源编码查询生成状态")
                    .build();

        } catch (Exception e) {
            log.error("图片生成任务提交失败，资源编码: {}, 错误: {}", resourceCode, e.getMessage(), e);

            // 更新资源记录为失败状态
            userResource.setGenerationStatus(UserResourceGenerationStatus.FAILED.getValue());
            userResource.setErrorMessage(e.getMessage());
            userResource.setUpdateTime(new Date());
            userResourceMapper.updateById(userResource);

            throw new BizException("图片生成任务提交失败: " + e.getMessage());
        }
    }

    @Override
    public UserResourceStatusResponse getResourceStatus(String code) {
        if (!StringUtils.hasText(code)) {
            throw new BizException("资源编码不能为空");
        }

        AiUserResourcePo userResource = userResourceMapper.selectByCode(code);
        if (userResource == null) {
            throw new BizException("资源不存在");
        }

        // 检查资源所有权
        String currentUserId = UserContext.getUser().getUserId();
        if (!userResource.getUserId().equals(currentUserId)) {
            throw new BizException("无权访问该资源");
        }

        return convertToStatusResponse(userResource);
    }

    @Override
    public List<UserResourceStatusResponse> getResourceStatusBatch(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }

        List<AiUserResourcePo> userResources = userResourceMapper.selectByCodes(codes);
        
        // 检查资源所有权
        String currentUserId = UserContext.getUser().getUserId();
        List<AiUserResourcePo> filteredResources = userResources.stream()
                .filter(resource -> resource.getUserId().equals(currentUserId))
                .collect(Collectors.toList());

        return filteredResources.stream()
                .map(this::convertToStatusResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserResourceStatusResponse> getUserResources(String userId, Integer resourceType, Integer limit) {
        // 检查用户权限
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(userId)) {
            throw new BizException("无权访问其他用户的资源");
        }

        List<AiUserResourcePo> userResources;
        if (resourceType != null) {
            userResources = userResourceMapper.selectByUserIdAndType(userId, resourceType, limit);
        } else {
            userResources = userResourceMapper.selectByUserId(userId, limit);
        }

        return userResources.stream()
                .map(this::convertToStatusResponse)
                .collect(Collectors.toList());
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(UniversalImageGenerationRequest request) {
        if (!StringUtils.hasText(request.getModelType())) {
            throw new BizException("模型类型不能为空");
        }

        if (!MODEL_KONTEXT_PRO.equalsIgnoreCase(request.getModelType()) && 
            !MODEL_KONTEXT_MAX.equalsIgnoreCase(request.getModelType())) {
            throw new BizException("不支持的模型类型: " + request.getModelType() + 
                    "，只支持 " + MODEL_KONTEXT_PRO + " 或 " + MODEL_KONTEXT_MAX);
        }

        if (!StringUtils.hasText(request.getPrompt())) {
            throw new BizException("提示词不能为空");
        }

        if (request.getNumImages() != null && (request.getNumImages() < 1 || request.getNumImages() > 4)) {
            throw new BizException("生成图片数量必须在1-4之间");
        }

        if (request.getGuidanceScale() != null && (request.getGuidanceScale() < 1 || request.getGuidanceScale() > 20)) {
            throw new BizException("引导比例必须在1-20之间");
        }
    }



    /**
     * 创建用户资源记录
     */
    private AiUserResourcePo createUserResourceRecord(String userId, String resourceCode, UniversalImageGenerationRequest request) {
        AiUserResourcePo userResource = AiUserResourcePo.builder()
                .code(resourceCode)
                .userId(userId)
                .resourceType(UserResourceType.IMAGE.getValue())
                .generationStatus(UserResourceGenerationStatus.PENDING.getValue())
                .modelType(request.getModelType())
                .prompt(request.getPrompt())
                .referenceImages(CollectionUtils.isEmpty(request.getImageUrls()) ? null : JSON.toJSONString(request.getImageUrls()))
                .generationParams(JSON.toJSONString(request))
                .createTime(new Date())
                .updateTime(new Date())
                .delFlag(0)
                .build();

        userResourceMapper.insert(userResource);
        return userResource;
    }

    /**
     * 提交生成请求
     */
    private QueueStatus submitGenerationRequest(UniversalImageGenerationRequest request) throws IOException {
        String modelType = request.getModelType();
        List<String> imageUrls = request.getImageUrls();
        boolean hasImages = !CollectionUtils.isEmpty(imageUrls);

        if (MODEL_KONTEXT_PRO.equalsIgnoreCase(modelType)) {
            if (hasImages && imageUrls.size() == 1) {
                // 单图像到图像
                return submitKontextImageToImage(request);
            } else if (!hasImages) {
                // 文本到图像
                return submitKontextTextToImage(request);
            } else {
                throw new BizException("kontext-pro模型只支持单张参考图片或无参考图片");
            }
        } else if (MODEL_KONTEXT_MAX.equalsIgnoreCase(modelType)) {
            if (hasImages && imageUrls.size() > 1) {
                // 多图像处理
                return submitKontextMaxMulti(request);
            } else if (hasImages && imageUrls.size() == 1) {
                // 单图像到图像
                return submitKontextMaxImageToImage(request);
            } else if (!hasImages) {
                // 文本到图像
                return submitKontextMaxTextToImage(request);
            } else {
                throw new BizException("kontext-max模型参数错误");
            }
        } else {
            throw new BizException("不支持的模型类型: " + modelType);
        }
    }

    /**
     * 提交Kontext文本到图像请求
     */
    private QueueStatus submitKontextTextToImage(UniversalImageGenerationRequest request) throws IOException {
        KontextTextToImageRequest kontextRequest = new KontextTextToImageRequest();
        kontextRequest.setPrompt(request.getPrompt());
        kontextRequest.setAspectRatio(request.getAspectRatio());
        kontextRequest.setNumImages(request.getNumImages());
        kontextRequest.setOutputFormat(request.getOutputFormat());
        kontextRequest.setSyncMode(request.getSyncMode());
        kontextRequest.setSafetyTolerance(request.getSafetyTolerance());
        kontextRequest.setGuidanceScale(request.getGuidanceScale());
        kontextRequest.setSeed(request.getSeed());

        return falAiFluxKontextClient.submitKontextTextToImageRequest(kontextRequest);
    }

    /**
     * 提交Kontext Max文本到图像请求
     */
    private QueueStatus submitKontextMaxTextToImage(UniversalImageGenerationRequest request) throws IOException {
        KontextMaxTextToImageRequest kontextRequest = new KontextMaxTextToImageRequest();
        kontextRequest.setPrompt(request.getPrompt());
        kontextRequest.setAspectRatio(request.getAspectRatio());
        kontextRequest.setNumImages(request.getNumImages());
        kontextRequest.setOutputFormat(request.getOutputFormat());
        kontextRequest.setSyncMode(request.getSyncMode());
        kontextRequest.setSafetyTolerance(request.getSafetyTolerance());
        kontextRequest.setGuidanceScale(request.getGuidanceScale());
        kontextRequest.setSeed(request.getSeed());

        return falAiFluxKontextClient.submitKontextMaxTextToImageRequest(kontextRequest);
    }

    /**
     * 提交Kontext图像到图像请求
     */
    private QueueStatus submitKontextImageToImage(UniversalImageGenerationRequest request) throws IOException {
        KontextImageToImageRequest kontextRequest = new KontextImageToImageRequest();
        kontextRequest.setPrompt(request.getPrompt());
        kontextRequest.setImageUrl(request.getImageUrls().get(0)); // 取第一张图片
        kontextRequest.setAspectRatio(request.getAspectRatio());
        kontextRequest.setNumImages(request.getNumImages());
        kontextRequest.setOutputFormat(request.getOutputFormat());
        kontextRequest.setSyncMode(request.getSyncMode());
        kontextRequest.setSafetyTolerance(request.getSafetyTolerance());
        kontextRequest.setGuidanceScale(request.getGuidanceScale());
        kontextRequest.setSeed(request.getSeed());

        return falAiFluxKontextClient.submitKontextImageToImageRequest(kontextRequest);
    }

    /**
     * 提交Kontext Max图像到图像请求
     */
    private QueueStatus submitKontextMaxImageToImage(UniversalImageGenerationRequest request) throws IOException {
        KontextMaxImageToImageRequest kontextRequest = new KontextMaxImageToImageRequest();
        kontextRequest.setPrompt(request.getPrompt());
        kontextRequest.setImageUrl(request.getImageUrls().get(0)); // 取第一张图片
        kontextRequest.setAspectRatio(request.getAspectRatio());
        kontextRequest.setNumImages(request.getNumImages());
        kontextRequest.setOutputFormat(request.getOutputFormat());
        kontextRequest.setSyncMode(request.getSyncMode());
        kontextRequest.setSafetyTolerance(request.getSafetyTolerance());
        kontextRequest.setGuidanceScale(request.getGuidanceScale());
        kontextRequest.setSeed(request.getSeed());

        return falAiFluxKontextClient.submitKontextMaxImageToImageRequest(kontextRequest);
    }

    /**
     * 提交Kontext Max多图像请求
     */
    private QueueStatus submitKontextMaxMulti(UniversalImageGenerationRequest request) throws IOException {
        KontextMaxMultiRequest kontextRequest = new KontextMaxMultiRequest();
        kontextRequest.setPrompt(request.getPrompt());
        kontextRequest.setImageUrls(request.getImageUrls());
        kontextRequest.setAspectRatio(request.getAspectRatio());
        kontextRequest.setNumImages(request.getNumImages());
        kontextRequest.setOutputFormat(request.getOutputFormat());
        kontextRequest.setSyncMode(request.getSyncMode());
        kontextRequest.setSafetyTolerance(request.getSafetyTolerance());
        kontextRequest.setGuidanceScale(request.getGuidanceScale());
        kontextRequest.setSeed(request.getSeed());

        return falAiFluxKontextClient.submitKontextMaxMultiRequest(kontextRequest);
    }

    /**
     * 转换为状态响应对象
     */
    private UserResourceStatusResponse convertToStatusResponse(AiUserResourcePo userResource) {
        List<String> resourceUrls = null;
        if (StringUtils.hasText(userResource.getResourceUrls())) {
            try {
                resourceUrls = JSON.parseObject(userResource.getResourceUrls(), new TypeReference<List<String>>() {});
            } catch (Exception e) {
                log.warn("解析资源URL失败: {}", e.getMessage());
            }
        }

        return UserResourceStatusResponse.builder()
                .code(userResource.getCode())
                .resourceType(userResource.getResourceType())
                .generationStatus(userResource.getGenerationStatus())
                .resourceUrls(resourceUrls)
                .resourceSize(userResource.getResourceSize())
                .width(userResource.getWidth())
                .height(userResource.getHeight())
                .duration(userResource.getDuration())
                .modelType(userResource.getModelType())
                .prompt(userResource.getPrompt())
                .errorMessage(userResource.getErrorMessage())
                .createTime(userResource.getCreateTime())
                .updateTime(userResource.getUpdateTime())
                .build();
    }
}
