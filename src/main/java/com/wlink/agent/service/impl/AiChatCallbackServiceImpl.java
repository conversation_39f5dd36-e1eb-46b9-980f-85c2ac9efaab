package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.wlink.agent.model.req.AiChatCallbackReq;
import com.wlink.agent.model.res.AiChatCallbackRes;
import com.wlink.agent.service.AiChatCallbackService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class AiChatCallbackServiceImpl implements AiChatCallbackService {

    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    @Value("${ai.chat.callback.url:http://49.234.19.72:8018/live/api/v1/agent/live/weiling/aichat_callback}")
    private String callbackUrl;

    @Value("${ai.chat.callback.token:123}")
    private String token;

    @Override
    public AiChatCallbackRes callback(AiChatCallbackReq req) {
        int maxRetries = 3;
        int retryCount = 0;
        long retryDelay = 1000; // 1秒

        while (retryCount < maxRetries) {
            try {
                req.setToken(token);
                String jsonBody = JSON.toJSONString(req);
                RequestBody body = RequestBody.create(jsonBody, JSON_TYPE);
                Request request = new Request.Builder()
                        .url(callbackUrl)
                        .post(body)
                        .build();

                log.info("Sending AI chat callback request: {}", jsonBody);

                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected response code: " + response);
                    }

                    ResponseBody responseBody = response.body();
                    if (responseBody == null) {
                        throw new IOException("Empty response body");
                    }

                    String responseStr = responseBody.string();
                    log.info("Received AI chat callback response: {}", responseStr);

                    AiChatCallbackRes aiChatCallbackRes = JSON.parseObject(responseStr, AiChatCallbackRes.class);

                    if (!aiChatCallbackRes.getSuccess()){
                        throw new BizException("AI chat callback failed: " + aiChatCallbackRes.getMessage());
                    }
                    return aiChatCallbackRes;
                }

            } catch (Exception e) {
                retryCount++;
                log.error("AI chat callback failed, attempt {}/{}", retryCount, maxRetries, e);

                if (retryCount >= maxRetries) {
                    log.error("Max retries reached for AI chat callback");
                    throw new BizException("AI chat callback failed after " + maxRetries + " retries");
                }

                try {
                    Thread.sleep(retryDelay * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Retry interrupted", ie);
                }
            }
        }

        throw new BizException("AI chat callback failed after " + maxRetries + " retries");
    }
}