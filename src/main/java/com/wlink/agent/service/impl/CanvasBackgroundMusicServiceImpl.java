package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.dao.mapper.AiCanvasBackgroundMusicMapper;
import com.wlink.agent.dao.po.AiCanvasBackgroundMusicPo;
import com.wlink.agent.model.req.CanvasBackgroundMusicReq;
import com.wlink.agent.model.res.CanvasBackgroundMusicRes;
import com.wlink.agent.service.CanvasBackgroundMusicService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 画布背景音乐服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CanvasBackgroundMusicServiceImpl extends ServiceImpl<AiCanvasBackgroundMusicMapper, AiCanvasBackgroundMusicPo>
        implements CanvasBackgroundMusicService {
    
    private final AiCanvasBackgroundMusicMapper backgroundMusicMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long setCanvasBackgroundMusic(CanvasBackgroundMusicReq req) {
        log.info("设置画布背景音乐: canvasId={}, audioUrl={}", req.getCanvasId(), req.getAudioUrl());

        // 如果音频URL为空，则删除背景音乐
        if (req.getAudioUrl() == null || req.getAudioUrl().trim().isEmpty()) {
            log.info("音频URL为空，删除画布背景音乐: canvasId={}", req.getCanvasId());
            AiCanvasBackgroundMusicPo existingMusic = backgroundMusicMapper.selectByCanvasId(req.getCanvasId());
            if (existingMusic != null) {
                int deletedCount = backgroundMusicMapper.deleteByCanvasId(req.getCanvasId());
                log.info("画布背景音乐删除完成: canvasId={}, 删除数量={}", req.getCanvasId(), deletedCount);
                return existingMusic.getId(); // 返回被删除的音乐ID
            } else {
                log.info("画布背景音乐不存在，无需删除: canvasId={}", req.getCanvasId());
            }
        }

        // 查询是否已存在背景音乐
        AiCanvasBackgroundMusicPo existingMusic = backgroundMusicMapper.selectByCanvasId(req.getCanvasId());

        Date now = new Date();

        if (existingMusic != null) {
            // 更新现有背景音乐
            log.info("更新现有背景音乐: musicId={}, canvasId={}", existingMusic.getId(), req.getCanvasId());
            BeanUtils.copyProperties(req, existingMusic);
            existingMusic.setAudioUrl(existingMusic.getAudioUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            existingMusic.setAudioName(req.getName());
            existingMusic.setStartPlayTime(req.getStartTime());
            existingMusic.setEndPlayTime(req.getEndTime());
            existingMusic.setUpdateTime(now);
            this.updateById(existingMusic);
            return existingMusic.getId();
        } else {
            // 创建新的背景音乐
            log.info("创建新的背景音乐: canvasId={}", req.getCanvasId());
            AiCanvasBackgroundMusicPo musicPo = new AiCanvasBackgroundMusicPo();
            BeanUtils.copyProperties(req, musicPo);
            musicPo.setAudioName(req.getName());
            musicPo.setAudioUrl(musicPo.getAudioUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            musicPo.setStartPlayTime(req.getStartTime());
            musicPo.setEndPlayTime(req.getEndTime());
            musicPo.setCreateTime(now);
            musicPo.setUpdateTime(now);
            musicPo.setDelFlag(0);

            this.save(musicPo);
            log.info("背景音乐创建成功: musicId={}, canvasId={}", musicPo.getId(), req.getCanvasId());
            return musicPo.getId();
        }
    }
    
    @Override
    public CanvasBackgroundMusicRes getCanvasBackgroundMusic(Long canvasId) {
        log.debug("获取画布背景音乐: canvasId={}", canvasId);
        
        AiCanvasBackgroundMusicPo musicPo = backgroundMusicMapper.selectByCanvasId(canvasId);
        if (musicPo == null) {
            log.debug("画布背景音乐不存在: canvasId={}", canvasId);
            return null;
        }
        
        CanvasBackgroundMusicRes musicRes = new CanvasBackgroundMusicRes();
        BeanUtils.copyProperties(musicPo, musicRes);
        musicRes.setName(musicPo.getAudioName());
        musicRes.setStartTime(musicPo.getStartPlayTime());
        musicRes.setEndTime(musicPo.getEndPlayTime());
        musicRes.setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(musicPo.getAudioUrl()));
        
        log.debug("获取画布背景音乐成功: canvasId={}, musicId={}", canvasId, musicRes.getId());
        return musicRes;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeCanvasBackgroundMusic(Long canvasId) {
        log.info("删除画布背景音乐: canvasId={}", canvasId);
        
        AiCanvasBackgroundMusicPo existingMusic = backgroundMusicMapper.selectByCanvasId(canvasId);
        if (existingMusic == null) {
            log.warn("要删除的背景音乐不存在: canvasId={}", canvasId);
            throw new BizException("背景音乐不存在");
        }
        
        int deletedCount = backgroundMusicMapper.deleteByCanvasId(canvasId);
        log.info("画布背景音乐删除完成: canvasId={}, 删除数量={}", canvasId, deletedCount);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCanvasBackgroundMusic(Long canvasId, CanvasBackgroundMusicReq req) {
        log.info("更新画布背景音乐: canvasId={}, audioName={}", canvasId, req.getName());
        
        AiCanvasBackgroundMusicPo existingMusic = backgroundMusicMapper.selectByCanvasId(canvasId);
        if (existingMusic == null) {
            log.warn("要更新的背景音乐不存在: canvasId={}", canvasId);
            throw new BizException("背景音乐不存在");
        }
        
        // 更新字段
        BeanUtils.copyProperties(req, existingMusic);
        existingMusic.setCanvasId(canvasId); // 确保画布ID不变
        existingMusic.setUpdateTime(new Date());
        
        this.updateById(existingMusic);
        log.info("画布背景音乐更新成功: canvasId={}, musicId={}", canvasId, existingMusic.getId());
    }
}
