package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.config.AliSmsConfig;
import com.wlink.agent.dao.mapper.AiSmsRecordMapper;
import com.wlink.agent.dao.po.AiSmsRecordPo;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.service.SmsService;
import com.wlink.agent.utils.I18nMessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云短信服务实现类
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    private final AliSmsConfig smsConfig;
    private final AiSmsRecordMapper smsRecordMapper;
    
    @Value("${spring.profiles.active}")
    private String env;

    public SmsServiceImpl(AliSmsConfig smsConfig, AiSmsRecordMapper smsRecordMapper) {
        this.smsConfig = smsConfig;
        this.smsRecordMapper = smsRecordMapper;
    }

    /**
     * 创建阿里云短信客户端
     */
    private Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(smsConfig.getAccessKeyId())
                .setAccessKeySecret(smsConfig.getAccessKeySecret())
                .setRegionId(smsConfig.getRegionId());
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    @Override
    public boolean sendSms(String phoneNumber, String content) {
        // 提取验证码
        String verificationCode = content;
        
        // 构建模板参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", verificationCode);
        
        // 调用模板发送
        return sendSmsWithTemplate(phoneNumber, smsConfig.getVerificationTemplateCode(), 
                JSON.toJSONString(paramMap), null);
    }

    /**
     * 从内容中提取验证码
     * 示例内容：您的验证码是：123456，5分钟内有效，请勿泄露给他人。
     */
//    private String extractVerificationCode(String content) {
//        try {
//            // 简单提取：从冒号后提取6位数字验证码
//            int colonIndex = content.indexOf("：");
//            if (colonIndex != -1) {
//                String afterColon = content.substring(colonIndex + 1);
//                // 假设验证码是6位数字
//                return afterColon.substring(0, 6);
//            }
//        } catch (Exception e) {
//            log.error("Failed to extract verification code from content: {}", content, e);
//        }
//
//        // 默认返回空，但应该不会发生
//        return "";
//    }

    @Override
    public boolean sendSmsWithTemplate(String phoneNumber, String templateCode, String templateParam, String userId) {
        // 非生产环境模拟发送
//        if (!"prod".equals(env)) {
//            // 记录发送记录
//            saveSuccessRecord(phoneNumber, templateParam, templateCode, "MOCK-REQ-ID", "MOCK-BIZ-ID", userId, 1);
//            log.info("Non-production environment, mock sending SMS to {}, template: {}, params: {}", phoneNumber, templateCode, templateParam);
//            return true;
//        }

        // 创建短信记录，初始状态为发送中(0)
        AiSmsRecordPo smsRecord = createSmsRecord(phoneNumber, templateCode, templateParam, userId);
        
        try {
            // 发送短信
            Client client = createClient();
            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(phoneNumber)
                    .setSignName(smsConfig.getSignName())
                    .setTemplateCode(templateCode)
                    .setTemplateParam(templateParam);
            
            // 发送请求
            SendSmsResponse response = client.sendSms(request);
            
            // 处理返回结果
            String requestId = response.getBody().getRequestId();
            String bizId = response.getBody().getBizId();
            
            // 判断是否发送成功
            if ("OK".equalsIgnoreCase(response.getBody().getCode())) {
                // 更新发送记录为成功
                updateSmsRecordSuccess(smsRecord.getId(), requestId, bizId);
                log.info("SMS sent successfully to {}, requestId: {}, bizId: {}", phoneNumber, requestId, bizId);
                return true;
            } else {
                // 更新发送记录为失败
                updateSmsRecordFailed(smsRecord.getId(), requestId, response.getBody().getCode(), response.getBody().getMessage());
                log.error("Failed to send SMS to {}, code: {}, message: {}", 
                        phoneNumber, response.getBody().getCode(), response.getBody().getMessage());
                return false;
            }
            
        } catch (TeaException e) {
            // 阿里云SDK异常处理
            log.error("Aliyun SMS TeaException: {}, {}", e.getCode(), e.getMessage(), e);
            // 更新发送记录为失败
            updateSmsRecordFailed(smsRecord.getId(), "", e.getCode(), e.getMessage());
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getMsg()));
            
        } catch (Exception e) {
            // 其他异常处理
            log.error("Failed to send SMS to {}", phoneNumber, e);
            // 更新发送记录为失败
            updateSmsRecordFailed(smsRecord.getId(), "", "UNKNOWN", e.getMessage());
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getMsg()));
        }
    }
    
    /**
     * 创建短信发送记录
     */
    private AiSmsRecordPo createSmsRecord(String phoneNumber, String templateCode, String templateParam, String userId) {
        AiSmsRecordPo record = new AiSmsRecordPo();
        record.setPhoneNumber(phoneNumber);
        record.setTemplateCode(templateCode);
        record.setTemplateParam(templateParam);
        record.setStatus(0); // 发送中
        record.setSendTime(new Date());
        record.setBusinessType(1); // 1-验证码
        
        if (userId != null) {
            record.setUserId(userId);
        }
        
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(0);
        
        smsRecordMapper.insert(record);
        return record;
    }
    
    /**
     * 记录成功发送的短信
     */
    private void saveSuccessRecord(String phoneNumber, String content, String templateCode, String requestId, String bizId, String userId, Integer businessType) {
        AiSmsRecordPo record = new AiSmsRecordPo();
        record.setPhoneNumber(phoneNumber);
        record.setContent(content);
        record.setTemplateCode(templateCode);
        record.setRequestId(requestId);
        record.setBizId(bizId);
        record.setStatus(1); // 发送成功
        record.setSendTime(new Date());
        record.setBusinessType(businessType);
        
        if (userId != null) {
            record.setUserId(userId);
        }
        
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(0);
        
        smsRecordMapper.insert(record);
    }
    
    /**
     * 更新短信发送记录为成功状态
     */
    private void updateSmsRecordSuccess(Long recordId, String requestId, String bizId) {
        AiSmsRecordPo record = new AiSmsRecordPo();
        record.setId(recordId);
        record.setStatus(1); // 发送成功
        record.setRequestId(requestId);
        record.setBizId(bizId);
        record.setUpdateTime(new Date());
        smsRecordMapper.updateById(record);
    }
    
    /**
     * 更新短信发送记录为失败状态
     */
    private void updateSmsRecordFailed(Long recordId, String requestId, String errorCode, String errorMessage) {
        AiSmsRecordPo record = new AiSmsRecordPo();
        record.setId(recordId);
        record.setStatus(2); // 发送失败
        record.setRequestId(requestId);
        record.setErrorCode(errorCode);
        record.setErrorMessage(errorMessage);
        record.setUpdateTime(new Date());
        smsRecordMapper.updateById(record);
    }
} 