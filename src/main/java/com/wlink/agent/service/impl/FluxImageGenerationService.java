package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.FalAiFluxClient;
import com.wlink.agent.client.FalAiFluxRequestContext;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.mq.MqTag;
import com.wlink.agent.service.ImageTaskQueueService;
import com.wlink.agent.utils.ReliableDistributedSemaphore;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Flux图像生成服务
 * 处理Flux API相关的图像生成任务
 */
@Slf4j
@Service
public class FluxImageGenerationService {
    
    // 任务阶段状态
    private static final String TASK_PHASE_SUBMIT = "SUBMIT";    // 提交阶段
    private static final String TASK_PHASE_CHECK = "CHECK";      // 检查阶段
    private static final String TASK_PHASE_RESULT = "RESULT";    // 结果阶段
    
    // Flux API状态
    private static final String FLUX_STATUS_IN_QUEUE = "IN_QUEUE";       // 在队列中
    private static final String FLUX_STATUS_IN_PROGRESS = "IN_PROGRESS"; // 处理中
    private static final String FLUX_STATUS_COMPLETED = "COMPLETED";     // 已完成
    private static final String FLUX_STATUS_FAILED = "FAILED";           // 失败
    
    // 重试配置
    private static final int MAX_RETRY_COUNT = 3;        // 最大重试次数
    private static final int STATUS_CHECK_DELAY = 3;     // 状态检查延迟级别（10秒）
    private static final int RETRY_DELAY = 4;            // 重试延迟级别（30秒）
    
    // 任务信息键名
    private static final String KEY_REQUEST_ID = "requestId";       // 请求ID
    private static final String KEY_TASK_PHASE = "taskPhase";       // 任务阶段
    private static final String KEY_RETRY_COUNT = "retryCount";     // 重试次数
    private static final String KEY_IS_TEXT_TO_IMAGE = "isTextToImage"; // 是否文生图
    private static final String KEY_CONTEXT_ID = "contextId";       // 请求上下文ID

    @Resource
    private ReliableDistributedSemaphore semaphore;
    
    @Resource
    private AiImageTaskQueueMapper taskMapper;
    
    @Resource
    private ImageTaskQueueService imageTaskQueueService;
    
    @Resource
    private ImageTaskMQHandler imageTaskMQHandler;
    
    @Autowired
    private FalAiFluxClient falAiFluxClient;
    
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 自动释放信号量的资源管理器
     */
    public class SemaphoreResource implements AutoCloseable {
        private final String taskId;
        private final boolean acquired;

        public SemaphoreResource(String taskId) {
            this.taskId = taskId;
            this.acquired = semaphore.tryAcquireWithTracking(taskId);
        }

        public boolean isAcquired() {
            return acquired;
        }

        @Override
        public void close() {
            if (acquired) {
                try {
                    semaphore.releaseWithTracking(taskId);
                } catch (Exception e) {
                    log.error("释放信号量资源失败, taskId: {}", taskId, e);
                    // 记录到数据库，用于后续修复
                    recordSemaphoreLeakage(taskId, e.getMessage());
                }
            }
        }
    }

    /**
     * 处理Flux图像生成任务
     * 三阶段处理：提交、检查、获取结果
     *
     * @param taskId 任务ID
     */
    public void processFluxImageTask(String taskId) {
        try (SemaphoreResource semaphoreResource = new SemaphoreResource(taskId)) {
            if (!semaphoreResource.isAcquired()) {
                // 获取信号量失败，稍后重试
                log.warn("无法获取任务处理信号量，稍后重试: {}", taskId);
                reEnqueueTask(taskId, STATUS_CHECK_DELAY);
                return;
            }

            // 获取任务详情
            AiImageTaskQueuePo task = taskMapper.selectById(taskId);
            if (task == null) {
                log.warn("未找到任务: {}", taskId);
                return;
            }
            
            // 从错误原因(errorReason)字段解析任务状态信息（如果有）
            Map<String, Object> taskInfo = parseTaskInfo(task);
            String taskPhase = (String) taskInfo.getOrDefault(KEY_TASK_PHASE, TASK_PHASE_SUBMIT);
            int retryCount = (int) taskInfo.getOrDefault(KEY_RETRY_COUNT, 0);
            
            log.info("处理Flux任务: {}, 阶段: {}, 重试次数: {}", taskId, taskPhase, retryCount);
            
            try {
                // 根据任务阶段进行处理
                switch (taskPhase) {
                    case TASK_PHASE_SUBMIT:
                        // 提交任务
                        submitFluxTask(task, taskInfo);
                        break;
                        
                    case TASK_PHASE_CHECK:
                        // 检查任务状态
                        checkFluxTaskStatus(task, taskInfo);
                        break;
                        
                    case TASK_PHASE_RESULT:
                        // 获取任务结果
                        processFluxTaskResult(task, taskInfo);
                        break;
                        
                    default:
                        log.error("未知的任务阶段: {}, 任务ID: {}", taskPhase, taskId);
                        handleTaskError(taskId, new IllegalStateException("未知的任务阶段: " + taskPhase));
                }
            } catch (Exception e) {
                log.error("处理Flux图像生成任务失败: {}", taskId, e);
                handleTaskError(taskId, e);
                
                // 如果可以重试，则重新入队
                if (retryCount < MAX_RETRY_COUNT) {
                    retryCount++;
                    taskInfo.put(KEY_RETRY_COUNT, retryCount);
                    taskInfo.put(KEY_TASK_PHASE, TASK_PHASE_SUBMIT); // 重置为提交阶段
                    
                    // 更新任务信息并重新入队
                    updateTaskInfo(task.getId(), taskInfo);
                    reEnqueueTask(taskId, RETRY_DELAY);
                    log.info("任务将重试，次数: {}, 任务ID: {}", retryCount, taskId);
                } else {
                    log.error("任务重试次数已达上限: {}, 任务ID: {}", MAX_RETRY_COUNT, taskId);
                    imageTaskQueueService.updateTaskStatus(
                            task.getId(), 
                            TaskStatus.FAILED.getValue(), 
                            null, 
                            "任务重试次数已达上限: " + MAX_RETRY_COUNT, 
                            null
                    );
                }
            }
        }
    }

    /**
     * 解析任务状态信息
     * 
     * @param task 任务对象
     * @return 任务信息Map
     */
    private Map<String, Object> parseTaskInfo(AiImageTaskQueuePo task) {
        Map<String, Object> taskInfo = new HashMap<>();
        
        // 优先从taskInfo字段获取任务信息
        if (task.getTaskInfo() != null && !task.getTaskInfo().isEmpty()) {
            try {
                taskInfo = objectMapper.readValue(task.getTaskInfo(), Map.class);
            } catch (Exception e) {
                log.warn("解析任务信息失败: {}", task.getTaskInfo(), e);
            }
        } 
        // 向后兼容：如果taskInfo为空，尝试从errorReason获取
        else if (task.getErrorReason() != null && task.getErrorReason().startsWith("{")) {
            try {
                taskInfo = objectMapper.readValue(task.getErrorReason(), Map.class);
                log.info("从errorReason读取任务信息（向后兼容）: {}", task.getId());
            } catch (Exception e) {
                log.warn("解析errorReason任务信息失败: {}", task.getErrorReason(), e);
            }
        }
        
        // 设置默认值
        if (!taskInfo.containsKey(KEY_TASK_PHASE)) {
            taskInfo.put(KEY_TASK_PHASE, TASK_PHASE_SUBMIT);
        }
        if (!taskInfo.containsKey(KEY_RETRY_COUNT)) {
            taskInfo.put(KEY_RETRY_COUNT, 0);
        }
        
        return taskInfo;
    }
    
    /**
     * 更新任务信息
     * 
     * @param taskId 任务ID
     * @param taskInfo 任务信息Map
     */
    private void updateTaskInfo(Long taskId, Map<String, Object> taskInfo) {
        try {
            String taskInfoJson = objectMapper.writeValueAsString(taskInfo);
            
            AiImageTaskQueuePo updateTask = new AiImageTaskQueuePo();
            updateTask.setId(taskId);
            updateTask.setTaskInfo(taskInfoJson);
            updateTask.setUpdateTime(new Date());
            
            taskMapper.updateById(updateTask);
        } catch (Exception e) {
            log.error("更新任务信息失败: {}", taskId, e);
        }
    }
    
    /**
     * 重新将任务放入队列
     * 
     * @param taskId 任务ID
     * @param delayLevel 延迟级别
     */
    private void reEnqueueTask(String taskId, int delayLevel) {
        try {
            imageTaskMQHandler.sendDelayTaskMessage(taskId, delayLevel, MqTag.TAG_GENERATE_FLUX);
            log.info("任务已重新入队，延迟级别: {}, 任务ID: {}", delayLevel, taskId);
        } catch (Exception e) {
            log.error("重新入队任务失败: {}", taskId, e);
        }
    }

    /**
     * 提交Flux任务
     * 
     * @param task 任务对象
     * @param taskInfo 任务信息Map
     * @throws IOException 如果API调用失败
     */
    private void submitFluxTask(AiImageTaskQueuePo task, Map<String, Object> taskInfo) throws IOException {
        log.info("提交Flux任务: {}", task.getId());
        
        // 更新任务状态为处理中
        imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.PROCESSING.getValue(), null, null, null);
        
        // 解析请求参数
        String requestParams = task.getRequestParams();
        Map<String, Object> requestMap = objectMapper.readValue(requestParams, Map.class);
        
        boolean isTextToImage = !requestMap.containsKey("image_url");
        taskInfo.put(KEY_IS_TEXT_TO_IMAGE, isTextToImage);
        
        try {
            FalAiFluxClient.QueueStatus queueStatus;
            
            // 创建请求上下文
            FalAiFluxRequestContext context;
            if (isTextToImage) {
                // 文生图请求
                FalAiFluxClient.TextToImageRequest request = objectMapper.readValue(
                        requestParams, FalAiFluxClient.TextToImageRequest.class);
                
                // 创建文本到图像请求上下文
                context = FalAiFluxRequestContext.createTextToImageContext(request.getPrompt());
                
                // 提交请求并传递上下文
                queueStatus = falAiFluxClient.submitTextToImageRequest(request, context);
            } else {
                // 图生图请求
                FalAiFluxClient.ImageToImageRequest request = objectMapper.readValue(
                        requestParams, FalAiFluxClient.ImageToImageRequest.class);
                
                // 创建图像到图像请求上下文
                context = FalAiFluxRequestContext.createImageToImageContext(request.getPrompt(), request.getImageUrl());
                
                // 提交请求并传递上下文
                queueStatus = falAiFluxClient.submitImageToImageRequest(request, context);
            }
            
            // 保存请求ID和上下文ID
            String requestId = queueStatus.getRequestId();
            if (requestId == null) {
                throw new BizException("提交任务失败，未获得请求ID");
            }
            
            log.info("Flux任务提交成功，请求ID: {}, 状态: {}, 上下文ID: {}", 
                    requestId, queueStatus.getStatus(), context.getRequestLogId());
            
            // 更新任务信息，包括请求上下文ID
            taskInfo.put(KEY_REQUEST_ID, requestId);
            taskInfo.put(KEY_TASK_PHASE, TASK_PHASE_CHECK);
            if (context.getRequestLogId() != null) {
                taskInfo.put(KEY_CONTEXT_ID, context.getRequestLogId());
            }
            updateTaskInfo(task.getId(), taskInfo);
            
            // 重新入队进行状态检查
            reEnqueueTask(String.valueOf(task.getId()), STATUS_CHECK_DELAY);
            
        } catch (IOException e) {
            log.error("提交Flux任务失败: {}", task.getId(), e);
            throw new BizException("提交Flux任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查Flux任务状态
     * 
     * @param task 任务对象
     * @param taskInfo 任务信息Map
     * @throws IOException 如果API调用失败
     */
    private void checkFluxTaskStatus(AiImageTaskQueuePo task, Map<String, Object> taskInfo) throws IOException {
        String requestId = (String) taskInfo.get(KEY_REQUEST_ID);
        if (requestId == null) {
            throw new BizException("任务信息中缺少请求ID");
        }
        
        log.info("检查Flux任务状态，任务ID: {}, 请求ID: {}", task.getId(), requestId);
        
        try {
            // 获取请求上下文ID（如果有）
            FalAiFluxRequestContext context = null;
            if (taskInfo.containsKey(KEY_CONTEXT_ID)) {
                Long contextId = ((Number) taskInfo.get(KEY_CONTEXT_ID)).longValue();
                context = new FalAiFluxRequestContext()
                        .setRequestLogId(contextId)
                        .setRequestId(requestId);
                log.debug("使用现有请求上下文，ID: {}", contextId);
            }
            
            // 调用API检查状态，传递上下文
            FalAiFluxClient.QueueStatus queueStatus = falAiFluxClient.checkQueueStatus(requestId, context);
            String status = queueStatus.getStatus();
            
            log.info("Flux任务状态: {}, 队列位置: {}, 任务ID: {}", 
                    status, queueStatus.getQueuePosition(), task.getId());
            
            if (FLUX_STATUS_COMPLETED.equals(status)) {
                // 任务完成，转到结果阶段
                taskInfo.put(KEY_TASK_PHASE, TASK_PHASE_RESULT);
                updateTaskInfo(task.getId(), taskInfo);
                
                // 立即进入结果处理阶段
                processFluxTaskResult(task, taskInfo);
                
            } else if (FLUX_STATUS_FAILED.equals(status)) {
                // 任务失败
                throw new BizException("Flux任务处理失败，状态: " + status);
                
            } else if (FLUX_STATUS_IN_QUEUE.equals(status) || FLUX_STATUS_IN_PROGRESS.equals(status)) {
                // 任务仍在处理中，重新入队
                updateTaskInfo(task.getId(), taskInfo);
                reEnqueueTask(String.valueOf(task.getId()), STATUS_CHECK_DELAY);
                
            } else {
                // 未知状态
                throw new BizException("Flux任务未知状态: " + status);
            }
            
        } catch (IOException e) {
            log.error("检查Flux任务状态失败: {}", task.getId(), e);
            throw new BizException("检查Flux任务状态失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理Flux任务结果
     * 
     * @param task 任务对象
     * @param taskInfo 任务信息Map
     * @throws IOException 如果API调用失败
     */
    private void processFluxTaskResult(AiImageTaskQueuePo task, Map<String, Object> taskInfo) throws IOException {
        String requestId = (String) taskInfo.get(KEY_REQUEST_ID);
        if (requestId == null) {
            throw new BizException("任务信息中缺少请求ID");
        }
        
        log.info("处理Flux任务结果，任务ID: {}, 请求ID: {}", task.getId(), requestId);
        
        try {
            // 获取请求上下文ID（如果有）
            FalAiFluxRequestContext context = null;
            if (taskInfo.containsKey(KEY_CONTEXT_ID)) {
                Long contextId = ((Number) taskInfo.get(KEY_CONTEXT_ID)).longValue();
                context = new FalAiFluxRequestContext()
                        .setRequestLogId(contextId)
                        .setRequestId(requestId);
                log.debug("使用现有请求上下文，ID: {}", contextId);
            }
            
            // 调用API获取结果，传递上下文
            FalAiFluxClient.FalAiResponse response = falAiFluxClient.getResult(requestId, context);
            
            if (response.getImages() == null || response.getImages().isEmpty() || 
                    response.getImages().get(0) == null || response.getImages().get(0).getUrl() == null) {
                // 结果中没有图像URL
                throw new BizException("Flux任务结果中无图像URL");
            }
            
            // 转换结果并更新任务状态
            ImageGenerateRes imageResult = convertToImageGenerateRes(response);
            imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.COMPLETED.getValue(), imageResult, null, null);
            
            log.info("Flux任务处理成功，任务ID: {}, 图像URL: {}", task.getId(), imageResult.getImageUrl());
            
        } catch (IOException e) {
            log.error("获取Flux任务结果失败: {}", task.getId(), e);
            throw new BizException("获取Flux任务结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理Flux请求参数并调用API
     * 仅用于完整处理（提交并等待结果）流程
     * 对于异步三阶段处理，请使用submitFluxTask, checkFluxTaskStatus, processFluxTaskResult
     *
     * @param requestParams 请求参数JSON
     * @return 图像生成结果
     * @throws IOException 如果API调用失败
     */
    private ImageGenerateRes processFluxRequest(String requestParams) throws IOException {
        try {
            // 将请求参数转换为Map
            Map<String, Object> requestMap = objectMapper.readValue(requestParams, Map.class);
            
            // 检查是否是图生图请求
            if (requestMap.containsKey("image_url")) {
                // 图生图请求
                FalAiFluxClient.ImageToImageRequest request = objectMapper.readValue(
                        requestParams, FalAiFluxClient.ImageToImageRequest.class);
                
                // 创建请求上下文
                FalAiFluxRequestContext context = FalAiFluxRequestContext.createImageToImageContext(
                        request.getPrompt(), request.getImageUrl());
                
                // 使用完整处理流程，但仍然记录请求
                FalAiFluxClient.QueueStatus queueStatus = falAiFluxClient.submitImageToImageRequest(request, context);
                if (queueStatus != null && queueStatus.getRequestId() != null) {
                    // 等待处理完成
                    // 注意：这里简化处理，实际应该使用轮询或其他方式等待
                    Thread.sleep(5000); // 等待5秒，仅作示例
                    FalAiFluxClient.FalAiResponse response = falAiFluxClient.getResult(queueStatus.getRequestId(), context);
                    return convertToImageGenerateRes(response);
                } else {
                    throw new BizException("提交图生图请求失败");
                }
                
            } else {
                // 文生图请求
                FalAiFluxClient.TextToImageRequest request = objectMapper.readValue(
                        requestParams, FalAiFluxClient.TextToImageRequest.class);
                
                // 创建请求上下文
                FalAiFluxRequestContext context = FalAiFluxRequestContext.createTextToImageContext(
                        request.getPrompt());
                
                // 使用完整处理流程，但仍然记录请求
                FalAiFluxClient.QueueStatus queueStatus = falAiFluxClient.submitTextToImageRequest(request, context);
                if (queueStatus != null && queueStatus.getRequestId() != null) {
                    // 等待处理完成
                    // 注意：这里简化处理，实际应该使用轮询或其他方式等待
                    Thread.sleep(5000); // 等待5秒，仅作示例
                    FalAiFluxClient.FalAiResponse response = falAiFluxClient.getResult(queueStatus.getRequestId(), context);
                    return convertToImageGenerateRes(response);
                } else {
                    throw new BizException("提交文生图请求失败");
                }
            }
        } catch (IOException e) {
            log.error("解析Flux请求参数失败", e);
            throw new BizException("解析Flux请求参数失败: " + e.getMessage());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("等待Flux任务结果时被中断");
        }
    }

    /**
     * 将FalAiResponse转换为ImageGenerateRes
     */
    private ImageGenerateRes convertToImageGenerateRes(FalAiFluxClient.FalAiResponse response) {
        if (response == null || response.getImages() == null || response.getImages().isEmpty()) {
            throw new BizException("Flux API未返回图像");
        }
        
        // 获取第一张图像
        FalAiFluxClient.GeneratedImage image = response.getImages().get(0);
        return new ImageGenerateRes(image.getUrl(), "SUCCESS", "Generated successfully with FLUX");
    }

    /**
     * 处理任务错误
     */
    private void handleTaskError(String taskId, Exception e) {
        try {
            String errorMessage = e.getMessage();
            if (errorMessage == null) {
                errorMessage = "Unknown error";
            }
            
            if (errorMessage.length() > 500) {
                errorMessage = errorMessage.substring(0, 500);
            }
            
            imageTaskQueueService.updateTaskStatus(Long.valueOf(taskId), TaskStatus.FAILED.getValue(), null, errorMessage, null);
        } catch (Exception ex) {
            log.error("更新任务状态失败: {}", taskId, ex);
        }
    }

    /**
     * 记录信号量泄漏
     */
    private void recordSemaphoreLeakage(String taskId, String errorMsg) {
        try {
            log.error("信号量泄漏记录 - 任务ID: {}, 错误: {}", taskId, errorMsg);
            // 可以记录到专门的表中，用于监控和告警
        } catch (Exception e) {
            log.error("记录信号量泄漏失败", e);
        }
    }
} 