package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.dao.mapper.AiAudioMergeTaskMapper;
import com.wlink.agent.dao.po.AiAudioMergeTaskPo;
import com.wlink.agent.model.dto.AudioMergeRequest;
import com.wlink.agent.model.dto.AudioMergeResult;
import com.wlink.agent.service.AudioMergeService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.DatabaseRetryUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 音频合成服务实现
 * 使用FFmpeg进行音频合成，限制CPU和内存使用
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "database")
@RefreshScope
@RequiredArgsConstructor
public class AudioMergeServiceImpl implements AudioMergeService {

    private final OssUtils ossUtils;
    private final AiAudioMergeTaskMapper audioMergeTaskMapper;

    @Value("${audio.merge.max-concurrent-tasks:2}")
    private int maxConcurrentTasks;

    @Value("${audio.merge.max-memory-mb:512}")
    private int maxMemoryMb;

    @Value("${audio.merge.cpu-limit:2}")
    private int cpuLimit;

    @Value("${audio.merge.temp-dir:/tmp/audio-merge}")
    private String tempDir;

    @Value("${audio.merge.ffmpeg-path:ffmpeg}")
    private String ffmpegPath;

    @Value("${spring.profiles.active}")
    private String env;

    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    // 线程池，限制并发数
    private ThreadPoolExecutor executorService;

    // 任务结果缓存
    private final ConcurrentHashMap<String, CompletableFuture<AudioMergeResult>> taskCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 创建受限的线程池
        this.executorService = new ThreadPoolExecutor(
                1, // 核心线程数
                maxConcurrentTasks, // 最大线程数
                60L, TimeUnit.SECONDS, // 空闲线程存活时间
                new LinkedBlockingQueue<>(10), // 任务队列
                new ThreadFactory() {
                    private final AtomicInteger counter = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread thread = new Thread(r, "audio-merge-" + counter.getAndIncrement());
                        thread.setDaemon(true);
                        return thread;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );

        // 创建临时目录
        try {
            Files.createDirectories(Paths.get(tempDir));
            log.info("音频合成服务初始化完成，临时目录: {}, 最大并发: {}, CPU限制: {}, 内存限制: {}MB", 
                    tempDir, maxConcurrentTasks, cpuLimit, maxMemoryMb);
        } catch (IOException e) {
            log.error("创建临时目录失败: {}", tempDir, e);
            throw new RuntimeException("音频合成服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("音频合成服务已关闭");
    }

    @Override
    public AudioMergeResult mergeAudios(List<String> audioUrls, String outputFileName) {
        if (audioUrls == null || audioUrls.isEmpty()) {
            throw new BizException("音频URL列表不能为空");
        }

        if (audioUrls.size() == 1) {
            // 只有一个音频，直接返回
            AudioMergeResult result = new AudioMergeResult();
            result.setTaskId(IdUtil.fastSimpleUUID());
            result.setSuccess(true);
            result.setMergedAudioUrl(audioUrls.get(0));
            return result;
        }

        // 直接进行音频合成，不使用数据库记录
        return mergeAudiosDirectly(audioUrls, outputFileName);
    }

    /**
     * 直接进行音频合成，不使用数据库记录
     */
    public AudioMergeResult mergeAudiosDirectly(List<String> audioUrls, String outputFileName) {
        String taskId = IdUtil.fastSimpleUUID();
        long startTime = System.currentTimeMillis();

        log.info("开始直接音频合成: taskId={}, audioCount={}", taskId, audioUrls.size());

        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setStartTime(startTime);

        String tempWorkDir = null;
        try {
            // 创建临时工作目录
            tempWorkDir = createTempWorkDir(taskId);

            // 下载音频文件
            List<String> localAudioFiles = downloadAudioFiles(audioUrls, tempWorkDir);

            // 创建合成请求
            AudioMergeRequest request = new AudioMergeRequest();
            request.setTaskId(taskId);
            request.setAudioUrls(audioUrls);
            request.setOutputFileName(outputFileName);

            // 使用FFmpeg合成音频
            String mergedFilePath = mergeAudioWithFFmpeg(localAudioFiles, tempWorkDir, request);

            // 上传到OSS
            String ossUrl = uploadToOss(mergedFilePath, outputFileName);

            // 获取音频时长
            Long duration = getAudioDuration(mergedFilePath);

            result.setSuccess(true);
            result.setMergedAudioUrl(ossUrl);
            result.setTotalDurationMs(duration);

            log.info("直接音频合成完成: taskId={}, duration={}ms, outputUrl={}",
                    taskId, duration, ossUrl);

        } catch (Exception e) {
            log.error("直接音频合成失败: taskId={}", taskId, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (tempWorkDir != null) {
                cleanupTempFiles(tempWorkDir);
            }

            long endTime = System.currentTimeMillis();
            result.setEndTime(endTime);
            result.setProcessingTimeMs(endTime - startTime);
        }

        return result;
    }

    @Override
    public CompletableFuture<AudioMergeResult> mergeAudiosAsync(AudioMergeRequest request) {
        if (request.getAudioUrls() == null || request.getAudioUrls().isEmpty()) {
            return CompletableFuture.completedFuture(
                    AudioMergeResult.failure(request.getTaskId(), "音频URL列表不能为空"));
        }

        // 检查并发限制（从数据库查询）
        int processingCount = audioMergeTaskMapper.countProcessingTasks();
        if (processingCount >= maxConcurrentTasks) {
            return CompletableFuture.completedFuture(
                    AudioMergeResult.failure(request.getTaskId(), "音频合成任务队列已满，请稍后重试"));
        }

        // 创建任务记录
        AiAudioMergeTaskPo taskPo = createTaskRecord(request);

        CompletableFuture<AudioMergeResult> future = CompletableFuture.supplyAsync(() -> {
            try {
                return processAudioMerge(request, taskPo);
            } finally {
                taskCache.remove(request.getTaskId());
            }
        }, executorService);

        taskCache.put(request.getTaskId(), future);
        return future;
    }

    @Override
    public AudioMergeResult getTaskResult(String taskId) {
        // 先从缓存查找
        CompletableFuture<AudioMergeResult> future = taskCache.get(taskId);
        if (future != null && future.isDone()) {
            try {
                return future.get();
            } catch (Exception e) {
                log.error("获取任务结果失败: taskId={}", taskId, e);
                return AudioMergeResult.failure(taskId, "获取任务结果失败: " + e.getMessage());
            }
        }

        // 从数据库查找
        LambdaQueryWrapper<AiAudioMergeTaskPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAudioMergeTaskPo::getTaskId, taskId)
                .eq(AiAudioMergeTaskPo::getDelFlag, 0);

        AiAudioMergeTaskPo taskPo = audioMergeTaskMapper.selectOne(queryWrapper);
        if (taskPo == null) {
            return null;
        }

        return convertToResult(taskPo);
    }

    @Override
    public boolean cancelTask(String taskId) {
        // 取消内存中的任务
        CompletableFuture<AudioMergeResult> future = taskCache.get(taskId);
        boolean cancelled = false;
        if (future != null) {
            cancelled = future.cancel(true);
            if (cancelled) {
                taskCache.remove(taskId);
            }
        }

        // 更新数据库状态
        LambdaUpdateWrapper<AiAudioMergeTaskPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiAudioMergeTaskPo::getTaskId, taskId)
                .eq(AiAudioMergeTaskPo::getDelFlag, 0)
                .set(AiAudioMergeTaskPo::getStatus, AiAudioMergeTaskPo.Status.CANCELLED)
                .set(AiAudioMergeTaskPo::getUpdateTime, new Date());

        int updateCount = audioMergeTaskMapper.update(null, updateWrapper);
        return cancelled || updateCount > 0;
    }

    /**
     * 处理音频合成
     */
    private AudioMergeResult processAudioMerge(AudioMergeRequest request, AiAudioMergeTaskPo taskPo) {
        long startTime = System.currentTimeMillis();
        String taskId = request.getTaskId();

        log.info("开始音频合成任务: taskId={}, audioCount={}", taskId, request.getAudioUrls().size());

        // 更新任务状态为处理中
        updateTaskStatus(taskId, AiAudioMergeTaskPo.Status.PROCESSING, null, startTime, null);

        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setStartTime(startTime);

        String tempWorkDir = null;
        try {
            // 创建临时工作目录
            tempWorkDir = createTempWorkDir(taskId);
            
            // 下载音频文件
            List<String> localAudioFiles = downloadAudioFiles(request.getAudioUrls(), tempWorkDir);
            
            // 使用FFmpeg合成音频
            String mergedFilePath = mergeAudioWithFFmpeg(localAudioFiles, tempWorkDir, request);
            
            // 上传到OSS
            String ossUrl = uploadToOss(mergedFilePath, request.getOutputFileName());
            
            // 获取音频时长
            Long duration = getAudioDuration(mergedFilePath);
            
            result.setSuccess(true);
            result.setMergedAudioUrl(ossUrl);
            result.setTotalDurationMs(duration);

            // 更新任务状态为完成
            updateTaskStatus(taskId, AiAudioMergeTaskPo.Status.COMPLETED, ossUrl, null, duration);

            log.info("音频合成任务完成: taskId={}, duration={}ms, outputUrl={}",
                    taskId, duration, ossUrl);

        } catch (Exception e) {
            log.error("音频合成任务失败: taskId={}", taskId, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());

            // 更新任务状态为失败
            updateTaskStatusToFailed(taskId, e.getMessage());
        } finally {
            // 清理临时文件
            if (tempWorkDir != null) {
                cleanupTempFiles(tempWorkDir);
            }

            long endTime = System.currentTimeMillis();
            result.setEndTime(endTime);
            result.setProcessingTimeMs(endTime - startTime);
        }

        return result;
    }

    /**
     * 创建临时工作目录
     */
    private String createTempWorkDir(String taskId) throws IOException {
        String workDir = tempDir + File.separator + taskId;
        Files.createDirectories(Paths.get(workDir));
        return workDir;
    }

    /**
     * 下载音频文件到本地
     */
    private List<String> downloadAudioFiles(List<String> audioUrls, String workDir) throws IOException {
        List<String> localFiles = new java.util.ArrayList<>();

        for (int i = 0; i < audioUrls.size(); i++) {
            String audioUrl = audioUrls.get(i);
            String fileName = String.format("audio_%03d.wav", i);
            String localPath = workDir + File.separator + fileName;

            downloadFile(MediaUrlPrefixUtil.getMediaUrl(audioUrl), localPath);
            localFiles.add(localPath);
        }

        return localFiles;
    }

    /**
     * 下载文件
     */
    private void downloadFile(String url, String localPath) throws IOException {
        log.debug("下载音频文件: {} -> {}", url, localPath);

        try (InputStream in = new URL(url).openStream();
             FileOutputStream out = new FileOutputStream(localPath)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }
    }

    /**
     * 使用FFmpeg合成音频
     */
    private String mergeAudioWithFFmpeg(List<String> audioFiles, String workDir, AudioMergeRequest request)
            throws IOException, InterruptedException {

        String outputPath = workDir + File.separator + "merged." + request.getOutputFormat();

        // 构建FFmpeg命令
        ProcessBuilder processBuilder = buildFFmpegCommand(audioFiles, outputPath, request);

        // 设置工作目录
        processBuilder.directory(new File(workDir));

        // 启动进程
        Process process = processBuilder.start();

        // 限制进程资源使用
        limitProcessResources(process);

        // 等待完成
        boolean finished = process.waitFor(request.getMaxProcessingTimeSeconds(), TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("FFmpeg处理超时");
        }

        if (process.exitValue() != 0) {
            String error = readProcessError(process);
            throw new RuntimeException("FFmpeg处理失败: " + error);
        }

        log.info("FFmpeg音频合成完成: {}", outputPath);
        return outputPath;
    }

    /**
     * 构建FFmpeg命令
     */
    private ProcessBuilder buildFFmpegCommand(List<String> audioFiles, String outputPath, AudioMergeRequest request) {
        ProcessBuilder processBuilder = new ProcessBuilder();

        // 基础命令
        processBuilder.command().add(ffmpegPath);
        processBuilder.command().add("-y"); // 覆盖输出文件

        // 添加输入文件
        for (String audioFile : audioFiles) {
            processBuilder.command().add("-i");
            processBuilder.command().add(audioFile);
        }

        // 音频过滤器：连接所有音频
        StringBuilder filterComplex = new StringBuilder();
        for (int i = 0; i < audioFiles.size(); i++) {
            if (i > 0) {
                filterComplex.append(";");
            }
            filterComplex.append(String.format("[%d:a]", i));
        }
        filterComplex.append("concat=n=").append(audioFiles.size()).append(":v=0:a=1[outa]");

        processBuilder.command().add("-filter_complex");
        processBuilder.command().add(filterComplex.toString());
        processBuilder.command().add("-map");
        processBuilder.command().add("[outa]");

        // 音频参数
        processBuilder.command().add("-ar");
        processBuilder.command().add(request.getSampleRate().toString());
        processBuilder.command().add("-ac");
        processBuilder.command().add(request.getChannels().toString());
        processBuilder.command().add("-b:a");
        processBuilder.command().add(request.getBitRate());

        // 音量标准化
        if (request.getNormalizeVolume()) {
            processBuilder.command().add("-af");
            processBuilder.command().add("loudnorm");
        }

        // 输出文件
        processBuilder.command().add(outputPath);

        log.debug("FFmpeg命令: {}", String.join(" ", processBuilder.command()));
        return processBuilder;
    }

    /**
     * 限制进程资源使用（Linux系统）
     */
    private void limitProcessResources(Process process) {
        // 注意：这里的资源限制在不同操作系统上实现方式不同
        // 在生产环境中，建议使用容器化部署来更好地控制资源
        try {
            // 设置进程优先级（降低优先级）
            if (System.getProperty("os.name").toLowerCase().contains("linux")) {
                Runtime.getRuntime().exec(new String[]{"renice", "10", String.valueOf(process.pid())});
            }
        } catch (Exception e) {
            log.warn("设置进程优先级失败", e);
        }
    }

    /**
     * 读取进程错误输出
     */
    private String readProcessError(Process process) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            StringBuilder error = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                error.append(line).append("\n");
            }
            return error.toString();
        } catch (IOException e) {
            return "无法读取错误信息";
        }
    }

    /**
     * 获取音频时长
     */
    private Long getAudioDuration(String audioPath) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    ffmpegPath, "-i", audioPath, "-f", "null", "-"
            );

            Process process = processBuilder.start();

            // 读取FFmpeg输出获取时长信息
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("Duration:")) {
                        // 解析时长：Duration: 00:00:10.50, start: 0.000000, bitrate: 128 kb/s
                        String[] parts = line.split("Duration: ")[1].split(",")[0].split(":");
                        if (parts.length == 3) {
                            int hours = Integer.parseInt(parts[0]);
                            int minutes = Integer.parseInt(parts[1]);
                            double seconds = Double.parseDouble(parts[2]);
                            return (long) ((hours * 3600 + minutes * 60 + seconds) * 1000);
                        }
                    }
                }
            }

            process.waitFor(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("获取音频时长失败: {}", audioPath, e);
        }

        return null;
    }

    /**
     * 上传到OSS
     */
    private String uploadToOss(String filePath, String fileName) {
        String ossPath = OSS_PATH.replace("{env}", env)
                .replace("{userId}", "system")
                .replace("{type}", "audio");

        String finalFileName = fileName + "_" + IdUtil.fastSimpleUUID() + ".wav";
        return ossUtils.uploadFile(filePath, ossPath + finalFileName);
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(String workDir) {
        try {
            Path workDirPath = Paths.get(workDir);
            if (Files.exists(workDirPath)) {
                Files.walk(workDirPath)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(path -> {
                            try {
                                Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", path, e);
                            }
                        });
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}", workDir, e);
        }
    }

    /**
     * 创建任务记录
     */
    private AiAudioMergeTaskPo createTaskRecord(AudioMergeRequest request) {
        AiAudioMergeTaskPo taskPo = new AiAudioMergeTaskPo();
        taskPo.setTaskId(request.getTaskId());
        taskPo.setShotId(request.getShotId());
        taskPo.setUserId(request.getUserId());
        taskPo.setAudioUrls(JSON.toJSONString(request.getAudioUrls()));
        taskPo.setAudioCount(request.getAudioUrls().size());
        taskPo.setStatus(AiAudioMergeTaskPo.Status.PENDING);
        taskPo.setRequestParams(JSON.toJSONString(request));
        taskPo.setCreateTime(new Date());
        taskPo.setUpdateTime(new Date());
        taskPo.setDelFlag(0);

        int insertResult = audioMergeTaskMapper.insert(taskPo);
        if (insertResult <= 0) {
            throw new BizException("创建音频合成任务记录失败");
        }

        log.info("创建音频合成任务记录: taskId={}, shotId={}, audioCount={}",
                request.getTaskId(), request.getShotId(), request.getAudioUrls().size());

        return taskPo;
    }

    /**
     * 更新任务状态（带重试机制）
     */
    private void updateTaskStatus(String taskId, String status, String mergedAudioUrl, Long startTime, Long duration) {
        DatabaseRetryUtils.executeWithRetry(() -> {
            LambdaUpdateWrapper<AiAudioMergeTaskPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiAudioMergeTaskPo::getTaskId, taskId)
                    .eq(AiAudioMergeTaskPo::getDelFlag, 0)
                    .set(AiAudioMergeTaskPo::getStatus, status)
                    .set(AiAudioMergeTaskPo::getUpdateTime, new Date());

            if (AiAudioMergeTaskPo.Status.PROCESSING.equals(status) && startTime != null) {
                updateWrapper.set(AiAudioMergeTaskPo::getProcessingStartTime, new Date(startTime));
            }

            if (AiAudioMergeTaskPo.Status.COMPLETED.equals(status)) {
                updateWrapper.set(AiAudioMergeTaskPo::getProcessingEndTime, new Date());
                if (mergedAudioUrl != null) {
                    updateWrapper.set(AiAudioMergeTaskPo::getMergedAudioUrl, mergedAudioUrl);
                }
                if (duration != null) {
                    updateWrapper.set(AiAudioMergeTaskPo::getTotalDurationMs, duration);
                }
            }

            int updateResult = audioMergeTaskMapper.update(null, updateWrapper);
            if (updateResult <= 0) {
                log.warn("更新音频合成任务状态失败，没有匹配的记录: taskId={}, status={}", taskId, status);
            }
            return updateResult;
        }, "updateTaskStatus-" + taskId + "-" + status);
    }

    /**
     * 更新任务状态为失败（带重试机制）
     */
    private void updateTaskStatusToFailed(String taskId, String errorMessage) {
        try {
            DatabaseRetryUtils.executeWithRetry(() -> {
                LambdaUpdateWrapper<AiAudioMergeTaskPo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(AiAudioMergeTaskPo::getTaskId, taskId)
                        .eq(AiAudioMergeTaskPo::getDelFlag, 0)
                        .set(AiAudioMergeTaskPo::getStatus, AiAudioMergeTaskPo.Status.FAILED)
                        .set(AiAudioMergeTaskPo::getProcessingEndTime, new Date())
                        .set(AiAudioMergeTaskPo::getErrorMessage, errorMessage)
                        .set(AiAudioMergeTaskPo::getUpdateTime, new Date());

                int updateResult = audioMergeTaskMapper.update(null, updateWrapper);
                if (updateResult <= 0) {
                    log.warn("更新音频合成任务失败状态失败，没有匹配的记录: taskId={}", taskId);
                }
                return updateResult;
            }, "updateTaskStatusToFailed-" + taskId);
        } catch (Exception e) {
            // 不抛出异常，避免影响主流程
            log.error("更新音频合成任务失败状态异常: taskId={}, error={}", taskId, e.getMessage());
        }
    }

    /**
     * 转换任务记录为结果对象
     */
    private AudioMergeResult convertToResult(AiAudioMergeTaskPo taskPo) {
        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskPo.getTaskId());
        result.setSuccess(AiAudioMergeTaskPo.Status.COMPLETED.equals(taskPo.getStatus()));
        result.setMergedAudioUrl(taskPo.getMergedAudioUrl());
        result.setTotalDurationMs(taskPo.getTotalDurationMs());
        result.setErrorMessage(taskPo.getErrorMessage());

        if (taskPo.getProcessingStartTime() != null) {
            result.setStartTime(taskPo.getProcessingStartTime().getTime());
        }
        if (taskPo.getProcessingEndTime() != null) {
            result.setEndTime(taskPo.getProcessingEndTime().getTime());
        }
        result.setProcessingTimeMs(taskPo.getProcessingTimeMs());

        return result;
    }
}
