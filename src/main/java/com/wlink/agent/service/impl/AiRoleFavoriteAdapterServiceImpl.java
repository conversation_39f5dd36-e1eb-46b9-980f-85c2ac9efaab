package com.wlink.agent.service.impl;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiRoleFavoriteMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiRoleFavoritePo;
import com.wlink.agent.enums.ResourceTypeEnum;
import com.wlink.agent.model.req.ResourceFavoriteReq;
import com.wlink.agent.model.req.RoleFavoriteReq;
import com.wlink.agent.model.req.SimpleFavoriteReq;
import com.wlink.agent.model.res.ResourceFavoriteRes;
import com.wlink.agent.model.res.RoleFavoriteRes;
import com.wlink.agent.service.AiResourceFavoriteService;
import com.wlink.agent.service.AiRoleFavoriteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色收藏适配器服务实现类
 * 用于保持原有API兼容性，实际调用新的通用资源收藏服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiRoleFavoriteAdapterServiceImpl extends ServiceImpl<AiRoleFavoriteMapper, AiRoleFavoritePo> implements AiRoleFavoriteService {

    private final AiResourceFavoriteService aiResourceFavoriteService;
    private final AiImageTaskQueueMapper aiImageTaskQueueMapper;

    /**
     * 添加角色收藏
     *
     * @param userId 用户ID
     * @param req    收藏请求
     * @return 操作结果
     */
    @Override
    public Response addFavorite(String userId, RoleFavoriteReq req) {
        //查询类型 taskType 为GENERATE  或者 REDRAW
        AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                .and(taskType -> taskType.eq(AiImageTaskQueuePo::getTaskType, "GENERATE").or().eq(AiImageTaskQueuePo::getTaskType, "REDRAW"))
                .eq(AiImageTaskQueuePo::getUserId, userId)
                .eq(AiImageTaskQueuePo::getContentId, req.getRoleId())
                .eq(AiImageTaskQueuePo::getDelFlag, 0)
                .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                .last("limit 1"));
        if (aiImageTaskQueuePo == null) {
            throw new BizException("角色收藏失败，请稍后再试");
        }
        // 转换为通用资源收藏请求
        SimpleFavoriteReq resourceReq = new SimpleFavoriteReq();
        resourceReq.setResourceId(aiImageTaskQueuePo.getId());
        resourceReq.setResourceType(ResourceTypeEnum.IMAGE.getCode());
        log.info("角色收藏适配器: 用户 {} 添加角色收藏，转换为通用资源收藏", userId);
        return aiResourceFavoriteService.addFavoriteById(userId, resourceReq);
    }

    /**
     * 移除角色收藏
     *
     * @param userId 用户ID
     * @param roleCode 角色ID
     * @return 操作结果
     */
    @Override
    public Response removeFavorite(String userId, String roleCode) {
        log.info("角色收藏适配器: 用户 {} 移除角色收藏，转换为通用资源收藏", userId);
        return aiResourceFavoriteService.removeFavorite(userId, roleCode);
    }

    /**
     * 查询用户收藏的角色列表
     *
     * @param userId 用户ID
     * @return 收藏角色列表
     */
    @Override
    public MultiResponse<RoleFavoriteRes> listUserFavorites(String userId) {
        log.info("角色收藏适配器: 用户 {} 查询角色收藏列表，转换为通用资源收藏", userId);
        
        // 调用通用资源收藏服务，查询角色类型的收藏
        MultiResponse<ResourceFavoriteRes> resourceResponse = 
                aiResourceFavoriteService.listUserFavoritesByType(userId, ResourceTypeEnum.ROLE.getCode());
        
        // 转换为原有响应格式
        List<RoleFavoriteRes> roleList = new ArrayList<>();
        if (resourceResponse != null && resourceResponse.getData() != null) {
            roleList = resourceResponse.getData().stream()
                    .map(this::convertToRoleFavoriteRes)
                    .collect(Collectors.toList());
        }
        
        return MultiResponse.of(roleList);
    }
    
    /**
     * 将通用资源收藏响应转换为角色收藏响应
     *
     * @param resourceRes 通用资源收藏响应
     * @return 角色收藏响应
     */
    private RoleFavoriteRes convertToRoleFavoriteRes(ResourceFavoriteRes resourceRes) {
        RoleFavoriteRes roleRes = new RoleFavoriteRes();
        roleRes.setRoleCode(resourceRes.getResourceCode());
        roleRes.setRoleId(resourceRes.getResourceId());
        roleRes.setSessionId(resourceRes.getSessionId());
        roleRes.setCreateTime(resourceRes.getCreateTime());
        
        // 处理额外数据
        if (resourceRes.getExtraData() != null) {
            if (resourceRes.getExtraData().containsKey("name")) {
                roleRes.setName(resourceRes.getExtraData().get("name").toString());
            }
            if (resourceRes.getExtraData().containsKey("image")) {
                roleRes.setImage(resourceRes.getExtraData().get("image").toString());
            }
            if (resourceRes.getExtraData().containsKey("voice_id")) {
                roleRes.setVoiceId(resourceRes.getExtraData().get("voice_id").toString());
            }
            if (resourceRes.getExtraData().containsKey("background")) {
                roleRes.setBackground(resourceRes.getExtraData().get("background").toString());
            }
            if (resourceRes.getExtraData().containsKey("description")) {
                roleRes.setDescription(resourceRes.getExtraData().get("description").toString());
            }
            if (resourceRes.getExtraData().containsKey("imageStatus")) {
                roleRes.setImageStatus(resourceRes.getExtraData().get("imageStatus").toString());
            }
        }
        
        return roleRes;
    }
} 