
package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wlink.agent.client.CompletionApiClient;
import com.wlink.agent.client.FalAiFluxClient;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionRequest;
import com.wlink.agent.config.ExternalApiConfig;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiImageModifyRecordMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiImageModifyRecordPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.model.dto.Dimensions;
import com.wlink.agent.model.req.ImageModifyReq;
import com.wlink.agent.model.req.ImageUploadReq;
import com.wlink.agent.model.req.RoleSaveReq;
import com.wlink.agent.model.req.SceneSaveReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.res.ImageModifyRes;
import com.wlink.agent.model.res.ImageModifyResultRes;
import com.wlink.agent.model.res.ImageUploadRes;
import com.wlink.agent.model.res.ModifyImageUrlInfoRes;
import com.wlink.agent.model.ImageModerationResult;
import com.wlink.agent.service.AiImageModifyService;
import com.wlink.agent.service.ContentModerationService;
import com.wlink.agent.service.ImageTaskQueueService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AiImageModifyServiceImpl implements AiImageModifyService {

    private final AiImageModifyRecordMapper imageModifyRecordMapper;
    private final CompletionApiClient completionApiClient;
    private final AiCreationSessionMapper aiCreationSessionMapper;
    private final AiCreationContentMapper aiCreationContentMapper;
    private final AiImageTaskQueueMapper taskQueueMapper;
    private final ImageTaskQueueService imageTaskQueueService;
    private final ContentModerationService contentModerationService;
    private final OssUtils ossUtils;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ExternalApiConfig externalApiConfig; // Inject ExecutorService
    private static final int STATUS_PROCESSING = 0;
    private static final int STATUS_SUCCESS = 1;
    private static final int STATUS_FAILURE = 2;
    private static final int STATUS_CONFIRMED = 3; // Assuming 3 for CONFIRMED
    private final AiImageTaskQueueMapper aiImageTaskQueueMapper;
    private final AiShotMapper aiShotMapper;

    @Value("${spring.profiles.active}")
    String env;
    /**
     * oss路径
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    private static final String OSS_PATH = "dify/{env}/{sessionId}/{type}/";


    @Override
    @Transactional // 建议添加事务注解
    public ImageModifyRes modifyImage(ImageModifyReq req) { // 修改返回类型
        log.info("Starting image modification process for session: {}", JSON.toJSONString(req)); // 使用 getSessionId()
        String userId = UserContext.getUser().getUserId();
        // 2. 创建并保存初始记录
        Long aLong = imageModifyRecordMapper.selectCount(new LambdaQueryWrapper<AiImageModifyRecordPo>()
                .eq(AiImageModifyRecordPo::getSessionId, req.getConversationId())
                .eq(AiImageModifyRecordPo::getPrimaryId, req.getPrimaryId())
                .eq(AiImageModifyRecordPo::getUserId, userId));
        if (aLong == 0) {
            AiImageModifyRecordPo record1 = new AiImageModifyRecordPo();
            record1.setModifyCode(IdUtil.getSnowflakeNextIdStr());
            record1.setSessionId(req.getConversationId());
            record1.setContentType(req.getContentType());
            record1.setPrimaryId(req.getPrimaryId());
            record1.setSecondaryId(req.getSecondaryId());
            record1.setFunctionType(req.getFunctionType());
            record1.setUserId(userId); // 设置用户ID
            record1.setModifiedImageUrl(StringUtils.isBlank(req.getSourceImageUrl()) ? null : req.getSourceImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            // prompt 字段在 AiImageModifyRecordPo 中不存在，如果需要记录，需在 Po 类中添加
            record1.setStatus(STATUS_SUCCESS); // 设置初始状态为处理中
            record1.setCreateTime(new Date());
            record1.setUpdateTime(new Date());
            record1.setDelFlag(0); // 默认未删除
            imageModifyRecordMapper.insert(record1);
        }
        AiImageModifyRecordPo record = new AiImageModifyRecordPo();
        record.setModifyCode(IdUtil.getSnowflakeNextIdStr());
        record.setSessionId(req.getConversationId());
        record.setContentType(req.getContentType());
        record.setPrimaryId(req.getPrimaryId());
        record.setSecondaryId(req.getSecondaryId());
        record.setFunctionType(req.getFunctionType());
        record.setUserId(userId); // 设置用户ID
        record.setSourceImageUrl(StringUtils.isBlank(req.getSourceImageUrl()) ? "" : req.getSourceImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
        record.setPrompt(req.getPrompt());
        record.setCharacterImageUrl1(StringUtils.isBlank(req.getCharacterImageUrl1()) ? "" : req.getCharacterImageUrl1().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
        record.setSceneImageUrl1(StringUtils.isBlank(req.getSceneImageUrl1()) ? "" : req.getSceneImageUrl1().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
        record.setCharacterImageUrl2(StringUtils.isBlank(req.getCharacterImageUrl2()) ? "" : req.getCharacterImageUrl2().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
        // prompt 字段在 AiImageModifyRecordPo 中不存在，如果需要记录，需在 Po 类中添加
        record.setStatus(STATUS_PROCESSING); // 设置初始状态为处理中
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(0); // 默认未删除
        imageModifyRecordMapper.insert(record);
        Long recordId = record.getId(); // 获取插入后生成的ID
        String modifyCode = record.getModifyCode();
        log.info("Created initial image modification record with ID: {}, ModifyCode: {}", recordId, modifyCode);
        AiImageTaskQueuePo taskQueuePo = taskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                .eq(AiImageTaskQueuePo::getSessionId, req.getConversationId())
                .eq(AiImageTaskQueuePo::getContentId, req.getPrimaryId())
                .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                .orderByAsc(AiImageTaskQueuePo::getCreateTime)
                .last("LIMIT 1"));
        if (null == taskQueuePo){
            throw new BizException("未找到图片生成记录");
        }
        if (Objects.equals(req.getFunctionType(), "redraw")) {
            int width = 512;
            int height = 512;
            Integer seed = -1;
            Float scale = 10.0f;
            String image = null;
            if (Objects.equals(taskQueuePo.getContentType(), 3)) {
                AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<AiCreationContentPo>()
                        .eq(AiCreationContentPo::getSessionId, taskQueuePo.getSessionId())
                        .eq(AiCreationContentPo::getContentType, 3)
                        .last("Limit 1"));
                if (Objects.nonNull(aiCreationContentPo)) {
                    String contentData = aiCreationContentPo.getContentData();
                    if (StringUtils.isNotBlank(contentData)) {
                        RoleSaveReq roleSaveReq = JSON.parseObject(contentData, RoleSaveReq.class);
                        roleSaveReq.getCharacters().forEach(character -> {
                            if (Objects.equals(character.getCharID(), taskQueuePo.getContentId())) {
                                character.setDescription(req.getPrompt());
                            }
                        });
                    }
                }
            }
            if (Objects.nonNull(taskQueuePo)) {
                if (Objects.equals("DOUBAO", taskQueuePo.getImageModel()) || StringUtils.isBlank(taskQueuePo.getImageModel())) {
                    String requestParams = taskQueuePo.getRequestParams();
                    VolcengineImageRequest volcengineImageRequest = JSON.parseObject(requestParams, VolcengineImageRequest.class);
                    if (null != volcengineImageRequest && null != volcengineImageRequest.getWidth() && null != volcengineImageRequest.getHeight()) {
                        width = volcengineImageRequest.getWidth() * 2;
                        height = volcengineImageRequest.getHeight() * 2;
                        seed = volcengineImageRequest.getSeed();
                        scale = volcengineImageRequest.getScale();
                    } else {
                        DoubaoImageGenerationRequest doubaoImageGenerationRequest = JSON.parseObject(requestParams, DoubaoImageGenerationRequest.class);
                        String size = doubaoImageGenerationRequest.getSize();
                        //把1536x864拆成两个数字
                        String[] split = size.split("x");
                        width = Integer.parseInt(split[0]);
                        height = Integer.parseInt(split[1]);
                        seed = doubaoImageGenerationRequest.getSeed();
                        scale = doubaoImageGenerationRequest.getGuidanceScale().floatValue();
                    }
                } else {
                    try {
                        String string = completionApiClient.requestCompletion(req.getPrompt(), userId, externalApiConfig.getTextTranslateApiKey());
                        log.info("重绘提示词翻译结果：{}", string);
                        if (StringUtils.isBlank(string)) {
                            log.warn("提示词翻译失败，返回空结果");
                            throw new BizException("提示词处理失败");
                        }
                        req.setPrompt(string);
                        FalAiFluxClient.ImageToImageRequest request = objectMapper.readValue(taskQueuePo.getRequestParams(), FalAiFluxClient.ImageToImageRequest.class);
                        image = request.getImageUrl();
                        seed = request.getSeed();
                        scale = request.getGuidanceScale().floatValue();
                        Dimensions dimensions = getFixedDimensionForRatio(request.getAspectRatio());
                        width = dimensions.getWidth();
                        height = dimensions.getHeight();
                    } catch (JsonProcessingException e) {
                        log.error("提示词处理失败", e);
                        throw new RuntimeException(e);
                    }
                }
            }
            if (StringUtils.isBlank(req.getCharacterImageUrl1())) {
                // 没有参考图片，调用文生图任务
                VolcengineImageRequest imageRequest = new VolcengineImageRequest();
                imageRequest.setImageModel(null == taskQueuePo ? "DOUBAO" : taskQueuePo.getImageModel());
                if (StringUtils.isNotBlank(image)) {
                    imageRequest.setImageUrls(Lists.newArrayList(image));
                }
                imageRequest.setConversationId(req.getConversationId());
                imageRequest.setType(req.getContentType());
                imageRequest.setContentId(record.getPrimaryId());
                imageRequest.setPrompt(req.getPrompt());
                imageRequest.setReqKey("high_aes_general_v21_L");
                imageRequest.setSeed(seed);
                imageRequest.setScale(scale);
                imageRequest.setDdimSteps(16);
                imageRequest.setWidth(width);
                imageRequest.setHeight(height);
                imageRequest.setUseSr(true);
                imageRequest.setUsePreLlm(true);
                imageRequest.setReturnUrl(true);
                imageTaskQueueService.queueGenerateTask(req.getConversationId(), imageRequest, TaskType.REDRAW.getValue());
            } else {
                // 图片内容安全检测
                String imageUrl = MediaUrlPrefixUtil.getMediaUrl(record.getCharacterImageUrl1());
                log.info("开始对图片进行内容安全检测: {}", imageUrl);
                ImageModerationResult moderationResult = contentModerationService.moderateImageUrl(imageUrl);
                // 检查审核结果
                if (!moderationResult.isSuccess()) {
                    log.error("图片内容安全检测失败: {}", moderationResult.getErrorMessage());
                    throw new BizException("图片审核服务异常，请稍后重试");
                }
                if (!"pass".equals(moderationResult.getSuggestion())) {
                    log.warn("图片内容安全检测未通过: suggestion={}, details={}",
                            moderationResult.getSuggestion(), moderationResult.getDetails());
                    String reason = "";
                    if ("block".equals(moderationResult.getSuggestion())) {
                        reason = "图片包含违规内容";
                    } else if ("review".equals(moderationResult.getSuggestion())) {
                        reason = "图片包含可疑内容";
                    } else {
                        reason = "图片审核未通过";
                    }
                    throw new BizException("图片审核不通过：" + reason + "，请更换其他图片后重试");
                }
                log.info("图片内容安全检测通过，继续后续处理流程");
                // 有参考图片，调用角色保留任务
                imageTaskQueueService.queueRetainTask(req.getConversationId(), VolcengineCharacterRetentionRequest.builder()
                        .conversationId(req.getConversationId())
                        .type(req.getContentType())
                        .contentId(record.getPrimaryId())
                        .prompt(req.getPrompt())
                        .reqKey("high_aes_ip_v20")
                        .imageUrls(Lists.newArrayList(MediaUrlPrefixUtil.getMediaUrl(record.getCharacterImageUrl1())))
                        .descPushback(true)
                        .seed(-1)
                        .scale(3.5)
                        .ddimSteps(9)
                        .width(width)
                        .height(height)
                        .cfgRescale(0.7)
                        .refIpWeight(0.0)
                        .refIdWeight(0.36)
                        .useSr(true)
                        .returnUrl(true)
                        .build());
            }
        }
        if (Objects.equals(req.getFunctionType(), "edit")) {
            imageTaskQueueService.queueEditTask(req.getConversationId(), VolcengineSeedEditRequest.builder()
                    .imageUrls(Lists.newArrayList(MediaUrlPrefixUtil.getMediaUrl(record.getSourceImageUrl())))
                    .conversationId(req.getConversationId())
                    .type(req.getContentType())
                    .contentId(record.getPrimaryId())
                    .prompt(req.getPrompt())
                    .reqKey("byteedit_v2.0")
                    .scale(1.0)
                    .seed(-1)
                    .returnUrl(true)
                    .build());
        }
        log.info("Submitted image modification request via OkHttp for record ID: {}. External processing pending asynchronously.", recordId);

        // 4. 返回包含 recordId 和 modifyCode 的响应对象
        return new ImageModifyRes(modifyCode); // 返回新的响应对象 (只包含 modifyCode)
    }

    // 示例：更新记录状态 (成功) - 这个方法可能在异步回调中被调用
    private void updateRecordStatus(Long recordId, String modifiedImageUrl) {
        AiImageModifyRecordPo record = imageModifyRecordMapper.selectById(recordId);
        if (record != null) {

            String path = OSS_PATH
                    .replace("{env}", env)
                    .replace("{sessionId}", record.getSessionId())
                    .replace("{type}", "image");

            String string = null;
            record.setStatus(STATUS_SUCCESS);
            try {
                string = ossUtils.uploadFile(modifiedImageUrl, path + IdUtil.fastSimpleUUID() + ".png");
                if (StringUtils.isBlank(string)) {
                    log.error("Failed to update record status for record ID: {}", recordId);
                    record.setStatus(STATUS_FAILURE);
                    record.setFailureReason("oss upload file fail");
                }
            } catch (Exception e) {
                log.error("Failed to update record status for record ID: {}", recordId, e);
                record.setStatus(STATUS_FAILURE);
                record.setFailureReason("oss upload file fail");
            }
            record.setModifiedImageUrl(string);
            record.setUpdateTime(new Date());
            imageModifyRecordMapper.updateById(record);

            if (Objects.equals(STATUS_SUCCESS, record.getStatus()) && Objects.equals(record.getFunctionType(), "redraw")) {
                AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<>(AiCreationContentPo.class)
                        .eq(AiCreationContentPo::getSessionId, record.getSessionId())
                        .eq(AiCreationContentPo::getContentType, record.getContentType())
                        .last("Limit 1"));
                if (aiCreationContentPo != null) {
                    String contentData = aiCreationContentPo.getContentData();
                    try {
                        switch (aiCreationContentPo.getContentType()) {
                            case 2:
                                SceneSaveReq sceneSaveReq = JSON.parseObject(contentData, SceneSaveReq.class);
                                String finalString = string;
                                sceneSaveReq.getScenes().forEach(scene -> {
                                    if (scene.getId().equals(record.getPrimaryId())) {
                                        scene.setImage(finalString);
                                    }
                                });
                                aiCreationContentPo.setContentData(objectMapper.writeValueAsString(sceneSaveReq));
                                break;
                            case 3:
                                RoleSaveReq roleSaveReq = JSON.parseObject(contentData, RoleSaveReq.class);
                                String finalString1 = string;
                                roleSaveReq.getCharacters().forEach(character -> {
                                    if (character.getCharID().equals(record.getPrimaryId())) {
                                        character.setImage(finalString1);
                                    }
                                });
                                aiCreationContentPo.setContentData(objectMapper.writeValueAsString(roleSaveReq));
                                break;
                            case 4:
                                ShotSaveReq shotSaveReq = JSON.parseObject(contentData, ShotSaveReq.class);
                                String finalString2 = string;
                                shotSaveReq.getShotGroups().forEach(shotGroups -> {
                                    if (shotGroups.getSceneId().equals(record.getPrimaryId())) {
                                        shotGroups.getShots().forEach(shot -> {
                                            if (shot.getId().equals(record.getSecondaryId())) {
                                                shot.setImage(finalString2);
                                            }
                                        });
                                    }

                                });
                                aiCreationContentPo.setContentData(objectMapper.writeValueAsString(shotSaveReq));
                                break;
                            default:
                                log.warn("Unsupported content type for image modification: {}", aiCreationContentPo.getContentType());
                                break;
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to parse contentData to SceneSaveReq", e);
                    }
                    aiCreationContentPo.setUpdateTime(new Date());
                    aiCreationContentMapper.updateById(aiCreationContentPo);
                }
            }
            log.info("Successfully updated image modification record ID: {}", recordId);
        } else {
            log.warn("Record not found for update (success), ID: {}", recordId);
        }
    }

    // 示例：更新记录状态 (失败) - 这个方法可能在异步回调或异常处理中被调用
    private void updateRecordStatusWithError(Long recordId, String failureReason) {
        AiImageModifyRecordPo record = imageModifyRecordMapper.selectById(recordId);
        if (record != null) {
            record.setStatus(STATUS_FAILURE);
            record.setFailureReason(failureReason.length() > 500 ? failureReason.substring(0, 500) : failureReason); // 限制长度
            record.setUpdateTime(new Date());
            imageModifyRecordMapper.updateById(record);
            log.error("Failed to process image modification record ID: {}, Reason: {}", recordId, failureReason);
        } else {
            log.warn("Record not found for update (error), ID: {}", recordId);
        }
    }

    /**
     * 根据 modifyCode 查询图片修改结果
     *
     * @param modifyCode 修改操作的唯一编码
     * @return 图片修改结果详情，如果找不到则返回 null
     */
    @Override
    public ImageModifyResultRes getModifyResultByCode(String modifyCode) {
        log.info("Querying image modification result for modifyCode: {}", modifyCode);
        // AiImageModifyRecordPo record = imageModifyRecordMapper.selectByModifyCode(modifyCode); // 使用 Mapper 中的方法
        // 或者直接使用 Wrapper 查询
        AiImageModifyRecordPo record = imageModifyRecordMapper.selectOne(
                new LambdaQueryWrapper<AiImageModifyRecordPo>()
                        .eq(AiImageModifyRecordPo::getModifyCode, modifyCode)
        );
        if (record == null) {
            log.warn("Image modification record not found for modifyCode: {}", modifyCode);
            throw new BizException("");
        }
        ImageModifyResultRes resultRes = new ImageModifyResultRes();
        // 使用 BeanUtils 复制属性 (要求字段名和类型匹配)
        BeanUtils.copyProperties(record, resultRes);
        if (Objects.equals(resultRes.getStatus(), STATUS_SUCCESS)) {
            resultRes.setModifiedImageUrl(MediaUrlPrefixUtil.getMediaUrl(resultRes.getModifiedImageUrl()));
            resultRes.setSourceImageUrl(MediaUrlPrefixUtil.getMediaUrl(resultRes.getSourceImageUrl()));
        }
        // 如果有不匹配的字段或需要特殊处理的逻辑，在这里补充
        // 例如，如果 Po 和 Res 中的字段名不同
        log.info("Found image modification record for modifyCode: {}, Status: {}, IsCurrentUsed: {}",
                modifyCode, record.getStatus(), record.getIsCurrentUsed());
        return resultRes;
    }

    @Override
    @Transactional // Add transaction management
    public void confirmImageModification(String modifyCode) {
        log.info("Starting confirmation process for modifyCode: {}", modifyCode);
        // 1. Find the modification record by modifyCode
        AiImageModifyRecordPo record = imageModifyRecordMapper.selectOne(new LambdaQueryWrapper<>(AiImageModifyRecordPo.class)
                .eq(AiImageModifyRecordPo::getModifyCode, modifyCode));

        if (record == null) {
            log.error("Image modification record not found for modifyCode: {}", modifyCode);
            throw new BizException("Modification record not found: " + modifyCode);
        }
        // 2. Check if the record status is SUCCESS (or ready for confirmation)
        // Allow re-confirmation if already confirmed? Or throw error? Let's allow for now.
        if (record.getStatus() != STATUS_SUCCESS) {
            log.warn("Cannot confirm modification for modifyCode: {}. Status is not SUCCESS. Current status: {}", modifyCode, record.getStatus());
            throw new BizException("Modification is not yet successful or already failed.");
            // Or potentially return without error if confirmation is not possible due to status
            // return;
        }
        // 3. Check if modified image URL exists
        if (StringUtils.isBlank(record.getModifiedImageUrl())) {
            log.error("Cannot confirm modification for modifyCode: {}. Modified image URL is blank.", modifyCode);
            throw new BizException("Modified image URL is missing.");
        }
        String finalImageUrl = record.getModifiedImageUrl(); // Add prefix back for storage
        try {
            AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<>(AiCreationContentPo.class)
                    .eq(AiCreationContentPo::getSessionId, record.getSessionId())
                    .eq(AiCreationContentPo::getContentType, record.getContentType())
                    .last("LIMIT 1")); // Use standard SQL LIMIT
            if (aiCreationContentPo == null && !Objects.equals(record.getContentType(), 4)) {
                log.error("AiCreationContentPo not found for sessionId: {} and contentType: {}", record.getSessionId(), record.getContentType());
                // Depending on requirements, this might be an error or just logged
                throw new BizException("Associated content data not found.");
                // return;
            }
            // 5. Update the contentData based on contentType

            boolean contentUpdated = false;
            String contentData = "";
            // Use integer literals directly for content type
            switch (record.getContentType()) {
                case 2: // Assuming 2 is SCENE
                    contentData = aiCreationContentPo.getContentData();
                    SceneSaveReq sceneSaveReq = JSON.parseObject(contentData, SceneSaveReq.class);
                    if (sceneSaveReq != null && sceneSaveReq.getScenes() != null) {
                        // Adjusting type based on common patterns, verify against actual DTO
                        for (SceneSaveReq.ScenesDTO scene : sceneSaveReq.getScenes()) {
                            if (scene.getId().equals(record.getPrimaryId())) {
                                scene.setImage(finalImageUrl);
                                contentUpdated = true;
                                break; // Found and updated, exit loop
                            }
                        }
                        if (contentUpdated) {
                            aiCreationContentPo.setContentData(objectMapper.writeValueAsString(sceneSaveReq));
                        }
                    }
                    break;
                case 3: // Assuming 3 is CHARACTER
                    contentData = aiCreationContentPo.getContentData();
                    RoleSaveReq roleSaveReq = JSON.parseObject(contentData, RoleSaveReq.class);
                    if (roleSaveReq != null && roleSaveReq.getCharacters() != null) {
                        // Adjusting type based on common patterns, verify against actual DTO
                        for (RoleSaveReq.CharactersDTO character : roleSaveReq.getCharacters()) {
                            if (character.getCharID().equals(record.getPrimaryId())) {
                                character.setImage(finalImageUrl);
                                character.setImageStatus(TaskStatus.COMPLETED.getValue());
                                if (Objects.equals(record.getFunctionType(), "redraw")) {
                                    character.setDescription(record.getPrompt());
                                }
                                contentUpdated = true;
                                break; // Found and updated, exit loop
                            }
                        }
                        if (contentUpdated) {
                            aiCreationContentPo.setContentData(objectMapper.writeValueAsString(roleSaveReq));
                        }
                    }
                    break;
                case 4: // Assuming 4 is SHOT
                    AiShotPo aiShotPo = aiShotMapper.selectOne(new LambdaQueryWrapper<AiShotPo>()
                            .eq(AiShotPo::getSessionId, record.getSessionId())
                            .eq(AiShotPo::getShotId, record.getPrimaryId())
                            .last("Limit 1"));
                    if (Objects.nonNull(aiShotPo)) {
                        contentData = aiShotPo.getShotData();
                        if (StringUtils.isNotBlank(contentData)) {
                            ShotSaveReq.ShotGroupsDTO.ShotsDTO shotsDTO = JSON.parseObject(contentData, ShotSaveReq.ShotGroupsDTO.ShotsDTO.class);
                            shotsDTO.setImage(finalImageUrl);
                            shotsDTO.setImageStatus(TaskStatus.COMPLETED.getValue());
                            String value = objectMapper.writeValueAsString(shotsDTO);
                            aiShotPo.setShotData(value);
                            aiShotPo.setUpdateTime(new Date());
                            aiShotMapper.updateById(aiShotPo);
                        }
                    }
                    break;
                default:
                    log.warn("Unsupported content type for image modification confirmation: {}", record.getContentType());
                    throw new BizException("Unsupported content type: " + record.getContentType());
            }
            // 6. Save the updated AiCreationContentPo if changes were made
            if (contentUpdated) {
                aiCreationContentPo.setUpdateTime(new Date());
                aiCreationContentMapper.updateById(aiCreationContentPo);
                log.info("Successfully updated AiCreationContentPo ID: {} for modifyCode: {}", aiCreationContentPo.getId(), modifyCode);
            } else {
                log.warn("Content not updated for modifyCode: {}. Target ID (Primary: {}, Secondary: {}) not found in content data.",
                        modifyCode, record.getPrimaryId(), record.getSecondaryId());
                // Decide if this is an error or just a warning. If it's expected that the ID might not be found, just log.
                // If it *must* be found, throw an exception.
                // throw new BizException("Target ID not found in content data.");
            }
            AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                    .eq(AiImageTaskQueuePo::getSessionId, record.getSessionId())
                    .eq(AiImageTaskQueuePo::getContentType, record.getContentType())
                    .eq(AiImageTaskQueuePo::getContentId, record.getPrimaryId())
                    .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                    .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                    .last("limit 1"));
            if (null != aiImageTaskQueuePo) {
                aiImageTaskQueuePo.setImageResult(finalImageUrl);
                aiImageTaskQueuePo.setUpdateTime(new Date());
                aiImageTaskQueuePo.setTaskStatus(TaskStatus.COMPLETED.getValue());
                aiImageTaskQueueMapper.updateById(aiImageTaskQueuePo);
            }
        } catch (Exception e) {
            log.error("Failed to parse or update contentData for modifyCode: {}. SessionId: {}, ContentType: {}",
                    modifyCode, record.getSessionId(), record.getContentType(), e);
            // Wrap Jackson exceptions or JSON parsing errors appropriately
            throw new BizException("Failed to process content data during confirmation.");
        }

        // 7. Update current usage status - 管理当前使用状态
        try {
            // 首先将同一 sessionId + primaryId 的所有记录设为非使用状态
            imageModifyRecordMapper.update(null,
                    new LambdaUpdateWrapper<AiImageModifyRecordPo>()
                            .eq(AiImageModifyRecordPo::getSessionId, record.getSessionId())
                            .eq(AiImageModifyRecordPo::getPrimaryId, record.getPrimaryId())
                            .set(AiImageModifyRecordPo::getIsCurrentUsed, false)
            );

            // 然后将当前确认的记录设为使用状态
            record.setIsCurrentUsed(true);
            record.setUpdateTime(new Date());
            imageModifyRecordMapper.updateById(record);

            log.info("Successfully updated current usage status for modifyCode: {}, sessionId: {}, primaryId: {}",
                    modifyCode, record.getSessionId(), record.getPrimaryId());
        } catch (Exception e) {
            log.error("Failed to update current usage status for modifyCode: {}", modifyCode, e);
            throw new BizException("Failed to update current usage status.");
        }
    }

    @Override
    public ModifyImageUrlInfoRes getSourceImageUrlsBySessionAndIds(String conversationId, String primaryId, String secondaryId) { // Match interface signature
        log.info("Querying source image URL infos for sessionId: {}, primaryId: {}, secondaryId: {}", conversationId, primaryId, secondaryId);

        // 查询修改记录
        List<AiImageModifyRecordPo> aiImageModifyRecordPos = imageModifyRecordMapper.selectList(new LambdaQueryWrapper<AiImageModifyRecordPo>()
                .eq(AiImageModifyRecordPo::getSessionId, conversationId)
                .eq(AiImageModifyRecordPo::getPrimaryId, primaryId)
                .orderByAsc(AiImageModifyRecordPo::getCreateTime)); // 改为降序，最新的在前面

        String prompt = "";

        if (CollUtil.isEmpty(aiImageModifyRecordPos)) {
            // 如果修改记录集合为空，从ai_image_task_queue表查询最新记录的prompt
            prompt = getPromptFromTaskQueue(conversationId, primaryId);
        } else {
            // 如果修改记录集合不为空，查找最近的redraw类型记录的prompt
            prompt = getPromptFromModifyRecords(aiImageModifyRecordPos, conversationId, primaryId);
        }
        ModifyImageUrlInfoRes modifyImageUrlInfoRes = new ModifyImageUrlInfoRes(prompt, new ArrayList<>());
        if (CollUtil.isNotEmpty(aiImageModifyRecordPos)) {
            // 构造ImageUrlInfoRes列表
            List<ModifyImageUrlInfoRes.ImageUrlInfoRes> imageUrlInfoResList = aiImageModifyRecordPos.stream()
                    .map(record -> {
                        return new ModifyImageUrlInfoRes.ImageUrlInfoRes(
                                MediaUrlPrefixUtil.getMediaUrl(record.getModifiedImageUrl()),
                                record.getModifyCode()
                        );
                    }).collect(Collectors.toList());

            // 创建ModifyImageUrlInfoRes对象
            modifyImageUrlInfoRes = new ModifyImageUrlInfoRes(prompt, imageUrlInfoResList);
        }
        log.info("Found source image URL infos with prompt: {}", prompt);
        return modifyImageUrlInfoRes;
    }

    /**
     * 从ai_image_task_queue表获取prompt
     */
    private String getPromptFromTaskQueue(String conversationId, String primaryId) {
        try {
            AiImageTaskQueuePo taskQueuePo = taskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                    .eq(AiImageTaskQueuePo::getSessionId, conversationId)
                    .eq(AiImageTaskQueuePo::getContentId, primaryId)
                    .orderByAsc(AiImageTaskQueuePo::getCreateTime)
                    .last("LIMIT 1"));

            if (taskQueuePo != null && StringUtils.isNotBlank(taskQueuePo.getRequestParams())) {
                // 解析request_params中的prompt
                try {
                    JSONObject requestParams = JSON.parseObject(taskQueuePo.getRequestParams());
                    String prompt = requestParams.getString("cnPrompt");
                    if (StringUtils.isNotBlank(prompt)) {
                        log.info("Found prompt from task queue: {}", prompt);
                        return prompt;
                    } else {
                        prompt = requestParams.getString("prompt");
                        return prompt;
                    }
                } catch (Exception e) {
                    log.warn("Failed to parse request_params from task queue record: {}", taskQueuePo.getRequestParams(), e);
                }
            }
        } catch (Exception e) {
            log.error("Error getting prompt from task queue for sessionId: {}, primaryId: {}", conversationId, primaryId, e);
        }

        log.info("No prompt found from task queue, returning empty string");
        return "";
    }

    /**
     * 从修改记录中获取prompt
     */
    private String getPromptFromModifyRecords(List<AiImageModifyRecordPo> records, String conversationId, String primaryId) {
        // 查找最近的redraw类型记录
        for (AiImageModifyRecordPo record : records) {
            if ("redraw".equals(record.getFunctionType()) && StringUtils.isNotBlank(record.getPrompt())) {
                log.info("Found prompt from redraw record: {}", record.getPrompt());
                return record.getPrompt();
            }
        }

        // 如果没有找到redraw类型的记录，从task_queue表获取
        log.info("No redraw record found, getting prompt from task queue");
        return getPromptFromTaskQueue(conversationId, primaryId);
    }


    /**
     * 根据给定的宽高比字符串，返回一个固定的、代表性的宽度和高度。
     *
     * @param aspectRatioStr 支持的宽高比字符串之一：
     *                       '21:9', '16:9', '4:3', '3:2', '1:1', '2:3', '3:4', '9:16', '9:21'。
     * @return 一个包含固定宽度和高度的 Dimensions 对象。
     * @throws IllegalArgumentException 如果输入的宽高比字符串不受支持或无效。
     */
    public static Dimensions getFixedDimensionForRatio(String aspectRatioStr) {
        if (aspectRatioStr == null || aspectRatioStr.trim().isEmpty()) {
            throw new IllegalArgumentException("宽高比字符串不能为空。");
        }

        switch (aspectRatioStr) {
            case "21:9":
                // 常见超宽屏分辨率 (例如，接近 2560x1080 或 3440x1440 的比例)
                // 我们选择一个基于 1080 高度的常见值
                return new Dimensions(2520, 1080);
            case "16:9":
                // 全高清 (Full HD)
                return new Dimensions(1920, 1080);
            case "4:3":
                // 常见的 4:3 高分辨率 (UXGA)
                // 备选: 1024x768 (XGA) 或 1440x1080 (基于1080高度)
                return new Dimensions(1600, 1200);
            case "3:2":
                // 常见的照片比例，基于 1080 高度计算宽度
                // 备选: 1080x720 (较小尺寸)
                return new Dimensions(1620, 1080);
            case "1:1":
                // 常见的方形分辨率
                return new Dimensions(1080, 1080);
            case "2:3": // 3:2 的纵向版本
                return new Dimensions(1080, 1620);
            case "3:4": // 4:3 的纵向版本
                return new Dimensions(1200, 1600);
            case "9:16": // 16:9 的纵向版本
                return new Dimensions(1080, 1920);
            case "9:21": // 21:9 的纵向版本 (实际比例为 3:7)
                return new Dimensions(1080, 2520);
            default:
                throw new IllegalArgumentException("不支持的宽高比字符串: " + aspectRatioStr +
                        ". 支持的比例包括: '21:9', '16:9', '4:3', '3:2', '1:1', '2:3', '3:4', '9:16', '9:21'.");
        }
    }

    @Override
    @Transactional
    public ImageUploadRes uploadImage(ImageUploadReq req) {
        log.info("Starting image upload process: {}", JSON.toJSONString(req));
        String userId = UserContext.getUser().getUserId();

        // 验证imageUrl字段不为空
        if (StringUtils.isBlank(req.getImageUrl())) {
            log.error("Upload request missing imageUrl");
            throw new BizException("上传图片URL不能为空");
        }
        String uploadImageUrl = MediaUrlPrefixUtil.getMediaUrl(req.getImageUrl());
        log.info("开始对图片进行内容安全检测: {}", uploadImageUrl);
        ImageModerationResult moderationResult = contentModerationService.moderateImageUrl(uploadImageUrl);
        // 检查审核结果
        if (!moderationResult.isSuccess()) {
            log.error("图片内容安全检测失败: {}", moderationResult.getErrorMessage());
            throw new BizException("图片审核服务异常，请稍后重试");
        }
        if (!"pass".equals(moderationResult.getSuggestion())) {
            log.warn("图片内容安全检测未通过: suggestion={}, details={}",
                    moderationResult.getSuggestion(), moderationResult.getDetails());
            String reason = "";
            if ("block".equals(moderationResult.getSuggestion())) {
                reason = "图片包含违规内容";
            } else if ("review".equals(moderationResult.getSuggestion())) {
                reason = "图片包含可疑内容";
            } else {
                reason = "图片审核未通过";
            }
            throw new BizException("图片审核不通过：" + reason + "，请更换其他图片后重试");
        }
        log.info("图片内容安全检测通过，继续后续处理流程");
        // 2. 创建并保存初始记录
        Long aLong = imageModifyRecordMapper.selectCount(new LambdaQueryWrapper<AiImageModifyRecordPo>()
                .eq(AiImageModifyRecordPo::getSessionId, req.getConversationId())
                .eq(AiImageModifyRecordPo::getPrimaryId, req.getContentId())
                .eq(AiImageModifyRecordPo::getUserId, userId));
        if (aLong == 0) {
            AiImageModifyRecordPo record1 = new AiImageModifyRecordPo();
            record1.setModifyCode(IdUtil.getSnowflakeNextIdStr());
            record1.setSessionId(req.getConversationId());
            record1.setContentType(req.getContentType());
            record1.setPrimaryId(req.getContentId());
            record1.setFunctionType("original");
            record1.setUserId(userId); // 设置用户ID
            record1.setModifiedImageUrl(StringUtils.isBlank(req.getOriginalImageUrl()) ? null : req.getOriginalImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            // prompt 字段在 AiImageModifyRecordPo 中不存在，如果需要记录，需在 Po 类中添加
            record1.setStatus(STATUS_SUCCESS); // 设置初始状态为处理中
            record1.setCreateTime(new Date());
            record1.setUpdateTime(new Date());
            record1.setDelFlag(0); // 默认未删除
            imageModifyRecordMapper.insert(record1);
        }

        // 创建记录
        AiImageModifyRecordPo record = new AiImageModifyRecordPo();
        String modifyCode = IdUtil.getSnowflakeNextIdStr();
        record.setModifyCode(modifyCode);
        record.setSessionId(req.getConversationId());
        record.setContentType(req.getContentType());
        record.setPrimaryId(req.getContentId());
        record.setFunctionType("upload"); // 设置功能类型为upload
        record.setUserId(userId);

        // 将上传的图片URL存储到modified_image_url字段
        String imageUrl = req.getImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "");
        record.setModifiedImageUrl(imageUrl);
        record.setStatus(STATUS_SUCCESS); // 直接设置为成功状态
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(0); // 默认未删除

        // 保存记录
        imageModifyRecordMapper.insert(record);

        log.info("Image upload processed successfully, ModifyCode: {}", modifyCode);

        // 返回结果
        return new ImageUploadRes(modifyCode, MediaUrlPrefixUtil.getMediaUrl(imageUrl));
    }
}