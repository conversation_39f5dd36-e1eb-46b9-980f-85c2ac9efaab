//package com.wlink.agent.service.impl;
//
//import cn.hutool.core.util.IdUtil;
//import com.alibaba.cola.exception.BizException;
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//
//import com.wlink.agent.constant.RedisKeyConstant;
//import com.wlink.agent.dao.dto.BarrageInfoDto;
//import com.wlink.agent.dao.dto.GiftInfoDto;
//import com.wlink.agent.dao.dto.SystemMsgDto;
//import com.wlink.agent.dao.mapper.AgentLiveDanmakuRecordMapper;
//import com.wlink.agent.dao.mapper.AgentLiveGiftRecordMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRecordMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRoomInfoMapper;
//import com.wlink.agent.dao.mapper.AgentLiveRoomTemplateMapper;
//import com.wlink.agent.dao.po.AgentLiveDanmakuRecordPo;
//import com.wlink.agent.dao.po.AgentLiveGiftRecordPo;
//import com.wlink.agent.dao.po.AgentLiveRecordPo;
//import com.wlink.agent.dao.po.AgentLiveRoomInfoPo;
//import com.wlink.agent.dao.po.AgentLiveRoomTemplatePo;
//import com.wlink.agent.enums.CmdTypeEnum;
//import com.wlink.agent.enums.LiveRoomStatusEnum;
//import com.wlink.agent.enums.LiveStatusEnum;
//import com.wlink.agent.exception.ErrorCodeEnum;
//import com.wlink.agent.model.dto.SimpleUserInfo;
//import com.wlink.agent.model.req.AgentLiveExitReq;
//import com.wlink.agent.model.req.AgentLiveStartReq;
//import com.wlink.agent.model.req.LiveRoomMsgPushReq;
//import com.wlink.agent.netty.DistributedRoomManager;
//import com.wlink.agent.proto.BaseMessage;
//import com.wlink.agent.proto.DispathchMessageType;
//import com.wlink.agent.proto.DptBaseMessage;
//import com.wlink.agent.proto.KillAppRequest;
//import com.wlink.agent.proto.LiveNoticeMessage;
//import com.wlink.agent.proto.MessageType;
//import com.wlink.agent.service.LiveChatService;
//import com.wlink.agent.service.RenderingServerService;
//import com.wlink.agent.utils.I18nMessageUtils;
//import com.wlink.agent.utils.ProtobufUtils;
//import com.wlink.agent.utils.UserContext;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.redisson.api.RBucket;
//import org.redisson.api.RList;
//import org.redisson.api.RTopic;
//import org.redisson.api.RedissonClient;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.Objects;
//
//
//@Slf4j
//@Service
//public class LiveChatServiceImpl implements LiveChatService {
//
//    @Resource
//    private DistributedRoomManager roomManager;
//
//    @Resource
//    AgentLiveRecordMapper agentLiveRecordMapper;
//    @Resource
//    AgentLiveRoomInfoMapper agentLiveRoomInfoMapper;
//    @Resource
//    RedissonClient redissonClient;
//    @Resource
//    private AgentLiveDanmakuRecordMapper agentLiveDanmakuRecordMapper;
//    @Resource
//    private AgentLiveGiftRecordMapper agentLiveGiftRecordMapper;
//    @Resource
//    AgentLiveRoomTemplateMapper agentLiveRoomTemplateMapper;
////    @Resource
////    RenderingServerService renderingServerService;
//
//
//
//    /**
//     * https://push.weilitech.cn/live/{roomId} 推
//     * https://pull.weilitech.cn/live/{roomId} 推
//     *
//     * @param roomId        房间ID
//     * @param pushStreamUrl
//     */
//
//    @Override
//    public void startLive(String roomId, String pushStreamUrl) {
//        log.info("收到开始直播请求: roomId={},pushStreamUrl={}", roomId, pushStreamUrl);
//
//        SimpleUserInfo simpleUserInfo = UserContext.getUser();
//
//        AgentLiveRoomInfoPo agentLiveRoomInfoPo = agentLiveRoomInfoMapper.selectOne(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                .eq(AgentLiveRoomInfoPo::getRoomId, roomId)
//                .eq(AgentLiveRoomInfoPo::getUserName, simpleUserInfo.getUsername())
//                .last("LIMIT 1"));
//
//        if (Objects.isNull(agentLiveRoomInfoPo)) {
//            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_EXISTS.getMsg());
//            throw new BizException(ErrorCodeEnum.ROOM_NOT_EXISTS.getCode(), message);
//        }
//
//        AgentLiveRoomTemplatePo agentLiveRoomTemplatePo = agentLiveRoomTemplateMapper.selectById(agentLiveRoomInfoPo.getTemplateId());
//        if (Objects.isNull(agentLiveRoomTemplatePo)) {
//            throw new BizException(ErrorCodeEnum.TEMPLATE_NOT_FOUND.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.TEMPLATE_NOT_FOUND.getMsg()));
//        }
//
//        if (!Objects.equals(agentLiveRoomInfoPo.getStatus(), LiveRoomStatusEnum.LIVE_ROOM_GENERATED.getStatus())) {
//            throw new BizException(ErrorCodeEnum.LIVE_ROOM_STATUS_CANNOT_LIVE.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LIVE_ROOM_STATUS_CANNOT_LIVE.getMsg()));
//        }
//        // 检查房间是否已存在
//        if (Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), LiveStatusEnum.LIVING.getCode())) {
//            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.LIVE_ROOM_IS_LIVING.getMsg());
//            throw new BizException(ErrorCodeEnum.LIVE_ROOM_IS_LIVING.getCode(), message);
//        }
//
//        if (!Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), LiveStatusEnum.START_SUCCESS.getCode())) {
//            throw new BizException(ErrorCodeEnum.LIVE_ROOM_STATUS_NOT_ALLOWED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LIVE_ROOM_STATUS_NOT_ALLOWED.getMsg()));
//        }
//
//        agentLiveRecordMapper.insert(AgentLiveRecordPo.builder()
//                .platform(1)
//                .startTime(new Date())
//                .pushStreamUrl(agentLiveRoomInfoPo.getPushStreamUrl())
//                .pullStreamUrl(agentLiveRoomInfoPo.getPullStreamUrl())
//                .roomId(roomId)
//                .build());
//
//        agentLiveRoomInfoPo.setLiveStatus(LiveStatusEnum.LIVING.getCode());
//        agentLiveRoomInfoPo.setUpdateTime(new Date());
//        agentLiveRoomInfoMapper.updateById(agentLiveRoomInfoPo);
//        // 创建房间
//        roomManager.createRoom(roomId);
//
//        LiveNoticeMessage startLiveNoticeMessage = LiveNoticeMessage.newBuilder()
//                .setRoomId(roomId)
//                .setPushStreamUrl(agentLiveRoomInfoPo.getPushStreamUrl())
//                .setType(1)
//                .build();
//        BaseMessage baseMessage = BaseMessage.newBuilder()
//                .setType(MessageType.START_LIVE_NOTICE)
//                .setMessageId(IdUtil.fastSimpleUUID())
//                .setTimestamp(System.currentTimeMillis())
//                .setStartLiveNoticeMessage(startLiveNoticeMessage).build();
//        roomManager.broadcastToRoom(roomId, baseMessage);
//
//        log.info("直播间创建成功: roomId={}", roomId);
//    }
//
//    @Override
//    public void stopLive(String roomId) {
//        log.info("收到结束直播请求: roomId={}", roomId);
//        SimpleUserInfo simpleUserInfo = UserContext.getUser();
//
//        AgentLiveRoomInfoPo agentLiveRoomInfoPo = agentLiveRoomInfoMapper.selectOne(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                .eq(AgentLiveRoomInfoPo::getRoomId, roomId)
//                .eq(AgentLiveRoomInfoPo::getUserName, simpleUserInfo.getUsername())
//                .last("LIMIT 1"));
//
//        if (Objects.isNull(agentLiveRoomInfoPo)) {
//            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_EXISTS.getMsg());
//            throw new BizException(ErrorCodeEnum.ROOM_NOT_EXISTS.getCode(), message);
//        }
//
////        if (!Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), LiveStatusEnum.LIVING.getCode())) {
////            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_LIVE.getMsg());
////            throw new BizException(ErrorCodeEnum.ROOM_NOT_LIVE.getCode(), message);
////        }
////
////        // 检查房间是否存在
////        if (!roomManager.isRoomExists(roomId)) {
////            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_LIVE.getMsg());
////            throw new BizException(ErrorCodeEnum.ROOM_NOT_LIVE.getCode(), message);
////        }
//
//        AgentLiveRecordPo agentLiveRecordPo = agentLiveRecordMapper.selectOne(new LambdaQueryWrapper<AgentLiveRecordPo>()
//                .eq(AgentLiveRecordPo::getRoomId, roomId)
//                .isNull(AgentLiveRecordPo::getEndTime)
//                .orderByDesc(AgentLiveRecordPo::getCreateTime)
//                .last("LIMIT 1"));
//        if (!Objects.isNull(agentLiveRecordPo)) {
//            agentLiveRecordPo.setEndTime(new Date());
//            agentLiveRecordPo.setUpdateTime(new Date());
//            agentLiveRecordMapper.updateById(agentLiveRecordPo);
//        }
//        agentLiveRoomInfoPo.setLiveStatus(LiveStatusEnum.NOT_LIVE.getCode());
//        agentLiveRoomInfoPo.setUpdateTime(new Date());
//        agentLiveRoomInfoMapper.updateById(agentLiveRoomInfoPo);
//
//        LiveNoticeMessage startLiveNoticeMessage = LiveNoticeMessage.newBuilder()
//                .setRoomId(roomId)
//                .setType(2)
//                .setPushStreamUrl("")
//                .build();
//        BaseMessage baseMessage = BaseMessage.newBuilder()
//                .setType(MessageType.START_LIVE_NOTICE)
//                .setMessageId(IdUtil.fastSimpleUUID())
//                .setTimestamp(System.currentTimeMillis())
//                .setStartLiveNoticeMessage(startLiveNoticeMessage).build();
//        // 广播消息给房间内所有用户
//        roomManager.broadcastToRoom(roomId, baseMessage);
//        try {
//            Thread.sleep(1000);
//        } catch (InterruptedException e) {
//            log.error("线程休眠异常", e);
//        }
//        // 关闭房间
//        roomManager.closeRoom(roomId);
//
//        String sessionId = renderingServerService.findSessionIdByLiveId(roomId);
//        if (StringUtils.isNotBlank(sessionId)) {
//            KillAppRequest killAppRequest = KillAppRequest.newBuilder().setSessionId(sessionId).build();
//            DptBaseMessage dptBaseMessage = DptBaseMessage.newBuilder()
//                    .setType(DispathchMessageType.KILL_APP)
//                    .setMessageId(IdUtil.fastSimpleUUID())
//                    .setTimestamp(System.currentTimeMillis())
//                    .setKillAppRequest(killAppRequest).build();
//
//            RTopic topic = redissonClient.getTopic(RedisKeyConstant.Rendering.AGENT_RENDERING);
//            String messageString = ProtobufUtils.toBase64String1(dptBaseMessage);
//            topic.publish(messageString);
//        }
//
//        log.info("直播间关闭成功: roomId={}", roomId);
//    }
//
//    @Override
//    public void start(AgentLiveStartReq req) {
//        log.info("收到开始直播请求: {}", JSON.toJSONString(req));
//        SimpleUserInfo simpleUserInfo = UserContext.getUser();
//
//        AgentLiveRoomInfoPo agentLiveRoomInfoPo = agentLiveRoomInfoMapper.selectOne(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                .eq(AgentLiveRoomInfoPo::getRoomId, req.getLiveId())
//                .eq(AgentLiveRoomInfoPo::getUserName, simpleUserInfo.getUsername())
//                .last("LIMIT 1"));
//
//        if (Objects.isNull(agentLiveRoomInfoPo)) {
//            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_EXISTS.getMsg());
//            throw new BizException(ErrorCodeEnum.ROOM_NOT_EXISTS.getCode(), message);
//        }
//        if (!Objects.equals(agentLiveRoomInfoPo.getStatus(), LiveRoomStatusEnum.LIVE_ROOM_GENERATED.getStatus())) {
//            throw new BizException(ErrorCodeEnum.LIVE_ROOM_STATUS_CANNOT_LIVE.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LIVE_ROOM_STATUS_CANNOT_LIVE.getMsg()));
//        }
//        // 检查房间是否已存在
//        if (Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), LiveStatusEnum.LIVING.getCode())) {
//            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.LIVE_ROOM_IS_LIVING.getMsg());
//            throw new BizException(ErrorCodeEnum.LIVE_ROOM_IS_LIVING.getCode(), message);
//        }
//
//        if (!Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), LiveStatusEnum.START_SUCCESS.getCode())) {
//            throw new BizException(ErrorCodeEnum.LIVE_ROOM_STATUS_NOT_ALLOWED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LIVE_ROOM_STATUS_NOT_ALLOWED.getMsg()));
//        }
//        agentLiveRecordMapper.insert(AgentLiveRecordPo.builder()
//                .platform(1)
//                .pushStreamUrl(req.getPushStreamUrl())
//                .pullStreamUrl(req.getPullStreamUrl())
//                .startTime(new Date())
//                .roomId(req.getLiveId())
//                .build());
//
//        agentLiveRoomInfoPo.setLiveStatus(LiveStatusEnum.LIVING.getCode());
//        agentLiveRoomInfoPo.setPushStreamUrl(req.getPushStreamUrl());
//        agentLiveRoomInfoPo.setPullStreamUrl(req.getPullStreamUrl());
//        agentLiveRoomInfoPo.setUpdateTime(new Date());
//        agentLiveRoomInfoMapper.updateById(agentLiveRoomInfoPo);
//
//        // 创建房间
//        roomManager.createRoom(req.getLiveId());
//
//        LiveNoticeMessage startLiveNoticeMessage = LiveNoticeMessage.newBuilder()
//                .setRoomId(req.getLiveId())
//                .setPushStreamUrl(req.getPushStreamUrl())
//                .setType(1)
//                .build();
//        BaseMessage baseMessage = BaseMessage.newBuilder()
//                .setType(MessageType.START_LIVE_NOTICE)
//                .setMessageId(IdUtil.fastSimpleUUID())
//                .setTimestamp(System.currentTimeMillis())
//                .setStartLiveNoticeMessage(startLiveNoticeMessage).build();
//        roomManager.broadcastToRoom(req.getLiveId(), baseMessage);
//        log.info("直播开始成功: roomId={}", req.getLiveId());
//    }
//
//    @Override
//    public void exit(AgentLiveExitReq req) {
//        log.info("收到退出直播请求: {}", JSON.toJSONString(req));
//        SimpleUserInfo simpleUserInfo = UserContext.getUser();
//        AgentLiveRoomInfoPo agentLiveRoomInfoPo = agentLiveRoomInfoMapper.selectOne(new LambdaQueryWrapper<AgentLiveRoomInfoPo>()
//                .eq(AgentLiveRoomInfoPo::getRoomId, req.getLiveId())
//                .eq(AgentLiveRoomInfoPo::getUserName, simpleUserInfo.getUsername())
//                .last("LIMIT 1"));
//        if (Objects.isNull(agentLiveRoomInfoPo)) {
//            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_EXISTS.getMsg());
//            throw new BizException(ErrorCodeEnum.ROOM_NOT_EXISTS.getCode(), message);
//        }
////
////        if (!Objects.equals(agentLiveRoomInfoPo.getLiveStatus(), LiveStatusEnum.LIVING.getCode())) {
////            String message = I18nMessageUtils.getMessage(ErrorCodeEnum.ROOM_NOT_LIVE.getMsg());
////            throw new BizException(ErrorCodeEnum.ROOM_NOT_LIVE.getCode(), message);
////        }
//        AgentLiveRecordPo agentLiveRecordPo = agentLiveRecordMapper.selectOne(new LambdaQueryWrapper<AgentLiveRecordPo>()
//                .eq(AgentLiveRecordPo::getRoomId, req.getLiveId())
//                .isNull(AgentLiveRecordPo::getEndTime)
//                .orderByDesc(AgentLiveRecordPo::getCreateTime)
//                .last("LIMIT 1"));
//        if (!Objects.isNull(agentLiveRecordPo)) {
//            agentLiveRecordPo.setEndTime(new Date());
//            agentLiveRecordPo.setUpdateTime(new Date());
//            agentLiveRecordMapper.updateById(agentLiveRecordPo);
//        }
//        agentLiveRoomInfoPo.setLiveStatus(LiveStatusEnum.NOT_LIVE.getCode());
//        agentLiveRoomInfoPo.setUpdateTime(new Date());
//        agentLiveRoomInfoMapper.updateById(agentLiveRoomInfoPo);
//
//        LiveNoticeMessage startLiveNoticeMessage = LiveNoticeMessage.newBuilder()
//                .setRoomId(req.getLiveId())
//                .setType(2)
//                .setPushStreamUrl("")
//                .build();
//        BaseMessage baseMessage = BaseMessage.newBuilder()
//                .setType(MessageType.START_LIVE_NOTICE)
//                .setMessageId(IdUtil.fastSimpleUUID())
//                .setTimestamp(System.currentTimeMillis())
//                .setStartLiveNoticeMessage(startLiveNoticeMessage).build();
//        roomManager.broadcastToRoom(req.getLiveId(), baseMessage);
//        roomManager.closeRoom(req.getLiveId());
//    }
//
//    @Override
//    public void pushLiveRoomMsg(LiveRoomMsgPushReq req) {
//        log.info("收到弹幕消息：{}", JSON.toJSONString(req));
//        AgentLiveRecordPo agentLiveRecordPo = agentLiveRecordMapper.selectOne(new LambdaQueryWrapper<AgentLiveRecordPo>()
//                .eq(AgentLiveRecordPo::getRoomId, req.getLiveId())
//                .orderByDesc(AgentLiveRecordPo::getCreateTime)
//                .last("LIMIT 1"));
//        if (Objects.isNull(agentLiveRecordPo)) {
//            log.error("没有找到项目开播记录，房间号：{}", req.getLiveId());
//
//        }
//        //异步处理弹幕消息
//        doLiveMsg(agentLiveRecordPo, req);
//        log.info("弹幕消息处理完成：{}", JSON.toJSONString(req));
//    }
//
//    private void doLiveMsg(AgentLiveRecordPo agentLiveRecordPo, LiveRoomMsgPushReq req) {
//        if (Objects.equals(CmdTypeEnum.LIVE_DM.getCmd(), req.getCmd())) {
//            LiveRoomMsgPushReq.LiveDmMsg msg = JSON.parseObject(JSON.toJSONString(req.getData()), LiveRoomMsgPushReq.LiveDmMsg.class);
//
//            // 1. 保存弹幕到Redis队列
//            String danmakuListKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.DANMAKU_MSG_QUEUE, req.getLiveId());
//            RList<BarrageInfoDto> danmakuList = redissonClient.getList(danmakuListKey);
//            if (Objects.equals(0, msg.getDmType())) {
//                danmakuList.add(BarrageInfoDto.builder().danmu(msg.getMsg()).username(msg.getUname()).timestamp(new Date()).build());
//            }
//            // 2. 保存弹幕到数据库
//            AgentLiveDanmakuRecordPo danmakuRecord = new AgentLiveDanmakuRecordPo();
//            danmakuRecord.setLiveRecordId(agentLiveRecordPo.getId());
//            danmakuRecord.setContent(msg.getMsg());
//            danmakuRecord.setRoomId(req.getLiveId());
//            danmakuRecord.setUserName(msg.getUname());
//            danmakuRecord.setType(msg.getDmType());
//            agentLiveDanmakuRecordMapper.insert(danmakuRecord);
//        }
//
//        if (Objects.equals(CmdTypeEnum.LIVE_SEND_GIFT.getCmd(), req.getCmd())) {
//            LiveRoomMsgPushReq.LiveGiftMsg msg = JSON.parseObject(JSON.toJSONString(req.getData()), LiveRoomMsgPushReq.LiveGiftMsg.class);
//
//            // 1. 保存到Redis队列
//            String giftInfoDtosKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.ROOM_GIFT_MSG_QUEUE, req.getLiveId());
//            RList<GiftInfoDto> giftList = redissonClient.getList(giftInfoDtosKey);
//            GiftInfoDto giftInfoDto = GiftInfoDto.builder()
//                    .giftName(msg.getGiftName())
//                    .giftCount(Integer.valueOf(msg.getGiftNum()))
//                    .giftPrice(Integer.valueOf(msg.getPrice()))
//                    .name(msg.getUname())
//                    .timestamp(new Date())
//                    .build();
//            giftList.add(giftInfoDto);
//
//            // 2. 保存到数据库
//            AgentLiveGiftRecordPo giftRecord = new AgentLiveGiftRecordPo();
//            giftRecord.setLiveRecordId(agentLiveRecordPo.getId());
//            giftRecord.setRoomId(req.getLiveId());
//            giftRecord.setUserName(msg.getUname());
//            giftRecord.setGiftName(msg.getGiftName());
//            giftRecord.setGiftCount(Integer.valueOf(msg.getGiftNum()));
//            giftRecord.setGiftPrice(new BigDecimal(msg.getPrice()));
//            agentLiveGiftRecordMapper.insert(giftRecord);
//        }
//
//        if (Objects.equals(CmdTypeEnum.LIVE_SYSTEM.getCmd(), req.getCmd())) {
//            LiveRoomMsgPushReq.LiveSystemMsg msg = JSON.parseObject(JSON.toJSONString(req.getData()), LiveRoomMsgPushReq.LiveSystemMsg.class);
//            // 1. 保存到Redis队列
//            String systemMsgKey = RedisKeyConstant.getKey(RedisKeyConstant.LiveRoom.BROADCAST_MESSAGE, req.getLiveId());
//            RBucket<SystemMsgDto> bucket = redissonClient.getBucket(systemMsgKey);
//            SystemMsgDto systemMsgDto = SystemMsgDto.builder()
//                    .msg(msg.getMsg())
//                    .type(msg.getType())
//                    .build();
//            bucket.set(systemMsgDto);
//        }
//    }
//
//}