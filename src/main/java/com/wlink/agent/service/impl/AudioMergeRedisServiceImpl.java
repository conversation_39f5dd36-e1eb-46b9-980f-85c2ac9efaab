package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.wlink.agent.model.dto.AudioMergeRequest;
import com.wlink.agent.model.dto.AudioMergeResult;
import com.wlink.agent.service.AudioMergeService;
import com.wlink.agent.utils.OssUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的音频合成服务实现
 * 避免数据库锁问题
 */
@Slf4j
@Service("audioMergeRedisService")
@ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "redis")
@RequiredArgsConstructor
public class AudioMergeRedisServiceImpl implements AudioMergeService {

    private final OssUtils ossUtils;
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${audio.merge.max-concurrent-tasks:2}")
    private int maxConcurrentTasks;

    @Value("${audio.merge.temp-dir:/tmp/audio-merge}")
    private String tempDir;

    @Value("${audio.merge.ffmpeg-path:ffmpeg}")
    private String ffmpegPath;

    @Value("${spring.profiles.active}")
    private String env;

    private static final String REDIS_KEY_PREFIX = "audio_merge:";
    private static final String REDIS_COUNTER_KEY = "audio_merge:counter";
    private static final int TASK_EXPIRE_HOURS = 2; // 任务记录过期时间
    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    @Override
    public AudioMergeResult mergeAudios(List<String> audioUrls, String outputFileName) {
        if (audioUrls == null || audioUrls.isEmpty()) {
            throw new BizException("音频URL列表不能为空");
        }

        if (audioUrls.size() == 1) {
            // 只有一个音频，直接返回
            AudioMergeResult result = new AudioMergeResult();
            result.setTaskId(IdUtil.fastSimpleUUID());
            result.setSuccess(true);
            result.setMergedAudioUrl(audioUrls.get(0));
            return result;
        }

        // 检查并发限制
        Long currentCount = redisTemplate.opsForValue().increment(REDIS_COUNTER_KEY, 1);
        if (currentCount > maxConcurrentTasks) {
            redisTemplate.opsForValue().decrement(REDIS_COUNTER_KEY, 1);
            throw new BizException("音频合成服务繁忙，请稍后重试");
        }

        String taskId = IdUtil.fastSimpleUUID();
        try {
            // 在Redis中记录任务开始
            recordTaskStart(taskId, audioUrls, outputFileName);

            // 直接进行音频合成
            AudioMergeResult result = processAudioMerge(taskId, audioUrls, outputFileName);

            // 记录任务结果
            recordTaskResult(taskId, result);

            return result;

        } catch (Exception e) {
            // 记录任务失败
            recordTaskFailure(taskId, e.getMessage());
            throw e;
        } finally {
            // 减少并发计数
            redisTemplate.opsForValue().decrement(REDIS_COUNTER_KEY, 1);
        }
    }

    @Override
    public CompletableFuture<AudioMergeResult> mergeAudiosAsync(AudioMergeRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            return mergeAudios(request.getAudioUrls(), request.getOutputFileName());
        });
    }

    @Override
    public AudioMergeResult getTaskResult(String taskId) {
        try {
            String key = REDIS_KEY_PREFIX + taskId;
            String resultJson = (String) redisTemplate.opsForValue().get(key);
            if (resultJson != null) {
                return JSON.parseObject(resultJson, AudioMergeResult.class);
            }
            return null;
        } catch (Exception e) {
            log.error("获取任务结果失败: taskId={}", taskId, e);
            return null;
        }
    }

    @Override
    public boolean cancelTask(String taskId) {
        try {
            String key = REDIS_KEY_PREFIX + taskId;
            AudioMergeResult result = AudioMergeResult.failure(taskId, "任务已取消");
            redisTemplate.opsForValue().set(key, JSON.toJSONString(result), TASK_EXPIRE_HOURS, TimeUnit.HOURS);
            return true;
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 记录任务开始
     */
    private void recordTaskStart(String taskId, List<String> audioUrls, String outputFileName) {
        try {
            AudioMergeResult result = new AudioMergeResult();
            result.setTaskId(taskId);
            result.setSuccess(null); // 表示进行中
            result.setStartTime(System.currentTimeMillis());

            String key = REDIS_KEY_PREFIX + taskId;
            redisTemplate.opsForValue().set(key, JSON.toJSONString(result), TASK_EXPIRE_HOURS, TimeUnit.HOURS);

            log.debug("记录音频合成任务开始: taskId={}, audioCount={}", taskId, audioUrls.size());
        } catch (Exception e) {
            log.warn("记录任务开始失败: taskId={}", taskId, e);
        }
    }

    /**
     * 记录任务结果
     */
    private void recordTaskResult(String taskId, AudioMergeResult result) {
        try {
            String key = REDIS_KEY_PREFIX + taskId;
            redisTemplate.opsForValue().set(key, JSON.toJSONString(result), TASK_EXPIRE_HOURS, TimeUnit.HOURS);

            log.debug("记录音频合成任务结果: taskId={}, success={}", taskId, result.getSuccess());
        } catch (Exception e) {
            log.warn("记录任务结果失败: taskId={}", taskId, e);
        }
    }

    /**
     * 记录任务失败
     */
    private void recordTaskFailure(String taskId, String errorMessage) {
        try {
            AudioMergeResult result = AudioMergeResult.failure(taskId, errorMessage);
            result.setEndTime(System.currentTimeMillis());

            String key = REDIS_KEY_PREFIX + taskId;
            redisTemplate.opsForValue().set(key, JSON.toJSONString(result), TASK_EXPIRE_HOURS, TimeUnit.HOURS);

            log.debug("记录音频合成任务失败: taskId={}, error={}", taskId, errorMessage);
        } catch (Exception e) {
            log.warn("记录任务失败失败: taskId={}", taskId, e);
        }
    }

    /**
     * 处理音频合成
     */
    private AudioMergeResult processAudioMerge(String taskId, List<String> audioUrls, String outputFileName) {
        long startTime = System.currentTimeMillis();

        log.info("开始音频合成: taskId={}, audioCount={}", taskId, audioUrls.size());

        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setStartTime(startTime);

        String tempWorkDir = null;
        try {
            // 创建临时工作目录
            tempWorkDir = createTempWorkDir(taskId);

            // 下载音频文件
            List<String> localAudioFiles = downloadAudioFiles(audioUrls, tempWorkDir);

            // 使用FFmpeg合成音频
            String mergedFilePath = mergeAudioWithFFmpeg(localAudioFiles, tempWorkDir, outputFileName);

            // 上传到OSS
            String ossUrl = uploadToOss(mergedFilePath, outputFileName);

            // 获取音频时长
            Long duration = getAudioDuration(mergedFilePath);

            result.setSuccess(true);
            result.setMergedAudioUrl(ossUrl);
            result.setTotalDurationMs(duration);

            log.info("音频合成完成: taskId={}, duration={}ms, outputUrl={}",
                    taskId, duration, ossUrl);

        } catch (Exception e) {
            log.error("音频合成失败: taskId={}", taskId, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            // 清理临时文件
            if (tempWorkDir != null) {
                cleanupTempFiles(tempWorkDir);
            }

            long endTime = System.currentTimeMillis();
            result.setEndTime(endTime);
            result.setProcessingTimeMs(endTime - startTime);
        }

        return result;
    }

    // 以下是音频处理的辅助方法

    private String createTempWorkDir(String taskId) throws IOException {
        String workDir = tempDir + File.separator + taskId;
        java.nio.file.Files.createDirectories(java.nio.file.Paths.get(workDir));
        return workDir;
    }

    private List<String> downloadAudioFiles(List<String> audioUrls, String workDir) throws IOException {
        List<String> localFiles = new java.util.ArrayList<>();

        for (int i = 0; i < audioUrls.size(); i++) {
            String audioUrl = audioUrls.get(i);
            String fileName = String.format("audio_%03d.wav", i);
            String localPath = workDir + File.separator + fileName;

            downloadFile(audioUrl, localPath);
            localFiles.add(localPath);
        }

        return localFiles;
    }

    private void downloadFile(String url, String localPath) throws IOException {
        try (java.io.InputStream in = new java.net.URL(url).openStream();
             java.io.FileOutputStream out = new java.io.FileOutputStream(localPath)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }
    }

    private String mergeAudioWithFFmpeg(List<String> audioFiles, String workDir, String outputFileName)
            throws IOException, InterruptedException {

        String outputPath = workDir + File.separator + outputFileName + ".wav";

        // 构建FFmpeg命令
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command().add(ffmpegPath);
        processBuilder.command().add("-y");

        // 添加输入文件
        for (String audioFile : audioFiles) {
            processBuilder.command().add("-i");
            processBuilder.command().add(audioFile);
        }

        // 音频过滤器：连接所有音频
        // 正确的格式应该是: [0:a][1:a]concat=n=2:v=0:a=1[outa]
        StringBuilder filterComplex = new StringBuilder();

        // 构建输入流标识符，不需要分号分隔
        for (int i = 0; i < audioFiles.size(); i++) {
            filterComplex.append(String.format("[%d:a]", i));
        }

        // 添加concat过滤器
        filterComplex.append("concat=n=").append(audioFiles.size()).append(":v=0:a=1[outa]");

        processBuilder.command().add("-filter_complex");
        processBuilder.command().add(filterComplex.toString());
        processBuilder.command().add("-map");
        processBuilder.command().add("[outa]");
        processBuilder.command().add(outputPath);

        processBuilder.directory(new java.io.File(workDir));

        Process process = processBuilder.start();
        boolean finished = process.waitFor(300, TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("FFmpeg处理超时");
        }

        if (process.exitValue() != 0) {
            throw new RuntimeException("FFmpeg处理失败");
        }

        return outputPath;
    }

    private Long getAudioDuration(String audioPath) {
        // 简化实现，返回null
        return null;
    }

    private String uploadToOss(String filePath, String fileName) {
        String ossPath = OSS_PATH.replace("{env}", env)
                .replace("{userId}", "system")
                .replace("{type}", "audio");

        String finalFileName = fileName + "_" + IdUtil.fastSimpleUUID() + ".wav";
        return ossUtils.uploadFile(filePath, ossPath + finalFileName);
    }

    private void cleanupTempFiles(String workDir) {
        try {
            java.nio.file.Path workDirPath = java.nio.file.Paths.get(workDir);
            if (java.nio.file.Files.exists(workDirPath)) {
                java.nio.file.Files.walk(workDirPath)
                        .sorted((a, b) -> b.compareTo(a))
                        .forEach(path -> {
                            try {
                                java.nio.file.Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", path, e);
                            }
                        });
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}", workDir, e);
        }
    }
}
