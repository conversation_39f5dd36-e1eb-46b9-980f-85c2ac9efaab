package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.dao.mapper.AiPointTransactionsMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiPointTransactionsPo;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.enums.TransactionTypeEnum;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.utils.I18nMessageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.util.Date;

/**
 * 用户积分服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPointsServiceImpl implements UserPointsService {

    private final AiUsersMapper aiUsersMapper;
    private final AiPointTransactionsMapper aiPointTransactionsMapper;
    
    // 积分兑换比例：1分钱1积分
    private static final int POINTS_EXCHANGE_RATE = 1;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductUserPoints(String userId, int points, String referenceId, String description, TransactionTypeEnum transactionType) {
        if (points <= 0 || StringUtils.isBlank(userId)) {
            log.warn("无效的积分扣除请求: userId={}, points={}", userId, points);
            return;
        }

        try {
            // 查询用户信息
            AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, userId));
            
            if (userPo == null || userPo.getPoints() == null) {
                log.warn("用户不存在或积分为空: userId={}", userId);
                throw new BizException(ErrorCodeEnum.USER_NOT_FOUND.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_FOUND.getMsg()));
            }
            
            // 检查积分是否足够
            if (userPo.getPoints() < points) {
                log.warn("用户积分不足: userId={}, currentPoints={}, requiredPoints={}", 
                        userId, userPo.getPoints(), points);
                throw new BizException(ErrorCodeEnum.VISUAL_RECORD_INSUFFICIENT_POINTS.getCode(),
                        "积分不足，需要" + points + "积分，当前积分" + userPo.getPoints());
            }
            
            // 计算剩余积分
            int remainingPoints = userPo.getPoints() - points;
            
            // 更新用户积分
            userPo.setPoints(remainingPoints);
            userPo.setUpdateTime(new Date());
            aiUsersMapper.updateById(userPo);
            
            // 记录积分交易
            AiPointTransactionsPo transaction = new AiPointTransactionsPo();
            transaction.setUserId(userId);
            transaction.setPoints(-points); // 负数表示减少
            transaction.setBalance(remainingPoints);
            transaction.setType(transactionType.getCode()); // 使用传入的交易类型
            transaction.setReferenceId(referenceId);
            transaction.setDescription(description);
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            transaction.setDelFlag(0);
            aiPointTransactionsMapper.insert(transaction);
            
            log.info("用户积分扣除成功: userId={}, points={}, remainingPoints={}, referenceId={}, transactionType={}", 
                    userId, points, remainingPoints, referenceId, transactionType.getCode());
        } catch (BizException e) {
            log.error("扣除用户积分失败(业务异常): userId={}, points={}, error={}", userId, points, e.getMessage());
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("扣除用户积分失败: userId={}, points={}, error={}", userId, points, e.getMessage(), e);
            throw new BizException("扣除用户积分失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductUserPoints(String userId, int points, String referenceId, String description) {
        // 兼容旧版本，使用默认的视频生成消耗类型
        deductUserPoints(userId, points, referenceId, description, TransactionTypeEnum.VIDEO_GENERATION);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundUserPoints(String userId, int points, String referenceId, String description, TransactionTypeEnum transactionType) {
        if (points <= 0 || StringUtils.isBlank(userId)) {
            log.warn("无效的积分返还请求: userId={}, points={}, referenceId={}", userId, points, referenceId);
            return;
        }

        try {
            log.info("开始处理积分返还: userId={}, points={}, referenceId={}, description={}, transactionType={}", 
                    userId, points, referenceId, description, transactionType.getCode());
                    
            // 查询用户当前积分
            AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, userId));
            
            if (userPo == null || userPo.getPoints() == null) {
                log.warn("用户不存在或积分为空，无法返还积分: userId={}, referenceId={}", userId, referenceId);
                return;
            }
            
            // 计算返还后的积分
            int currentPoints = userPo.getPoints();
            int updatedPoints = currentPoints + points;
            
            log.info("积分返还计算: userId={}, currentPoints={}, pointsToRefund={}, updatedPoints={}", 
                    userId, currentPoints, points, updatedPoints);
            
            // 更新用户积分
            userPo.setPoints(updatedPoints);
            userPo.setUpdateTime(new Date());
            int updateResult = aiUsersMapper.updateById(userPo);
            
            if (updateResult != 1) {
                log.warn("用户积分更新异常，影响行数: {}, userId={}, referenceId={}", updateResult, userId, referenceId);
            }
            
            // 记录积分返还交易
            AiPointTransactionsPo refundTransaction = new AiPointTransactionsPo();
            refundTransaction.setUserId(userId);
            refundTransaction.setPoints(points); // 正数表示增加
            refundTransaction.setBalance(updatedPoints);
            refundTransaction.setType(transactionType.getCode()); // 使用传入的交易类型
            refundTransaction.setReferenceId(referenceId);
            refundTransaction.setDescription(description);
            refundTransaction.setCreateTime(new Date());
            refundTransaction.setUpdateTime(new Date());
            refundTransaction.setDelFlag(0);
            int insertResult = aiPointTransactionsMapper.insert(refundTransaction);
            
            if (insertResult != 1) {
                log.warn("积分交易记录异常，影响行数: {}, userId={}, referenceId={}", insertResult, userId, referenceId);
            }
            
            log.info("用户积分返还成功完成: userId={}, points={}, updatedPoints={}, referenceId={}, transactionId={}, transactionType={}", 
                    userId, points, updatedPoints, referenceId, refundTransaction.getId(), transactionType.getCode());
        } catch (Exception e) {
            log.error("返还用户积分失败: userId={}, points={}, referenceId={}, error={}", 
                    userId, points, referenceId, e.getMessage(), e);
            // 这里选择记录错误但不抛出异常，避免影响其他流程
            // 异常会由调用方根据需要进行重试
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundUserPoints(String userId, int points, String referenceId, String description) {
        // 兼容旧版本，使用默认的视频生成失败退款类型
        refundUserPoints(userId, points, referenceId, description, TransactionTypeEnum.VIDEO_REFUND);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void refundPointsForFailedVideoGeneration(String visualRecordCode) {
        log.info("开始处理视频生成失败积分返还: visualRecordCode={}", visualRecordCode);
        
        // 重试参数
        final int MAX_RETRY_ATTEMPTS = 3;
        final long RETRY_DELAY_MS = 1000; // 1秒
        int retryCount = 0;
        
        while (retryCount < MAX_RETRY_ATTEMPTS) {
            try {
                // 查找相关的积分扣除记录
                AiPointTransactionsPo deductionRecord = aiPointTransactionsMapper.selectOne(
                        new LambdaQueryWrapper<AiPointTransactionsPo>()
                                .eq(AiPointTransactionsPo::getReferenceId, visualRecordCode)
                                .eq(AiPointTransactionsPo::getType, TransactionTypeEnum.VIDEO_GENERATION.getCode())
                                .orderByDesc(AiPointTransactionsPo::getCreateTime)
                                .last("LIMIT 1")
                );
                
                if (deductionRecord == null) {
                    log.warn("找不到相关的积分扣除记录，无法返还积分: visualRecordCode={}", visualRecordCode);
                    return;
                }
                
                String userId = deductionRecord.getUserId();
                int pointsToRefund = -deductionRecord.getPoints(); // 取绝对值，因为扣除记录是负数
                
                // 调用返还方法
                refundUserPoints(userId, pointsToRefund, visualRecordCode, "视频生成失败返还" + pointsToRefund + "积分", TransactionTypeEnum.VIDEO_REFUND);
                
                // 成功执行，退出循环
                log.info("积分返还成功: visualRecordCode={}, userId={}, points={}", 
                        visualRecordCode, userId, pointsToRefund);
                return;
                
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= MAX_RETRY_ATTEMPTS) {
                    log.error("返还用户积分失败，已达最大重试次数: visualRecordCode={}, retryCount={}, error={}", 
                            visualRecordCode, retryCount, e.getMessage(), e);
                } else {
                    log.warn("返还用户积分失败，准备重试: visualRecordCode={}, retryCount={}, error={}", 
                            visualRecordCode, retryCount, e.getMessage());
                    
                    // 延迟重试
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("积分返还重试等待被中断: visualRecordCode={}", visualRecordCode, ie);
                    }
                }
            }
        }
    }

    @Override
    public boolean hasEnoughPoints(String userId, int requiredPoints) {
        if (StringUtils.isBlank(userId) || requiredPoints <= 0) {
            return false;
        }
        
        AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                .eq(AiUsersPo::getUserId, userId));
        
        return userPo != null && userPo.getPoints() != null && userPo.getPoints() >= requiredPoints;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void addPointsAfterPayment(String userId, String orderNo, Integer amount) {
        if (amount <= 0 || StringUtils.isBlank(userId) || StringUtils.isBlank(orderNo)) {
            log.warn("无效的积分增加请求: userId={}, orderNo={}, amount={}", userId, orderNo, amount);
            return;
        }
        
        // 计算应增加的积分：1分钱1积分
        int pointsToAdd = amount;
        if (pointsToAdd <= 0) {
            log.info("支付金额太小，无需增加积分: userId={}, orderNo={}, amount={}", userId, orderNo, amount);
            return;
        }
        
        try {
            log.info("开始处理支付后积分增加: userId={}, orderNo={}, amount={}, pointsToAdd={}", 
                    userId, orderNo, amount, pointsToAdd);
            
            // 查询用户当前积分
            AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, userId));
            
            if (userPo == null || userPo.getPoints() == null) {
                log.warn("用户不存在或积分为空，无法增加积分: userId={}, orderNo={}", userId, orderNo);
                return;
            }
            
            // 计算增加后的积分
            int currentPoints = userPo.getPoints();
            int updatedPoints = currentPoints + pointsToAdd;
            
            log.info("积分增加计算: userId={}, currentPoints={}, pointsToAdd={}, updatedPoints={}", 
                    userId, currentPoints, pointsToAdd, updatedPoints);
            
            // 更新用户积分
            userPo.setPoints(updatedPoints);
            userPo.setUpdateTime(new Date());
            int updateResult = aiUsersMapper.updateById(userPo);
            
            if (updateResult != 1) {
                log.warn("用户积分更新异常，影响行数: {}, userId={}, orderNo={}", updateResult, userId, orderNo);
                throw new BizException("更新用户积分失败");
            }
            
            // 记录积分增加交易
            AiPointTransactionsPo transaction = new AiPointTransactionsPo();
            transaction.setUserId(userId);
            transaction.setPoints(pointsToAdd); // 正数表示增加
            transaction.setBalance(updatedPoints);
            transaction.setType(TransactionTypeEnum.PAYMENT_REWARD.getCode());
            transaction.setReferenceId(orderNo);
            transaction.setDescription("支付订单获得" + pointsToAdd + "积分");
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            transaction.setDelFlag(0);
            aiPointTransactionsMapper.insert(transaction);
            
            log.info("支付后积分增加成功: userId={}, orderNo={}, pointsAdded={}, newBalance={}", 
                    userId, orderNo, pointsToAdd, updatedPoints);
        } catch (Exception e) {
            log.error("支付后积分增加异常: userId={}, orderNo={}, error={}", userId, orderNo, e.getMessage(), e);
            throw new BizException("支付后积分增加失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void deductPointsAfterRefund(String userId, String orderNo, Integer amount) {
        if (amount <= 0 || StringUtils.isBlank(userId) || StringUtils.isBlank(orderNo)) {
            log.warn("无效的退款积分扣除请求: userId={}, orderNo={}, amount={}", userId, orderNo, amount);
            return;
        }
        
        // 计算应扣除的积分：1分钱1积分
        int pointsToDeduct = amount;
        if (pointsToDeduct <= 0) {
            log.info("退款金额太小，无需扣除积分: userId={}, orderNo={}, amount={}", userId, orderNo, amount);
            return;
        }
        
        try {
            log.info("开始处理退款后积分扣除: userId={}, orderNo={}, amount={}, pointsToDeduct={}", 
                    userId, orderNo, amount, pointsToDeduct);
            
            // 查询用户当前积分
            AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, userId));
            
            if (userPo == null || userPo.getPoints() == null) {
                log.warn("用户不存在或积分为空，无法扣除积分: userId={}, orderNo={}", userId, orderNo);
                return;
            }
            
            // 计算扣除后的积分（不能为负数）
            int currentPoints = userPo.getPoints();
            int updatedPoints = Math.max(0, currentPoints - pointsToDeduct);
            int actualDeducted = currentPoints - updatedPoints; // 实际扣除的积分
            
            log.info("积分扣除计算: userId={}, currentPoints={}, pointsToDeduct={}, actualDeducted={}, updatedPoints={}", 
                    userId, currentPoints, pointsToDeduct, actualDeducted, updatedPoints);
            
            // 更新用户积分
            userPo.setPoints(updatedPoints);
            userPo.setUpdateTime(new Date());
            int updateResult = aiUsersMapper.updateById(userPo);
            
            if (updateResult != 1) {
                log.warn("用户积分更新异常，影响行数: {}, userId={}, orderNo={}", updateResult, userId, orderNo);
                throw new BizException("更新用户积分失败");
            }
            
            // 只有在实际扣除了积分的情况下才记录交易
            if (actualDeducted > 0) {
                // 记录积分扣除交易
                AiPointTransactionsPo transaction = new AiPointTransactionsPo();
                transaction.setUserId(userId);
                transaction.setPoints(-actualDeducted); // 负数表示扣除
                transaction.setBalance(updatedPoints);
                transaction.setType(TransactionTypeEnum.REFUND_DEDUCT.getCode());
                transaction.setReferenceId(orderNo);
                transaction.setDescription("退款订单扣除" + actualDeducted + "积分");
                transaction.setCreateTime(new Date());
                transaction.setUpdateTime(new Date());
                transaction.setDelFlag(0);
                aiPointTransactionsMapper.insert(transaction);
            }
            
            log.info("退款后积分扣除成功: userId={}, orderNo={}, pointsDeducted={}, newBalance={}", 
                    userId, orderNo, actualDeducted, updatedPoints);
        } catch (Exception e) {
            log.error("退款后积分扣除异常: userId={}, orderNo={}, error={}", userId, orderNo, e.getMessage(), e);
            throw new BizException("退款后积分扣除失败: " + e.getMessage());
        }
    }
} 