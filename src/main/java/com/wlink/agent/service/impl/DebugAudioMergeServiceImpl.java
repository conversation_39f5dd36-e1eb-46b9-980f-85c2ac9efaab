package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.model.dto.AudioMergeRequest;
import com.wlink.agent.model.dto.AudioMergeResult;
import com.wlink.agent.service.AudioMergeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 调试用的音频合成服务实现
 * 用于测试，不进行实际的音频合成
 */
@Slf4j
@Service("debugAudioMergeService")
@ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "debug")
public class DebugAudioMergeServiceImpl implements AudioMergeService {

    @Override
    public AudioMergeResult mergeAudios(List<String> audioUrls, String outputFileName) {
        if (audioUrls == null || audioUrls.isEmpty()) {
            throw new BizException("音频URL列表不能为空");
        }

        String taskId = IdUtil.fastSimpleUUID();
        log.info("调试模式：模拟音频合成 taskId={}, audioCount={}, audioUrls={}", 
                taskId, audioUrls.size(), audioUrls);

        if (audioUrls.size() == 1) {
            // 只有一个音频，直接返回
            AudioMergeResult result = new AudioMergeResult();
            result.setTaskId(taskId);
            result.setSuccess(true);
            result.setMergedAudioUrl(audioUrls.get(0));
            result.setTotalDurationMs(5000L); // 模拟5秒时长
            log.info("调试模式：单个音频直接返回 taskId={}, url={}", taskId, audioUrls.get(0));
            return result;
        }

        // 模拟多个音频合成
        AudioMergeResult result = new AudioMergeResult();
        result.setTaskId(taskId);
        result.setSuccess(true);
        result.setMergedAudioUrl(audioUrls.get(0)); // 使用第一个音频作为合成结果
        result.setTotalDurationMs((long) (audioUrls.size() * 3000)); // 模拟每个音频3秒
        
        log.info("调试模式：模拟音频合成完成 taskId={}, resultUrl={}, duration={}ms", 
                taskId, result.getMergedAudioUrl(), result.getTotalDurationMs());
        
        return result;
    }

    @Override
    public CompletableFuture<AudioMergeResult> mergeAudiosAsync(AudioMergeRequest request) {
        return CompletableFuture.completedFuture(
                mergeAudios(request.getAudioUrls(), request.getOutputFileName())
        );
    }

    @Override
    public AudioMergeResult getTaskResult(String taskId) {
        log.info("调试模式：获取任务结果 taskId={}", taskId);
        return null;
    }

    @Override
    public boolean cancelTask(String taskId) {
        log.info("调试模式：取消任务 taskId={}", taskId);
        return true;
    }
}
