package com.wlink.agent.service.impl;

import com.wlink.agent.utils.ReliableDistributedSemaphore;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/5/26 11:05
 */
@Slf4j
// 3. 系统启动时的信号量修复
@Component
public class SemaphoreRepairService {

    @Autowired
    private ReliableDistributedSemaphore semaphore;

    @PostConstruct
    public void repairOnStartup() {
        try {
            // 延迟5秒执行，确保其他组件初始化完成
            Thread.sleep(5000);
            semaphore.repairSemaphoreOnStartup();
        } catch (Exception e) {
            log.error("启动时修复信号量失败", e);
        }
    }
}
