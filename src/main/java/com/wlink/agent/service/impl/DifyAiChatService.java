package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.dao.dto.DifyGptResponseV2;
import com.wlink.agent.service.StreamResponseCallback;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSource;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
@Service
public class DifyAiChatService {

    private static final String API_URL = "http://61.153.100.125:30080/v1/chat-messages";
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");


    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 聊天接口
     *
     * @param query 问题内容
     * @param imageUrl 图片URL，可选
     * @param isStream 是否使用流式返回
     * @param callback 流式返回的消息处理器，非流式可传null
     * @return 非流式返回完整回答，流式返回null
     */
    public String chat(String userName,String roomId,String apiKey, JSONObject jsonObject, String query, String imageUrl, boolean isStream, StreamResponseCallback callback){
       return chat(userName, roomId, apiKey, jsonObject, query, imageUrl, isStream, callback, false, "000");
    }


    public String chat(String userName,String roomId,String apiKey, JSONObject jsonObject, String query, String imageUrl, boolean isStream, StreamResponseCallback callback, Boolean conversation,String conversationKey) {
        try {

            // 构建请求
            ChatRequest request = new ChatRequest();
            request.setQuery(query);
            request.setUser(userName);
            request.setInputs(jsonObject);
            // 设置响应模式
            request.setResponseMode(isStream ? "streaming" : "blocking");
            RBucket<String> bucket = redissonClient.getBucket(conversationKey);
            if (conversation){
                request.setConversationId(bucket.get());
            }
            // 如果有图片，添加图片信息
            if (StringUtils.hasText(imageUrl)) {
                FileInfo fileInfo = new FileInfo();
                fileInfo.setUrl(imageUrl);
                request.setFiles(Lists.newArrayList(fileInfo));
            }
            log.info("DifyAiChatService:请求JSON: {}", JSON.toJSONString(request));
            RequestBody body = RequestBody.create(JSON.toJSONString(request), JSON_TYPE);
            Request httpRequest = new Request.Builder()
                    .url(API_URL)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .post(body)
                    .build();

            if (isStream) {
                // 流式调用
                okHttpClient.newCall(httpRequest).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        log.error("流式聊天请求失败", e);
                        callback.onError(e);
                    }

                    @Override
                    public void onResponse(Call call, Response response) {
                        try (ResponseBody responseBody = response.body()) {
                            if (!response.isSuccessful()) {
                                callback.onError(new IOException("Unexpected response code: " + response));
                                return;
                            }
                            if (responseBody == null) {
                                callback.onError(new IOException("Response body is null"));
                                return;
                            }
                            BufferedSource source = responseBody.source();
                            while (!source.exhausted()) {
                                String line = source.readUtf8Line();
                                if (line == null || line.isEmpty()) continue;
                                if (line.startsWith("data: ")) {
                                    String data = line.substring(6);
                                    DifyGptResponseV2 difyGptResponse = JSON.parseObject(data, DifyGptResponseV2.class);
                                    if (difyGptResponse.getEvent().equals("message_end")) {
                                        log.info(">>>>>>>>>>>>>>>>dify response: {}", JSON.toJSONString(difyGptResponse));
                                        callback.onComplete();
                                        bucket.set(difyGptResponse.getConversationId(), 10, TimeUnit.MINUTES);
                                        return;
                                    }
                                    if (Objects.nonNull(difyGptResponse) && Objects.equals(difyGptResponse.getEvent(), "message")) {
                                        callback.onToken(difyGptResponse.getAnswer());
                                    }
                                }
                            }
                        } catch (IOException e) {
                            callback.onError(e);
                        }
                    }
                });
                return null;
            } else {
                // 非流式调用
                try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                    if (!response.isSuccessful() || response.body() == null) {
                        throw new RuntimeException("API调用失败: " + response);
                    }
                    String responseStr = response.body().string();
                    JSONObject jsonResponse = JSON.parseObject(responseStr);
                    if (jsonResponse.containsKey("conversation_id")) {
                        bucket.set(jsonResponse.getString("conversation_id"), 5, TimeUnit.MINUTES);
                    }
                    return extractResponseText(jsonResponse);
                }
            }
        } catch (Exception e) {
            log.error("接口调用错误: query={}, isStream={}", query, isStream, e);
            throw new RuntimeException("接口调用错误", e);
        }
    }

    /**
     * 从响应JSON中提取文本内容
     */
    private String extractResponseText(JSONObject jsonResponse) {
        try {
            log.info("DifyAiChatService:响应JSON: {}", jsonResponse);

            // 根据实际的JSON结构提取文本内容
            if (jsonResponse.containsKey("answer")) {
                return jsonResponse.getString("answer");
            }

            if (jsonResponse.containsKey("message")) {
                JSONObject message = jsonResponse.getJSONObject("message");
                if (message.containsKey("content")) {
                    return message.getString("content");
                }
            }
            return null;
        } catch (Exception e) {
            log.error("提取响应文本失败: {}", jsonResponse, e);
            return null;
        }
    }

    @Data
    public static class ChatRequest {
        private JSONObject inputs = new JSONObject();
        private String query;
        @JSONField(name = "response_mode")
        private String responseMode = "blocking";// blocking或streaming
        @JSONField(name = "conversation_id")
        private String conversationId = "";
        private String user;
        private List<FileInfo> files;
    }

    @Data
    public static class FileInfo {
        private String type = "image";
        @JSONField(name = "transfer_method")
        private String transferMethod = "remote_url";
        private String url;
    }
} 