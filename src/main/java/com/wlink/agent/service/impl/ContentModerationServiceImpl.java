package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.ImageModerationRequest;
import com.aliyun.green20220302.models.ImageModerationResponse;
import com.aliyun.green20220302.models.ImageModerationResponseBody;
import com.aliyun.green20220302.models.TextModerationRequest;
import com.aliyun.green20220302.models.TextModerationResponse;
import com.aliyun.green20220302.models.TextModerationResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.wlink.agent.config.AliyunContentSecurityConfig;
import com.wlink.agent.dao.mapper.AiImageModerationLogMapper;
import com.wlink.agent.dao.mapper.AiTextModerationLogMapper;
import com.wlink.agent.dao.po.AiImageModerationLogPo;
import com.wlink.agent.dao.po.AiTextModerationLogPo;
import com.wlink.agent.model.DetectionDetail;
import com.wlink.agent.model.ImageModerationResult;
import com.wlink.agent.model.TextModerationResult;
import com.wlink.agent.service.ContentModerationService;
import com.wlink.agent.utils.UserContext;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 内容安全检测服务实现类
 * 基于阿里云内容安全SDK实现图片和文本检测功能
 * 
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContentModerationServiceImpl implements ContentModerationService {

    private final AliyunContentSecurityConfig config;
    private final AiImageModerationLogMapper moderationLogMapper;
    private final AiTextModerationLogMapper textModerationLogMapper;
    private final Executor contentExecutor;
    private Client client;

    /**
     * 初始化阿里云客户端
     */
    @PostConstruct
    public void initClient() {
        if (!config.getEnabled()) {
            log.info("阿里云内容安全检测服务已禁用");
            return;
        }

        try {
            Config clientConfig = new Config()
                    .setAccessKeyId(config.getAccessKeyId())
                    .setAccessKeySecret(config.getAccessKeySecret())
                    .setEndpoint(config.getEndpoint())
                    .setConnectTimeout(config.getConnectionTimeout())
                    .setReadTimeout(config.getReadTimeout());

            this.client = new Client(clientConfig);
            log.info("阿里云内容安全客户端初始化成功，endpoint: {}", config.getEndpoint());
        } catch (Exception e) {
            log.error("阿里云内容安全客户端初始化失败", e);
            throw new BizException("内容安全服务初始化失败：" + e.getMessage());
        }
    }

    @Override
    public ImageModerationResult moderateImageUrl(String imageUrl) {
        return moderateImageUrl(imageUrl, config.getDefaultService());
    }

    @Override
    public ImageModerationResult moderateImageUrl(String imageUrl, String serviceType) {
        long startTime = System.currentTimeMillis();
        log.info("开始检测图片URL: {}, 检测服务: {}", imageUrl, serviceType);
        
        if (!StringUtils.hasText(imageUrl)) {
            throw new BizException("图片URL不能为空");
        }

        if (Boolean.FALSE.equals(config.getEnabled())) {
            log.info("内容安全检测服务已禁用，跳过检测");
            ImageModerationResult result = new ImageModerationResult(true, "disabled");
            result.setSuggestion("pass");
            result.setDataId(UUID.randomUUID().toString());
            
            // 异步保存审核记录
            saveAuditLogAsync(imageUrl, serviceType, result, System.currentTimeMillis() - startTime, null);
            
            return result;
        }

        try {
            // 构建检测请求
            Map<String, Object> serviceParameters = new HashMap<>();
            serviceParameters.put("imageUrl", imageUrl);

            ImageModerationRequest request = new ImageModerationRequest()
                    .setService(serviceType);
            // 将Map转换为JSON字符串
            request.setServiceParameters(JSON.toJSONString(serviceParameters));

            RuntimeOptions runtime = new RuntimeOptions();
            
            // 调用阿里云接口
            ImageModerationResponse response = client.imageModerationWithOptions(request, runtime);
            
            long responseTime = System.currentTimeMillis() - startTime;
            log.info("图片检测完成，RequestId: {}, 响应时间: {}ms", response.getBody().getRequestId(), responseTime);
            
            ImageModerationResult result = parseResponse(response);
            
            // 异步保存审核记录
            saveAuditLogAsync(imageUrl, serviceType, result, responseTime, null);
            
            return result;
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            log.error("图片URL检测失败: {}, 响应时间: {}ms", imageUrl, responseTime, e);
            
            ImageModerationResult result = new ImageModerationResult(false, null);
            result.setErrorMessage("检测失败：" + e.getMessage());
            result.setErrorCode("DETECTION_FAILED");
            
            // 异步保存审核记录（包含错误信息）
            saveAuditLogAsync(imageUrl, serviceType, result, responseTime, e);
            
            return result;
        }
    }

    @Override
    public ImageModerationResult moderateOssFile(String region, String bucket, String objectKey) {
        return moderateOssFile(region, bucket, objectKey, config.getDefaultService());
    }

    @Override
    public ImageModerationResult moderateOssFile(String region, String bucket, String objectKey, String serviceType) {
        log.info("开始检测OSS文件: region={}, bucket={}, objectKey={}, 检测服务: {}", 
                region, bucket, objectKey, serviceType);
        
        if (!StringUtils.hasText(region) || !StringUtils.hasText(bucket) || !StringUtils.hasText(objectKey)) {
            throw new BizException("OSS文件参数不能为空");
        }

        if (!config.getEnabled()) {
            log.warn("内容安全检测服务已禁用，跳过检测");
            ImageModerationResult result = new ImageModerationResult(true, "disabled");
            result.setSuggestion("pass");
            result.setDataId(UUID.randomUUID().toString());
            return result;
        }

        try {
            // 构建OSS文件检测请求
            Map<String, Object> serviceParameters = new HashMap<>();
            serviceParameters.put("ossBucketName", bucket);
            serviceParameters.put("ossObjectName", objectKey);

            ImageModerationRequest request = new ImageModerationRequest()
                    .setService(serviceType);
            // 将Map转换为JSON字符串
            request.setServiceParameters(JSON.toJSONString(serviceParameters));

            RuntimeOptions runtime = new RuntimeOptions();
            
            // 调用阿里云接口
            ImageModerationResponse response = client.imageModerationWithOptions(request, runtime);
            
            log.info("OSS文件检测完成，RequestId: {}", response.getBody().getRequestId());
            
            return parseResponse(response);
            
        } catch (Exception e) {
            log.error("OSS文件检测失败: region={}, bucket={}, objectKey={}", region, bucket, objectKey, e);
            
            ImageModerationResult result = new ImageModerationResult(false, null);
            result.setErrorMessage("检测失败：" + e.getMessage());
            result.setErrorCode("DETECTION_FAILED");
            
            return result;
        }
    }

    @Override
    public ImageModerationResult moderateBase64Image(String base64Data) {
        log.info("开始检测Base64图片数据，数据长度: {}", base64Data != null ? base64Data.length() : 0);
        
        if (!StringUtils.hasText(base64Data)) {
            throw new BizException("Base64图片数据不能为空");
        }

        if (!config.getEnabled()) {
            log.warn("内容安全检测服务已禁用，跳过检测");
            ImageModerationResult result = new ImageModerationResult(true, "disabled");
            result.setSuggestion("pass");
            result.setDataId(UUID.randomUUID().toString());
            return result;
        }

        try {
            // 构建Base64检测请求
            Map<String, Object> serviceParameters = new HashMap<>();
            serviceParameters.put("imageData", base64Data);

            ImageModerationRequest request = new ImageModerationRequest()
                    .setService(config.getDefaultService());
            // 将Map转换为JSON字符串
            request.setServiceParameters(JSON.toJSONString(serviceParameters));

            RuntimeOptions runtime = new RuntimeOptions();
            
            // 调用阿里云接口
            ImageModerationResponse response = client.imageModerationWithOptions(request, runtime);
            
            log.info("Base64图片检测完成，RequestId: {}", response.getBody().getRequestId());
            
            return parseResponse(response);
            
        } catch (Exception e) {
            log.error("Base64图片检测失败", e);
            
            ImageModerationResult result = new ImageModerationResult(false, null);
            result.setErrorMessage("检测失败：" + e.getMessage());
            result.setErrorCode("DETECTION_FAILED");
            
            return result;
        }
    }

    @Override
    public List<ImageModerationResult> batchModerateImageUrls(List<String> imageUrls) {
        log.info("开始批量检测图片URL，数量: {}", imageUrls != null ? imageUrls.size() : 0);
        
        if (imageUrls == null || imageUrls.isEmpty()) {
            throw new BizException("图片URL列表不能为空");
        }

        List<ImageModerationResult> results = new ArrayList<>();
        
        for (String imageUrl : imageUrls) {
            try {
                ImageModerationResult result = moderateImageUrl(imageUrl);
                results.add(result);
            } catch (Exception e) {
                log.error("批量检测中单个URL检测失败: {}", imageUrl, e);
                
                ImageModerationResult errorResult = new ImageModerationResult(false, null);
                errorResult.setErrorMessage("检测失败：" + e.getMessage());
                errorResult.setErrorCode("DETECTION_FAILED");
                results.add(errorResult);
            }
        }
        
        log.info("批量检测完成，总数: {}, 成功: {}", 
                results.size(), 
                results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum());
        
        return results;
    }

    /**
     * 解析阿里云检测响应
     */
    private ImageModerationResult parseResponse(ImageModerationResponse response) {
        ImageModerationResponseBody body = response.getBody();
        ImageModerationResult result = new ImageModerationResult(true, body.getRequestId());
        
        if (body.getData() == null) {
            result.setSuggestion("pass");
            result.setDataId(UUID.randomUUID().toString());
            return result;
        }

        ImageModerationResponseBody.ImageModerationResponseBodyData data = body.getData();
        result.setDataId(data.getDataId());
        
        // 解析检测结果
        List<ImageModerationResponseBody.ImageModerationResponseBodyDataResult> resultList = data.getResult();
        if (resultList != null && !resultList.isEmpty()) {
            List<DetectionDetail> details = new ArrayList<>();
            String finalSuggestion = "pass";

            for (ImageModerationResponseBody.ImageModerationResponseBodyDataResult item : resultList) {
                DetectionDetail detail = new DetectionDetail();
                detail.setLabel(item.getLabel());
                
                // 尝试获取检测结果，兼容不同的API方法名
                String itemResult = null;
                Double confidence = null;
                
                try {
                    String label = item.getLabel();
                    if (!Objects.equals(label, "nonLabel")){
                        finalSuggestion = "block";
                    }
                } catch (Exception e) {
                    log.warn("获取检测结果属性失败", e);
                    itemResult = "block"; // 默认值
                    confidence = 0.0;
                }
                detail.setResult(itemResult != null ? itemResult : "pass");
                detail.setConfidence(confidence != null ? confidence : 0.0);
                
                details.add(detail);
                
                // 确定最终建议（优先级: block > review > pass）
                if ("block".equals(itemResult)) {
                    finalSuggestion = "block";
                } else if ("review".equals(itemResult) && !"block".equals(finalSuggestion)) {
                    finalSuggestion = "review";
                }
            }
            
            result.setDetails(details);
            result.setSuggestion(finalSuggestion);
        } else {
            result.setSuggestion("pass");
        }
        
        return result;
    }

    /**
     * 异步保存审核记录
     */
    private void saveAuditLogAsync(String imageUrl, String serviceType, ImageModerationResult result, 
                                   long responseTime, Exception exception) {
        CompletableFuture.runAsync(() -> {
            try {
                saveAuditLog(imageUrl, serviceType, result, responseTime, exception);
            } catch (Exception e) {
                // 记录日志保存失败，但不影响主流程
                log.error("保存图片审核记录失败: imageUrl={}, serviceType={}", imageUrl, serviceType, e);
            }
        }, contentExecutor);
    }

    /**
     * 保存审核记录到数据库
     */
    private void saveAuditLog(String imageUrl, String serviceType, ImageModerationResult result, 
                              long responseTime, Exception exception) {
        try {
            AiImageModerationLogPo logPo = new AiImageModerationLogPo();
            
            // 获取用户信息
            try {
                String userId = UserContext.getUser() != null ? UserContext.getUser().getUserId() : null;
                logPo.setUserId(userId);
            } catch (Exception e) {
                log.warn("获取用户信息失败，将使用空值", e);
                logPo.setUserId(null);
            }
            
            // 基本信息
            logPo.setImageUrl(imageUrl);
            logPo.setServiceType(serviceType);
            logPo.setResponseTime(responseTime);
            logPo.setCreateTime(new Date());
            logPo.setUpdateTime(new Date());
            
            // 审核结果信息
            if (result != null) {
                logPo.setIsSuccess(result.isSuccess());
                logPo.setRequestId(result.getRequestId());
                logPo.setDataId(result.getDataId());
                logPo.setSuggestion(result.getSuggestion());
                logPo.setErrorCode(result.getErrorCode());
                logPo.setErrorMessage(result.getErrorMessage());
                
                // 将详细结果转换为JSON
                if (result.getDetails() != null && !result.getDetails().isEmpty()) {
                    try {
                        logPo.setDetails(JSON.toJSONString(result.getDetails()));
                    } catch (Exception e) {
                        log.warn("转换审核详情为JSON失败", e);
                        logPo.setDetails(null);
                    }
                }
            } else {
                logPo.setIsSuccess(false);
            }
            
            // 异常信息
            if (exception != null) {
                logPo.setIsSuccess(false);
                if (StringUtils.isEmpty(logPo.getErrorMessage())) {
                    logPo.setErrorMessage(exception.getMessage());
                }
                if (StringUtils.isEmpty(logPo.getErrorCode())) {
                    logPo.setErrorCode("SYSTEM_ERROR");
                }
            }
            
            // 保存到数据库
            moderationLogMapper.insert(logPo);
            
            log.debug("图片审核记录保存成功: id={}, imageUrl={}, suggestion={}", 
                    logPo.getId(), imageUrl, logPo.getSuggestion());
            
        } catch (Exception e) {
            log.error("保存图片审核记录到数据库失败", e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 检查对象是否有指定方法
     */
    private boolean hasMethod(Object obj, String methodName) {
        try {
            obj.getClass().getMethod(methodName);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    /**
     * 反射调用方法
     */
    private Object invokeMethod(Object obj, String methodName) throws Exception {
        return obj.getClass().getMethod(methodName).invoke(obj);
    }

    /**
     * 判断是否为可重试的异常
     */
    private boolean isRetryableException(Exception e) {
        // 这里可以根据具体的异常类型判断是否可重试
        // 例如网络超时、服务暂时不可用等
        String message = e.getMessage();
        return message != null && (
                message.contains("timeout") || 
                message.contains("connection") ||
                message.contains("network")
        );
    }

    /**
     * 重试检测（简单重试机制）
     */
    private ImageModerationResult retryDetection(String imageUrl, String serviceType, int retryCount) {
        if (retryCount > config.getMaxRetries()) {
            log.error("图片检测重试次数已达上限: {}", config.getMaxRetries());
            ImageModerationResult result = new ImageModerationResult(false, null);
            result.setErrorMessage("检测失败，重试次数已达上限");
            result.setErrorCode("MAX_RETRY_EXCEEDED");
            return result;
        }
        
        log.info("开始第{}次重试检测图片URL: {}", retryCount, imageUrl);
        
        try {
            Thread.sleep(1000 * retryCount); // 简单的退避策略
            return moderateImageUrl(imageUrl, serviceType);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            ImageModerationResult result = new ImageModerationResult(false, null);
            result.setErrorMessage("检测被中断");
            result.setErrorCode("INTERRUPTED");
            return result;
        } catch (Exception e) {
            if (isRetryableException(e)) {
                return retryDetection(imageUrl, serviceType, retryCount + 1);
            } else {
                ImageModerationResult result = new ImageModerationResult(false, null);
                result.setErrorMessage("检测失败：" + e.getMessage());
                result.setErrorCode("DETECTION_FAILED");
                return result;
            }
        }
    }

    @Override
    public TextModerationResult moderateText(String content) {
        return moderateText(content, config.getDefaultTextService());
    }

    @Override
    public TextModerationResult moderateText(String content, String serviceType) {
        long startTime = System.currentTimeMillis();
        log.info("开始检测文本内容, 长度: {}, 检测服务: {}", content != null ? content.length() : 0, serviceType);
        
        if (!StringUtils.hasText(content)) {
            throw new BizException("文本内容不能为空");
        }

        if (Boolean.FALSE.equals(config.getEnabled())) {
            log.info("内容安全检测服务已禁用，跳过检测");
            TextModerationResult result = new TextModerationResult(true, "disabled");
            result.setSuggestion("pass");
            result.setDataId(UUID.randomUUID().toString());
            result.setContent(content);
            
            // 异步保存审核记录
            saveTextAuditLogAsync(content, serviceType, result, System.currentTimeMillis() - startTime, null);
            
            return result;
        }

        try {
            // 构建检测请求
            JSONObject serviceParameters = new JSONObject();
            serviceParameters.put("content", content);

            TextModerationRequest request = new TextModerationRequest();
            request.setService(serviceType);
            request.setServiceParameters(serviceParameters.toJSONString());

            RuntimeOptions runtime = new RuntimeOptions();
            
            // 调用阿里云接口
            TextModerationResponse response = client.textModerationWithOptions(request, runtime);
            
            long responseTime = System.currentTimeMillis() - startTime;
            log.info("文本检测完成，RequestId: {}, 响应时间: {}ms", response.getBody().getRequestId(), responseTime);
            
            TextModerationResult result = parseTextResponse(response);
            result.setContent(content);
            
            // 异步保存审核记录
            saveTextAuditLogAsync(content, serviceType, result, responseTime, null);
            
            return result;
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            log.error("文本检测失败, 响应时间: {}ms", responseTime, e);
            
            TextModerationResult result = new TextModerationResult(false, null);
            result.setErrorMessage("检测失败：" + e.getMessage());
            result.setErrorCode("DETECTION_FAILED");
            result.setContent(content);
            
            // 异步保存审核记录（包含错误信息）
            saveTextAuditLogAsync(content, serviceType, result, responseTime, e);
            
            return result;
        }
    }

    @Override
    public List<TextModerationResult> batchModerateTexts(List<String> contentList) {
        log.info("开始批量检测文本内容，数量: {}", contentList != null ? contentList.size() : 0);
        
        if (contentList == null || contentList.isEmpty()) {
            throw new BizException("文本内容列表不能为空");
        }

        List<TextModerationResult> results = new ArrayList<>();
        
        for (String content : contentList) {
            try {
                TextModerationResult result = moderateText(content);
                results.add(result);
            } catch (Exception e) {
                log.error("批量检测中单个文本检测失败: {}", content, e);
                
                TextModerationResult errorResult = new TextModerationResult(false, null);
                errorResult.setErrorMessage("检测失败：" + e.getMessage());
                errorResult.setErrorCode("DETECTION_FAILED");
                errorResult.setContent(content);
                
                // 异步保存审核记录（包含错误信息）
                saveTextAuditLogAsync(content, config.getDefaultTextService(), errorResult, 0L, e);
                
                results.add(errorResult);
            }
        }
        
        log.info("批量文本检测完成，总数: {}, 成功: {}", 
                results.size(), 
                results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum());
        
        return results;
    }

    /**
     * 解析阿里云文本检测响应
     */
    private TextModerationResult parseTextResponse(TextModerationResponse response) {
        TextModerationResponseBody body = response.getBody();
        TextModerationResult result = new TextModerationResult(true, body.getRequestId());
        
        if (body.getData() == null) {
            result.setSuggestion("pass");
            result.setDataId(UUID.randomUUID().toString());
            return result;
        }

        TextModerationResponseBody.TextModerationResponseBodyData data = body.getData();
        
        // 设置标签和理由
        List<String> labelList = new ArrayList<>();
        String labelsStr = data.getLabels();
        if (StringUtils.hasText(labelsStr)) {
            // 如果是逗号分隔的字符串，则拆分
            if (labelsStr.contains(",")) {
                String[] labelArray = labelsStr.split(",");
                for (String label : labelArray) {
                    labelList.add(label.trim());
                }
            } else {
                // 否则直接添加单个标签
                labelList.add(labelsStr.trim());
            }
        }
        result.setLabels(labelList);
        result.setReason(data.getReason());
        
        // 设置建议
        // 如果有标签，通常表示存在风险，设置为block，否则为pass
        if (!labelList.isEmpty()) {
            result.setSuggestion("block");
        } else {
            result.setSuggestion("pass");
        }
        
        // 添加详细信息
        if (!labelList.isEmpty()) {
            List<DetectionDetail> details = new ArrayList<>();
            
            for (String label : labelList) {
                DetectionDetail detail = new DetectionDetail();
                detail.setLabel(label);
                detail.setResult("block");
                // 阿里云文本检测可能没有返回具体的置信度，设置为默认值
                detail.setConfidence(100.0);
                detail.setDescription(data.getReason());
                
                details.add(detail);
            }
            
            result.setDetails(details);
        }
        
        return result;
    }

    /**
     * 异步保存文本审核记录
     */
    private void saveTextAuditLogAsync(String content, String serviceType, TextModerationResult result, 
                                      long responseTime, Exception exception) {
        CompletableFuture.runAsync(() -> {
            try {
                saveTextAuditLog(content, serviceType, result, responseTime, exception);
            } catch (Exception e) {
                // 记录日志保存失败，但不影响主流程
                log.error("保存文本审核记录失败: content={}, serviceType={}", 
                         content != null ? content.substring(0, Math.min(content.length(), 50)) + "..." : null, 
                         serviceType, e);
            }
        }, contentExecutor);
    }

    /**
     * 保存文本审核记录到数据库
     */
    private void saveTextAuditLog(String content, String serviceType, TextModerationResult result, 
                                 long responseTime, Exception exception) {
        try {
            AiTextModerationLogPo logPo = new AiTextModerationLogPo();
            
            // 获取用户信息
            try {
                String userId = UserContext.getUser() != null ? UserContext.getUser().getUserId() : null;
                logPo.setUserId(userId);
            } catch (Exception e) {
                log.warn("获取用户信息失败，将使用空值", e);
                logPo.setUserId(null);
            }
            
            // 基本信息
            // 截断文本内容，避免过长
            if (content != null && content.length() > 500) {
                logPo.setContent(content.substring(0, 500) + "...");
            } else {
                logPo.setContent(content);
            }
            logPo.setServiceType(serviceType);
            logPo.setResponseTime(responseTime);
            logPo.setCreateTime(new Date());
            logPo.setUpdateTime(new Date());
            
            // 审核结果信息
            if (result != null) {
                logPo.setIsSuccess(result.isSuccess());
                logPo.setRequestId(result.getRequestId());
                logPo.setDataId(result.getDataId());
                logPo.setSuggestion(result.getSuggestion());
                logPo.setErrorCode(result.getErrorCode());
                logPo.setErrorMessage(result.getErrorMessage());
                logPo.setReason(result.getReason());
                
                // 将标签列表转换为JSON
                if (result.getLabels() != null && !result.getLabels().isEmpty()) {
                    try {
                        logPo.setLabels(JSON.toJSONString(result.getLabels()));
                    } catch (Exception e) {
                        log.warn("转换标签列表为JSON失败", e);
                        logPo.setLabels(null);
                    }
                }
                
                // 将详细结果转换为JSON
                if (result.getDetails() != null && !result.getDetails().isEmpty()) {
                    try {
                        logPo.setDetails(JSON.toJSONString(result.getDetails()));
                    } catch (Exception e) {
                        log.warn("转换审核详情为JSON失败", e);
                        logPo.setDetails(null);
                    }
                }
                
                // 设置风险分数
                logPo.setRiskScore(result.getRiskScore());
            } else {
                logPo.setIsSuccess(false);
            }
            
            // 异常信息
            if (exception != null) {
                logPo.setIsSuccess(false);
                if (StringUtils.isEmpty(logPo.getErrorMessage())) {
                    logPo.setErrorMessage(exception.getMessage());
                }
                if (StringUtils.isEmpty(logPo.getErrorCode())) {
                    logPo.setErrorCode("SYSTEM_ERROR");
                }
            }
            
            // 保存到数据库
            textModerationLogMapper.insert(logPo);
            
            log.debug("文本审核记录保存成功: id={}, content={}, suggestion={}", 
                    logPo.getId(), 
                    content != null ? content.substring(0, Math.min(content.length(), 30)) + "..." : null,
                    logPo.getSuggestion());
            
        } catch (Exception e) {
            log.error("保存文本审核记录到数据库失败", e);
            // 不抛出异常，避免影响主业务流程
        }
    }
} 