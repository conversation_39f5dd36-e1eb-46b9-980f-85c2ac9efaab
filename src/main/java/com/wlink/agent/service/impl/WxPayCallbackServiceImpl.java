package com.wlink.agent.service.impl;

import com.wechat.pay.java.service.payments.model.Transaction;
import com.wlink.agent.dao.po.PayOrderPo;
import com.wlink.agent.service.PayCallbackService;
import com.wlink.agent.service.PayService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;



/**
 * 微信支付回调处理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxPayCallbackServiceImpl implements PayCallbackService {

    private final PayService payService;

    /**
     * 处理微信支付回调
     */
    @Override
    public String handleWxPayCallback(String xmlData) {
        log.info("接收到微信支付回调，使用V3版本API处理");
        // 获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("获取HttpServletRequest失败");
            return "FAIL";
        }
        HttpServletRequest request = attributes.getRequest();
        // 调用V3版本的回调处理方法
        return payService.handlePayNotify(xmlData, request);
    }
    
    /**
     * 主动查询订单支付状态
     * 
     * @param orderNo 商户订单号
     * @return 订单信息
     */
    public PayOrderPo checkOrderStatus(String orderNo) {
        log.info("主动查询订单支付状态: orderNo={}", orderNo);
        try {
            // 首先查询本地订单状态
            PayOrderPo orderPo = payService.queryOrder(orderNo);
            
            // 如果订单不存在，直接返回null
            if (orderPo == null) {
                log.error("订单不存在: {}", orderNo);
                return null;
            }
            
            // 如果订单已经是终态，直接返回
            if (orderPo.getStatus() == 2 || orderPo.getStatus() == 3 || orderPo.getStatus() == 4) {
                log.info("订单已是终态，无需查询微信支付状态: orderNo={}, status={}", orderNo, orderPo.getStatus());
                return orderPo;
            }
            
            // 主动查询微信支付订单状态并同步本地订单
            return payService.syncOrderStatus(orderNo);
        } catch (Exception e) {
            log.error("主动查询订单支付状态异常: orderNo={}, error={}", orderNo, e.getMessage(), e);
            throw e;
        }
    }
} 