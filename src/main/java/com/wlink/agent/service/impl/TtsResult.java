package com.wlink.agent.service.impl;

import lombok.Data;

/**
 * TTS结果实体类
 */
@Data
public class TtsResult {
    /**
     * 音频文件路径
     */
    private String filePath;
    
    /**
     * 音频长度（毫秒）
     */
    private Long audioLength;
    
    /**
     * 音频大小（字节）
     */
    private Long audioSize;


    private String subtitleFile;

    
    public TtsResult(String filePath, Long audioLength, Long audioSize) {
        this.filePath = filePath;
        this.audioLength = audioLength;
        this.audioSize = audioSize;
    }
} 