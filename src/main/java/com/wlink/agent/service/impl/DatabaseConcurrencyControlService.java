package com.wlink.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.config.ConcurrencyControlConfig;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 基于数据库的并发控制服务
 * 替代Redis信号量，提供更稳定的并发控制
 */
@Slf4j
@Service
public class DatabaseConcurrencyControlService {

    @Autowired
    private AiImageTaskQueueMapper taskQueueMapper;

    @Autowired
    private ConcurrencyControlConfig config;

    /**
     * 尝试获取任务执行权限
     * 使用数据库乐观锁机制
     * 
     * @param taskId 任务ID
     * @return 是否成功获取执行权限
     */
    @Transactional
    public boolean tryAcquireTask(String taskId) {
        try {
            // 1. 检查当前正在处理的任务数量
            int processingCount = getProcessingTaskCount();
            if (processingCount >= config.getMaxConcurrentTasks()) {
                log.info("当前处理任务数已达上限: {}/{}, 任务 {} 等待执行",
                    processingCount, config.getMaxConcurrentTasks(), taskId);
                return false;
            }

            // 2. 尝试将任务状态从PENDING更新为PROCESSING
            LambdaUpdateWrapper<AiImageTaskQueuePo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiImageTaskQueuePo::getId, Long.valueOf(taskId))
                        .eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                        .set(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue())
                        .set(AiImageTaskQueuePo::getUpdateTime, new Date());

            int updatedRows = taskQueueMapper.update(null, updateWrapper);
            
            if (updatedRows > 0) {
                log.info("任务 {} 成功获取执行权限，当前处理任务数: {}/{}",
                    taskId, processingCount + 1, config.getMaxConcurrentTasks());
                return true;
            } else {
                log.info("任务 {} 获取执行权限失败，可能已被其他线程处理或状态已变更", taskId);
                return false;
            }

        } catch (Exception e) {
            log.error("获取任务执行权限失败, taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 释放任务执行权限
     * 将任务状态更新为完成或失败
     * 
     * @param taskId 任务ID
     * @param success 是否成功
     * @param errorMessage 错误信息（失败时）
     */
    @Transactional
    public void releaseTask(String taskId, boolean success, String errorMessage) {
        try {
            LambdaUpdateWrapper<AiImageTaskQueuePo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiImageTaskQueuePo::getId, Long.valueOf(taskId))
                        .eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue())
                        .set(AiImageTaskQueuePo::getTaskStatus, 
                            success ? TaskStatus.COMPLETED.getValue() : TaskStatus.FAILED.getValue())
                        .set(AiImageTaskQueuePo::getUpdateTime, new Date());

            if (!success && errorMessage != null) {
                updateWrapper.set(AiImageTaskQueuePo::getErrorReason, errorMessage);
            }

            int updatedRows = taskQueueMapper.update(null, updateWrapper);
            
            if (updatedRows > 0) {
                log.info("任务 {} 执行权限已释放，状态: {}", taskId, success ? "成功" : "失败");
            } else {
                log.warn("释放任务执行权限失败，任务可能已被其他线程处理, taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("释放任务执行权限失败, taskId: {}", taskId, e);
        }
    }

    /**
     * 获取当前正在处理的任务数量
     */
    private int getProcessingTaskCount() {
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue());
        
        return Math.toIntExact(taskQueueMapper.selectCount(queryWrapper));
    }

    /**
     * 清理超时的处理中任务
     * 定时任务调用，将超时的PROCESSING任务重置为PENDING
     */
    @Transactional
    public int cleanupTimeoutTasks() {
        try {
            // 计算超时时间点
            LocalDateTime timeoutPoint = LocalDateTime.now().minusMinutes(config.getTaskTimeoutMinutes());
            Date timeoutDate = Date.from(timeoutPoint.atZone(ZoneId.systemDefault()).toInstant());

            // 查询超时的处理中任务
            LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue())
                       .lt(AiImageTaskQueuePo::getUpdateTime, timeoutDate);

            List<AiImageTaskQueuePo> timeoutTasks = taskQueueMapper.selectList(queryWrapper);
            
            if (timeoutTasks.isEmpty()) {
                return 0;
            }

            // 将超时任务重置为PENDING状态
            LambdaUpdateWrapper<AiImageTaskQueuePo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue())
                        .lt(AiImageTaskQueuePo::getUpdateTime, timeoutDate)
                        .set(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                        .set(AiImageTaskQueuePo::getUpdateTime, new Date())
                        .set(AiImageTaskQueuePo::getErrorReason, "任务处理超时，已重置为待处理状态");

            int updatedCount = taskQueueMapper.update(null, updateWrapper);
            
            log.info("清理超时任务完成，重置了 {} 个超时任务", updatedCount);
            
            // 记录详细信息
            for (AiImageTaskQueuePo task : timeoutTasks) {
                log.warn("任务 {} 处理超时，已重置为待处理状态。最后更新时间: {}", 
                    task.getId(), task.getUpdateTime());
            }
            
            return updatedCount;

        } catch (Exception e) {
            log.error("清理超时任务失败", e);
            return 0;
        }
    }

    /**
     * 获取系统状态信息
     */
    public ConcurrencyStatus getStatus() {
        try {
            int processingCount = getProcessingTaskCount();
            
            LambdaQueryWrapper<AiImageTaskQueuePo> pendingQuery = new LambdaQueryWrapper<>();
            pendingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue());
            int pendingCount = Math.toIntExact(taskQueueMapper.selectCount(pendingQuery));

            return new ConcurrencyStatus(
                config.getMaxConcurrentTasks(),
                processingCount,
                config.getMaxConcurrentTasks() - processingCount,
                pendingCount
            );

        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return new ConcurrencyStatus(config.getMaxConcurrentTasks(), 0, config.getMaxConcurrentTasks(), 0);
        }
    }

    /**
     * 并发状态信息
     */
    public static class ConcurrencyStatus {
        private final int maxConcurrent;
        private final int currentProcessing;
        private final int available;
        private final int pendingTasks;

        public ConcurrencyStatus(int maxConcurrent, int currentProcessing, int available, int pendingTasks) {
            this.maxConcurrent = maxConcurrent;
            this.currentProcessing = currentProcessing;
            this.available = available;
            this.pendingTasks = pendingTasks;
        }

        public int getMaxConcurrent() { return maxConcurrent; }
        public int getCurrentProcessing() { return currentProcessing; }
        public int getAvailable() { return available; }
        public int getPendingTasks() { return pendingTasks; }

        @Override
        public String toString() {
            return String.format("ConcurrencyStatus{max=%d, processing=%d, available=%d, pending=%d}", 
                maxConcurrent, currentProcessing, available, pendingTasks);
        }
    }
}
