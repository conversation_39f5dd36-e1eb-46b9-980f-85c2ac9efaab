package com.wlink.agent.service.impl;

import java.io.IOException;
import java.util.Date;
import java.util.concurrent.TimeUnit;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.utils.I18nMessageUtils;
import com.wlink.agent.utils.JwtUtil;
import com.wlink.agent.utils.OssUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.config.WechatProperties;
import com.wlink.agent.constant.WechatConstant;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.service.WechatService;
import com.wlink.agent.user.model.WechatLoginCheckRes;
import com.wlink.agent.user.model.WechatLoginRes;
import com.wlink.agent.user.model.WechatQrCodeRes;


import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 微信开放平台服务实现
 */
@Slf4j
@Service
public class WechatServiceImpl implements WechatService {

    private final WechatProperties wechatProperties;
    private final OkHttpClient okHttpClient;
    private final RedissonClient redissonClient;
    private final AiUsersMapper aiUsersMapper;
    private final JwtUtil jwtUtil;
    private final OssUtils ossUtils;


    @Value("${spring.profiles.active}")
    private String env;
    /**
     * oss路径
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    private static final String OSS_PATH = "dify/{env}/user/{userId}/avatar/";



    @Autowired
    public WechatServiceImpl(WechatProperties wechatProperties, OkHttpClient okHttpClient, RedissonClient redissonClient, AiUsersMapper aiUsersMapper, JwtUtil jwtUtil, OssUtils ossUtils) {
        this.wechatProperties = wechatProperties;
        this.okHttpClient = okHttpClient;
        this.redissonClient = redissonClient;
        this.aiUsersMapper = aiUsersMapper;
        this.jwtUtil = jwtUtil;
        this.ossUtils = ossUtils;
    }


    @Override
    public WechatQrCodeRes generateLoginQrCodeUrl() {
        // 生成随机state，用于防止CSRF攻击
        String state = generateState();

        // 构建微信扫码登录页面URL
        HttpUrl url = HttpUrl.parse(WechatConstant.ApiUrl.QRCONNECT).newBuilder()
                .addQueryParameter(WechatConstant.ApiParam.APPID, wechatProperties.getAppId())
                .addQueryParameter(WechatConstant.ApiParam.REDIRECT_URI, wechatProperties.getRedirectUrl())
                .addQueryParameter(WechatConstant.ApiParam.RESPONSE_TYPE, WechatConstant.ResponseType.CODE)
                .addQueryParameter(WechatConstant.ApiParam.SCOPE, wechatProperties.getScope())
                .addQueryParameter(WechatConstant.ApiParam.STATE, state)
                .build();

        // 初始化该state的登录状态为未扫码
        setLoginStatus(state, WechatConstant.LoginStatus.NOT_SCAN, null);

        // 创建并返回响应对象
        return new WechatQrCodeRes(url.toString(), state);
    }

    @Override
    public JSONObject getAccessToken(String code, String state) {
        if (!validateState(state)) {
            log.warn("Invalid state parameter: {}", state);
            return null;
        }

        try {
            HttpUrl url = HttpUrl.parse(WechatConstant.ApiUrl.ACCESS_TOKEN).newBuilder()
                    .addQueryParameter(WechatConstant.ApiParam.APPID, wechatProperties.getAppId())
                    .addQueryParameter(WechatConstant.ApiParam.SECRET, wechatProperties.getAppSecret())
                    .addQueryParameter(WechatConstant.ApiParam.CODE, code)
                    .addQueryParameter(WechatConstant.ApiParam.GRANT_TYPE, WechatConstant.GrantType.AUTHORIZATION_CODE)
                    .build();

            Request request = new Request.Builder()
                    .url(url)
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to get access token, code: {}", response.code());
                    return null;
                }

                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);

                // 检查是否有错误码
                if (jsonObject.containsKey(WechatConstant.ApiResponse.ERRCODE)) {
                    log.error("Failed to get access token, errcode: {}, errmsg: {}",
                            jsonObject.getInteger(WechatConstant.ApiResponse.ERRCODE),
                            jsonObject.getString(WechatConstant.ApiResponse.ERRMSG));
                    return null;
                }

                return jsonObject;
            }
        } catch (IOException e) {
            log.error("Failed to get access token", e);
            return null;
        }
    }

    @Override
    public JSONObject getUserInfo(String accessToken, String openid) {
        try {
            HttpUrl url = HttpUrl.parse(WechatConstant.ApiUrl.USER_INFO).newBuilder()
                    .addQueryParameter(WechatConstant.ApiParam.ACCESS_TOKEN, accessToken)
                    .addQueryParameter(WechatConstant.ApiParam.OPENID, openid)
                    .addQueryParameter(WechatConstant.ApiParam.LANG, "zh_CN")
                    .build();

            Request request = new Request.Builder()
                    .url(url)
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to get user info, code: {}", response.code());
                    return null;
                }

                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);

                // 检查是否有错误码
                if (jsonObject.containsKey(WechatConstant.ApiResponse.ERRCODE)) {
                    log.error("Failed to get user info, errcode: {}, errmsg: {}",
                            jsonObject.getInteger(WechatConstant.ApiResponse.ERRCODE),
                            jsonObject.getString(WechatConstant.ApiResponse.ERRMSG));
                    return null;
                }

                return jsonObject;
            }
        } catch (IOException e) {
            log.error("Failed to get user info", e);
            return null;
        }
    }

    @Override
    public boolean validateState(String state) {
        if (StringUtils.isBlank(state)) {
            return false;
        }

        String cacheKey = wechatProperties.getRedisPrefix() + "state:" + state;
        RBucket<Integer> bucket = redissonClient.getBucket(cacheKey);

        return bucket.isExists();
    }

    @Override
    public int getLoginStatus(String state) {
        if (StringUtils.isBlank(state)) {
            return WechatConstant.LoginStatus.EXPIRED;
        }

        String statusKey = wechatProperties.getRedisPrefix() + "status:" + state;
        RBucket<Integer> bucket = redissonClient.getBucket(statusKey);

        if (!bucket.isExists()) {
            return WechatConstant.LoginStatus.EXPIRED;
        }

        Integer status = bucket.get();
        return status != null ? status : WechatConstant.LoginStatus.EXPIRED;
    }

    @Override
    public void setLoginStatus(String state, int status, JSONObject data) {
        if (StringUtils.isBlank(state)) {
            return;
        }
        // 保存状态
        String statusKey = wechatProperties.getRedisPrefix() + "status:" + state;
        RBucket<Integer> statusBucket = redissonClient.getBucket(statusKey);
        statusBucket.set(status, wechatProperties.getRedisExpire(), TimeUnit.SECONDS);

        // 保存state本身，用于验证
        String stateKey = wechatProperties.getRedisPrefix() + "state:" + state;
        RBucket<Integer> stateBucket = redissonClient.getBucket(stateKey);
        stateBucket.set(1, wechatProperties.getRedisExpire(), TimeUnit.SECONDS);

        // 如果有数据，保存数据
        if (data != null) {
            String dataKey = wechatProperties.getRedisPrefix() + "data:" + state;
            RBucket<String> dataBucket = redissonClient.getBucket(dataKey);
            dataBucket.set(data.toJSONString(), wechatProperties.getRedisExpire(), TimeUnit.SECONDS);
        }
    }

    @Override
    public JSONObject getLoginData(String state) {
        if (StringUtils.isBlank(state)) {
            return new JSONObject();
        }

        String dataKey = wechatProperties.getRedisPrefix() + "data:" + state;
        RBucket<String> bucket = redissonClient.getBucket(dataKey);

        if (!bucket.isExists()) {
            return new JSONObject();
        }

        String data = bucket.get();
        return StringUtils.isBlank(data) ? new JSONObject() : JSON.parseObject(data);
    }

    @Override
    public WechatLoginCheckRes checkLoginStatusAndAutoLogin(String state, HttpServletRequest request) {
        log.info("Checking WeChat login status for state: {}", state);

        // 创建响应对象
        WechatLoginCheckRes response = new WechatLoginCheckRes();

        // 获取登录状态
        int status = getLoginStatus(state);
        response.setWxStatus(status);
        response.setLoginSuccess(false); // 默认未登录成功
        response.setMobileBound(false); // 默认未绑定手机号

        // 如果已确认登录，尝试自动完成登录流程
        if (status == WechatConstant.LoginStatus.CONFIRMED) {
            JSONObject loginData = getLoginData(state);
            if (loginData != null && !loginData.isEmpty()) {
                // 检查是否已经登录成功，若已登录成功则直接返回登录信息
                if (loginData.containsKey("alreadyLoggedIn") && loginData.getBooleanValue("alreadyLoggedIn") &&
                        loginData.containsKey("userId") && loginData.containsKey("authorization")) {

                    response.setLoginSuccess(true);
                    response.setUserId(loginData.getString("userId"));
                    response.setNickname(loginData.getString("nickname"));
                    response.setAvatar(loginData.getString("avatar"));
                    response.setAuthorization(loginData.getString("authorization"));
                    response.setStatus(loginData.getIntValue("status"));

                    // 检查是否已绑定手机号
                    if (loginData.containsKey("isMobileBound")) {
                        response.setMobileBound(loginData.getBooleanValue("isMobileBound"));
                    }

                    log.info("Returning previously completed login information for state: {}", state);
                    return response;
                }

                try {
                    // 调用登录服务完成登录 - code参数在这里只是一个标识，实际loginByWechat会从Redis获取数据
                    WechatLoginRes loginRes = loginByWechat("CACHED", state, request);

                    // 登录成功，设置返回值
                    response.setLoginSuccess(true);
                    response.setUserId(loginRes.getUserId());
                    response.setNickname(loginRes.getNickname());
                    response.setAvatar(loginRes.getAvatar());
                    response.setAuthorization(loginRes.getToken());
                    response.setStatus(loginRes.getStatus());

                    // 查询用户是否已绑定手机号
                    AiUsersPo user = aiUsersMapper.selectOne(
                            new LambdaQueryWrapper<AiUsersPo>()
                                    .eq(AiUsersPo::getUserId, loginRes.getUserId())
                    );

                    boolean isMobileBound = user != null && StringUtils.isNotBlank(user.getPhone());
                    response.setMobileBound(isMobileBound);

                    // 在Redis中保存登录成功信息
                    loginData.put("alreadyLoggedIn", true);
                    loginData.put("userId", loginRes.getUserId());
                    loginData.put("nickname", loginRes.getNickname());
                    loginData.put("avatar", loginRes.getAvatar());
                    loginData.put("authorization", loginRes.getToken());
                    loginData.put("status", loginRes.getStatus());
                    loginData.put("isMobileBound", isMobileBound);

                    // 更新Redis中的数据
                    setLoginStatus(state, WechatConstant.LoginStatus.CONFIRMED, loginData);

                    log.info("Auto login completed for state: {}, isMobileBound: {}", state, isMobileBound);
                } catch (Exception e) {
                    log.error("Failed to auto-complete login for state: {}", state, e);
                    response.setLoginSuccess(false);
                    response.setErrorMsg("登录失败: " + e.getMessage());
                }
            }
        }

        return response;
    }

    private WechatLoginRes loginByWechat(String code, String state, HttpServletRequest request) {
        // 验证状态码，防止CSRF攻击
        if (!validateState(state)) {
            log.warn("Invalid state parameter for WeChat login: {}", state);
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.invalid.state"));
        }

        // 先从Redis中获取之前保存的登录数据
        JSONObject loginData = getLoginData(state);
        if (loginData == null || loginData.isEmpty()) {
            log.error("No login data found in Redis for state: {}", state);
            throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.data.missing"));
        }

        String accessToken;
        String openid;
        String unionid = null;
        JSONObject userInfo;

        // 优先使用Redis中已保存的Token和用户信
        if (loginData.containsKey("accessToken") && loginData.containsKey("openid") &&
                loginData.containsKey("wechatUserInfo")) {

            log.info("Using cached WeChat token and user info for state: {}", state);
            accessToken = loginData.getString("accessToken");
            openid = loginData.getString("openid");

            if (loginData.containsKey("unionid")) {
                unionid = loginData.getString("unionid");
            }

            userInfo = loginData.getJSONObject("wechatUserInfo");
        } else {
            // 兼容旧逻辑，如果Redis中没有保存完整信息，则调用微信API
            log.warn("Cached WeChat info not complete, calling WeChat API for state: {}", state);

            // 获取微信访问令牌
            JSONObject tokenInfo = getAccessToken(code, state);
            if (tokenInfo == null) {
                log.error("Failed to get access token from WeChat API with code: {}", code);
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.token.failed"));
            }

            // 从访问令牌响应中提取数据
            accessToken = tokenInfo.getString(WechatConstant.ApiResponse.ACCESS_TOKEN);
            openid = tokenInfo.getString(WechatConstant.ApiResponse.OPENID);
            unionid = tokenInfo.getString(WechatConstant.ApiResponse.UNIONID); // 可能为null

            // 使用访问令牌获取用户信息
            userInfo = getUserInfo(accessToken, openid);
            if (userInfo == null) {
                log.error("Failed to get user info from WeChat API with openid: {}", openid);
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.userinfo.failed"));
            }
        }

        // 提取用户信息
        String nickname = userInfo.getString(WechatConstant.ApiResponse.NICKNAME);
        String headimgurl = userInfo.getString(WechatConstant.ApiResponse.HEADIMGURL);

        // 将微信头像上传到OSS并获取OSS URL
        String ossAvatarUrl = uploadWechatAvatarToOss(headimgurl, openid);
        // 如果上传失败，使用原始微信头像URL
        String finalAvatarUrl = ossAvatarUrl.isEmpty() ? headimgurl : ossAvatarUrl;

        // 查询用户 - 优先使用unionid，unionid为空再使用openid
        AiUsersPo user = null;
        if (org.springframework.util.StringUtils.hasText(unionid)) {
            // 有unionid时，优先使用unionid查询
            log.info("Searching user by unionid: {}", unionid);
            LambdaQueryWrapper<AiUsersPo> unionidQuery = new LambdaQueryWrapper<>();
            unionidQuery.eq(AiUsersPo::getWechatUnionid, unionid);
            user = aiUsersMapper.selectOne(unionidQuery);
        }

        // 如果unionid为空或未查询到用户，则使用openid作为备
        if (user == null && org.springframework.util.StringUtils.hasText(openid)) {
            log.info("Searching user by openid: {}", openid);
            LambdaQueryWrapper<AiUsersPo> openidQuery = new LambdaQueryWrapper<>();
            openidQuery.eq(AiUsersPo::getWechatOpenid, openid);
            user = aiUsersMapper.selectOne(openidQuery);
        }

        boolean isNewUser = false;
        // 如果用户不存在，则注册新用户
        if (user == null) {
            user = new AiUsersPo();
            // 生成用户ID
            user.setUserId(IdUtil.getSnowflakeNextIdStr());
            user.setWechatOpenid(openid);
            user.setWechatUnionid(unionid);
            user.setWechatNickname(nickname);
            user.setWechatAvatar(finalAvatarUrl); // 保存原始微信头像URL
            user.setWechatBindTime(new Date());

            // 使用微信昵称作为默认昵称
            user.setNickname(nickname);
            user.setAvatar(finalAvatarUrl); // 使用OSS头像URL或原始微信头像URL
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setStatus(0); // 未激活状
            user.setPoints(0); // 初始积分
            user.setDelFlag(0);

            // 获取客户端IP
            String registerIp = getClientIpAddr(request);
            user.setRegisterIp(registerIp);

            int inserted = aiUsersMapper.insert(user);
            if (inserted <= 0) {
                log.error("Failed to insert new user for WeChat openid: {}", openid);
                throw new BizException(ErrorCodeEnum.USER_REGISTRATION_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_REGISTRATION_FAILED.getMsg()));
            }

            isNewUser = true;
            log.info("New user registered via WeChat: {}", nickname);
        } else if (user.getStatus() == 2) {
            // 用户账户已被禁用
            log.warn("Login attempt via WeChat for disabled user: {}", user.getUserId());
            throw new BizException(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getMsg()));
        } else {
            // 更新用户的微信相关信息（如有必要
            boolean needUpdate = false;
            AiUsersPo updateUser = new AiUsersPo();
            updateUser.setId(user.getId());

            // 检查并更新微信昵称
            if (!nickname.equals(user.getWechatNickname())) {
                updateUser.setWechatNickname(nickname);
                needUpdate = true;
            }

            // 检查并更新微信头像
            if (!headimgurl.equals(user.getWechatAvatar())) {
                updateUser.setWechatAvatar(headimgurl);
                needUpdate = true;
            }

            // 检查并更新用户头像（使用OSS URL
            if (!finalAvatarUrl.equals(user.getAvatar())) {
                updateUser.setAvatar(finalAvatarUrl);
                needUpdate = true;
            }

            // 检查并更新UnionID（如果之前为空且现在有值）
            if (unionid != null && !unionid.equals(user.getWechatUnionid())) {
                updateUser.setWechatUnionid(unionid);
                needUpdate = true;
            }

            if (needUpdate) {
                updateUser.setUpdateTime(new Date());
                aiUsersMapper.updateById(updateUser);
                log.info("Updated WeChat info for user: {}", user.getUserId());
            }
        }

        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUserId(), user.getEmail());

        // 更新最后登录时间和IP
        AiUsersPo updateUserLoginTime = new AiUsersPo();
        updateUserLoginTime.setId(user.getId());
        updateUserLoginTime.setLastLoginTime(new Date());
        updateUserLoginTime.setUpdateTime(new Date());
        String requestIp = getClientIpAddr(request);
        updateUserLoginTime.setLastLoginIp(requestIp);
        aiUsersMapper.updateById(updateUserLoginTime);

        // 构造响应对
        WechatLoginRes wechatLoginRes = new WechatLoginRes();
        wechatLoginRes.setUserId(user.getUserId());
        wechatLoginRes.setNickname(user.getNickname());
        wechatLoginRes.setAvatar(user.getAvatar());
        wechatLoginRes.setToken(token);
        wechatLoginRes.setIsNewUser(isNewUser);
        wechatLoginRes.setStatus(user.getStatus());

        log.info("User {} logged in via WeChat from IP {}", user.getUserId(), requestIp);
        return wechatLoginRes;
    }


    /**
     * 将微信头像上传到OSS
     *
     * @param headimgurl 微信头像URL
     * @param openid 用户的微信openid
     * @return OSS URL，上传失败则返回空字符串
     */
    private String uploadWechatAvatarToOss(String headimgurl, String openid) {
        if (headimgurl == null || headimgurl.isEmpty()) {
            return "";
        }

        try {
            // 构建OSS对象名称，使用openid作为唯一标识，避免重
            String fileName = "avatar_" + openid + "_" + System.currentTimeMillis() + ".png";
            String ossPath = OSS_PATH.replace("{env}", env)
                    .replace("{userId}", openid)
                    + fileName;
            // 上传文件到OSS
            String uploadResult = ossUtils.uploadFile(headimgurl, ossPath);
            if (org.springframework.util.StringUtils.hasText(uploadResult)) {
                return uploadResult;
            } else {
                log.warn("Failed to upload WeChat avatar to OSS for openid: {}", openid);
                return "";
            }
        } catch (Exception e) {
            log.error("Error uploading WeChat avatar to OSS: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取客户端真实IP地址，考虑反向代理情况
     * @param request HttpServletRequest 对象
     * @return 客户端IP地址
     */
    private String getClientIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 对于通过多个代理的情况，第一个IP为客户端真实IP，多个IP按照','分割
            if(ip.contains(",")){
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 对于 IPv6 本地回环地址 "0:0:0:0:0:0:0:1"，转换为 IPv4 "127.0.0.1"
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }

    /**
     * 生成用于防CSRF攻击的state参数
     */
    private String generateState() {
        String randomStr = RandomUtil.randomString(16);
        String timestamp = String.valueOf(System.currentTimeMillis());

        // 使用应用密钥、随机字符串和时间戳生成state
        return DigestUtil.md5Hex(wechatProperties.getStateKey() + randomStr + timestamp);
    }
} 