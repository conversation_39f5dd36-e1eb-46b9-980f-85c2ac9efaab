package com.wlink.agent.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.model.req.CanvasMaterialPageReq;
import com.wlink.agent.model.req.CanvasMaterialCreateReq;
import com.wlink.agent.model.req.ImageGenerateReq;
import com.wlink.agent.model.res.CanvasMaterialRes;
import com.wlink.agent.model.res.ImageGenerateRes;

/**
* <AUTHOR>
* @description 针对表【ai_canvas_material(画布素材表)】的数据库操作Service
* @createDate 2025-06-24 17:39:35
*/
public interface AiCanvasMaterialService extends IService<AiCanvasMaterialPo> {

    /**
     * 分页查询画布素材
     *
     * @param req 分页查询请求
     * @return 分页结果
     */
    Page<CanvasMaterialRes> pageQueryMaterials(CanvasMaterialPageReq req);

    /**
     * 新增画布素材
     *
     * @param req 新增素材请求
     * @return 新创建的素材ID
     */
    Long createMaterial(CanvasMaterialCreateReq req);


    /**
     * 生成图片
     *
     * @param req 图片生成请求
     * @return 图片生成响应
     */
    ImageGenerateRes generateImage(ImageGenerateReq req);

    /**
     * 删除画布素材
     *
     * @param materialId 素材ID
     */
    void deleteMaterial(Long materialId);
}
