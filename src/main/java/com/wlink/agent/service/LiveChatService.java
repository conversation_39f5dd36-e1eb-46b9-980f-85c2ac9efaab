//package com.wlink.agent.service;
//
//import com.alibaba.cola.dto.Response;
//import com.wlink.agent.model.req.AgentLiveExitReq;
//import com.wlink.agent.model.req.AgentLiveStartReq;
//import com.wlink.agent.model.req.LiveRoomMsgPushReq;
//
//public interface LiveChatService {
//
//    /**
//     * 开始直播
//     *
//     * @param roomId 房间ID
//     */
//    void startLive(String roomId,String pushStreamUrl);
//
//    /**
//     * 结束直播
//     *
//     * @param roomId 房间ID
//     */
//    void stopLive(String roomId);
//
//    /**
//     * 开始直播
//     *
//     * @param req 开始直播请求
//     */
//    void start(AgentLiveStartReq req);
//
//    void exit(AgentLiveExitReq req);
//
//    void pushLiveRoomMsg(LiveRoomMsgPushReq req);
//}