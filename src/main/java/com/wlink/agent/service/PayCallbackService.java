package com.wlink.agent.service;

import com.wlink.agent.dao.po.PayOrderPo;

/**
 * 支付回调服务接口
 */
public interface PayCallbackService {

    /**
     * 处理微信支付回调
     *
     * @param xmlData 微信支付回调数据
     * @return 处理结果（返回给微信的XML）
     */
    String handleWxPayCallback(String xmlData);
    
    /**
     * 主动查询订单支付状态
     * 
     * @param orderNo 商户订单号
     * @return 订单信息
     */
    PayOrderPo checkOrderStatus(String orderNo);
} 