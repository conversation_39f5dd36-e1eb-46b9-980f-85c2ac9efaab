package com.wlink.agent.service;

import com.wlink.agent.model.res.ImageStyleRes;
import com.wlink.agent.model.res.TagRes;

import java.util.List;

public interface ContentService {

    /**
     * 获取所有可用的图片风格列表
     * @return 图片风格DTO列表
     */
    List<ImageStyleRes> getAllActiveStyles();

    /**
     * 获取所有可用的标签列表
     * @return 标签列表
     */
    List<TagRes> getAllTags();



    void updateTagsAsync(String userId);
}