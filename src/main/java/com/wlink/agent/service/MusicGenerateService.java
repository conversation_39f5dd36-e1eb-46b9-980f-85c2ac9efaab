package com.wlink.agent.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.utils.OssUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;

@Slf4j
@Service
public class MusicGenerateService {

    private static final String API_URL = "https://api.minimax.chat/v1/music_generation";
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    @Value("${minimax.api-key:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}")
    private String apiKey;

    @Resource
    private OkHttpClient okHttpClient;
    
    @Resource
    private OssUtils ossUtils;

    /**
     * 生成音乐
     */
    public String generateMusic(String lyrics, String referVoice, String referInstrumental, String referVocal) {
        try {
            // 1. 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("refer_voice", referVoice);
            requestBody.put("refer_instrumental", referInstrumental);
            requestBody.put("refer_vocal", referVocal);
            requestBody.put("lyrics", lyrics);
            requestBody.put("model", "music-01");
            
            JSONObject audioSetting = new JSONObject();
            audioSetting.put("sample_rate", 44100);
            audioSetting.put("bitrate", 256000);
            audioSetting.put("format", "mp3");
            requestBody.put("audio_setting", audioSetting.toString());

            // 6. 上传音频到 OSS2. 发送请求
            Request request = new Request.Builder()
                    .url(API_URL)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .post(RequestBody.create(requestBody.toString(), JSON_TYPE))
                    .build();

            // 3. 处理响应
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful() || response.body() == null) {
                    log.error("Generate music failed, response: {}", response);
                    throw new RuntimeException("生成音乐失败");
                }

                String responseBody = response.body().string();
                log.info("Generate music response: {}", responseBody);

                // 4. 解析响应
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                String audioHex = jsonResponse.getJSONObject("data").getString("audio");

                // 5. 将音频保存为文件
                byte[] audioBytes = hexStringToByteArray(audioHex);
                String fileName = System.currentTimeMillis() + ".mp3";
                String audioPath = "music/" + fileName;
                
                // 确保目录存在
                File dir = new File("music");
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                
                // 写入文件
                File audioFile = new File(dir, fileName);
                try (FileOutputStream fos = new FileOutputStream(audioFile)) {
                    fos.write(audioBytes);
                    fos.flush();
                }
                
                log.info("音频文件已保存到: {}", audioFile.getAbsolutePath());
                return audioPath;
            }
        } catch (Exception e) {
            log.error("Generate music failed", e);
            throw new RuntimeException("生成音乐失败", e);
        }
    }

    /**
     * 将16进制字符串转换为字节数组
     */
    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
} 