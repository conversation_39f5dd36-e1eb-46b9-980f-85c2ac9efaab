package com.wlink.agent.service;

import com.wlink.agent.model.ImageModerationResult;
import com.wlink.agent.model.TextModerationResult;
import java.util.List;

/**
 * 内容安全检测服务接口
 * 提供基于阿里云内容安全的图片和文本检测功能
 * 
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */
public interface ContentModerationService {

    /**
     * 检测图片URL
     * 
     * @param imageUrl 图片URL地址
     * @return 检测结果，包含检测建议和详细信息
     */
    ImageModerationResult moderateImageUrl(String imageUrl);

    /**
     * 检测图片URL（指定检测服务）
     * 
     * @param imageUrl 图片URL地址
     * @param serviceType 检测服务类型（如baselineCheck、tonalityImprove等）
     * @return 检测结果
     */
    ImageModerationResult moderateImageUrl(String imageUrl, String serviceType);

    /**
     * 检测OSS文件
     * 
     * @param region OSS存储区域
     * @param bucket OSS存储桶名称
     * @param objectKey OSS对象键名
     * @return 检测结果
     */
    ImageModerationResult moderateOssFile(String region, String bucket, String objectKey);

    /**
     * 检测OSS文件（指定检测服务）
     * 
     * @param region OSS存储区域
     * @param bucket OSS存储桶名称
     * @param objectKey OSS对象键名
     * @param serviceType 检测服务类型
     * @return 检测结果
     */
    ImageModerationResult moderateOssFile(String region, String bucket, String objectKey, String serviceType);

    /**
     * 检测Base64编码的图片数据
     * 
     * @param base64Data Base64编码的图片数据
     * @return 检测结果
     */
    ImageModerationResult moderateBase64Image(String base64Data);

    /**
     * 批量检测图片URL
     * 
     * @param imageUrls 图片URL列表
     * @return 检测结果列表，顺序与输入保持一致
     */
    List<ImageModerationResult> batchModerateImageUrls(List<String> imageUrls);
    
    /**
     * 检测文本内容
     * 
     * @param content 需要检测的文本内容
     * @return 检测结果，包含检测建议和详细信息
     */
    TextModerationResult moderateText(String content);

    /**
     * 检测文本内容（指定检测服务）
     * 
     * @param content 需要检测的文本内容
     * @param serviceType 检测服务类型
     * @return 检测结果
     */
    TextModerationResult moderateText(String content, String serviceType);
    
    /**
     * 批量检测文本内容
     * 
     * @param contentList 文本内容列表
     * @return 检测结果列表，顺序与输入保持一致
     */
    List<TextModerationResult> batchModerateTexts(List<String> contentList);
} 