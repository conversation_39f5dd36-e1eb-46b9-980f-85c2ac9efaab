package com.wlink.agent.service;

import com.wlink.agent.enums.TransactionTypeEnum;

/**
 * 用户积分服务接口
 * 处理用户积分的扣除、返还和查询
 */
public interface UserPointsService {

    /**
     * 检查并扣除用户积分
     * @param userId 用户ID
     * @param points 扣除的积分数量
     * @param referenceId 关联ID (用于记录交易)
     * @param description 交易描述
     * @param transactionType 交易类型
     * @throws com.alibaba.cola.exception.BizException 如果积分不足或出现其他错误
     */
    void deductUserPoints(String userId, int points, String referenceId, String description, TransactionTypeEnum transactionType);

    /**
     * 检查并扣除用户积分（兼容旧版本，使用默认交易类型）
     * @param userId 用户ID
     * @param points 扣除的积分数量
     * @param referenceId 关联ID (用于记录交易)
     * @param description 交易描述
     * @throws com.alibaba.cola.exception.BizException 如果积分不足或出现其他错误
     * @deprecated 请使用带交易类型的方法
     */
    @Deprecated
    void deductUserPoints(String userId, int points, String referenceId, String description);

    /**
     * 返还用户积分
     * @param userId 用户ID
     * @param points 返还的积分数量
     * @param referenceId 关联ID (用于记录交易)
     * @param description 交易描述
     * @param transactionType 交易类型
     */
    void refundUserPoints(String userId, int points, String referenceId, String description, TransactionTypeEnum transactionType);

    /**
     * 返还用户积分（兼容旧版本，使用默认交易类型）
     * @param userId 用户ID
     * @param points 返还的积分数量
     * @param referenceId 关联ID (用于记录交易)
     * @param description 交易描述
     * @deprecated 请使用带交易类型的方法
     */
    @Deprecated
    void refundUserPoints(String userId, int points, String referenceId, String description);
    
    /**
     * 为视频生成失败返还积分
     * 根据视觉记录代码查找相关交易记录并返还积分
     * @param visualRecordCode 视觉记录代码
     */
    void refundPointsForFailedVideoGeneration(String visualRecordCode);
    
    /**
     * 检查用户积分是否足够
     * @param userId 用户ID
     * @param requiredPoints 所需积分
     * @return 积分是否足够
     */
    boolean hasEnoughPoints(String userId, int requiredPoints);
    
    /**
     * 支付成功后增加用户积分
     * @param userId 用户ID
     * @param orderNo 订单号
     * @param amount 支付金额（分）
     */
    void addPointsAfterPayment(String userId, String orderNo, Integer amount);

    /**
     * 退款后扣除用户积分
     * @param userId 用户ID
     * @param orderNo 订单号
     * @param amount 退款金额（分）
     */
    void deductPointsAfterRefund(String userId, String orderNo, Integer amount);
} 