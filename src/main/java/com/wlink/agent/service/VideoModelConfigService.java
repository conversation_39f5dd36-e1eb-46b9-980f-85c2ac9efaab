package com.wlink.agent.service;

import com.wlink.agent.model.req.VideoModelConfigQueryReq;
import com.wlink.agent.model.res.VideoModelConfigRes;

import java.util.List;

/**
 * 视频模型配置服务接口
 */
public interface VideoModelConfigService {

    /**
     * 查询所有启用的视频模型配置列表（级联查询）
     *
     * @return 视频模型配置列表
     */
    List<VideoModelConfigRes> listAllEnabledConfigs();

    /**
     * 根据条件查询视频模型配置列表（级联查询）
     *
     * @param queryReq 查询条件
     * @return 视频模型配置列表
     */
    List<VideoModelConfigRes> listConfigsByCondition(VideoModelConfigQueryReq queryReq);

    /**
     * 根据提供商查询视频模型配置列表（级联查询）
     *
     * @param provider 提供商
     * @return 视频模型配置列表
     */
    List<VideoModelConfigRes> listConfigsByProvider(String provider);

    /**
     * 根据模型类型查询视频模型配置列表（级联查询）
     *
     * @param modelType 模型类型
     * @return 视频模型配置列表
     */
    List<VideoModelConfigRes> listConfigsByModelType(String modelType);

    /**
     * 根据模型名称获取视频模型配置（级联查询）
     *
     * @param modelName 模型名称
     * @return 视频模型配置
     */
    VideoModelConfigRes getConfigByModelName(String modelName);

    /**
     * 根据提供商和模型类型查询视频模型配置列表（级联查询）
     *
     * @param provider 提供商
     * @param modelType 模型类型
     * @return 视频模型配置列表
     */
    List<VideoModelConfigRes> listConfigsByProviderAndModelType(String provider, String modelType);

    /**
     * 根据图片数量查询视频模型配置列表
     *
     * @param imageCount 图片数量(0-不支持图片,1-支持首帧,2-支持首尾帧)
     * @return 视频模型配置列表
     */
    List<VideoModelConfigRes> listConfigsByImageCount(Integer imageCount);
}
