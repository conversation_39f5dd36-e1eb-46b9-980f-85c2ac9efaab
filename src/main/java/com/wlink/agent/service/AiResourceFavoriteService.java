package com.wlink.agent.service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.dao.po.AiResourceFavoritePo;
import com.wlink.agent.model.req.BatchFavoriteReq;
import com.wlink.agent.model.req.BatchRemoveFavoriteReq;
import com.wlink.agent.model.req.ResourceFavoriteReq;
import com.wlink.agent.model.req.SimpleFavoriteReq;
import com.wlink.agent.model.res.ResourceFavoriteRes;

/**
 * 资源收藏服务接口
 */
public interface AiResourceFavoriteService extends IService<AiResourceFavoritePo> {

    /**
     * 添加资源收藏
     *
     * @param userId 用户ID
     * @param req    收藏请求
     * @return 操作结果
     */
    Response addFavorite(String userId, ResourceFavoriteReq req);

    /**
     * 移除资源收藏
     *
     * @param userId      用户ID
     * @param resourceCode 资源编码
     * @return 操作结果
     */
    Response removeFavorite(String userId, String resourceCode);

    /**
     * 查询用户收藏的所有资源列表
     *
     * @param userId 用户ID
     * @return 收藏资源列表
     */
    MultiResponse<ResourceFavoriteRes> listUserFavorites(String userId);

    /**
     * 查询用户收藏的指定类型资源列表
     *
     * @param userId       用户ID
     * @param resourceType 资源类型
     * @return 收藏资源列表
     */
    MultiResponse<ResourceFavoriteRes> listUserFavoritesByType(String userId, Integer resourceType);

    /**
     * 查询用户收藏的指定类型和子类型资源列表
     *
     * @param userId          用户ID
     * @param resourceType    资源类型
     * @param resourceSubtype 资源子类型
     * @return 收藏资源列表
     */
    MultiResponse<ResourceFavoriteRes> listUserFavoritesByTypeAndSubtype(String userId, Integer resourceType, Integer resourceSubtype);
    
    /**
     * 通过资源ID和类型添加收藏
     * 对于图片类型，会从AiImageTaskQueuePo表中查询详细信息
     *
     * @param userId 用户ID
     * @param req    简化的收藏请求
     * @return 操作结果
     */
    Response addFavoriteById(String userId, SimpleFavoriteReq req);
    
    /**
     * 批量添加资源收藏
     * 对于图片类型，会从AiImageTaskQueuePo表中查询详细信息
     *
     * @param userId 用户ID
     * @param req    批量收藏请求
     * @return 操作结果
     */
    Response batchAddFavoriteById(String userId, BatchFavoriteReq req);
    
    /**
     * 批量移除资源收藏
     *
     * @param userId 用户ID
     * @param req    批量移除请求
     * @return 操作结果
     */
    Response batchRemoveFavorite(String userId, BatchRemoveFavoriteReq req);
} 