//package com.wlink.agent.service;
//
//import com.wlink.agent.dao.po.AgentRenderingServerDispatchPo;
//
//public interface RenderingServerService {
//
//    /**
//     * 当客户端首次连接时，插入新的服务器记录。
//     *
//     * @param serverIp 服务器外网IP
//     * @param serverName 服务器名称
//     * @param serverInnerIp 服务器内网IP
//     * @param channelName 通道名称
//     */
//    Integer registerServer(String serverIp, String serverName, String serverInnerIp, String channelName, String username, String deviceName);
//
//    /**
//     * 释放服务器
//     * @param channelName
//     */
//    void releaseServer(String channelName, String userName);
//
//    /**
//     * 更新状态
//     * @param channelName
//     * @param runningStatus
//     */
//    void updateServerStatus(String channelName, String runningStatus, String detailStatus);
//
//    /**
//     * 更新分发状态
//     * @param dispatchStatus
//     * @param sessionId
//     */
//    void updateDispatchStatus(int dispatchStatus, String sessionId);
//
//    /**
//     * 根据请求调度一台在线且空闲的服务器。
//     *
//     * @param roomId 房间编号
//     * @param token token
//     * @return 调度sessionId
//     */
//    String dispatch(String roomId, Integer serverId, String token);
//
//    /**
//     * 根据sessionId获取channelName
//     * @param sessionId
//     * @return
//     */
//    String findChannelNameBySesisonId(String sessionId);
//
//    /**
//     * 根据sessionId获取服务器信息
//     * @param sessionId
//     * @return
//     */
//    AgentRenderingServerDispatchPo findBySessionId(String sessionId);
//
//    /**
//     * @param liveId
//     * @return
//     */
//    String findSessionIdByLiveId(String liveId);
//}
