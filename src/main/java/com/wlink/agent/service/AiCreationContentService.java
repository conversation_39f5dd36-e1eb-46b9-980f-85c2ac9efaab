package com.wlink.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.model.req.DesignSaveReq;
import com.wlink.agent.model.req.NarrationSaveReq;
import com.wlink.agent.model.req.RoleSaveReq;
import com.wlink.agent.model.req.SceneSaveReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.StorySaveReq;
import com.wlink.agent.model.req.VisualSaveReq;
import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.model.req.TaskProgressSaveReq;
import com.wlink.agent.model.res.ShotAudioUpdateRes;
import com.wlink.agent.model.res.ShotTaskStatusRes;
import com.wlink.agent.model.res.ShotTaskStatusGroupRes;
import com.wlink.agent.model.res.ShotTaskStatusGroupsRes;

import java.util.List;

/**
 * AI创作内容服务接口
 */
public interface AiCreationContentService extends IService<AiCreationContentPo> {

    /**
     * 保存故事内容
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    void saveStory(String conversationId, StorySaveReq storyData);

    /**
     * 更新故事内容
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据 (应包含用于定位记录的标识符，如ID)
     */
    void updateStory(String conversationId, StorySaveReq storyData);

    /**
     * 删除故事内容
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据 (通常只需要ID或其他唯一标识符)
     */
    void deleteStory(String conversationId, StorySaveReq storyData);

    /**
     * 保存场景内容
     *
     * @param conversationId 会话ID
     * @param sceneData      场景数据
     */
    void saveScene(String conversationId, SceneSaveReq sceneData);

    /**
     * 更新场景内容
     *
     * @param conversationId 会话ID
     * @param sceneData      场景数据
     */
    void updateScene(String conversationId, SceneSaveReq sceneData);

    /**
     * 删除场景内容
     *
     * @param conversationId 会话ID
     * @param sceneData      场景数据
     */
    void deleteScene(String conversationId, SceneSaveReq sceneData);

    /**
     * 保存角色内容
     *
     * @param conversationId 会话ID
     * @param roleData       角色数据
     */
    void saveRole(String conversationId, RoleSaveReq roleData);

    /**
     * 更新角色内容
     *
     * @param conversationId 会话ID
     * @param roleData       角色数据
     */
    void updateRole(String conversationId, RoleSaveReq roleData);

    /**
     * 删除角色内容
     *
     * @param conversationId 会话ID
     * @param roleData       角色数据
     */
    void deleteRole(String conversationId, RoleSaveReq roleData);

    /**
     * 保存分镜内容
     *
     * @param conversationId 会话ID
     * @param shotData       分镜数据
     */
    void saveShot(String conversationId, ShotSaveReq shotData);

    /**
     * 更新分镜内容
     *
     * @param conversationId 会话ID
     * @param shotData       分镜数据
     */
    void updateShot(String conversationId, ShotSaveReq shotData);

    /**
     * 删除分镜内容
     *
     * @param conversationId 会话ID
     * @param shotData       分镜数据
     */
    void deleteShot(String conversationId, ShotSaveReq shotData);

    /**
     * 保存视觉内容
     *
     * @param conversationId 会话ID
     * @param visualData     视觉数据
     */
    void saveVisual(String conversationId, VisualSaveReq visualData);

    /**
     * 根据会话ID和内容类型查询内容数据
     * @param conversationId 会话ID
     * @param contentType 内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉)
     * @return 内容数据
     */
    Object getContentData(String conversationId, Integer contentType);

    /**
     * 根据会话ID和内容类型查询内容数据
     * @param conversationId 会话ID
     * @param contentType 内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉)
     * @return 内容数据
     */
    Object getContentDataByDify(String conversationId, Integer contentType);

    /**
     * 保存设计内容
     * @param conversationId
     * @param shotData
     */
    void saveDesign(String conversationId, DesignSaveReq shotData);

    /**
     * 保存旁白内容
     * @param conversationId
     * @param narrationData
     */
    void saveNarration(String conversationId, List<NarrationSaveReq> narrationData);

    /**
     * 更新旁白内容
     *
     * @param conversationId 会话ID
     * @param narrationData  旁白数据
     */
    void updateNarration(String conversationId, List<NarrationSaveReq> narrationData);

    /**
     * 删除旁白内容
     *
     * @param conversationId 会话ID
     * @param narrationData  旁白数据 (通常只需要ID)
     */
    void deleteNarration(String conversationId, List<NarrationSaveReq> narrationData);

    /**
     * 生成TTS音频
     * @param req 请求参数
     * @return 生成结果
     */
    TtsGenerateRes generateTts(TtsGenerateReq req);

    /**
     * 保存任务进度
     *
     * @param progressReq 任务进度数据
     */
    void saveTaskProgress(String conversationId,TaskProgressSaveReq progressReq);

    /**
     * 查询最新的任务进度
     *
     * @param conversationId 会话ID
     * @return 最新的任务进度数据 (具体返回类型待定，可能是DTO或Map)
     */
    Object getLatestTaskProgress(String conversationId);
    
    /**
     * 更新会话内容
     *
     * @param conversationId 会话ID
     * @param contentType 内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉,10-故事设计,11-故事旁白)
     * @param contentData 内容数据
     * @return 如果是分镜(contentType=4)且内容变化，返回更新的音频信息；其他情况返回空列表
     */
    List<ShotAudioUpdateRes> updateConversationContent(String conversationId, Integer contentType, Object contentData);
    
    /**
     * 查询分镜任务状态
     *
     * @param conversationId 会话ID
     * @param contentId 内容ID，可为空，为空时查询所有分镜任务状态
     * @param contentIdType 内容ID类型(1-segment 2-scenes 3-shot)，当 contentId 为空时此参数无效
     * @return 分组的分镜任务状态
     */
    ShotTaskStatusGroupsRes getShotTaskStatus(String conversationId, String contentId, Integer contentIdType);
}