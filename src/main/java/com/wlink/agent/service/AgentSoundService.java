package com.wlink.agent.service;

import com.wlink.agent.model.req.DeleteSoundReq;
import com.wlink.agent.model.req.SessionSoundQueryReq;
import com.wlink.agent.model.req.SoundQueryReq;
import com.wlink.agent.model.req.UpdateSoundNameReq;
import com.wlink.agent.model.res.SoundRes;

import java.util.List;

public interface AgentSoundService {

    List<SoundRes> listSounds(SoundQueryReq req);

    /**
     * 修改定制声音名称
     *
     * @param req 修改请求
     */
    void updateSoundName(UpdateSoundNameReq req);

    /**
     * 删除定制声音
     *
     * @param req 删除请求
     */
    void deleteSound(DeleteSoundReq req);

    /**
     * 根据会话ID查询声音列表（包括系统声音和用户定制声音）
     *
     * @param req 查询请求
     * @return 声音列表
     */
    List<SoundRes> listSoundsBySession(SessionSoundQueryReq req);
}