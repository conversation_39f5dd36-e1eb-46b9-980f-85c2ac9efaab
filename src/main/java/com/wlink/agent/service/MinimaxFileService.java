package com.wlink.agent.service;

import com.wlink.agent.model.req.MinimaxFileUploadReq;
import com.wlink.agent.model.req.MinimaxVoiceCloneReq;
import com.wlink.agent.model.req.MinimaxVoiceCloneUploadReq;
import com.wlink.agent.model.req.VoiceCloneConfirmReq;
import com.wlink.agent.model.res.MinimaxFileUploadRes;
import com.wlink.agent.model.res.MinimaxVoiceCloneUploadRes;

public interface MinimaxFileService {
    /**
     * 上传文件到MiniMax
     * @param req 请求参数
     * @return 上传结果
     */
    MinimaxFileUploadRes uploadFile(MinimaxFileUploadReq req);
    
    /**
     * 上传语音克隆文件到MiniMax
     * @param req 请求参数
     * @return 上传结果
     */
    MinimaxVoiceCloneUploadRes uploadVoiceCloneFile(MinimaxVoiceCloneUploadReq req);
    
    /**
     * 执行语音克隆
     * @param req 请求参数
     * @return 语音克隆结果
     */
    MinimaxVoiceCloneUploadRes cloneVoice(MinimaxVoiceCloneReq req);

    /**
     * 确认语音克隆
     * @param req 确认请求参数
     */
    void confirmVoiceClone(VoiceCloneConfirmReq req);
}