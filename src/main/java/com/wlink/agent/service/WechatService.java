package com.wlink.agent.service;

import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.user.model.WechatLoginCheckRes;
import com.wlink.agent.user.model.WechatQrCodeRes;
import jakarta.servlet.http.HttpServletRequest;


import java.util.Map;

/**
 * 微信开放平台服务接口
 */
public interface WechatService {

    /**
     * 生成微信扫码登录二维码URL
     *
     * @return 包含二维码URL和state的响应对象
     */
    WechatQrCodeRes generateLoginQrCodeUrl();

    /**
     * 通过授权码获取访问令牌
     *
     * @param code 授权码
     * @param state 状态码，用于防CSRF攻击
     * @return 包含访问令牌、OpenID等信息的JSON对象
     */
    JSONObject getAccessToken(String code, String state);

    /**
     * 使用访问令牌获取用户信息
     *
     * @param accessToken 访问令牌
     * @param openid 用户的OpenID
     * @return 包含用户信息的JSON对象
     */
    JSONObject getUserInfo(String accessToken, String openid);

    /**
     * 验证state参数，防止CSRF攻击
     *
     * @param state 状态参数
     * @return 如果验证通过返回true，否则返回false
     */
    boolean validateState(String state);

    /**
     * 获取微信登录状态
     *
     * @param state 状态参数
     * @return 登录状态，对应WechatConstant.LoginStatus中的值
     */
    int getLoginStatus(String state);

    /**
     * 设置微信登录状态
     *
     * @param state 状态参数
     * @param status 登录状态
     * @param data 相关数据（如果有）
     */
    void setLoginStatus(String state, int status, JSONObject data);

    /**
     * 获取微信登录相关数据
     *
     * @param state 状态参数
     * @return 相关数据的JSON对象
     */
    JSONObject getLoginData(String state);
    
    /**
     * 检查微信登录状态并自动完成登录
     * 当状态为CONFIRMED时，将自动执行登录过程
     *
     * @param state 状态参数
     * @param request HTTP请求对象，用于获取IP等信息
     * @return 登录检查响应，包含状态和登录结果
     */
    WechatLoginCheckRes checkLoginStatusAndAutoLogin(String state, HttpServletRequest request);
} 