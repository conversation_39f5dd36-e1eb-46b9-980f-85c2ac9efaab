package com.wlink.agent.service;

import com.wlink.agent.model.req.CanvasBackgroundMusicReq;
import com.wlink.agent.model.res.CanvasBackgroundMusicRes;

/**
 * 画布背景音乐服务接口
 */
public interface CanvasBackgroundMusicService {
    
    /**
     * 设置画布背景音乐（新增或更新）
     *
     * @param req 背景音乐请求
     * @return 背景音乐ID
     */
    Long setCanvasBackgroundMusic(CanvasBackgroundMusicReq req);
    
    /**
     * 获取画布背景音乐
     *
     * @param canvasId 画布ID
     * @return 背景音乐信息
     */
    CanvasBackgroundMusicRes getCanvasBackgroundMusic(Long canvasId);
    
    /**
     * 删除画布背景音乐
     *
     * @param canvasId 画布ID
     */
    void removeCanvasBackgroundMusic(Long canvasId);
    
    /**
     * 更新画布背景音乐
     *
     * @param canvasId 画布ID
     * @param req 更新请求
     */
    void updateCanvasBackgroundMusic(Long canvasId, CanvasBackgroundMusicReq req);
}
