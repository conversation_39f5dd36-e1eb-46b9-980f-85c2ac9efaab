package com.wlink.agent.service;

import com.wlink.agent.model.dto.AudioMergeRequest;
import com.wlink.agent.model.dto.AudioMergeResult;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 音频合成服务接口
 */
public interface AudioMergeService {

    /**
     * 同步合成多个音频文件
     *
     * @param audioUrls 音频URL列表（按顺序）
     * @param outputFileName 输出文件名（不含扩展名）
     * @return 合成结果
     */
    AudioMergeResult mergeAudios(List<String> audioUrls, String outputFileName);

    /**
     * 异步合成多个音频文件
     *
     * @param request 音频合成请求
     * @return 异步合成结果
     */
    CompletableFuture<AudioMergeResult> mergeAudiosAsync(AudioMergeRequest request);

    /**
     * 检查音频合成任务状态
     *
     * @param taskId 任务ID
     * @return 合成结果，如果任务未完成返回null
     */
    AudioMergeResult getTaskResult(String taskId);

    /**
     * 取消音频合成任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelTask(String taskId);
}
