package com.wlink.agent.service;

import com.alibaba.cola.dto.PageResponse;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.model.req.CanvasUpdateReq;
import com.wlink.agent.model.req.ChapterToCanvasReq;
import com.wlink.agent.model.req.ShotOrderUpdateReq;
import com.wlink.agent.model.req.ShotOrderUpdateReq;
import com.wlink.agent.model.res.AiCanvasDetailRes;
import com.wlink.agent.model.res.AiCanvasRes;

/**
* <AUTHOR>
* @description 针对表【ai_canvas(画布表)】的数据库操作Service
* @createDate 2025-06-23 17:39:35
*/
public interface AiCanvasService extends IService<AiCanvasPo> {
    
    /**
     * 创建新画布（无需参数，默认命名为"未命名x"）
     * 
     * @param
     * @return 新创建的画布ID
     */
    Long createCanvas();
    
    /**
     * 更新画布信息（支持修改名称和封面）
     * 
     * @param req 更新请求
     * @return 是否更新成功
     */
    void updateCanvas(CanvasUpdateReq req);
    
    /**
     * 分页查询用户的画布列表
     *
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageResponse<AiCanvasRes> getCanvasList(int pageNum, int pageSize);
    
    /**
     * 获取画布详情（包含分镜信息）
     *
     * @param canvasId 画布ID
     * @return 画布详情
     */
    AiCanvasDetailRes getCanvasDetail(Long canvasId);

    /**
     * 修改分镜顺序
     *
     * @param req 分镜顺序更新请求
     */
    void updateShotOrder(ShotOrderUpdateReq req);
    
    /**
     * 将章节转换为画布
     *
     * @param req 章节转画布请求
     * @return 新创建的画布ID
     */
    Long convertChapterToCanvas(ChapterToCanvasReq req);

    /**
     * 删除画布
     *
     * @param canvasId 画布ID
     */
    void deleteCanvas(Long canvasId);

    /**
     * 根据会话ID和章节ID查询画布详情
     *
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @return 画布详情
     */
    AiCanvasDetailRes getCanvasBySessionAndSegment(String sessionId, String segmentId);
}
