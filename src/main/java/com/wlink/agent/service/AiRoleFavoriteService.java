package com.wlink.agent.service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.dao.po.AiRoleFavoritePo;
import com.wlink.agent.model.req.RoleFavoriteReq;
import com.wlink.agent.model.res.RoleFavoriteRes;

/**
 * 角色收藏服务接口
 */
public interface AiRoleFavoriteService extends IService<AiRoleFavoritePo> {

    /**
     * 添加角色收藏
     *
     * @param userId 用户ID
     * @param req    收藏请求
     * @return 操作结果
     */
    Response addFavorite(String userId, RoleFavoriteReq req);

    /**
     * 移除角色收藏
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 操作结果
     */
    Response removeFavorite(String userId, String roleId);

    /**
     * 查询用户收藏的角色列表
     *
     * @param userId 用户ID
     * @return 收藏角色列表
     */
    MultiResponse<RoleFavoriteRes> listUserFavorites(String userId);
} 