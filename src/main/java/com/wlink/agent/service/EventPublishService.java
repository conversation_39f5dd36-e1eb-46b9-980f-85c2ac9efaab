package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.event.CanvasGenerateCompletedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 事件发布服务
 * 使用Java 17特性优化事件发布
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventPublishService {
    
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布画布生成完成事件
     * 使用Java 17的switch表达式和模式匹配
     */
    public void publishCanvasGenerateEvent(AiImageTaskQueuePo task) {
        if (task == null) {
            log.warn("任务对象为空，无法发布事件");
            return;
        }
        
        // 使用Java 17的switch表达式根据任务状态创建不同类型的事件
        var event = switch (task.getTaskStatus()) {
            case "COMPLETED" -> new CanvasGenerateCompletedEvent(
                    this, 
                    task, 
                    "CANVAS_GENERATE_COMPLETED", 
                    "画布生成任务成功完成"
            );
            case "FAILED" -> new CanvasGenerateCompletedEvent(
                    this, 
                    task, 
                    "CANVAS_GENERATE_FAILED", 
                    "画布生成任务执行失败"
            );
            case "PROCESSING" -> new CanvasGenerateCompletedEvent(
                    this, 
                    task, 
                    "CANVAS_GENERATE_PROCESSING", 
                    "画布生成任务正在处理中"
            );
            default -> {
                log.warn("未知的任务状态: {}", task.getTaskStatus());
                yield new CanvasGenerateCompletedEvent(
                        this, 
                        task, 
                        "CANVAS_GENERATE_UNKNOWN", 
                        "画布生成任务状态未知"
                );
            }
        };
        
        // 发布事件
        try {
            eventPublisher.publishEvent(event);
            
            // 使用Java 17的文本块记录日志
            var logMessage = """
                    成功发布画布生成事件:
                    - 任务ID: %s
                    - 事件类型: %s
                    - 任务状态: %s
                    - 用户ID: %s
                    """.formatted(
                    task.getId(),
                    event.getEventType(),
                    task.getTaskStatus(),
                    task.getUserId()
            );
            
            log.info(logMessage);
            
        } catch (Exception e) {
            log.error("发布画布生成事件失败 - 任务ID: {}", task.getId(), e);
        }
    }
    
    /**
     * 批量发布事件
     * 使用Java 17的Stream API增强
     */
    public void publishCanvasGenerateEvents(java.util.List<AiImageTaskQueuePo> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            log.warn("任务列表为空，无法批量发布事件");
            return;
        }
        
        // 使用Java 17的并行流和方法引用
        var publishedCount = tasks.parallelStream()
                .filter(task -> task != null && task.getId() != null)
                .peek(this::publishCanvasGenerateEvent)
                .count();
        
        log.info("批量发布画布生成事件完成，共发布 {} 个事件", publishedCount);
    }
    
    /**
     * 发布自定义事件
     * 使用Java 17的sealed类特性（如果需要扩展事件类型）
     */
    public void publishCustomCanvasEvent(AiImageTaskQueuePo task, String eventType, String message) {
        if (task == null) {
            log.warn("任务对象为空，无法发布自定义事件");
            return;
        }
        
        var event = new CanvasGenerateCompletedEvent(this, task, eventType, message);
        
        try {
            eventPublisher.publishEvent(event);
            log.info("成功发布自定义画布事件 - 类型: {}, 任务ID: {}", eventType, task.getId());
        } catch (Exception e) {
            log.error("发布自定义画布事件失败 - 类型: {}, 任务ID: {}", eventType, task.getId(), e);
        }
    }
}
