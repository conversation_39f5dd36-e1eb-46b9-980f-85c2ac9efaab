package com.wlink.agent.service;

import com.wlink.agent.model.dto.QueueStatus;
import com.wlink.agent.model.dto.SoundEffectsRequest;
import com.wlink.agent.model.dto.SoundEffectsResult;
import com.wlink.agent.model.dto.StableAudioResult;
import com.wlink.agent.model.dto.StableAudioResult;

import java.util.concurrent.CompletableFuture;

/**
 * 音效生成服务接口
 */
public interface SoundEffectsService {
    
    /**
     * 提交音效生成请求
     *
     * @param request 音效生成请求
     * @return 队列状态
     */
    CompletableFuture<QueueStatus> submitRequest(SoundEffectsRequest request);
    
    /**
     * 查询请求状态
     *
     * @param requestId 请求ID
     * @return 队列状态
     */
    CompletableFuture<QueueStatus> getStatus(String requestId);
    
    /**
     * 获取生成结果
     *
     * @param requestId 请求ID
     * @return 音效生成结果
     */
    CompletableFuture<SoundEffectsResult> getResult(String requestId);
    
    /**
     * 取消请求
     *
     * @param requestId 请求ID
     * @return 是否取消成功
     */
    CompletableFuture<Boolean> cancelRequest(String requestId);
    
    /**
     * 一键生成音效（提交请求并等待完成）
     *
     * @param prompt 音效描述
     * @param duration 时长（秒）
     * @param conversationId 会话ID
     * @return 音效生成结果
     */
    CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt, int duration, String conversationId);

    /**
     * 一键生成音效（提交请求并等待完成）
     *
     * @param prompt 音效描述
     * @param duration 时长（秒）
     * @param conversationId 会话ID
     * @param contentId 内容ID
     * @param index 音频索引
     * @return 音效生成结果
     */
    CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt, int duration, String conversationId, String contentId, Integer index);


    /**
     * 一键生成音效（提交请求并等待完成）
     *
     * @param prompt 音效描述
     * @param duration 时长（秒）
     * @return 音效生成结果
     */
    CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt, int duration);


    /**
     * 一键生成音效（默认30秒）
     *
     * @param prompt 音效描述
     * @return 音效生成结果
     */
    CompletableFuture<SoundEffectsResult> generateSoundEffect(String prompt);

    /**
     * 为画布生成音效（同步方法）
     *
     * @param prompt 音效描述
     * @param duration 时长（秒）
     * @param conversationId 会话ID（画布code）
     * @param contentId 内容ID（分镜code）
     * @param index 音频索引
     * @return 音效生成结果
     */
    SoundEffectsResult generateSoundEffectForCanvas(String prompt, int duration, String conversationId, String contentId, Integer index);


    /**
     * 使用 Stable Audio 生成高质量音频（同步方法）
     *
     * @param prompt 音频生成提示词
     * @param duration 音频时长（秒，最多47秒）
     * @param conversationId 会话ID（画布code）
     * @param contentId 内容ID（分镜code）
     * @param index 音频索引
     * @return Stable Audio 生成结果
     */
    StableAudioResult generateStableAudio(String prompt, int duration, String conversationId, String contentId, Integer index);

    /**
     * 使用 Stable Audio 异步生成高质量音频
     *
     * @param prompt 音频生成提示词
     * @param duration 音频时长（秒，最多47秒）
     * @return Stable Audio 生成结果的 CompletableFuture
     */
    CompletableFuture<StableAudioResult> generateStableAudioAsync(String prompt, int duration);

    /**
     * 使用 Stable Audio 异步生成高质量音频（完整参数版本）
     *
     * @param prompt 音频生成提示词
     * @param duration 音频时长（秒，最多47秒）
     * @param conversationId 会话ID（画布code）
     * @param contentId 内容ID（分镜code）
     * @param index 音频索引
     * @return Stable Audio 生成结果的 CompletableFuture
     */
    CompletableFuture<StableAudioResult> generateStableAudioAsync(String prompt, int duration, String conversationId, String contentId, Integer index);
}
