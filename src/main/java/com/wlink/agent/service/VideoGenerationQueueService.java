package com.wlink.agent.service;

import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.model.req.VideoGenerationReq;
import com.wlink.agent.model.res.VideoGenerationRes;

import java.util.List;

/**
 * 视频生成队列服务接口
 */
public interface VideoGenerationQueueService {
    
    /**
     * 提交视频生成任务
     * 
     * @param req 视频生成请求
     * @return 生成记录ID
     */
    Long submitVideoGeneration(VideoGenerationReq req);
    
    /**
     * 获取下一个待处理的任务
     * 
     * @return 待处理的任务，如果没有则返回null
     */
    AiVideoGenerationPo getNextTask();
    
    /**
     * 开始处理任务
     *
     * @param taskId 任务ID
     */
    void startProcessing(Long taskId,String generateTaskId);
    
    /**
     * 完成任务处理
     * 
     * @param taskId 任务ID
     * @param videoUrl 生成的视频URL
     */
    void completeTask(Long taskId, String videoUrl);
    
    /**
     * 任务处理失败
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    void failTask(Long taskId, String errorMessage);
    
    /**
     * 获取当前队列状态
     * 
     * @return 队列状态信息
     */
    QueueStatus getQueueStatus();
    
    /**
     * 获取用户的视频生成记录
     *
     * @param userId 用户ID
     * @return 视频生成记录列表
     */
    List<VideoGenerationRes> getUserVideoGenerations(String userId);

    /**
     * 根据任务ID获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    AiVideoGenerationPo getTaskById(Long taskId);
    
    /**
     * 根据外部任务ID查找内部任务
     *
     * @param externalTaskId 外部任务ID
     * @return 内部任务详情，如果不存在则返回null
     */
    AiVideoGenerationPo findTaskByExternalId(String externalTaskId);
    
    /**
     * 异步更新ai_video_generation_record表
     * 
     * @param externalTaskId 外部任务ID
     * @param status 任务状态
     * @param videoUrl 视频URL，成功时必须提供
     * @param errorMessage 错误信息，失败时提供
     * @param completionTokens 完成token数
     * @param totalTokens 总token数
     */
    void asyncUpdateVideoGenerationRecord(String externalTaskId, String status, String videoUrl, 
                                         String errorMessage, Integer completionTokens, Integer totalTokens);
    
    /**
     * 队列状态信息
     */
    record QueueStatus(int queuedCount, int processingCount, int maxConcurrent) {
        
        /**
         * 是否可以开始新的处理
         */
        public boolean canStartNewProcessing() {
            return processingCount < maxConcurrent;
        }
        
        /**
         * 获取状态描述
         */
        public String getStatusDescription() {
            return "队列中: %d, 处理中: %d, 最大并发: %d".formatted(queuedCount, processingCount, maxConcurrent);
        }
    }
}
