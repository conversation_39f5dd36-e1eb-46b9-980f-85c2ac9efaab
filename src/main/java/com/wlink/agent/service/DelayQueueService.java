package com.wlink.agent.service;

import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.model.DelayTask;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;


import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DelayQueueService {

    
    @Resource
    private RedissonClient redissonClient;


    /**
     * 添加延时任务
     */
    public void DelayQueueService(DelayTask task, long delay) {
        try {
            // 获取延迟队列
            RBlockingDeque<DelayTask> blockingDeque = redissonClient.getBlockingDeque(RedisKeyConstant.LiveRoom.DANMAKU_REPLY_QUEUE);
            RDelayedQueue<DelayTask> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
            
            // 添加延迟任务
            delayedQueue.offer(task, delay, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("Failed to add delay task", e);
        }
    }

    /**
     * 在Spring容器销毁前执行，关闭延迟队列
     * 防止内存泄漏
     */
    @PreDestroy
    public void destroy() {
        try {
            log.info("Destroying delayed queue...");
            RBlockingDeque<DelayTask> blockingDeque = redissonClient.getBlockingDeque(RedisKeyConstant.LiveRoom.DANMAKU_REPLY_QUEUE);
            RDelayedQueue<DelayTask> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
            delayedQueue.destroy();
            log.info("Delayed queue destroyed successfully");
        } catch (Exception e) {
            log.error("Error destroying delayed queue", e);
        }
    }
} 