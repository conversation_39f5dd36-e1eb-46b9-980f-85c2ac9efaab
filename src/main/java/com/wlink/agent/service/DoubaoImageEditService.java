package com.wlink.agent.service;

import com.wlink.agent.model.req.DoubaoImageEditReq;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;

/**
 * 豆包图像编辑服务接口
 */
public interface DoubaoImageEditService {

    /**
     * 编辑图像
     *
     * @param req 编辑请求
     * @return 编辑结果
     */
    ImageGenerateRes editImage(DoubaoImageEditReq req);

    /**
     * 使用指定API密钥编辑图像
     *
     * @param req 编辑请求
     * @param apiKey API密钥
     * @return 编辑结果
     */
    ImageGenerateRes editImageWithApiKey(DoubaoImageEditReq req, String apiKey);
}
