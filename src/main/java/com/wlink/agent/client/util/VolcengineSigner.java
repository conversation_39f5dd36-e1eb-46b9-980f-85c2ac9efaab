package com.wlink.agent.client.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.Charset; // Added
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
// import java.util.HexFormat; // Removed as it might not be available
import java.util.*;
import java.util.stream.Collectors;
@Component
public class VolcengineSigner {

    private static final Charset UTF_8 = StandardCharsets.UTF_8;
    private static final BitSet URLENCODER_SAFE_CHARS = new BitSet(256);

    static {
        for (int i = 'a'; i <= 'z'; i++) {
            URLENCODER_SAFE_CHARS.set(i);
        }
        for (int i = 'A'; i <= 'Z'; i++) {
            URLENCODER_SAFE_CHARS.set(i);
        }
        for (int i = '0'; i <= '9'; i++) {
            URLENCODER_SAFE_CHARS.set(i);
        }
        URLENCODER_SAFE_CHARS.set('-');
        URLENCODER_SAFE_CHARS.set('_');
        URLENCODER_SAFE_CHARS.set('.');
        URLENCODER_SAFE_CHARS.set('~');
    }

    @Value("${volcengine.api.accessKeyId:AKLTZGY2MjFjYWNkZWQ4NDcxZTliMGEzY2E0YjA3ZWEzNWM}")
    private String accessKeyId;
    @Value("${volcengine.api.secretAccessKey:TVRZME5UWTVNRFEyTlRsbE5EUmlZMkkwTjJabFpqVTROMlkyWldRek1ESQ==}")
    private String secretAccessKey;


    public Map<String, String> signHeaders(String method, String path,
                                           Map<String, String> queryParams,
                                           Map<String, String> headers, // Input headers, e.g., Content-Type
                                           byte[] body,String region, String service) throws Exception {
        if (body == null) {
            body = new byte[0];
        }

        Date date = new Date();
        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdfDate.setTimeZone(TimeZone.getTimeZone("UTC"));
        String xDate = sdfDate.format(date);
        String shortDate = xDate.substring(0, 8);

        String xContentSha256 = hashSHA256(body);

        // Ensure essential headers are present or add them
        Map<String, String> allHeaders = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        if (headers != null) {
            allHeaders.putAll(headers);
        }
        allHeaders.put("X-Date", xDate);
        allHeaders.put("X-Content-Sha256", xContentSha256);
        // Host header will be added by OkHttp, but we need it for signing
        // It should be passed in the 'headers' map if not using default OkHttp behavior or if it's a non-standard port
        // For this API, host is visual.volcengineapi.com

        // Canonical Query String
        String canonicalQueryString = "";
        if (queryParams != null && !queryParams.isEmpty()) {
            canonicalQueryString = queryParams.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> encodeURIComponent(entry.getKey()) + "=" + encodeURIComponent(entry.getValue()))
                    .collect(Collectors.joining("&"));
        }

        // Canonical Headers
        // SignedHeaders must include host and x-date.
        // If Content-Type is present, it should also be included.
        // For POST with JSON body, Content-Type is application/json
        List<String> signedHeaderNames = new ArrayList<>();
        signedHeaderNames.add("host"); // OkHttp adds this, ensure it's in allHeaders for signing
        signedHeaderNames.add("x-date");
        signedHeaderNames.add("x-content-sha256");
        if (allHeaders.containsKey("Content-Type")) {
            signedHeaderNames.add("content-type");
        }
        // Add other headers that should be signed if any, ensure they are in allHeaders
        // Sort them alphabetically for canonical request
        signedHeaderNames.sort(String::compareToIgnoreCase);


        StringBuilder canonicalHeadersString = new StringBuilder();
        for (String headerName : signedHeaderNames) {
            canonicalHeadersString.append(headerName.toLowerCase(Locale.US)).append(":")
                                  .append(allHeaders.get(headerName) != null ? allHeaders.get(headerName).trim() : "")
                                  .append("\n");
        }

        String signedHeadersString = signedHeaderNames.stream()
                                                     .map(s -> s.toLowerCase(Locale.US))
                                                     .collect(Collectors.joining(";"));

        // Canonical Request
        String canonicalRequest = method.toUpperCase(Locale.US) + "\n" +
                                  (path == null || path.isEmpty() ? "/" : path) + "\n" +
                                  canonicalQueryString + "\n" +
                                  canonicalHeadersString.toString() + "\n" + // Includes trailing newline
                                  signedHeadersString + "\n" +
                                  xContentSha256;

        // String to Sign
        String hashedCanonicalRequest = hashSHA256(canonicalRequest.getBytes(UTF_8));
        String credentialScope = shortDate + "/" + region + "/" + service + "/request";
        String stringToSign = "HMAC-SHA256" + "\n" +
                              xDate + "\n" +
                              credentialScope + "\n" +
                              hashedCanonicalRequest;

        // Signature
        byte[] signingKey = genSigningSecretKeyV4(secretAccessKey, shortDate, region, service);
        String signature = bytesToHex(hmacSHA256(signingKey, stringToSign));

        // Authorization Header
        String authorizationHeader = "HMAC-SHA256 Credential=" + accessKeyId + "/" + credentialScope +
                                     ", SignedHeaders=" + signedHeadersString +
                                     ", Signature=" + signature;

        Map<String, String> resultHeaders = new HashMap<>();
        resultHeaders.put("Authorization", authorizationHeader);
        resultHeaders.put("X-Date", xDate);
        resultHeaders.put("X-Content-Sha256", xContentSha256);
        // The caller should add Content-Type if it's a POST request with body
        // resultHeaders.put("Content-Type", "application/json; charset=utf-8"); // if applicable

        return resultHeaders;
    }

    /**
     * 使用指定的Access Key和Secret Key签名请求
     *
     * @param method HTTP方法
     * @param path API路径
     * @param queryParams 查询参数
     * @param headers 请求
     * @param body 请求
     * @param region 区域
     * @param service 服务
     * @param customAccessKey 自定义Access Key
     * @param customSecretKey 自定义Secret Key
     * @return 签名后的请求
     * @throws Exception
     */
    public Map<String, String> signHeadersWithCredentials(String method, String path,
                                           Map<String, String> queryParams,
                                           Map<String, String> headers,
                                           byte[] body, String region, String service,
                                           String customAccessKey, String customSecretKey) throws Exception {
        if (body == null) {
            body = new byte[0];
        }

        Date date = new Date();
        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdfDate.setTimeZone(TimeZone.getTimeZone("UTC"));
        String xDate = sdfDate.format(date);
        String shortDate = xDate.substring(0, 8);

        String xContentSha256 = hashSHA256(body);

        // Ensure essential headers are present or add them
        Map<String, String> allHeaders = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        if (headers != null) {
            allHeaders.putAll(headers);
        }
        allHeaders.put("X-Date", xDate);
        allHeaders.put("X-Content-Sha256", xContentSha256);

        // Canonical Query String
        String canonicalQueryString = "";
        if (queryParams != null && !queryParams.isEmpty()) {
            canonicalQueryString = queryParams.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> encodeURIComponent(entry.getKey()) + "=" + encodeURIComponent(entry.getValue()))
                    .collect(Collectors.joining("&"));
        }

        // Canonical Headers
        List<String> signedHeaderNames = new ArrayList<>();
        signedHeaderNames.add("host");
        signedHeaderNames.add("x-date");
        signedHeaderNames.add("x-content-sha256");
        if (allHeaders.containsKey("Content-Type")) {
            signedHeaderNames.add("content-type");
        }
        signedHeaderNames.sort(String::compareToIgnoreCase);

        StringBuilder canonicalHeadersString = new StringBuilder();
        for (String headerName : signedHeaderNames) {
            canonicalHeadersString.append(headerName.toLowerCase(Locale.US)).append(":")
                                  .append(allHeaders.get(headerName) != null ? allHeaders.get(headerName).trim() : "")
                                  .append("\n");
        }

        String signedHeadersString = signedHeaderNames.stream()
                                                     .map(s -> s.toLowerCase(Locale.US))
                                                     .collect(Collectors.joining(";"));

        // Canonical Request
        String canonicalRequest = method.toUpperCase(Locale.US) + "\n" +
                                  (path == null || path.isEmpty() ? "/" : path) + "\n" +
                                  canonicalQueryString + "\n" +
                                  canonicalHeadersString.toString() + "\n" +
                                  signedHeadersString + "\n" +
                                  xContentSha256;

        // String to Sign
        String hashedCanonicalRequest = hashSHA256(canonicalRequest.getBytes(UTF_8));
        String credentialScope = shortDate + "/" + region + "/" + service + "/request";
        String stringToSign = "HMAC-SHA256" + "\n" +
                              xDate + "\n" +
                              credentialScope + "\n" +
                              hashedCanonicalRequest;

        // Signature 使用自定义Secret Key
        byte[] signingKey = genSigningSecretKeyV4(customSecretKey, shortDate, region, service);
        String signature = bytesToHex(hmacSHA256(signingKey, stringToSign));

        // Authorization Header 使用自定义Access Key
        String authorizationHeader = "HMAC-SHA256 Credential=" + customAccessKey + "/" + credentialScope +
                                     ", SignedHeaders=" + signedHeadersString +
                                     ", Signature=" + signature;

        Map<String, String> resultHeaders = new HashMap<>();
        resultHeaders.put("Authorization", authorizationHeader);
        resultHeaders.put("X-Date", xDate);
        resultHeaders.put("X-Content-Sha256", xContentSha256);

        return resultHeaders;
    }

    private static String hashSHA256(byte[] content) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        return bytesToHex(md.digest(content));
    }

    private static byte[] hmacSHA256(byte[] key, String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(key, "HmacSHA256"));
        return mac.doFinal(data.getBytes(UTF_8));
    }

    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(UTF_8), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }

    // This is a more standard URI component encoder.
    // The one in the example Sign.java is specific and might not cover all cases as per RFC 3986.
    // For query parameters, URLEncoder.encode(s, UTF_8) is generally safe.
    // However, the provided example uses a custom encoder, so we try to mimic its safe characters.
    private String encodeURIComponent(String s) {
        if (s == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        byte[] bytes = s.getBytes(UTF_8);
        for (byte b : bytes) {
            int val = b & 0xFF;
            if (URLENCODER_SAFE_CHARS.get(val)) {
                sb.append((char) val);
            } else if (val == ' ') { // Special case for space, though RFC3986 prefers %20
                 sb.append("%20");
            }
            else {
                sb.append('%');
                sb.append(String.format("%02X", val));
            }
        }
        return sb.toString();
    }

    // Helper to convert byte array to hex string (Java < 17 compatible)
    private static final char[] HEX_ARRAY = "0123456789abcdef".toCharArray();
    private static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }
}
