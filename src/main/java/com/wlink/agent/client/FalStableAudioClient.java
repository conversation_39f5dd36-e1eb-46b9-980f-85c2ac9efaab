package com.wlink.agent.client;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.model.dto.QueueStatus;
import com.wlink.agent.model.dto.StableAudioRequest;
import com.wlink.agent.model.dto.StableAudioResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * FAL Stable Audio 客户端
 * 基于 fal-ai/stable-audio API
 * 使用 JDK 17 新特性
 */
@Slf4j
@Component
public class FalStableAudioClient {
    
    private static final String BASE_URL = "https://queue.fal.run";
    private static final String ENDPOINT = "/fal-ai/stable-audio";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    
    private final OkHttpClient httpClient;
    private final String apiKey;
    
    public FalStableAudioClient(@Value("${fal.api.key:63e02ace-5988-4df0-9492-e6b7258cc5b6:fa9a4ee526b783d30a926a9314b1aba7}") String apiKey) {
        this.apiKey = apiKey;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(30))
                .readTimeout(Duration.ofMinutes(5))
                .writeTimeout(Duration.ofSeconds(30))
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 提交 Stable Audio 生成请求
     *
     * @param request Stable Audio 生成请求
     * @return 队列状态
     */
    public CompletableFuture<QueueStatus> submitRequest(StableAudioRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String requestJson = JSON.toJSONString(request);
                log.info("提交 Stable Audio 生成请求: {}", requestJson);
                
                RequestBody body = RequestBody.create(requestJson, JSON_MEDIA_TYPE);
                Request httpRequest = new Request.Builder()
                        .url(BASE_URL + ENDPOINT)
                        .post(body)
                        .addHeader("Authorization", "Key " + apiKey)
                        .addHeader("Content-Type", "application/json")
                        .build();
                
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("Stable Audio 生成请求响应: {}", responseBody);
                    
                    QueueStatus queueStatus = JSON.parseObject(responseBody, QueueStatus.class);
                    log.info("Stable Audio 生成请求提交成功: requestId={}, status={}", 
                            queueStatus.requestId(), queueStatus.status());
                    
                    return queueStatus;
                }
            } catch (IOException e) {
                log.error("提交 Stable Audio 生成请求失败", e);
                throw new RuntimeException("提交 Stable Audio 生成请求失败", e);
            }
        });
    }
    
    /**
     * 查询请求状态
     *
     * @param requestId 请求ID
     * @param includeLogs 是否包含日志
     * @return 队列状态
     */
    public CompletableFuture<QueueStatus> getStatus(String requestId, boolean includeLogs) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                HttpUrl.Builder urlBuilder = HttpUrl.parse(BASE_URL + ENDPOINT + "/requests/" + requestId + "/status")
                        .newBuilder();
                
                if (includeLogs) {
                    urlBuilder.addQueryParameter("logs", "1");
                }
                
                Request request = new Request.Builder()
                        .url(urlBuilder.build())
                        .get()
                        .addHeader("Authorization", "Key " + apiKey)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("查询状态响应: requestId={}, response={}", requestId, responseBody);
                    
                    return JSON.parseObject(responseBody, QueueStatus.class);
                }
            } catch (IOException e) {
                log.error("查询请求状态失败: requestId={}", requestId, e);
                throw new RuntimeException("查询请求状态失败", e);
            }
        });
    }
    
    /**
     * 获取生成结果
     *
     * @param requestId 请求ID
     * @return Stable Audio 生成结果
     */
    public CompletableFuture<StableAudioResult> getResult(String requestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Request request = new Request.Builder()
                        .url(BASE_URL + ENDPOINT + "/requests/" + requestId)
                        .get()
                        .addHeader("Authorization", "Key " + apiKey)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("获取结果响应: requestId={}, response={}", requestId, responseBody);
                    
                    StableAudioResult result = JSON.parseObject(responseBody, StableAudioResult.class);
                    log.info("获取 Stable Audio 生成结果成功: requestId={}, audioUrl={}", 
                            requestId, result.getAudioUrl());
                    
                    return result;
                }
            } catch (IOException e) {
                log.error("获取生成结果失败: requestId={}", requestId, e);
                throw new RuntimeException("获取生成结果失败", e);
            }
        });
    }
    
    /**
     * 取消请求
     *
     * @param requestId 请求ID
     * @return 是否取消成功
     */
    public CompletableFuture<Boolean> cancelRequest(String requestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Request request = new Request.Builder()
                        .url(BASE_URL + ENDPOINT + "/requests/" + requestId + "/cancel")
                        .put(RequestBody.create("", JSON_MEDIA_TYPE))
                        .addHeader("Authorization", "Key " + apiKey)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        log.warn("取消请求失败: requestId={}, code={}", requestId, response.code());
                        return false;
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("取消请求响应: requestId={}, response={}", requestId, responseBody);
                    
                    var result = JSON.parseObject(responseBody);
                    boolean success = result.getBooleanValue("success");
                    log.info("取消请求结果: requestId={}, success={}", requestId, success);
                    
                    return success;
                }
            } catch (IOException e) {
                log.error("取消请求失败: requestId={}", requestId, e);
                return false;
            }
        });
    }

    /**
     * 轮询等待结果完成
     * 使用 JDK 17 的 CompletableFuture 和 switch 表达式
     *
     * @param requestId 请求ID
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return Stable Audio 生成结果
     */
    public CompletableFuture<StableAudioResult> waitForCompletion(String requestId, int maxWaitMinutes) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            long maxWaitTime = TimeUnit.MINUTES.toMillis(maxWaitMinutes);

            log.info("开始轮询等待 Stable Audio 生成完成: requestId={}, maxWaitMinutes={}", requestId, maxWaitMinutes);

            while (System.currentTimeMillis() - startTime < maxWaitTime) {
                try {
                    QueueStatus status = getStatus(requestId, false).get();
                    log.debug("轮询状态: requestId={}, status={}", requestId, status.getFormattedStatus());

                    String currentStatus = switch (status.status()) {
                        case "IN_QUEUE" -> {
                            log.debug("请求在队列中: requestId={}, position={}", requestId, status.queuePosition());
                            yield "waiting";
                        }
                        case "IN_PROGRESS" -> {
                            log.debug("请求处理中: requestId={}", requestId);
                            yield "processing";
                        }
                        case "COMPLETED" -> {
                            log.info("请求已完成: requestId={}", requestId);
                            yield "completed";
                        }
                        default -> {
                            log.warn("未知状态: requestId={}, status={}", requestId, status.status());
                            yield "unknown";
                        }
                    };

                    if ("completed".equals(currentStatus)) {
                        StableAudioResult result = getResult(requestId).get();
                        log.info("Stable Audio 生成完成: requestId={}, audioUrl={}", requestId, result.getAudioUrl());
                        return result;
                    }

                    // 等待一段时间后再次查询
                    Thread.sleep(TimeUnit.SECONDS.toMillis(3));

                } catch (Exception e) {
                    log.error("轮询状态时发生错误: requestId={}", requestId, e);
                    // 继续轮询，不立即失败
                    try {
                        Thread.sleep(TimeUnit.SECONDS.toMillis(5));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("轮询被中断", ie);
                    }
                }
            }

            log.error("Stable Audio 生成超时: requestId={}, maxWaitMinutes={}", requestId, maxWaitMinutes);
            throw new RuntimeException("Stable Audio 生成超时，超过 " + maxWaitMinutes + " 分钟");
        });
    }

    /**
     * 一键生成 Stable Audio（提交请求并等待完成）
     * 使用 JDK 17 的 CompletableFuture 链式调用
     *
     * @param prompt 音频生成提示词
     * @param secondsTotal 音频总时长（秒）
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return Stable Audio 生成结果
     */
    public CompletableFuture<StableAudioResult> generateAndWait(String prompt, int secondsTotal, int maxWaitMinutes) {
        var request = StableAudioRequest.of(prompt, secondsTotal);

        return submitRequest(request)
                .thenCompose(queueStatus -> waitForCompletion(queueStatus.requestId(), maxWaitMinutes))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("Stable Audio 生成失败: prompt={}, secondsTotal={}", prompt, secondsTotal, throwable);
                    } else {
                        log.info("Stable Audio 生成成功: prompt={}, secondsTotal={}, audioUrl={}",
                                prompt, secondsTotal, result.getAudioUrl());
                    }
                });
    }

    /**
     * 一键生成 Stable Audio（带自定义步数）
     *
     * @param prompt 音频生成提示词
     * @param secondsTotal 音频总时长（秒）
     * @param steps 去噪步数
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return Stable Audio 生成结果
     */
    public CompletableFuture<StableAudioResult> generateAndWait(String prompt, int secondsTotal, int steps, int maxWaitMinutes) {
        var request = StableAudioRequest.of(prompt, secondsTotal, steps);

        return submitRequest(request)
                .thenCompose(queueStatus -> waitForCompletion(queueStatus.requestId(), maxWaitMinutes))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("Stable Audio 生成失败: prompt={}, secondsTotal={}, steps={}",
                                prompt, secondsTotal, steps, throwable);
                    } else {
                        log.info("Stable Audio 生成成功: prompt={}, secondsTotal={}, steps={}, audioUrl={}",
                                prompt, secondsTotal, steps, result.getAudioUrl());
                    }
                });
    }
}
