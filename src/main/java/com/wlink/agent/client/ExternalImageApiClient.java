package com.wlink.agent.client;

import java.time.Duration; // 添加 Duration 导入
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.ExternalApiRequest;
import com.wlink.agent.client.model.ExternalApiResponse;
import com.wlink.agent.config.ExternalApiConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*; // Import OkHttp classes
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import jakarta.annotation.Resource;
import java.io.IOException; // Import IOException

@Slf4j
@Component
@RequiredArgsConstructor
public class ExternalImageApiClient {

    // Define OkHttp JSON Media Type
    public static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final OkHttpClient okHttpClient; // Inject OkHttpClient
    private final ExternalApiConfig externalApiConfig;

    public Mono<ExternalApiResponse> requestImageModification(ExternalApiRequest request) {
        try {
            String requestBodyJson = objectMapper.writeValueAsString(request);
            log.info("[WebClient] Serialized request body: {}", requestBodyJson);
            return webClient
                    .post()
                    .uri(externalApiConfig.getImageModifyUrl())
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + externalApiConfig.getImageModifyApiKey())
                    .contentType(org.springframework.http.MediaType.APPLICATION_JSON) // Use Spring's MediaType here
                    .bodyValue(requestBodyJson)
                    // 移除错误位置timeout
                    .retrieve()
                    .bodyToMono(ExternalApiResponse.class)
                    .timeout(Duration.ofSeconds(70)) // bodyToMono 之后添加超时
//                    .doOnNext(responseBody -> log.info("[WebClient] Raw response body from external API: {}", responseBody))
//                    .flatMap(responseBody -> {
//                        try {
//                            ExternalApiResponse apiResponse = objectMapper.readValue(responseBody, ExternalApiResponse.class);
//                            // Explicitly log the deserialized outputs object
//                            if (apiResponse != null && apiResponse.getData() != null) {
//                                log.info("[WebClient] Deserialized outputs: {}", apiResponse.getData().getOutputs());
//                            } else {
//                                log.warn("[WebClient] Deserialized response or data is null.");
//                            }
//                            return Mono.just(apiResponse);
//                        } catch (JsonProcessingException jsonEx) {
//                            log.error("[WebClient] Failed to deserialize response body: {}", responseBody, jsonEx);
//                            return Mono.error(jsonEx);
//                        }
//                    })
                    .doOnSuccess(response -> log.info("[WebClient] Successfully deserialized response: {}", response))
                    .doOnError(error -> {
                        if (!(error instanceof JsonProcessingException)) {
                            log.error("[WebClient] Error response from external API: URL={}, Error={}", externalApiConfig.getImageModifyUrl(), error.getMessage(), error);
                        }
                    });
        } catch (JsonProcessingException e) {
            log.error("[WebClient] Failed to serialize request to JSON: Request={}, Error={}", request, e.getMessage(), e);
            return Mono.error(e); // Propagate the serialization error
        }
    }


    // --- New method using OkHttp ---
    public ExternalApiResponse requestImageModificationWithOkHttp(ExternalApiRequest request) throws IOException {
        String apiUrl = externalApiConfig.getImageModifyUrl();
        log.info("[OkHttp] Sending request to external image API: URL={}, Payload={}", apiUrl, request);
        String requestBodyJson;
        try {
            requestBodyJson = objectMapper.writeValueAsString(request);
            log.debug("[OkHttp] Serialized request body: {}", requestBodyJson);
        } catch (JsonProcessingException e) {
            log.error("[OkHttp] Failed to serialize request to JSON: Request={}, Error={}", request, e.getMessage(), e);
            throw new RuntimeException("Failed to serialize request", e); // Or handle more gracefully
        }

        RequestBody body = RequestBody.create(requestBodyJson, JSON);
        Request okHttpRequest = new Request.Builder()
                .url(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + externalApiConfig.getImageModifyApiKey())
                // Content-Type is set by RequestBody.create for JSON media type
                .post(body)
                .build();

        log.debug("[OkHttp] Executing request: {}", okHttpRequest);

        // Synchronous execution
        try (Response response = okHttpClient.newCall(okHttpRequest).execute()) {
            String responseBodyString = response.body() != null ? response.body().string() : null; // Read body once
            log.info("[OkHttp] Raw response received. Status Code: {}, Body: {}", response.code(), responseBodyString);

            if (!response.isSuccessful()) {
                log.error("[OkHttp] Unsuccessful response from external API: Code={}, Message={}, Body={}",
                          response.code(), response.message(), responseBodyString);
                // Consider throwing a custom exception
                throw new IOException("Unexpected code " + response + " Body: " + responseBodyString);
            }

            if (responseBodyString == null || responseBodyString.isEmpty()) {
                 log.error("[OkHttp] Empty response body received.");
                 // Handle empty body case, maybe return null or throw exception
                 return null; // Or throw new IOException("Empty response body");
            }

            try {
                ExternalApiResponse apiResponse = objectMapper.readValue(responseBodyString, ExternalApiResponse.class);
                log.info("[OkHttp] Successfully deserialized response: {}", apiResponse);
                 // Explicitly log the deserialized outputs object
                if (apiResponse != null && apiResponse.getData() != null) {
                     log.info("[OkHttp] Deserialized outputs: {}", apiResponse.getData().getOutputs());
                } else {
                     log.warn("[OkHttp] Deserialized response or data is null.");
                }
                return apiResponse;
            } catch (JsonProcessingException e) {
                log.error("[OkHttp] Failed to deserialize response JSON: Body={}, Error={}", responseBodyString, e.getMessage(), e);
                // Consider throwing a custom exception
                throw new RuntimeException("Failed to deserialize response", e);
            }
        }
        // Catch IOException from execute() or body reading issues
        catch (IOException e) {
             log.error("[OkHttp] IOException during API call: URL={}, Error={}", apiUrl, e.getMessage(), e);
             throw e; // Re-throw original IOException
        }
    }

    // Consider adding specific error handling logic if needed, e.g., mapping exceptions
} 
