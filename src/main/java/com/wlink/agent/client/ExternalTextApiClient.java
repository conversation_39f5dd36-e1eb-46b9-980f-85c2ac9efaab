package com.wlink.agent.client;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.wlink.agent.client.model.text.TextApiInputs;
import com.wlink.agent.client.model.text.TextApiRequest;
import com.wlink.agent.client.model.text.TextApiResponse;
import com.wlink.agent.client.model.text.TextApiResponseData;
import com.wlink.agent.client.model.text.TextApiResponseOutputs;
import com.wlink.agent.client.model.text.TextItem;
import com.wlink.agent.config.ExternalApiConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 外部文本API客户
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExternalTextApiClient {

    // 定义OkHttp JSON Media Type
    public static final MediaType JSON_UTF8 = MediaType.get("application/json; charset=utf-8");

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final OkHttpClient okHttpClient;
    private final ExternalApiConfig externalApiConfig;

    /**
     * 使用WebClient异步请求文本生成
     *
     * @param request 请求参数
     * @return 响应Mono
     */
    public Mono<TextApiResponse> requestTextGeneration(TextApiRequest request) {
        try {
            String requestBodyJson = objectMapper.writeValueAsString(request);
            log.info("[WebClient] 序列化请求体: {}", requestBodyJson);
            return webClient
                    .post()
                    .uri(externalApiConfig.getTextUrl())
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + externalApiConfig.getTextApiKey())
                    .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                    .bodyValue(requestBodyJson)
                    .retrieve()
                    .bodyToMono(TextApiResponse.class)
                    .timeout(Duration.ofSeconds(70))
                    .doOnSuccess(response -> log.info("[WebClient] 成功反序列化响应: {}", response))
                    .doOnError(error -> {
                        if (!(error instanceof JsonProcessingException)) {
                            log.error("[WebClient] 外部API响应错误: URL={}, Error={}", externalApiConfig.getTextUrl(), error.getMessage(), error);
                        }
                    });
        } catch (JsonProcessingException e) {
            log.error("[WebClient] 请求序列化为JSON失败: Request={}, Error={}", request, e.getMessage(), e);
            return Mono.error(e);
        }
    }

    /**
     * 使用OkHttp同步请求文本生成
     *
     * @param request 请求参数
     * @return 响应对象
     * @throws IOException 网络IO异常
     */
    public TextApiResponse requestTextGenerationWithOkHttp(TextApiRequest request) throws IOException {
        String apiUrl = externalApiConfig.getTextUrl();
        log.info("[OkHttp] 发送请求到外部文本API: URL={}, Payload={}", apiUrl, request);
        String requestBodyJson;
        try {
            requestBodyJson = objectMapper.writeValueAsString(request);
            log.debug("[OkHttp] 序列化请求体: {}", requestBodyJson);
        } catch (JsonProcessingException e) {
            log.error("[OkHttp] 请求序列化为JSON失败: Request={}, Error={}", request, e.getMessage(), e);
            throw new RuntimeException("序列化请求失败", e);
        }

        RequestBody body = RequestBody.create(requestBodyJson, JSON_UTF8);
        Request okHttpRequest = new Request.Builder()
                .url(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + externalApiConfig.getTextApiKey())
                .post(body)
                .build();

        log.debug("[OkHttp] 执行请求: {}", okHttpRequest);

        // 同步执行
        try (Response response = okHttpClient.newCall(okHttpRequest).execute()) {
            String responseBodyString = response.body() != null ? response.body().string() : null;
            log.info("[OkHttp] 收到原始响应. 状态码: {}, 响应体长 {}", response.code(),
                    responseBodyString != null ? responseBodyString.length() : 0);
            log.debug("[OkHttp] 响应体内 {}", responseBodyString);

            if (!response.isSuccessful()) {
                log.error("[OkHttp] 外部API返回失败: Code={}, Message={}, Body={}",
                        response.code(), response.message(), responseBodyString);
                throw new IOException("意外的响应码 " + response + " 响应 " + responseBodyString);
            }

            if (responseBodyString == null || responseBodyString.isEmpty()) {
                log.error("[OkHttp] 收到空响应体.");
                return null;
            }

            try {
                // 使用JsonNode进行灵活解析
                TextApiResponse textApiResponse = JSON.parseObject(responseBodyString, TextApiResponse.class);
                if (null == textApiResponse){
                    return null;
                }
                TextApiResponse.DataDTO dataDTO = textApiResponse.getData();
                if (null == dataDTO){
                    return null;
                }
                String status = dataDTO.getStatus();
                if (!Objects.equals(status, "succeeded")){
                    log.error("[OkHttp] 外部API返回失败: Status={}", status);
                    return null;
                }
                TextApiResponse.DataDTO.OutputsDTO outputs1 = dataDTO.getOutputs();
                List<TextApiResponse.DataDTO.OutputsDTO.TextDTO> text1 = outputs1.getText();
                if (null == text1 || text1.isEmpty()){
                    return null;
                }
                return textApiResponse;
            } catch (Exception e) {
                log.error("[OkHttp] 响应JSON解析失败: Error={}, ResponseBody={}", e.getMessage(), responseBodyString, e);
                throw new RuntimeException("响应JSON解析失败: " + e.getMessage(), e);
            }
        } catch (IOException e) {
            log.error("[OkHttp] API调用过程中IO异常: URL={}, Error={}", apiUrl, e.getMessage(), e);
            throw e;
        }
    }
} 
