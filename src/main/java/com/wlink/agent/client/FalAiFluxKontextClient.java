package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.dao.mapper.FalAiFluxRequestLogMapper;
import com.wlink.agent.dao.po.FalAiFluxRequestLog;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Java客户端类用于调用fal.ai的Flux Pro Kontext API
 * 支持文本到图像、图像到图像和多图像编辑功能
 */
@Slf4j
@Component
public class FalAiFluxKontextClient {
    private static final String API_BASE_URL = "https://queue.fal.run";
    
    // Kontext 基础端点
    public static final String KONTEXT_ENDPOINT = "/fal-ai/flux-pro/kontext";
    public static final String KONTEXT_MAX_ENDPOINT = "/fal-ai/flux-pro/kontext/max";
    public static final String KONTEXT_MAX_MULTI_ENDPOINT = "/fal-ai/flux-pro/kontext/max/multi";
    public static final String KONTEXT_TEXT_TO_IMAGE_ENDPOINT = "/fal-ai/flux-pro/kontext/text-to-image";
    public static final String KONTEXT_MAX_TEXT_TO_IMAGE_ENDPOINT = "/fal-ai/flux-pro/kontext/max/text-to-image";
    
    private static final int MAX_RETRIES = 3;
    private static final long INITIAL_RETRY_DELAY_MS = 1000; // 初始重试延迟1秒
    private static final double RETRY_DELAY_MULTIPLIER = 2.0; // 指数退避因子
    private static final long MAX_RETRY_DELAY_MS = 10000; // 最大重试延迟10秒
    private static final long POLL_INTERVAL_MS = 1000;
    private static final long MAX_WAIT_TIME_MS = 300000; // 5分钟超时
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String apiKey;
    
    @Resource
    private FalAiFluxRequestLogMapper falAiFluxRequestLogMapper;
    
    @Resource
    private PlatformTransactionManager transactionManager;
    
    private TransactionTemplate transactionTemplate;  
  /**
     * 创建FalAiFluxKontextClient实例
     * @param apiKey FAL API密钥
     */
    public FalAiFluxKontextClient(@Value("${fal.ai.api-key:58371be0-d55d-4542-a7ce-7018b4aa0226:44e655c15df8eca02ab38a127e299b94}") String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            log.warn("FAL API密钥未配置，请在application.properties中设置fal.ai.api-key");
        }
        this.apiKey = apiKey;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
        // 配置ObjectMapper以忽略未知属性
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 初始化事务模板
     * 在所有依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        if (transactionManager != null) {
            this.transactionTemplate = new TransactionTemplate(transactionManager);
            log.info("TransactionTemplate 初始化成功");
        } else {
            log.warn("TransactionManager 未注入，无法初始化TransactionTemplate");
        }
    }
    
    /**
     * 提交Kontext文本到图像请求
     * @param request Kontext文本到图像请求参数
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextTextToImageRequest(KontextTextToImageRequest request) throws IOException {
        return submitKontextTextToImageRequest(request, null);
    }
    
    /**
     * 提交Kontext文本到图像请求（带请求上下文）
     * @param request Kontext文本到图像请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextTextToImageRequest(KontextTextToImageRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + KONTEXT_TEXT_TO_IMAGE_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交Kontext文本到图像请求URL: {}, 请求体: {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), null, requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("Kontext文本到图像API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交Kontext文本到图像请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交Kontext文本到图像请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状态
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 提交Kontext Max文本到图像请求
     * @param request Kontext Max文本到图像请求参数
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextMaxTextToImageRequest(KontextMaxTextToImageRequest request) throws IOException {
        return submitKontextMaxTextToImageRequest(request, null);
    } 
   
    /**
     * 提交Kontext Max文本到图像请求（带请求上下文）
     * @param request Kontext Max文本到图像请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextMaxTextToImageRequest(KontextMaxTextToImageRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + KONTEXT_MAX_TEXT_TO_IMAGE_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交Kontext Max文本到图像请求URL: {}, 请求体: {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), null, requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("Kontext Max文本到图像API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交Kontext Max文本到图像请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交Kontext Max文本到图像请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状态
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 提交Kontext图像到图像请求
     * @param request Kontext图像到图像请求参数
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextImageToImageRequest(KontextImageToImageRequest request) throws IOException {
        return submitKontextImageToImageRequest(request, null);
    }
    
    /**
     * 提交Kontext图像到图像请求（带请求上下文）
     * @param request Kontext图像到图像请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextImageToImageRequest(KontextImageToImageRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + KONTEXT_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交Kontext图像到图像请求URL: {}, 请求体: {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), request.getImageUrl(), requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("Kontext图像到图像API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交Kontext图像到图像请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交Kontext图像到图像请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状态
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }    
 
   /**
     * 提交Kontext Max图像到图像请求
     * @param request Kontext Max图像到图像请求参数
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextMaxImageToImageRequest(KontextMaxImageToImageRequest request) throws IOException {
        return submitKontextMaxImageToImageRequest(request, null);
    }
    
    /**
     * 提交Kontext Max图像到图像请求（带请求上下文）
     * @param request Kontext Max图像到图像请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextMaxImageToImageRequest(KontextMaxImageToImageRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + KONTEXT_MAX_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交Kontext Max图像到图像请求URL: {}, 请求体: {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), request.getImageUrl(), requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("Kontext Max图像到图像API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交Kontext Max图像到图像请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交Kontext Max图像到图像请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状态
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 提交Kontext Max Multi多图像请求
     * @param request Kontext Max Multi多图像请求参数
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextMaxMultiRequest(KontextMaxMultiRequest request) throws IOException {
        return submitKontextMaxMultiRequest(request, null);
    }
    
    /**
     * 提交Kontext Max Multi多图像请求（带请求上下文）
     * @param request Kontext Max Multi多图像请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitKontextMaxMultiRequest(KontextMaxMultiRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + KONTEXT_MAX_MULTI_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交Kontext Max Multi多图像请求URL: {}, 请求体: {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), String.join(",", request.getImageUrls()), requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("Kontext Max Multi多图像API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交Kontext Max Multi多图像请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交Kontext Max Multi多图像请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状态
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }  
  
    /**
     * 检查请求状态
     * @param requestId 请求ID
     * @param endpoint API端点
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    public QueueStatus checkKontextQueueStatus(String requestId, String endpoint) throws IOException {
        return checkKontextQueueStatus(requestId, endpoint, null);
    }
    
    /**
     * 检查请求状态（带请求上下文）
     * @param requestId 请求ID
     * @param endpoint API端点
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus checkKontextQueueStatus(String requestId, String endpoint, FalAiFluxRequestContext context) throws IOException {
        String statusEndpoint = endpoint + "/requests/" + requestId + "/status";
        Request request = new Request.Builder()
                .url(API_BASE_URL + statusEndpoint)
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("状态查询API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("检查状态失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "检查状态失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException(response.code() + "", response.message());
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状态
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            requestId,
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 获取请求结果
     * @param requestId 请求ID
     * @param endpoint API端点
     * @return 生成的图像结果
     * @throws IOException 如果API调用失败
     */
    public KontextResponse getKontextResult(String requestId, String endpoint) throws IOException {
        return getKontextResult(requestId, endpoint, null);
    }
    
    /**
     * 获取请求结果（带请求上下文）
     * @param requestId 请求ID
     * @param endpoint API端点
     * @param context 请求上下文（可选）
     * @return 生成的图像结果
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public KontextResponse getKontextResult(String requestId, String endpoint, FalAiFluxRequestContext context) throws IOException {
        String resultEndpoint = endpoint + "/requests/" + requestId;
        
        log.info("结果查询请求URL: {}, 请求方法: GET", API_BASE_URL + resultEndpoint);
        
        Request request = new Request.Builder()
                .url(API_BASE_URL + resultEndpoint)
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("结果查询API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("获取结果失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失败
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "获取结果失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("获取结果失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            KontextResponse kontextResponse = objectMapper.readValue(responseBody, KontextResponse.class);
            
            // 更新请求记录为完成
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null && kontextResponse.getImages() != null && !kontextResponse.getImages().isEmpty()) {
                    List<String> imageUrls = new ArrayList<>();
                    for (KontextImage image : kontextResponse.getImages()) {
                        imageUrls.add(image.getUrl());
                    }
                    completeRequestLog(
                            requestLog,
                            responseBody,
                            objectMapper.writeValueAsString(imageUrls)
                    );
                    context.setCompleted(true);
                } else if (requestLog != null) {
                    failRequestLog(requestLog, "未生成任何图像");
                    context.setFailed(true);
                }
            }
            
            return kontextResponse;
        }
    }    
  
  /**
     * 取消请求
     * @param requestId 请求ID
     * @param endpoint API端点
     * @return 是否成功取消
     * @throws IOException 如果API调用失败
     */
    public boolean cancelKontextRequest(String requestId, String endpoint) throws IOException {
        String cancelEndpoint = endpoint + "/requests/" + requestId + "/cancel";
        
        log.debug("取消请求URL: {}, 请求方法: PUT", API_BASE_URL + cancelEndpoint);
        
        Request request = new Request.Builder()
                .url(API_BASE_URL + cancelEndpoint)
                .addHeader("Authorization", "Key " + apiKey)
                .put(RequestBody.create("", MediaType.parse("application/json")))
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.debug("取消请求API响应状态: {}, 响应头: {}, 响应体: {}", 
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("取消请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                throw new BizException("取消请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            
            return Boolean.TRUE.equals(responseMap.get("success"));
        }
    }
    
    /**
     * 计算指数退避的重试延迟时间
     * @param attempt 当前尝试次数（从1开始）
     * @return 重试延迟时间（毫秒）
     */
    private long calculateRetryDelayMs(int attempt) {
        // 计算指数退避的延迟时间
        long delay = (long) (INITIAL_RETRY_DELAY_MS * Math.pow(RETRY_DELAY_MULTIPLIER, attempt - 1));
        // 确保不超过最大延迟时间
        return Math.min(delay, MAX_RETRY_DELAY_MS);
    }
    
    /**
     * 创建请求上下文
     * @param context 请求上下文
     * @param prompt 提示词
     * @param imageUrl 图像URL（可选）
     * @param requestParams 请求参数JSON
     */
    private void createRequestContext(FalAiFluxRequestContext context, String prompt, String imageUrl, String requestParams) {
        FalAiFluxRequestLog requestLog = createRequestLog(
                context.getRequestType(),
                prompt,
                imageUrl,
                requestParams
        );
        context.setRequestLogId(requestLog.getId());
    }
    
    /**
     * 创建请求记录
     * @param requestType 请求类型
     * @param prompt 提示词
     * @param imageUrl 图像URL（可选）
     * @param requestParams 请求参数JSON
     * @return 创建的请求记录
     */
    private FalAiFluxRequestLog createRequestLog(String requestType, String prompt, String imageUrl, String requestParams) {
        FalAiFluxRequestLog requestLog = new FalAiFluxRequestLog();
        requestLog.setRequestType(requestType);
        requestLog.setPrompt(prompt);
        requestLog.setImageUrl(imageUrl);
        requestLog.setRequestParams(requestParams);
        requestLog.setStatus("SUBMITTED");
        requestLog.setRetryCount(0);
        requestLog.setCreateTime(new Date());
        requestLog.setUpdateTime(new Date());
        
        if (transactionTemplate != null) {
            return transactionTemplate.execute(status -> {
                falAiFluxRequestLogMapper.insert(requestLog);
                return requestLog;
            });
        } else {
            falAiFluxRequestLogMapper.insert(requestLog);
            return requestLog;
        }
    }  
  
    /**
     * 获取请求记录
     * @param context 请求上下文
     * @return 请求记录
     */
    private FalAiFluxRequestLog getRequestLog(FalAiFluxRequestContext context) {
        if (context.getRequestLogId() == null) {
            return null;
        }
        return falAiFluxRequestLogMapper.selectById(context.getRequestLogId());
    }
    
    /**
     * 更新请求记录状态
     * @param requestLog 请求记录
     * @param requestId 请求ID
     * @param status 状态
     * @param queuePosition 队列位置
     */
    private void updateRequestLogStatus(FalAiFluxRequestLog requestLog, String requestId, String status, Integer queuePosition) {
        requestLog.setRequestId(requestId);
        requestLog.setStatus(status);
        requestLog.setQueuePosition(queuePosition);
        requestLog.setUpdateTime(new Date());
        
        if (transactionTemplate != null) {
            transactionTemplate.execute(transactionStatus -> {
                falAiFluxRequestLogMapper.updateById(requestLog);
                return null;
            });
        } else {
            falAiFluxRequestLogMapper.updateById(requestLog);
        }
    }
    
    /**
     * 更新请求记录重试次数
     * @param requestLog 请求记录
     */
    private void updateRequestLogRetry(FalAiFluxRequestLog requestLog) {
        requestLog.setRetryCount(requestLog.getRetryCount() + 1);
        requestLog.setUpdateTime(new Date());
        
        if (transactionTemplate != null) {
            transactionTemplate.execute(transactionStatus -> {
                falAiFluxRequestLogMapper.updateById(requestLog);
                return null;
            });
        } else {
            falAiFluxRequestLogMapper.updateById(requestLog);
        }
    }
    
    /**
     * 完成请求记录
     * @param requestLog 请求记录
     * @param responseData 响应数据
     * @param imageUrls 图像URL列表
     */
    private void completeRequestLog(FalAiFluxRequestLog requestLog, String responseData, String imageUrls) {
        requestLog.setStatus("COMPLETED");
        requestLog.setResponseData(responseData);
        requestLog.setResultImageUrls(imageUrls);
        requestLog.setEndTime(new Date());
        requestLog.setUpdateTime(new Date());
        
        if (transactionTemplate != null) {
            transactionTemplate.execute(transactionStatus -> {
                falAiFluxRequestLogMapper.updateById(requestLog);
                return null;
            });
        } else {
            falAiFluxRequestLogMapper.updateById(requestLog);
        }
    }
    
    /**
     * 标记请求记录为失败
     * @param requestLog 请求记录
     * @param errorMessage 错误信息
     */
    private void failRequestLog(FalAiFluxRequestLog requestLog, String errorMessage) {
        requestLog.setStatus("FAILED");
        requestLog.setErrorMessage(errorMessage);
        requestLog.setEndTime(new Date());
        requestLog.setUpdateTime(new Date());
        
        if (transactionTemplate != null) {
            transactionTemplate.execute(transactionStatus -> {
                falAiFluxRequestLogMapper.updateById(requestLog);
                return null;
            });
        } else {
            falAiFluxRequestLogMapper.updateById(requestLog);
        }
    } 
   
    /**
     * 生成图像（同步方法）
     * @param request Kontext文本到图像请求参数
     * @return 生成的图像结果
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public KontextResponse generateKontextImage(KontextTextToImageRequest request) throws IOException {
        // 提交请求
        log.info("提交Kontext文本到图像请求: {}", request.getPrompt());
        
        // 创建请求记录
        String requestParams = objectMapper.writeValueAsString(request);
        FalAiFluxRequestLog requestLog = createRequestLog(
                "KONTEXT_TEXT_TO_IMAGE", 
                request.getPrompt(), 
                null, 
                requestParams
        );
        
        QueueStatus queueStatus = null;
        // 添加重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                queueStatus = submitKontextTextToImageRequest(request);
                log.info("提交Kontext文本到图像请求成功，queueStatus: {}", JSON.toJSONString(queueStatus));
                break;
            } catch (IOException e) {
                // 更新重试次数
                updateRequestLogRetry(requestLog);
                
                if (attempt == MAX_RETRIES) {
                    log.error("提交Kontext文本到图像请求失败，重试次数用尽: {}", e.getMessage());
                    failRequestLog(requestLog, "提交请求失败: " + e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("提交Kontext文本到图像请求失败，将在{}ms后重试（{}/{}）: {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }
        
        if (queueStatus == null) {
            failRequestLog(requestLog, "提交Kontext文本到图像请求失败，未获取到队列状态");
            throw new IOException("提交Kontext文本到图像请求失败，未获取到队列状态");
        }
        
        String requestId = queueStatus.getRequestId();
        // 更新请求记录状态
        updateRequestLogStatus(
                requestLog, 
                requestId, 
                queueStatus.getStatus(), 
                queueStatus.getQueuePosition()
        );
        
        // 如果是同步模式，直接返回结果
        if (request.getSyncMode() != null && request.getSyncMode()) {
            log.info("同步模式，直接获取结果");
            KontextResponse response = getKontextResult(requestId, KONTEXT_TEXT_TO_IMAGE_ENDPOINT);
            // 完成请求记录
            if (response != null && response.getImages() != null && !response.getImages().isEmpty()) {
                List<String> imageUrls = new ArrayList<>();
                for (KontextImage image : response.getImages()) {
                    imageUrls.add(image.getUrl());
                }
                completeRequestLog(
                        requestLog, 
                        objectMapper.writeValueAsString(response), 
                        objectMapper.writeValueAsString(imageUrls)
                );
            } else {
                failRequestLog(requestLog, "未生成任何图像");
            }
            return response;
        }
        
        // 轮询状态直到完成
        long startTime = System.currentTimeMillis();
        int pollCount = 0;
        
        while (System.currentTimeMillis() - startTime < MAX_WAIT_TIME_MS) {
            try {
                Thread.sleep(POLL_INTERVAL_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                failRequestLog(requestLog, "等待结果时被中断: " + e.getMessage());
                throw new IOException("等待结果时被中断", e);
            }
            
            pollCount++;
            
            // 添加重试逻辑
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                try {
                    queueStatus = checkKontextQueueStatus(requestId, KONTEXT_TEXT_TO_IMAGE_ENDPOINT);
                    // 更新请求记录状态
                    updateRequestLogStatus(
                            requestLog, 
                            requestId, 
                            queueStatus.getStatus(), 
                            queueStatus.getQueuePosition()
                    );
                    break;
                } catch (IOException e) {
                    // 更新重试次数
                    updateRequestLogRetry(requestLog);
                    
                    if (attempt == MAX_RETRIES) {
                        log.error("检查状态失败，重试次数用尽: {}", e.getMessage());
                        failRequestLog(requestLog, "检查状态失败: " + e.getMessage());
                        throw e;
                    }
                    long retryDelay = calculateRetryDelayMs(attempt);
                    log.warn("检查状态失败，将在{}ms后重试（{}/{}）: {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                        throw new IOException("等待重试时被中断", ie);
                    }
                }
            }   
         
            // 输出状态日志
            if ("IN_QUEUE".equals(queueStatus.getStatus())) {
                log.info("请求在队列中，位置: {}, 轮询次数: {}", 
                        queueStatus.getQueuePosition() != null ? queueStatus.getQueuePosition() : "未知", 
                        pollCount);
            } else if ("IN_PROGRESS".equals(queueStatus.getStatus())) {
                log.info("请求正在处理中..., 轮询次数: {}", pollCount);
            }
            
            // 检查状态
            if ("COMPLETED".equals(queueStatus.getStatus())) {
                log.info("请求已完成，总轮询次数: {}, 总耗时: {}ms", pollCount, System.currentTimeMillis() - startTime);
                break;
            } else if (!"IN_QUEUE".equals(queueStatus.getStatus()) && !"IN_PROGRESS".equals(queueStatus.getStatus())) {
                log.error("请求失败，状态: {}", queueStatus.getStatus());
                failRequestLog(requestLog, "生成图像失败，未知状态: " + queueStatus.getStatus());
                throw new IOException("生成图像失败，未知状态: " + queueStatus.getStatus());
            }
        }
        
        if (!"COMPLETED".equals(queueStatus.getStatus())) {
            log.error("请求超时，已等待{}ms", MAX_WAIT_TIME_MS);
            failRequestLog(requestLog, "生成图像请求超时，状态: " + queueStatus.getStatus());
            throw new IOException("生成图像请求超时，状态: " + queueStatus.getStatus());
        }
        
        // 获取结果
        log.info("获取生成的图像结果");
        KontextResponse response = null;
        
        // 添加重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                response = getKontextResult(requestId, KONTEXT_TEXT_TO_IMAGE_ENDPOINT);
                break;
            } catch (IOException e) {
                // 更新重试次数
                updateRequestLogRetry(requestLog);
                
                if (attempt == MAX_RETRIES) {
                    log.error("获取结果失败，重试次数用尽: {}", e.getMessage());
                    failRequestLog(requestLog, "获取结果失败: " + e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("获取结果失败，将在{}ms后重试（{}/{}）: {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }
        
        if (response != null && response.getImages() != null && !response.getImages().isEmpty()) {
            log.info("成功生成{}张图像", response.getImages().size());
            List<String> imageUrls = new ArrayList<>();
            for (int i = 0; i < response.getImages().size(); i++) {
                KontextImage image = response.getImages().get(i);
                imageUrls.add(image.getUrl());
                log.info("图像 #{}: 尺寸={}x{}, URL={}", i + 1, image.getWidth(), image.getHeight(), image.getUrl());
            }
            
            // 完成请求记录
            completeRequestLog(
                    requestLog, 
                    objectMapper.writeValueAsString(response), 
                    objectMapper.writeValueAsString(imageUrls)
            );
        } else {
            log.warn("未生成任何图像");
            failRequestLog(requestLog, "未生成任何图像");
        }
        
        return response;
    }
    
    /**
     * 异步生成图像
     * @param request Kontext文本到图像请求参数
     * @return 包含结果的CompletableFuture
     */
    public CompletableFuture<KontextResponse> generateKontextImageAsync(KontextTextToImageRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateKontextImage(request);
            } catch (IOException e) {
                throw new RuntimeException("生成图像失败", e);
            }
        });
    }
    
    /**
     * 请求上下文类
     */
    @Data
    public static class FalAiFluxRequestContext {
        private Long requestLogId;
        private String requestId;
        private String requestType;
        private boolean completed;
        private boolean failed;
    }
    
    /**
     * 队列状态响应类
     */
    @Getter
    @Setter
    public static class QueueStatus {
        private String status;
        private String requestId;
        private String responseUrl;
        private String statusUrl;
        private String cancelUrl;
        private Map<String, Object> logs;
        private Map<String, Object> metrics;
        private Integer queuePosition;
    }
    
    /**
     * Kontext文本到图像请求参数类
     */
    @Getter
    @Setter
    public static class KontextTextToImageRequest {
        private String prompt;
        private String aspectRatio;
        private Integer numImages;
        private String outputFormat;
        private Boolean syncMode;
        private String safetyTolerance;
        private Double guidanceScale;
        private Integer seed;
    }
    
    /**
     * Kontext Max文本到图像请求参数类
     */
    @Getter
    @Setter
    public static class KontextMaxTextToImageRequest {
        private String prompt;
        private String aspectRatio;
        private Integer numImages;
        private String outputFormat;
        private Boolean syncMode;
        private String safetyTolerance;
        private Double guidanceScale;
        private Integer seed;
    }
    
    /**
     * Kontext图像到图像请求参数类
     */
    @Getter
    @Setter
    public static class KontextImageToImageRequest {
        private String prompt;
        private String imageUrl;
        private String aspectRatio;
        private Integer numImages;
        private String outputFormat;
        private Boolean syncMode;
        private String safetyTolerance;
        private Double guidanceScale;
        private Integer seed;
    }
    
    /**
     * Kontext Max图像到图像请求参数类
     */
    @Getter
    @Setter
    public static class KontextMaxImageToImageRequest {
        private String prompt;
        private String imageUrl;
        private String aspectRatio;
        private Integer numImages;
        private String outputFormat;
        private Boolean syncMode;
        private String safetyTolerance;
        private Double guidanceScale;
        private Integer seed;
    }
    
    /**
     * Kontext Max Multi多图像请求参数类
     */
    @Getter
    @Setter
    public static class KontextMaxMultiRequest {
        private String prompt;
        private List<String> imageUrls;
        private String aspectRatio;
        private Integer numImages;
        private String outputFormat;
        private Boolean syncMode;
        private String safetyTolerance;
        private Double guidanceScale;
        private Integer seed;
    }
    
    /**
     * Kontext响应类
     */
    @Getter
    @Setter
    public static class KontextResponse {
        private String prompt;
        private List<KontextImage> images;
        private Map<String, Double> timings;
        private List<Boolean> hasNsfwConcepts;
        private Integer seed;
    }
    
    /**
     * Kontext生成的图像类
     */
    @Getter
    @Setter
    public static class KontextImage {
        private String url;
        private Integer width;
        private Integer height;
        private String contentType;
        private String fileName;
        private Integer fileSize;
    }
}