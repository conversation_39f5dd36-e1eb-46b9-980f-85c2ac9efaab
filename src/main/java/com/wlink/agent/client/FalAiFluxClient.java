package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.dao.mapper.FalAiFluxRequestLogMapper;
import com.wlink.agent.dao.po.FalAiFluxRequestLog;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Java客户端类用于调用fal.ai的Flux Pro Kontext API
 * 支持文本到图像和图像编辑功能
 */

@Slf4j
@Component
public class FalAiFluxClient {
    private static final String API_BASE_URL = "https://queue.fal.run";
    private static final String TEXT_TO_IMAGE_ENDPOINT = "/fal-ai/flux-pro/kontext/text-to-image";
    private static final String IMAGE_TO_IMAGE_ENDPOINT = "/fal-ai/flux-pro/kontext";
    
    private static final int MAX_RETRIES = 3;
    private static final long INITIAL_RETRY_DELAY_MS = 1000; // 初始重试延迟1秒
    private static final double RETRY_DELAY_MULTIPLIER = 2.0; // 指数退避因子
    private static final long MAX_RETRY_DELAY_MS = 10000; // 最大重试延迟10秒
    private static final long POLL_INTERVAL_MS = 1000;
    private static final long MAX_WAIT_TIME_MS = 300000; // 5分钟超时
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String apiKey;
    
    @Resource
    private FalAiFluxRequestLogMapper falAiFluxRequestLogMapper;
    
    @Resource
    private PlatformTransactionManager transactionManager;
    
    private TransactionTemplate transactionTemplate;
    
    /**
     * 创建FalAiFluxClient实例
     * @param apiKey FAL API密钥
     */
    public FalAiFluxClient(@Value("${fal.ai.api-key:58371be0-d55d-4542-a7ce-7018b4aa0226:44e655c15df8eca02ab38a127e299b94}") String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            log.warn("FAL API密钥未配置，请在application.properties中设置fal.ai.api-key");
        }
        this.apiKey = apiKey;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
        // 配置ObjectMapper以忽略未知属性
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 初始化事务模板
     * 在所有依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        if (transactionManager != null) {
            this.transactionTemplate = new TransactionTemplate(transactionManager);
            log.info("TransactionTemplate 初始化成功");
        } else {
            log.warn("TransactionManager 未注入，无法初始化TransactionTemplate");
        }
    }
    
    /**
     * 提交文本到图像的请求
     * @param request 文本到图像请求参数
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    public QueueStatus submitTextToImageRequest(TextToImageRequest request) throws IOException {
        return submitTextToImageRequest(request, null);
    }
    
    /**
     * 提交文本到图像的请求（带请求上下文）
     * @param request 文本到图像请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响应
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitTextToImageRequest(TextToImageRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + TEXT_TO_IMAGE_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交文本到图像请求URL: {}, 请求体: {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), null, requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("文本到图像API响应状态: {}, 响应头: {}, 响应体: {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交文本到图像请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                
                // 更新请求记录为失
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交文本到图像请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 提交图像编辑请求
     * @param request 图像编辑请求参数
     * @return 队列状态响
     * @throws IOException 如果API调用失败
     */
    public QueueStatus submitImageToImageRequest(ImageToImageRequest request) throws IOException {
        return submitImageToImageRequest(request, null);
    }
    
    /**
     * 提交图像编辑请求（带请求上下文）
     * @param request 图像编辑请求参数
     * @param context 请求上下文（可选）
     * @return 队列状态响
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus submitImageToImageRequest(ImageToImageRequest request, FalAiFluxRequestContext context) throws IOException {
        String url = API_BASE_URL + IMAGE_TO_IMAGE_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(request);
        log.info("提交图像编辑请求URL: {}, 请求 {}", url, requestBody);
        
        // 创建请求上下文和记录
        if (context != null && context.getRequestLogId() == null) {
            createRequestContext(context, request.getPrompt(), request.getImageUrl(), requestBody);
        }
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(
                        requestBody,
                        MediaType.parse("application/json")
                ))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("图像编辑API响应状 {}, 响应 {}, 响应 {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交图像编辑请求失败: 状态码={}, 响应{}", response.code(), responseBody);
                
                // 更新请求记录为失
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "提交请求失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("提交图像编辑请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            queueStatus.getRequestId(),
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                    context.setRequestId(queueStatus.getRequestId());
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 检查请求状
     * @param requestId 请求ID
     * @return 队列状态响
     * @throws IOException 如果API调用失败
     */
    public QueueStatus checkQueueStatus(String requestId) throws IOException {
        return checkQueueStatus(requestId, null);
    }
    
    /**
     * 检查请求状态（带请求上下文
     * @param requestId 请求ID
     * @param context 请求上下文（可选）
     * @return 队列状态响
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public QueueStatus checkQueueStatus(String requestId, FalAiFluxRequestContext context) throws IOException {
        String statusEndpoint = "/fal-ai/flux-pro/requests/" + requestId + "/status";
        Request request = new Request.Builder()
                .url(API_BASE_URL + statusEndpoint)
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("状态查询API响应状 {}, 响应 {}, 响应 {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("检查状态失 状态码={}, 响应{}", response.code(), responseBody);
                
                // 更新请求记录为失
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "检查状态失 " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException(response.code() + "",response.message());
            }
            
            QueueStatus queueStatus = objectMapper.readValue(responseBody, QueueStatus.class);
            
            // 更新请求记录状
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null) {
                    updateRequestLogStatus(
                            requestLog,
                            requestId,
                            queueStatus.getStatus(),
                            queueStatus.getQueuePosition()
                    );
                }
            }
            
            return queueStatus;
        }
    }
    
    /**
     * 获取请求结果
     * @param requestId 请求ID
     * @return 生成的图像结
     * @throws IOException 如果API调用失败
     */
    public FalAiResponse getResult(String requestId) throws IOException {
        return getResult(requestId, null);
    }
    
    /**
     * 获取请求结果（带请求上下文）
     * @param requestId 请求ID
     * @param context 请求上下文（可选）
     * @return 生成的图像结
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public FalAiResponse getResult(String requestId, FalAiFluxRequestContext context) throws IOException {
        String resultEndpoint = "/fal-ai/flux-pro" + "/requests/" + requestId;
        
        log.info("结果查询请求URL: {}, 请求方法: GET", API_BASE_URL + resultEndpoint);
        
        Request request = new Request.Builder()
                .url(API_BASE_URL + resultEndpoint)
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("结果查询API响应状 {}, 响应 {}, 响应 {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("获取结果失败: 状态码={}, 响应{}", response.code(), responseBody);
                
                // 更新请求记录为失
                if (context != null && context.getRequestLogId() != null) {
                    FalAiFluxRequestLog requestLog = getRequestLog(context);
                    if (requestLog != null) {
                        failRequestLog(requestLog, "获取结果失败: " + response.code() + " " + response.message());
                        context.setFailed(true);
                    }
                }
                
                throw new BizException("获取结果失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            FalAiResponse falAiResponse = objectMapper.readValue(responseBody, FalAiResponse.class);
            
            // 更新请求记录为完
            if (context != null && context.getRequestLogId() != null) {
                FalAiFluxRequestLog requestLog = getRequestLog(context);
                if (requestLog != null && falAiResponse.getImages() != null && !falAiResponse.getImages().isEmpty()) {
                    List<String> imageUrls = new ArrayList<>();
                    for (GeneratedImage image : falAiResponse.getImages()) {
                        imageUrls.add(image.getUrl());
                    }
                    completeRequestLog(
                            requestLog,
                            responseBody,
                            objectMapper.writeValueAsString(imageUrls)
                    );
                    context.setCompleted(true);
                } else if (requestLog != null) {
                    failRequestLog(requestLog, "未生成任何图");
                    context.setFailed(true);
                }
            }
            
            return falAiResponse;
        }
    }
    
    /**
     * 取消请求
     * @param requestId 请求ID
     * @param isTextToImage 是否为文本到图像请求（true）或图像编辑请求（false
     * @return 是否成功取消
     * @throws IOException 如果API调用失败
     */
    public boolean cancelRequest(String requestId, boolean isTextToImage) throws IOException {
        String endpoint = isTextToImage ? TEXT_TO_IMAGE_ENDPOINT : IMAGE_TO_IMAGE_ENDPOINT;
        String cancelEndpoint = endpoint.replace("/fal-ai/", "/") + "/requests/" + requestId + "/cancel";
        
        log.debug("取消请求URL: {}, 请求方法: PUT", API_BASE_URL + cancelEndpoint);
        
        Request request = new Request.Builder()
                .url(API_BASE_URL + cancelEndpoint)
                .addHeader("Authorization", "Key " + apiKey)
                .put(RequestBody.create("", MediaType.parse("application/json")))
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.debug("取消请求API响应状 {}, 响应 {}, 响应 {}", 
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("取消请求失败: 状态码={}, 响应{}", response.code(), responseBody);
                throw new BizException("取消请求失败: " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            
            return Boolean.TRUE.equals(responseMap.get("success"));
        }
    }
    
    /**
     * 使用完整的状态URL检查请求状
     * @param statusUrl 完整的状态URL
     * @return 队列状态响
     * @throws IOException 如果API调用失败
     */
    public QueueStatus checkQueueStatusByUrl(String statusUrl) throws IOException {
        log.debug("使用完整URL查询状 {}, 请求方法: GET", statusUrl);
        
        // 构建URL并添加logs参数（如果URL中尚未包含）
        HttpUrl httpUrl = HttpUrl.parse(statusUrl);
        if (httpUrl == null) {
            throw new IllegalArgumentException("无效的状态URL: " + statusUrl);
        }
        
        HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
        // 检查URL是否已包含logs参数
        if (httpUrl.queryParameter("logs") == null) {
            urlBuilder.addQueryParameter("logs", "1");
        }
        
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.debug("状态查询API响应 {}, 响应{}, 响应 {}",
                    response.code(),
                    response.headers(),
                    responseBody);
            
            if (!response.isSuccessful()) {
                log.error("检查状态失 状态码={}, 响应{}", response.code(), responseBody);
                throw new BizException("检查状态失 " + response.code() + " " + response.message(),
                        response.code() + "");
            }
            
            return objectMapper.readValue(responseBody, QueueStatus.class);
        }
    }
    
    /**
     * 计算指数退避的重试延迟时间
     * @param attempt 当前尝试次数（从1开始）
     * @return 重试延迟时间（毫秒）
     */
    private long calculateRetryDelayMs(int attempt) {
        // 计算指数退避的延迟时间
        long delay = (long) (INITIAL_RETRY_DELAY_MS * Math.pow(RETRY_DELAY_MULTIPLIER, attempt - 1));
        // 确保不超过最大延迟时
        return Math.min(delay, MAX_RETRY_DELAY_MS);
    }
    
    /**
     * 生成图像（同步方法）
     * @param request 文本到图像请求参
     * @return 生成的图像结
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public FalAiResponse generateImage(TextToImageRequest request) throws IOException {
        // 提交请求
        log.info("提交文本到图像请求 {}", request.getPrompt());
        
        // 创建请求记录
        String requestParams = objectMapper.writeValueAsString(request);
        FalAiFluxRequestLog requestLog = createRequestLog(
                "TEXT_TO_IMAGE", 
                request.getPrompt(), 
                null, 
                requestParams
        );
        
        QueueStatus queueStatus = null;
        // 添加重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                queueStatus = submitTextToImageRequest(request);
                log.info("提交文本到图像请求成queueStatus: {}", JSON.toJSONString(queueStatus));
                break;
            } catch (IOException e) {
                // 更新重试次数
                updateRequestLogRetry(requestLog);
                
                if (attempt == MAX_RETRIES) {
                    log.error("提交文本到图像请求失败，重试次数用尽: {}", e.getMessage());
                    failRequestLog(requestLog, "提交请求失败: " + e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("提交文本到图像请求失败，将在{}ms后重试（{}/{} {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }
        
        if (queueStatus == null) {
            failRequestLog(requestLog, "提交文本到图像请求失败，未获取到队列状");
            throw new IOException("提交文本到图像请求失败，未获取到队列状");
        }
        
        String requestId = queueStatus.getRequestId();
        // 更新请求记录状
        updateRequestLogStatus(
                requestLog, 
                requestId, 
                queueStatus.getStatus(), 
                queueStatus.getQueuePosition()
        );
        
        // 如果是同步模式，直接返回结果
        if (request.getSyncMode() != null && request.getSyncMode()) {
            log.info("同步模式，直接获取结");
            FalAiResponse response = getResult(requestId);
            // 完成请求记录
            if (response != null && response.getImages() != null && !response.getImages().isEmpty()) {
                List<String> imageUrls = new ArrayList<>();
                for (GeneratedImage image : response.getImages()) {
                    imageUrls.add(image.getUrl());
                }
                completeRequestLog(
                        requestLog, 
                        objectMapper.writeValueAsString(response), 
                        objectMapper.writeValueAsString(imageUrls)
                );
            } else {
                failRequestLog(requestLog, "未生成任何图");
            }
            return response;
        }
        
        // 轮询状态直到完
        long startTime = System.currentTimeMillis();
        int pollCount = 0;
        
        while (System.currentTimeMillis() - startTime < MAX_WAIT_TIME_MS) {
            try {
                Thread.sleep(POLL_INTERVAL_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                failRequestLog(requestLog, "等待结果时被中断: " + e.getMessage());
                throw new IOException("等待结果时被中断", e);
            }
            
            pollCount++;
            
            // 添加重试逻辑
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                try {
                    queueStatus = checkQueueStatus(requestId);
                    // 更新请求记录状
                    updateRequestLogStatus(
                            requestLog, 
                            requestId, 
                            queueStatus.getStatus(), 
                            queueStatus.getQueuePosition()
                    );
                    break;
                } catch (IOException e) {
                    // 更新重试次数
                    updateRequestLogRetry(requestLog);
                    
                    if (attempt == MAX_RETRIES) {
                        log.error("检查状态失败，重试次数用尽: {}", e.getMessage());
                        failRequestLog(requestLog, "检查状态失 " + e.getMessage());
                        throw e;
                    }
                    long retryDelay = calculateRetryDelayMs(attempt);
                    log.warn("检查状态失败，将在{}ms后重试（{}/{} {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                        throw new IOException("等待重试时被中断", ie);
                    }
                }
            }
            
            // 输出状态日
            if ("IN_QUEUE".equals(queueStatus.getStatus())) {
                log.info("请求在队列中，位 {}, 轮询次数: {}", 
                        queueStatus.getQueuePosition() != null ? queueStatus.getQueuePosition() : "未知", 
                        pollCount);
            } else if ("IN_PROGRESS".equals(queueStatus.getStatus())) {
                log.info("请求正在处理.., 轮询次数: {}", pollCount);
            }
            
            // 检查状
            if ("COMPLETED".equals(queueStatus.getStatus())) {
                log.info("请求已完成，总轮询次 {}, 总耗时: {}ms", pollCount, System.currentTimeMillis() - startTime);
                break;
            } else if (!"IN_QUEUE".equals(queueStatus.getStatus()) && !"IN_PROGRESS".equals(queueStatus.getStatus())) {
                log.error("请求失败，状 {}", queueStatus.getStatus());
                failRequestLog(requestLog, "生成图像失败，未知状 " + queueStatus.getStatus());
                throw new IOException("生成图像失败，未知状 " + queueStatus.getStatus());
            }
        }
        
        if (!"COMPLETED".equals(queueStatus.getStatus())) {
            log.error("请求超时，已等待{}ms", MAX_WAIT_TIME_MS);
            failRequestLog(requestLog, "生成图像请求超时，状 " + queueStatus.getStatus());
            throw new IOException("生成图像请求超时，状 " + queueStatus.getStatus());
        }
        
        // 获取结果
        log.info("获取生成的图像结");
        FalAiResponse response = null;
        
        // 添加重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                response = getResult(requestId);
                break;
            } catch (IOException e) {
                // 更新重试次数
                updateRequestLogRetry(requestLog);
                
                if (attempt == MAX_RETRIES) {
                    log.error("获取结果失败，重试次数用尽: {}", e.getMessage());
                    failRequestLog(requestLog, "获取结果失败: " + e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("获取结果失败，将在{}ms后重试（{}/{} {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }
        
        if (response != null && response.getImages() != null && !response.getImages().isEmpty()) {
            log.info("成功生成{}张图", response.getImages().size());
            List<String> imageUrls = new ArrayList<>();
            for (int i = 0; i < response.getImages().size(); i++) {
                GeneratedImage image = response.getImages().get(i);
                imageUrls.add(image.getUrl());
                log.info("图像 #{}: 尺寸={}x{}, URL={}", i + 1, image.getWidth(), image.getHeight(), image.getUrl());
            }
            
            // 完成请求记录
            completeRequestLog(
                    requestLog, 
                    objectMapper.writeValueAsString(response), 
                    objectMapper.writeValueAsString(imageUrls)
            );
        } else {
            log.warn("未生成任何图");
            failRequestLog(requestLog, "未生成任何图");
        }
        
        return response;
    }
    
    /**
     * 异步生成图像
     * @param request 文本到图像请求参
     * @return 包含结果的CompletableFuture
     */
    public CompletableFuture<FalAiResponse> generateImageAsync(TextToImageRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateImage(request);
            } catch (IOException e) {
                throw new RuntimeException("生成图像失败", e);
            }
        });
    }
    
    /**
     * 编辑图像（同步方法）
     * @param request 图像编辑请求参数
     * @return 编辑后的图像结果
     * @throws IOException 如果API调用失败
     */
    @Transactional
    public FalAiResponse editImage(ImageToImageRequest request) throws IOException {
        // 提交请求
        log.info("提交图像编辑请求: {}, 图像URL: {}", request.getPrompt(), request.getImageUrl());
        
        // 创建请求记录
        String requestParams = objectMapper.writeValueAsString(request);
        FalAiFluxRequestLog requestLog = createRequestLog(
                "IMAGE_TO_IMAGE", 
                request.getPrompt(), 
                request.getImageUrl(), 
                requestParams
        );
        
        QueueStatus queueStatus = null;
        // 添加重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                queueStatus = submitImageToImageRequest(request);
                break;
            } catch (IOException e) {
                // 更新重试次数
                updateRequestLogRetry(requestLog);
                
                if (attempt == MAX_RETRIES) {
                    log.error("提交请求失败，重试次数用尽: {}", e.getMessage());
                    failRequestLog(requestLog, "提交请求失败: " + e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("提交图像编辑请求失败，将在{}ms后重试（{}/{} {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }
        
        if (queueStatus == null) {
            failRequestLog(requestLog, "提交图像编辑请求失败，未获取到队列状");
            throw new IOException("提交图像编辑请求失败，未获取到队列状");
        }
        
        String requestId = queueStatus.getRequestId();
        log.info("请求ID: {}, 初始状 {}", requestId, queueStatus.getStatus());
        
        // 更新请求记录状
        updateRequestLogStatus(
                requestLog, 
                requestId, 
                queueStatus.getStatus(), 
                queueStatus.getQueuePosition()
        );
        
        // 如果是同步模式，直接返回结果
        if (request.getSyncMode() != null && request.getSyncMode()) {
            log.info("同步模式，直接获取结");
            FalAiResponse response = getResult(requestId);
            // 完成请求记录
            if (response != null && response.getImages() != null && !response.getImages().isEmpty()) {
                List<String> imageUrls = new ArrayList<>();
                for (GeneratedImage image : response.getImages()) {
                    imageUrls.add(image.getUrl());
                }
                completeRequestLog(
                        requestLog, 
                        objectMapper.writeValueAsString(response), 
                        objectMapper.writeValueAsString(imageUrls)
                );
            } else {
                failRequestLog(requestLog, "未生成任何图");
            }
            return response;
        }
        
        // 轮询状态直到完
        long startTime = System.currentTimeMillis();
        int pollCount = 0;
        
        while (System.currentTimeMillis() - startTime < MAX_WAIT_TIME_MS) {
            try {
                Thread.sleep(POLL_INTERVAL_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                failRequestLog(requestLog, "等待结果时被中断: " + e.getMessage());
                throw new IOException("等待结果时被中断", e);
            }
            
            pollCount++;
            
            // 添加重试逻辑
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                try {
                    queueStatus = checkQueueStatus(requestId);
                    // 更新请求记录状
                    updateRequestLogStatus(
                            requestLog, 
                            requestId, 
                            queueStatus.getStatus(), 
                            queueStatus.getQueuePosition()
                    );
                    break;
                } catch (IOException e) {
                    // 更新重试次数
                    updateRequestLogRetry(requestLog);
                    
                    if (attempt == MAX_RETRIES) {
                        log.error("检查状态失败，重试次数用尽: {}", e.getMessage());
                        failRequestLog(requestLog, "检查状态失 " + e.getMessage());
                        throw e;
                    }
                    long retryDelay = calculateRetryDelayMs(attempt);
                    log.warn("检查状态失败，将在{}ms后重试（{}/{} {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                        throw new IOException("等待重试时被中断", ie);
                    }
                }
            }
            
            // 输出状态日
            if ("IN_QUEUE".equals(queueStatus.getStatus())) {
                log.info("请求在队列中，位 {}, 轮询次数: {}", 
                        queueStatus.getQueuePosition() != null ? queueStatus.getQueuePosition() : "未知", 
                        pollCount);
            } else if ("IN_PROGRESS".equals(queueStatus.getStatus())) {
                log.info("请求正在处理.., 轮询次数: {}", pollCount);
            }
            
            // 检查状
            if ("COMPLETED".equals(queueStatus.getStatus())) {
                log.info("请求已完成，总轮询次 {}, 总耗时: {}ms", pollCount, System.currentTimeMillis() - startTime);
                break;
            } else if (!"IN_QUEUE".equals(queueStatus.getStatus()) && !"IN_PROGRESS".equals(queueStatus.getStatus())) {
                log.error("请求失败，状 {}", queueStatus.getStatus());
                failRequestLog(requestLog, "编辑图像失败，未知状 " + queueStatus.getStatus());
                throw new IOException("编辑图像失败，未知状 " + queueStatus.getStatus());
            }
        }
        
        if (!"COMPLETED".equals(queueStatus.getStatus())) {
            log.error("请求超时，已等待{}ms", MAX_WAIT_TIME_MS);
            failRequestLog(requestLog, "编辑图像请求超时，状 " + queueStatus.getStatus());
            throw new IOException("编辑图像请求超时，状 " + queueStatus.getStatus());
        }
        
        // 获取结果
        log.info("获取编辑后的图像结果");
        FalAiResponse response = null;
        
        // 添加重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                response = getResult(requestId);
                break;
            } catch (IOException e) {
                // 更新重试次数
                updateRequestLogRetry(requestLog);
                
                if (attempt == MAX_RETRIES) {
                    log.error("获取结果失败，已重试{}", MAX_RETRIES);
                    failRequestLog(requestLog, "获取结果失败: " + e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("获取结果失败，将在{}ms后重试（{}/{} {}", retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    failRequestLog(requestLog, "等待重试时被中断: " + ie.getMessage());
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }
        
        if (response != null && response.getImages() != null && !response.getImages().isEmpty()) {
            log.info("成功编辑{}张图", response.getImages().size());
            List<String> imageUrls = new ArrayList<>();
            for (int i = 0; i < response.getImages().size(); i++) {
                GeneratedImage image = response.getImages().get(i);
                imageUrls.add(image.getUrl());
                log.info("图像 #{}: 尺寸={}x{}, URL={}", i + 1, image.getWidth(), image.getHeight(), image.getUrl());
            }
            
            // 完成请求记录
            completeRequestLog(
                    requestLog, 
                    objectMapper.writeValueAsString(response), 
                    objectMapper.writeValueAsString(imageUrls)
            );
        } else {
            log.warn("未生成任何编辑后的图");
            failRequestLog(requestLog, "未生成任何编辑后的图");
        }
        
        return response;
    }
    
    /**
     * 异步编辑图像
     * @param request 图像编辑请求参数
     * @return 包含结果的CompletableFuture
     */
    public CompletableFuture<FalAiResponse> editImageAsync(ImageToImageRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return editImage(request);
            } catch (IOException e) {
                throw new RuntimeException("编辑图像失败", e);
            }
        });
    }
    
    /**
     * 创建并保存请求记
     * @param requestType 请求类型（TEXT_TO_IMAGE IMAGE_TO_IMAGE
     * @param prompt 提示
     * @param imageUrl 图像URL（对于图像到图像请求
     * @param requestParams 请求参数JSON
     * @return 创建的请求记
     */
    private FalAiFluxRequestLog createRequestLog(String requestType, String prompt, String imageUrl, String requestParams) {
        final FalAiFluxRequestLog requestLog = FalAiFluxRequestLog.builder()
                .requestType(requestType)
                .prompt(prompt)
                .imageUrl(imageUrl)
                .status("PENDING")
                .requestParams(requestParams)
                .startTime(new Date())
                .retryCount(0)
                .build();
        
        if (transactionTemplate != null) {
            // 使用编程式事务管
            transactionTemplate.execute(status -> {
                try {
                    int rows = falAiFluxRequestLogMapper.insert(requestLog);
                    log.info("创建请求记录成功，ID: {}, 影响行数: {}", requestLog.getId(), rows);
                    return true;
                } catch (Exception e) {
                    log.error("创建请求记录失败: {}", e.getMessage(), e);
                    status.setRollbackOnly();
                    throw new RuntimeException("创建请求记录失败", e);
                }
            });
        } else {
            // 回退到直接调
            try {
                int rows = falAiFluxRequestLogMapper.insert(requestLog);
                log.info("创建请求记录成功，ID: {}, 影响行数: {}", requestLog.getId(), rows);
            } catch (Exception e) {
                log.error("创建请求记录失败: {}", e.getMessage(), e);
                throw e;
            }
        }
        
        return requestLog;
    }
    
    /**
     * 创建请求上下文并保存请求记录
     * @param context 请求上下
     * @param prompt 提示
     * @param imageUrl 图像URL（对于图像到图像请求
     * @param requestParams 请求参数JSON
     * @return 更新后的请求上下
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public FalAiFluxRequestContext createRequestContext(FalAiFluxRequestContext context, String prompt, String imageUrl, String requestParams) {
        FalAiFluxRequestLog requestLog = FalAiFluxRequestLog.builder()
                .requestType(context.getRequestType())
                .prompt(prompt)
                .imageUrl(imageUrl)
                .status("PENDING")
                .requestParams(requestParams)
                .startTime(new Date())
                .retryCount(0)
                .build();
        
        try {
            int rows = falAiFluxRequestLogMapper.insert(requestLog);
            log.info("创建请求上下文记录成功，ID: {}, 类型: {}, 影响行数: {}", 
                    requestLog.getId(), context.getRequestType(), rows);
        } catch (Exception e) {
            log.error("创建请求上下文记录失 {}", e.getMessage(), e);
            throw e;
        }
        context.setRequestLogId(requestLog.getId());
        return context;
    }
    
    /**
     * 获取请求记录
     * @param context 请求上下
     * @return 请求记录
     */
    private FalAiFluxRequestLog getRequestLog(FalAiFluxRequestContext context) {
        if (context == null || context.getRequestLogId() == null) {
            return null;
        }
        return falAiFluxRequestLogMapper.selectById(context.getRequestLogId());
    }
    
    /**
     * 更新请求记录状
     * @param requestLog 请求记录
     * @param requestId fal.ai请求ID
     * @param status 状
     * @param queuePosition 队列位置
     */
    private void updateRequestLogStatus(FalAiFluxRequestLog requestLog, String requestId, String status, Integer queuePosition) {
        requestLog.setRequestId(requestId)
                .setStatus(status)
                .setQueuePosition(queuePosition)
                .setUpdateTime(new Date());
        
        try {
            int rows = falAiFluxRequestLogMapper.updateById(requestLog);
            log.info("更新请求记录状态成功，ID: {}, 状 {}, 影响行数: {}", requestLog.getId(), status, rows);
        } catch (Exception e) {
            log.error("更新请求记录状态失败，ID: {}: {}", requestLog.getId(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 更新请求记录重试次数
     * @param requestLog 请求记录
     */
    private void updateRequestLogRetry(FalAiFluxRequestLog requestLog) {
        requestLog.setRetryCount(requestLog.getRetryCount() + 1)
                .setUpdateTime(new Date());
        
        falAiFluxRequestLogMapper.updateById(requestLog);
    }
    
    /**
     * 完成请求记录
     * @param requestLog 请求记录
     * @param response 响应数据
     * @param resultImageUrls 结果图像URLs
     */
    private void completeRequestLog(FalAiFluxRequestLog requestLog, String response, String resultImageUrls) {
        Date endTime = new Date();
        long processingTime = endTime.getTime() - requestLog.getStartTime().getTime();
        
        requestLog.setStatus("COMPLETED")
                .setResponseData(response)
                .setResultImageUrls(resultImageUrls)
                .setEndTime(endTime)
                .setProcessingTime(processingTime)
                .setUpdateTime(endTime);
        
        falAiFluxRequestLogMapper.updateById(requestLog);
    }
    
    /**
     * 失败请求记录
     * @param requestLog 请求记录
     * @param errorMessage 错误信息
     */
    private void failRequestLog(FalAiFluxRequestLog requestLog, String errorMessage) {
        Date endTime = new Date();
        long processingTime = endTime.getTime() - requestLog.getStartTime().getTime();
        
        requestLog.setStatus("FAILED")
                .setErrorMessage(errorMessage)
                .setEndTime(endTime)
                .setProcessingTime(processingTime)
                .setUpdateTime(endTime);
        
        falAiFluxRequestLogMapper.updateById(requestLog);
    }

    /**
     * 上传文件到fal.ai存储
     * 注意：此方法需要额外的API支持，可能需要根据实际API调整
     * @param file 要上传的文件
     * @return 上传的文件URL
     * @throws IOException 如果API调用失败
     */
    public String uploadFile(File file) throws IOException {
        // 此处为示例实现，实际上传功能可能需要根据fal.ai的存储API进行调整
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(),
                        RequestBody.create(file, MediaType.parse("application/octet-stream")))
                .build();
        
        Request request = new Request.Builder()
                .url("https://api.fal.ai/v1/storage/upload") // 假设的上传端
                .addHeader("Authorization", "Key " + apiKey)
                .post(requestBody)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("上传文件失败: " + response.code() + " " + response.message());
            }

            assert response.body() != null;
            Map<String, Object> responseMap = objectMapper.readValue(response.body().string(), Map.class);
            
            return (String) responseMap.get("url");
        }
    }
    
    /**
     * 文本到图像请求参数类
     */
    @Getter
    public static class TextToImageRequest {
        private String prompt;
        private String cnPrompt;
        private Integer seed;
        @JsonProperty("guidance_scale")
        private Double guidanceScale;
        @JsonProperty("sync_mode")
        private Boolean syncMode;
        @JsonProperty("num_images")
        private Integer numImages;
        @JsonProperty("safety_tolerance")
        private String safetyTolerance;
        @JsonProperty("output_format")
        private String outputFormat;
        @JsonProperty("aspect_ratio")
        private String aspectRatio;
        
        private TextToImageRequest() {}

        public static class Builder {
            private final TextToImageRequest request = new TextToImageRequest();
            
            public Builder prompt(String prompt) {
                request.prompt = prompt;
                return this;
            }

            public Builder cnPrompt(String cnPrompt) {
                request.cnPrompt = cnPrompt;
                return this;
            }
            
            public Builder seed(Integer seed) {
                request.seed = seed;
                return this;
            }
            
            public Builder guidanceScale(Double guidanceScale) {
                request.guidanceScale = guidanceScale;
                return this;
            }
            
            public Builder syncMode(Boolean syncMode) {
                request.syncMode = syncMode;
                return this;
            }
            
            public Builder numImages(Integer numImages) {
                request.numImages = numImages;
                return this;
            }
            
            public Builder safetyTolerance(String safetyTolerance) {
                request.safetyTolerance = safetyTolerance;
                return this;
            }
            
            public Builder outputFormat(String outputFormat) {
                request.outputFormat = outputFormat;
                return this;
            }
            
            public Builder aspectRatio(String aspectRatio) {
                request.aspectRatio = aspectRatio;
                return this;
            }
            
            public TextToImageRequest build() {
                if (request.prompt == null || request.prompt.isEmpty()) {
                    throw new IllegalStateException("prompt不能为空");
                }
                return request;
            }
        }
    }
    
    /**
     * 图像编辑请求参数
     */
    @Setter
    @Getter
    public static class ImageToImageRequest {
        private String prompt;
        private String cnPrompt;
        @JsonProperty("image_url")
        private String imageUrl;
        private Integer seed;
        @JsonProperty("guidance_scale")
        private Double guidanceScale;
        @JsonProperty("sync_mode")
        private Boolean syncMode;
        @JsonProperty("num_images")
        private Integer numImages;
        @JsonProperty("safety_tolerance")
        private String safetyTolerance;
        @JsonProperty("output_format")
        private String outputFormat;
        @JsonProperty("aspect_ratio")
        private String aspectRatio;
        
        public ImageToImageRequest() {}

        public static class Builder {
            private final ImageToImageRequest request = new ImageToImageRequest();
            
            public Builder prompt(String prompt) {
                request.prompt = prompt;
                return this;
            }

            public Builder cnPrompt(String cnPrompt) {
                request.cnPrompt = cnPrompt;
                return this;
            }
            
            public Builder imageUrl(String imageUrl) {
                request.imageUrl = imageUrl;
                return this;
            }
            
            public Builder seed(Integer seed) {
                request.seed = seed;
                return this;
            }
            
            public Builder guidanceScale(Double guidanceScale) {
                request.guidanceScale = guidanceScale;
                return this;
            }
            
            public Builder syncMode(Boolean syncMode) {
                request.syncMode = syncMode;
                return this;
            }
            
            public Builder numImages(Integer numImages) {
                request.numImages = numImages;
                return this;
            }
            
            public Builder safetyTolerance(String safetyTolerance) {
                request.safetyTolerance = safetyTolerance;
                return this;
            }
            
            public Builder outputFormat(String outputFormat) {
                request.outputFormat = outputFormat;
                return this;
            }
            
            public Builder aspectRatio(String aspectRatio) {
                request.aspectRatio = aspectRatio;
                return this;
            }
            
            public ImageToImageRequest build() {
                if (request.prompt == null || request.prompt.isEmpty()) {
                    throw new IllegalStateException("prompt不能为空");
                }
                if (request.imageUrl == null || request.imageUrl.isEmpty()) {
                    throw new IllegalStateException("imageUrl不能为空");
                }
                return request;
            }
        }
    }
    
    /**
     * 队列状态响应类
     */
    @Setter
    @Getter
    public static class QueueStatus {
        private String status;
        @JsonProperty("request_id")
        private String requestId;
        @JsonProperty("response_url")
        private String responseUrl;
        @JsonProperty("status_url")
        private String statusUrl;
        @JsonProperty("cancel_url")
        private String cancelUrl;
        private Object logs;
        private Map<String, Object> metrics;
        @JsonProperty("queue_position")
        private Integer queuePosition;

    }
    
    /**
     * 生成的图像信息类
     */
    @Setter
    @Getter
    public static class GeneratedImage {
        private String url;
        @JsonProperty("content_type")
        private String contentType;
        private Integer width;
        private Integer height;
        @JsonProperty("file_name")
        private String fileName;
        @JsonProperty("file_size")
        private Integer fileSize;

    }
    
    /**
     * API响应
     */
    @Setter
    @Getter
    public static class FalAiResponse {
        private List<GeneratedImage> images;
        private Map<String, Object> timings;
        private Integer seed;
        @JsonProperty("has_nsfw_concepts")
        private List<Boolean> hasNsfwConcepts;
        private String prompt;

    }
    
    /**
     * 示例使用方法
     */
    public static void main(String[] args) {
        try {
            // 创建客户
            FalAiFluxClient client = new FalAiFluxClient("58371be0-d55d-4542-a7ce-7018b4aa0226:44e655c15df8eca02ab38a127e299b94");
            
            // 方法1：使用高级封装方
            System.out.println("=== 方法1：使用高级封装方===");
            
            // 文本到图像示
            TextToImageRequest textToImageRequest = new TextToImageRequest.Builder()
                    .prompt("一只可爱的猫咪坐在窗台")
                    .numImages(1)
                    .aspectRatio("1:1")
                    .build();
            
            System.out.println("正在生成图像...");
            FalAiResponse textToImageResponse = client.generateImage(textToImageRequest);
            System.out.println("生成的图像URL: " + textToImageResponse.getImages().get(0).getUrl());
            
            // 图像编辑示例
            ImageToImageRequest imageToImageRequest = new ImageToImageRequest.Builder()
                    .prompt("给猫咪添加一顶红色的帽子")
                    .imageUrl(textToImageResponse.getImages().get(0).getUrl())
                    .numImages(1)
                    .build();
            
            System.out.println("正在编辑图像...");
            FalAiResponse imageToImageResponse = client.editImage(imageToImageRequest);
            System.out.println("编辑后的图像URL: " + imageToImageResponse.getImages().get(0).getUrl());
            
            // 方法2：使用底层API方法（带请求记录
            System.out.println("\n=== 方法2：使用底层API方法（带请求记录===");
            
            // 创建请求上下
            FalAiFluxRequestContext context = FalAiFluxRequestContext.createTextToImageContext("一只可爱的狗狗在草地上");
            
            // 创建请求
            TextToImageRequest request = new TextToImageRequest.Builder()
                    .prompt("一只可爱的狗狗在草地上")
                    .numImages(1)
                    .aspectRatio("1:1")
                    .build();
            
            // 提交请求
            System.out.println("提交文本到图像请..");
            QueueStatus queueStatus = client.submitTextToImageRequest(request, context);
            System.out.println("请求ID: " + queueStatus.getRequestId());
            
            // 轮询状
            String requestId = queueStatus.getRequestId();
            int maxPolls = 30;
            int pollCount = 0;
            
            while (pollCount < maxPolls) {
                Thread.sleep(2000); // 等待2
                pollCount++;
                

                queueStatus = client.checkQueueStatus(requestId, context);
                
                if ("COMPLETED".equals(queueStatus.getStatus())) {
                    System.out.println("请求已完");
                    break;
                } else if ("IN_QUEUE".equals(queueStatus.getStatus())) {
                    System.out.println("请求在队列中，位 " + queueStatus.getQueuePosition());
                } else if ("IN_PROGRESS".equals(queueStatus.getStatus())) {
                    System.out.println("请求正在处理..");
                } else {
                    System.out.println("请求状 " + queueStatus.getStatus());
                    break;
                }
            }
            
            // 获取结果
            if ("COMPLETED".equals(queueStatus.getStatus())) {
                System.out.println("获取结果...");
                FalAiResponse response = client.getResult(requestId, context);
                
                if (response.getImages() != null && !response.getImages().isEmpty()) {
                    System.out.println("生成的图像URL: " + response.getImages().get(0).getUrl());
                } else {

                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
} 
