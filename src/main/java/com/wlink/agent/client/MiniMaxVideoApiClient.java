package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.minimax.MiniMaxVideoCallbackRequest;
import com.wlink.agent.client.model.minimax.MiniMaxVideoGenerationRequest;
import com.wlink.agent.client.model.minimax.MiniMaxVideoGenerationResponse;
import com.wlink.agent.client.model.minimax.MiniMaxVideoStatusResponse;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MiniMax视频生成API客户端
 * 使用JDK 17新特性实现
 */
@RefreshScope
@Slf4j
@Component
public class MiniMaxVideoApiClient {
    
    private static final String DEFAULT_API_URL = "https://api.minimaxi.com/v1/video_generation";
    private static final String DEFAULT_STATUS_URL = "https://api.minimaxi.com/v1/query/video_generation";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    
    @Resource
    private ObjectMapper objectMapper;

    private final OkHttpClient httpClient;
    
    @Value("${minimax.api.url:" + DEFAULT_API_URL + "}")
    private String apiUrl;
    
    @Value("${minimax.api.status-url:" + DEFAULT_STATUS_URL + "}")
    private String statusUrl;

    @Value("${minimax.api-key:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}")
    private String defaultApiKey;
    
    @Value("${minimax.api.timeout:60}")
    private int timeoutSeconds;

    //回调地址
    @Value("${minimax.api.callback-url:https://dev.neodomain.cn/agent/video/minimax/callback}")
    private String callbackUrl;

    @Autowired
    private AiVideoGenerationRecordMapper videoGenerationRecordMapper;
    
    public MiniMaxVideoApiClient() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
        
        // 配置ObjectMapper
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    /**
     * 提交视频生成请求（使用默认API密钥）
     * 
     * @param request 视频生成请求
     * @return 异步响应结果
     */
    public CompletableFuture<MiniMaxVideoGenerationResponse> submitVideoGeneration(MiniMaxVideoGenerationRequest request) {
        return submitVideoGeneration(request, defaultApiKey);
    }
    
    /**
     * 提交视频生成请求
     * 
     * @param request 视频生成请求
     * @param apiKey API密钥
     * @return 异步响应结果
     */
    public CompletableFuture<MiniMaxVideoGenerationResponse> submitVideoGeneration(MiniMaxVideoGenerationRequest request, String apiKey) {
        return CompletableFuture.supplyAsync(() -> {
            AiVideoGenerationRecordPo record = null;
            try {
                log.info("提交MiniMax视频生成请求: model={}, prompt={}", request.getModel(), request.getPrompt());

                // 1. 创建ai_video_generation_record记录
                record = createVideoGenerationRecord(request);
                log.info("创建MiniMax视频生成记录: recordId={}, model={}", record.getId(), request.getModel());

                request.setCallbackUrl(callbackUrl);
                String requestJson = objectMapper.writeValueAsString(request);
                log.info("MiniMax视频生成请求JSON: {}", requestJson);
                RequestBody requestBody = RequestBody.create(requestJson, JSON_MEDIA_TYPE);
                Request httpRequest = new Request.Builder()
                        .url(apiUrl)
                        .post(requestBody)
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("Content-Type", "application/json")
                        .build();
                
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    log.info("MiniMax视频生成响应: status={}, body={}", response.code(), responseBody);
                    
                    if (!response.isSuccessful()) {
                        log.error("MiniMax视频生成请求失败: status={}, body={}", response.code(), responseBody);
                        throw new BizException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    MiniMaxVideoGenerationResponse result = objectMapper.readValue(responseBody, MiniMaxVideoGenerationResponse.class);

                    if (result.isSuccess()) {
                        log.info("MiniMax视频生成请求提交成功: taskId={}", result.getTaskId());
                        // 2. 更新记录状态为running，并设置taskId
                        updateVideoGenerationRecord(record.getId(), result.getTaskId(), "running", null, null);
                    } else {
                        log.error("MiniMax视频生成请求失败: statusCode={}, message={}",
                                result.getStatusCode(), result.getErrorMessage());
                        // 3. 更新记录状态为failed
                        updateVideoGenerationRecord(record.getId(), null, "failed",
                                String.valueOf(result.getStatusCode()), result.getErrorMessage());
                        throw new BizException("视频生成请求失败: " + result.getErrorMessage());
                    }

                    return result;
                }
            } catch (JsonProcessingException e) {
                log.error("序列化MiniMax视频生成请求失败", e);
                if (record != null) {
                    updateVideoGenerationRecord(record.getId(), null, "failed", null, "序列化请求失败: " + e.getMessage());
                }
                throw new BizException("序列化请求失败");
            } catch (IOException e) {
                log.error("MiniMax视频生成HTTP请求失败", e);
                if (record != null) {
                    updateVideoGenerationRecord(record.getId(), null, "failed", null, "HTTP请求失败: " + e.getMessage());
                }
                throw new BizException("HTTP请求失败");
            } catch (Exception e) {
                log.error("MiniMax视频生成请求异常", e);
                if (record != null) {
                    updateVideoGenerationRecord(record.getId(), null, "failed", null, "请求异常: " + e.getMessage());
                }
                throw e;
            }
        });
    }
    
    /**
     * 查询视频生成状态（使用默认API密钥）
     * 
     * @param taskId 任务ID
     * @return 异步状态响应
     */
    public CompletableFuture<MiniMaxVideoStatusResponse> getVideoStatus(String taskId) {
        return getVideoStatus(taskId, defaultApiKey);
    }
    
    /**
     * 查询视频生成状态
     *
     * @param taskId 任务ID
     * @param apiKey API密钥
     * @return 异步状态响应
     */
    public CompletableFuture<MiniMaxVideoStatusResponse> getVideoStatus(String taskId, String apiKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("查询MiniMax视频生成状态: taskId={}", taskId);

                HttpUrl url = HttpUrl.parse(statusUrl).newBuilder()
                        .addQueryParameter("task_id", taskId)
                        .build();

                Request httpRequest = new Request.Builder()
                        .url(url)
                        .get()
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .build();

                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    log.debug("MiniMax视频状态查询响应: status={}, body={}", response.code(), responseBody);

                    if (!response.isSuccessful()) {
                        log.error("MiniMax视频状态查询失败: status={}, body={}", response.code(), responseBody);
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }

                    MiniMaxVideoStatusResponse result = objectMapper.readValue(responseBody, MiniMaxVideoStatusResponse.class);
                    log.debug("MiniMax视频状态: taskId={}, status={}", taskId, result.getStatus());

                    return result;
                }
            } catch (IOException e) {
                log.error("MiniMax视频状态查询HTTP请求失败: taskId={}", taskId, e);
                throw new RuntimeException("HTTP请求失败", e);
            }
        });
    }

    /**
     * 便捷方法：文生视频
     *
     * @param model 模型名称
     * @param prompt 提示词
     * @param duration 视频时长（秒）
     * @param resolution 分辨率
     * @return 异步响应结果
     */
    public CompletableFuture<MiniMaxVideoGenerationResponse> textToVideo(String model, String prompt,
                                                                         Integer duration, String resolution) {
        MiniMaxVideoGenerationRequest request = MiniMaxVideoGenerationRequest.buildTextToVideoRequest(
                model, prompt, duration, resolution);
        return submitVideoGeneration(request);
    }

    /**
     * 便捷方法：图生视频
     *
     * @param model 模型名称
     * @param prompt 提示词
     * @param firstFrameImage 首帧图片
     * @param duration 视频时长（秒）
     * @param resolution 分辨率
     * @return 异步响应结果
     */
    public CompletableFuture<MiniMaxVideoGenerationResponse> imageToVideo(String model, String prompt,
                                                                          String firstFrameImage, Integer duration, String resolution) {
        MiniMaxVideoGenerationRequest request = MiniMaxVideoGenerationRequest.buildImageToVideoRequest(
                model, prompt, firstFrameImage, duration, resolution);
        return submitVideoGeneration(request);
    }

    /**
     * 便捷方法：主体参考视频生成
     *
     * @param prompt 提示词
     * @param subjectImage 主体图片
     * @param subjectDescription 主体描述
     * @param duration 视频时长（秒）
     * @param resolution 分辨率
     * @return 异步响应结果
     */
    public CompletableFuture<MiniMaxVideoGenerationResponse> subjectReferenceVideo(String prompt, String subjectImage,
                                                                                   String subjectDescription, Integer duration, String resolution) {
        MiniMaxVideoGenerationRequest request = MiniMaxVideoGenerationRequest.buildSubjectReferenceVideoRequest(
                "S2V-01", prompt, subjectImage, subjectDescription, duration, resolution);
        return submitVideoGeneration(request);
    }

    /**
     * 处理回调验证请求
     *
     * @param callbackRequest 回调请求
     * @return 验证响应
     */
    public MiniMaxVideoCallbackRequest handleCallbackVerification(MiniMaxVideoCallbackRequest callbackRequest) {
        if (callbackRequest.isVerificationRequest()) {
            log.info("处理MiniMax回调验证请求: challenge={}", callbackRequest.getChallenge());
            return MiniMaxVideoCallbackRequest.createVerificationResponse(callbackRequest.getChallenge());
        }

        log.info("处理MiniMax视频生成回调: taskId={}, status={}",
                callbackRequest.getTaskId(), callbackRequest.getStatus());
        return callbackRequest;
    }

    /**
     * 运镜控制工具类
     */
    public static class CameraMovement {

        // 左右移动
        public static final String LEFT_MOVE = "[左移]";
        public static final String RIGHT_MOVE = "[右移]";

        // 左右摇摆
        public static final String LEFT_PAN = "[左摇]";
        public static final String RIGHT_PAN = "[右摇]";

        // 推拉镜头
        public static final String PUSH_IN = "[推进]";
        public static final String PULL_OUT = "[拉远]";

        // 升降镜头
        public static final String MOVE_UP = "[上升]";
        public static final String MOVE_DOWN = "[下降]";

        // 上下摇摆
        public static final String TILT_UP = "[上摇]";
        public static final String TILT_DOWN = "[下摇]";

        // 变焦
        public static final String ZOOM_IN = "[变焦推近]";
        public static final String ZOOM_OUT = "[变焦拉远]";

        // 其他运镜
        public static final String SHAKE = "[晃动]";
        public static final String FOLLOW = "[跟随]";
        public static final String FIXED = "[固定]";

        /**
         * 组合多个运镜指令
         *
         * @param movements 运镜指令数组
         * @return 组合后的运镜指令
         */
        public static String combine(String... movements) {
            if (movements == null || movements.length == 0) {
                return "";
            }

            if (movements.length == 1) {
                return movements[0];
            }

            // 移除方括号并用逗号连接
            StringBuilder combined = new StringBuilder("[");
            for (int i = 0; i < movements.length; i++) {
                String movement = movements[i];
                if (movement.startsWith("[") && movement.endsWith("]")) {
                    movement = movement.substring(1, movement.length() - 1);
                }
                combined.append(movement);
                if (i < movements.length - 1) {
                    combined.append(",");
                }
            }
            combined.append("]");

            return combined.toString();
        }

        /**
         * 在提示词中插入运镜指令
         *
         * @param prompt 原始提示词
         * @param movement 运镜指令
         * @param position 插入位置（0表示开头，-1表示结尾）
         * @return 插入运镜指令后的提示词
         */
        public static String insertMovement(String prompt, String movement, int position) {
            if (prompt == null || prompt.isEmpty()) {
                return movement;
            }

            if (movement == null || movement.isEmpty()) {
                return prompt;
            }

            if (position == 0) {
                return movement + prompt;
            } else if (position == -1) {
                return prompt + movement;
            } else {
                if (position >= prompt.length()) {
                    return prompt + movement;
                }
                return prompt.substring(0, position) + movement + prompt.substring(position);
            }
        }
    }

    /**
     * 创建视频生成记录
     *
     * @param request 视频生成请求
     * @return 创建的记录
     */
    private AiVideoGenerationRecordPo createVideoGenerationRecord(MiniMaxVideoGenerationRequest request) {
        try {
            AiVideoGenerationRecordPo record = AiVideoGenerationRecordPo.builder()
                    .userId(getCurrentUserId())
                    .model(request.getModel())
                    .prompt(request.getPrompt())
                    .sourceType(determineSourceType(request))
                    .firstFrameUrl(request.getFirstFrameImage())
                    .status("queued")
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();

            videoGenerationRecordMapper.insert(record);
            log.info("创建MiniMax视频生成记录成功: recordId={}, model={}, prompt={}",
                    record.getId(), request.getModel(), request.getPrompt());
            return record;
        } catch (Exception e) {
            log.error("创建MiniMax视频生成记录失败: model={}, prompt={}",
                    request.getModel(), request.getPrompt(), e);
            throw new RuntimeException("创建视频生成记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新视频生成记录
     *
     * @param recordId 记录ID
     * @param taskId 任务ID
     * @param status 状态
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    private void updateVideoGenerationRecord(Long recordId, String taskId, String status,
                                           String errorCode, String errorMessage) {
        try {
            AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
            updateRecord.setId(recordId);
            updateRecord.setStatus(status);
            updateRecord.setUpdateTime(LocalDateTime.now());

            if (StringUtils.hasText(taskId)) {
                updateRecord.setTaskId(taskId);
            }
            if (StringUtils.hasText(errorCode)) {
                updateRecord.setErrorCode(errorCode);
            }
            if (StringUtils.hasText(errorMessage)) {
                updateRecord.setErrorMsg(errorMessage);
            }

            videoGenerationRecordMapper.updateById(updateRecord);
            log.info("更新MiniMax视频生成记录成功: recordId={}, taskId={}, status={}",
                    recordId, taskId, status);
        } catch (Exception e) {
            log.error("更新MiniMax视频生成记录失败: recordId={}, taskId={}, status={}",
                    recordId, taskId, status, e);
        }
    }

    /**
     * 确定生成源类型
     *
     * @param request 视频生成请求
     * @return 源类型
     */
    private String determineSourceType(MiniMaxVideoGenerationRequest request) {
        if (StringUtils.hasText(request.getFirstFrameImage())) {
            return "image_to_video";
        } else if (request.getSubjectReference() != null && !request.getSubjectReference().isEmpty()) {
            return "subject_reference";
        } else {
            return "text_to_video";
        }
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    private Long getCurrentUserId() {
        // 这里需要根据实际的用户上下文获取用户ID
        // 如果没有用户上下文，可以返回null或默认值
        try {
            // 假设有UserContext类来获取当前用户
            // return Long.valueOf(UserContext.getUser().getUserId());
            return null; // 暂时返回null，实际使用时需要根据具体情况修改
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，使用默认值", e);
            return null;
        }
    }
}
