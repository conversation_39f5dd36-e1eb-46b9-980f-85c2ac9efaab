package com.wlink.agent.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 简化版的fal.ai SDK客户端，使用原始FalAiFluxClient作为实际实现
 * 在Maven依赖和项目配置正确后，可以替换为完整的SDK实现
 */
@Slf4j
@Component
public class FalAiSdkClientSimple {
    
    private final FalAiFluxClient falAiFluxClient;
    
    /**
     * 创建FalAiSdkClientSimple实例
     * @param apiKey FAL API密钥
     */
    public FalAiSdkClientSimple(@Value("${fal.ai.api-key:}") String apiKey) {
        this.falAiFluxClient = new FalAiFluxClient(apiKey);
        log.info("已创建FalAiSdkClientSimple，使用FalAiFluxClient作为底层实现");
        log.info("注意：这是一个临时解决方案，当Maven依赖和项目配置正确后，应替换为完整的SDK实现");
    }

    /**
     * 使用文本提示生成图像（同步方法）
     * @param request 文本到图像请求参
     * @return 生成的图像结
     * @throws IOException 如果API调用失败
     */
    public FalAiFluxClient.FalAiResponse generateImage(FalAiFluxClient.TextToImageRequest request) throws IOException {
        log.info("调用FalAiFluxClient.generateImage生成图像: {}", request.getPrompt());
        return falAiFluxClient.generateImage(request);
    }
    
    /**
     * 异步生成图像
     * @param request 文本到图像请求参
     * @return 包含结果的CompletableFuture
     */
    public CompletableFuture<FalAiFluxClient.FalAiResponse> generateImageAsync(FalAiFluxClient.TextToImageRequest request) {
        log.info("调用FalAiFluxClient.generateImageAsync异步生成图像: {}", request.getPrompt());
        return falAiFluxClient.generateImageAsync(request);
    }
    
    /**
     * 使用文本提示和图像编辑图像（同步方法
     * @param request 图像编辑请求参数
     * @return 编辑后的图像结果
     * @throws IOException 如果API调用失败
     */
    public FalAiFluxClient.FalAiResponse editImage(FalAiFluxClient.ImageToImageRequest request) throws IOException {
        log.info("调用FalAiFluxClient.editImage编辑图像: {}, 图像URL: {}", request.getPrompt(), request.getImageUrl());
        return falAiFluxClient.editImage(request);
    }
    
    /**
     * 异步编辑图像
     * @param request 图像编辑请求参数
     * @return 包含结果的CompletableFuture
     */
    public CompletableFuture<FalAiFluxClient.FalAiResponse> editImageAsync(FalAiFluxClient.ImageToImageRequest request) {
        log.info("调用FalAiFluxClient.editImageAsync异步编辑图像: {}, 图像URL: {}", request.getPrompt(), request.getImageUrl());
        return falAiFluxClient.editImageAsync(request);
    }
    
    /**
     * 提交图像生成请求但不等待结果（异步）
     * 注意：此方法仅为占位符，实际使用的是FalAiFluxClient的同步方法，然后立即返回requestId
     * @param request 文本到图像请求参
     * @param webhookUrl 可选的webhook URL，未使用
     * @return 请求ID
     * @throws IOException 如果API调用失败
     */
    public String submitImageGenerationJob(FalAiFluxClient.TextToImageRequest request, String webhookUrl) throws IOException {
        log.info("提交文本到图像作 {}", request.getPrompt());
        log.warn("注意：此方法为兼容性占位符，实际使用的是FalAiFluxClient的同步方");
        
        // 使用原始客户端提交请
        FalAiFluxClient.QueueStatus status = falAiFluxClient.submitTextToImageRequest(request);
        
        log.info("作业已提交，请求ID: {}", status.getRequestId());
        return status.getRequestId();
    }
    
    /**
     * 提交图像编辑请求但不等待结果（异步）
     * 注意：此方法仅为占位符，实际使用的是FalAiFluxClient的同步方法，然后立即返回requestId
     * @param request 图像编辑请求参数
     * @param webhookUrl 可选的webhook URL，未使用
     * @return 请求ID
     * @throws IOException 如果API调用失败
     */
    public String submitImageEditJob(FalAiFluxClient.ImageToImageRequest request, String webhookUrl) throws IOException {
        log.info("提交图像编辑作业: {}, 图像URL: {}", request.getPrompt(), request.getImageUrl());
        log.warn("注意：此方法为兼容性占位符，实际使用的是FalAiFluxClient的同步方");
        
        // 使用原始客户端提交请
        FalAiFluxClient.QueueStatus status = falAiFluxClient.submitImageToImageRequest(request);
        
        log.info("作业已提交，请求ID: {}", status.getRequestId());
        return status.getRequestId();
    }
    
    /**
     * 获取作业状
     * @param requestId 请求ID
     * @param isTextToImage 是否为文本到图像请求（true）或图像编辑请求（false
     * @return 队列状
     * @throws IOException 如果API调用失败
     */
    public FalAiFluxClient.QueueStatus getJobStatus(String requestId, boolean isTextToImage) throws IOException {
        log.info("获取作业状 请求ID={}, 是否为文本到图像={}", requestId, isTextToImage);
        return falAiFluxClient.checkQueueStatus(requestId);
    }
    
    /**
     * 使用完整的状态URL获取作业状
     * @param statusUrl 完整的状态URL，通常从QueueStatus.getStatusUrl()获取
     * @return 队列状
     * @throws IOException 如果API调用失败
     */
    public FalAiFluxClient.QueueStatus getJobStatusByUrl(String statusUrl) throws IOException {
        log.info("使用完整URL获取作业状 {}", statusUrl);
        return falAiFluxClient.checkQueueStatusByUrl(statusUrl);
    }
    
    /**
     * 获取作业结果
     * @param requestId 请求ID
     * @param isTextToImage 是否为文本到图像请求（true）或图像编辑请求（false
     * @return 生成的图像结
     * @throws IOException 如果API调用失败
     */
    public FalAiFluxClient.FalAiResponse getJobResult(String requestId, boolean isTextToImage) throws IOException {
        log.info("获取作业结果: 请求ID={}, 是否为文本到图像={}", requestId, isTextToImage);
        return falAiFluxClient.getResult(requestId);
    }
    
    /**
     * 示例使用方法
     */
    public static void main(String[] args) {
        try {
            // 创建客户
            FalAiSdkClientSimple client = new FalAiSdkClientSimple("58371be0-d55d-4542-a7ce-7018b4aa0226:44e655c15df8eca02ab38a127e299b94");
            
            // 文本到图像示
            FalAiFluxClient.TextToImageRequest textToImageRequest = new FalAiFluxClient.TextToImageRequest.Builder()
                    .prompt("Extreme close-up of a single tiger eye, direct frontal view. Detailed iris and pupil. Sharp focus on eye texture and color. Natural lighting to capture authentic eye shine and depth. The word \\\"FLUX\\\" is painted over it in big, white brush strokes with visible texture.")
                    .numImages(1)
                    .aspectRatio("1:1")
                    .guidanceScale(3.5)
                    .syncMode(false)
                    .outputFormat("jpeg")
                    .seed(10000)
                    .safetyTolerance("2")
                    .build();
            
            System.out.println("正在生成图像...");
            FalAiFluxClient.FalAiResponse textToImageResponse = client.generateImage(textToImageRequest);
            System.out.println("生成的图像URL: " + textToImageResponse.getImages().get(0).getUrl());
            
            // 图像编辑示例
            FalAiFluxClient.ImageToImageRequest imageToImageRequest = new FalAiFluxClient.ImageToImageRequest.Builder()
                    .prompt("\"FLUX\" changed to \"UVX\"")
                    .imageUrl(textToImageResponse.getImages().get(0).getUrl())
                    .numImages(1)
                    .syncMode(false)
                    .safetyTolerance("2")
                    .outputFormat("jpeg")
                    .seed(10000)
                    .aspectRatio("9:16")
                    .guidanceScale(3.5)
                    .build();
            
            System.out.println("正在编辑图像...");
            FalAiFluxClient.FalAiResponse imageToImageResponse = client.editImage(imageToImageRequest);
            System.out.println("编辑后的图像URL: " + imageToImageResponse.getImages().get(0).getUrl());
            
            // 异步作业示例
            System.out.println("提交异步图像生成作业...");
            
            // 提交请求并获取初始状
            FalAiFluxClient.QueueStatus initialStatus = client.falAiFluxClient.submitTextToImageRequest(textToImageRequest);
            String jobId = initialStatus.getRequestId();
            String statusUrl = initialStatus.getStatusUrl();
            
            System.out.println("作业ID: " + jobId);
            System.out.println("状态URL: " + statusUrl);
            
            // 示例1：使用请求ID轮询作业状态（传统方式
            System.out.println("\n示例1：使用请求ID轮询状");
            FalAiFluxClient.QueueStatus status1 = client.getJobStatus(jobId, true);
            System.out.println("使用请求ID查询的状 " + status1.getStatus());
            
            // 示例2：使用完整状态URL轮询作业状态（直接方式
            System.out.println("\n示例2：使用完整状态URL轮询状");
            FalAiFluxClient.QueueStatus status2 = client.getJobStatusByUrl(statusUrl);
            System.out.println("使用状态URL查询的状 " + status2.getStatus());
            
            // 完整的状态轮询示例（使用statusUrl方式
            System.out.println("\n开始轮询作业状态（使用statusUrl方式..");
            while (true) {
                FalAiFluxClient.QueueStatus status = client.getJobStatusByUrl(statusUrl);
                
                if ("COMPLETED".equals(status.getStatus())) {
                    System.out.println("作业已完成，获取结果...");
                    FalAiFluxClient.FalAiResponse jobResult = client.getJobResult(jobId, true);
                    System.out.println("作业结果图像URL: " + jobResult.getImages().get(0).getUrl());
                    break;
                } else if ("IN_QUEUE".equals(status.getStatus())) {
                    System.out.println("作业在队列中，位 " + status.getQueuePosition());
                } else if ("IN_PROGRESS".equals(status.getStatus())) {
                    System.out.println("作业正在处理..");
                } else {
                    System.out.println("未知状 " + status.getStatus());
                    break;
                }
                
                Thread.sleep(1000);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
} 
