package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class VolcengineSeedEditResponse {
    private int code;
    private SeedEditData data;
    private String message;
    @JsonProperty("request_id") // 注意：这里接口文档的 request_id status 的位置与之前的不
    private String requestId;
    private int status;
    @JsonProperty("time_elapsed") // 接口文档中是 tine elapsed，这里修正为 time_elapsed
    private String timeElapsed;

    @Data
    public static class SeedEditData {
        @JsonProperty("algorithm_base_resp")
        private AlgorithmBaseResp algorithmBaseResp;

        @JsonProperty("binary_data_base64")
        private List<String> binaryDataBase64;

        @JsonProperty("image_urls")
        private List<String> imageUrls;

        @JsonProperty("infer_ctx")
        private InferCtx inferCtx;

        @JsonProperty("llm_result") // 文档中是 11m_result，这里修正为 llm_result
        private String llmResult;

        @JsonProperty("pe_result")
        private String peResult;

        @JsonProperty("predict_tags_result")
        private String predictTagsResult;

        @JsonProperty("rephraser_result") // 文档中是 rephraser result，这里修正为 rephraser_result
        private String rephraserResult;

        @JsonProperty("request_id")
        private String requestId; // data 内部还有一request_id

        @JsonProperty("vlm_result")
        private String vlmResult;
    }

    @Data
    public static class InferCtx {
        @JsonProperty("algorithm_key")
        private String algorithmKey;

        @JsonProperty("app_key")
        private String appKey;

        @JsonProperty("created_at")
        private String createdAt;

        @JsonProperty("generate_id")
        private String generateId;

        @JsonProperty("log_id")
        private String logId;

        private Map<String, Object> params; // params 是一个复杂的对象，用 Map<String, Object> 接收

        @JsonProperty("request_id") // infer_ctx 内部还有一request_id
        private String requestId;

        @JsonProperty("session_id")
        private String sessionId;

        @JsonProperty("time_stamp")
        private String timeStamp;
    }
}
