package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "火山引擎图生图（Seed编辑）请求")
public class VolcengineSeedEditRequest {

    //会话id
    @NotBlank(message = "conversationId is required")
    @Schema(description = "会话id")
    private String conversationId;

    //类型  3-角色  4-分镜
    @NotNull(message = "type is required")
    @Schema(description = "类型  3-角色  4-分镜")
    private Integer type;

    @NotBlank(message = "contentId is required")
    @Schema(description = "内容id")
    private String contentId;

    @Schema(description = "请求类型Key", example = "byteedit_v2.0")
    @JsonProperty("req_key")
    private String reqKey = "byteedit_v2.0";

    @NotEmpty(message = "image_urls is required")
    @Schema(description = "输入图片URL列表", required = true, example = "[")
    @JsonProperty("image_urls")
    private List<String> imageUrls;

    @NotBlank(message = "prompt is required")
    @Schema(description = "图像描述文本", required = true, example = "戴着帽子的猫")
    @JsonProperty("prompt")
    private String prompt;

    @Schema(description = "随机种子, -1表示随机", example = "-1")
    private Integer seed = -1;

    @Schema(description = "控制图像与输入图像的相似程度", example = "0.5")
    private Double scale = 0.5;

    @Schema(description = "是否返回图片URL", example = "true")
    @JsonProperty("return_url")
    private Boolean returnUrl = true;

    @Schema(description = "Logo信息")
    @JsonProperty("logo_info")
    private LogoInfo logoInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "图生图Logo信息")
    public static class LogoInfo {
        @Schema(description = "是否添加Logo", example = "false")
        @JsonProperty("add_logo")
        private Boolean addLogo;

        @Schema(description = "Logo位置: 0-右下 1-左下 2-左上 3-右上, example = ", example = "0")
        private Integer position;

        @Schema(description = "Logo语言: 0-中文（AI生成 1-英文（Generated by AI, example = ", example = "0")
        private Integer language;

        @Schema(description = "Logo文本内容", example = "AI生成")
        @JsonProperty("logo_text_content")
        private String logoTextContent;
    }
}
