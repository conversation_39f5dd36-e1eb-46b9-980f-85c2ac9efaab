package com.wlink.agent.client.model.comfyui;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ComfyUI 运行响应
 */
@Data
public class ComfyUIRunResponse {
    
    /**
     * 响应代码
     */
    @JsonProperty("code")
    private Integer code;
    
    /**
     * 响应消息
     */
    @JsonProperty("msg")
    private String msg;
    
    /**
     * 响应数据
     */
    @JsonProperty("data")
    private ComfyUIRunResponseData data;
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }
}
