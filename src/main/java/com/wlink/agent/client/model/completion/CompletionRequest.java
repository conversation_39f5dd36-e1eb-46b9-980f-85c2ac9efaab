package com.wlink.agent.client.model.completion;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 完成API请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompletionRequest {
    
    /**
     * 输入参数
     */
    private CompletionInput inputs;
    
    /**
     * 用户ID
     */
    private String user;
    
    /**
     * 响应模式，默认为blocking
     */
    @JsonProperty("response_mode")
    @Builder.Default
    private String responseMode = "blocking";
} 
