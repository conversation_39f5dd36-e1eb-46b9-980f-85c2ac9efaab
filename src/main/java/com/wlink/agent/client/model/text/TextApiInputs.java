package com.wlink.agent.client.model.text;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表示文本API请求的inputs部分
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TextApiInputs {
    
    /**
     * 地点
     */
    private String location;
    
    /**
     * 职业
     */
    private String job;
    
    /**
     * 年龄范围
     */
    private String age;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 主题
     */
    private String topic;
} 
