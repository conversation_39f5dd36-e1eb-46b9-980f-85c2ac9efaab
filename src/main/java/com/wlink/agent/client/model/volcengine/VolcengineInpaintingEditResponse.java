package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class VolcengineInpaintingEditResponse {
    private Integer code;
    private String message;
    @JsonProperty("request_id")
    private String requestId;
    private Integer status;
    @JsonProperty("time_elapsed")
    private String timeElapsed;
    private InpaintingEditData data;

    @Data
    public static class InpaintingEditData {
        @JsonProperty("algorithm_base_resp")
        private AlgorithmBaseResp algorithmBaseResp;
        @JsonProperty("binary_data_base64")
        private List<String> binaryDataBase64;
        @JsonProperty("image_urls")
        private List<String> imageUrls;
        @JsonProperty("request_id")
        private String requestId;
    }

    @Data
    public static class AlgorithmBaseResp {
        @JsonProperty("status_code")
        private Integer statusCode;
        @JsonProperty("status_message")
        private String statusMessage;
    }
} 
