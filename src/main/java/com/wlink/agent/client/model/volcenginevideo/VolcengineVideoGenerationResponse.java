package com.wlink.agent.client.model.volcenginevideo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频生成响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolcengineVideoGenerationResponse {
    
    /**
     * 任务ID
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 错误码，非成功状态下会返
     */
    @JsonProperty("error_code")
    private String errorCode;
    
    /**
     * 错误消息，非成功状态下会返
     */
    @JsonProperty("error_msg")
    private String errorMsg;
} 
