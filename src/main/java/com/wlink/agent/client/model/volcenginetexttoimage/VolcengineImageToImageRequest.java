package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolcengineImageToImageRequest {

    @JsonProperty("req_key")
    private String reqKey;

    @JsonProperty("prompt")
    private String prompt;

    @JsonProperty("seed")
    private Integer seed;

    @JsonProperty("scale")
    private Float scale;

    @JsonProperty("ddim_steps")
    private Integer ddimSteps;

    @JsonProperty("image_urls")
    private List<String> imageUrls;

    @JsonProperty("strength")
    private Double strength;

    @JsonProperty("use_pre_llm")
    private Boolean usePreLlm;

    @JsonProperty("return_url")
    private Boolean returnUrl;

    @JsonProperty("logo_info")
    private LogoInfo logoInfo;
} 
