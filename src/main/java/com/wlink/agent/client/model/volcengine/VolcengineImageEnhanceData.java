package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class VolcengineImageEnhanceData {
    @JsonProperty("algorithm_base_resp")
    private AlgorithmBaseResp algorithmBaseResp;

    @JsonProperty("binary_data_base64")
    private List<String> binaryDataBase64;

    @JsonProperty("image_urls")
    private List<String> imageUrls;

    @JsonProperty("predict_tags_result")
    private String predictTagsResult;

    @JsonProperty("rephraser_result")
    private String rephraserResult;

    @JsonProperty("request_id")
    private String requestId;
}
