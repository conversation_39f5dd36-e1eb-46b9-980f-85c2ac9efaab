package com.wlink.agent.client.model.doubao;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 豆包图像生成API - 请求模型
 * 
 * <AUTHOR> Assistant
 */
@Data
public class DoubaoImageGenerationRequest {
    
    /**
     * 模型名称
     */
    @NotBlank(message = "模型名称不能为空")
    @JsonProperty("model")
    private String model;
    
    /**
     * 图像生成提示
     */
    @NotBlank(message = "提示词不能为空")
    @JsonProperty("prompt")
    private String prompt;


    /**
     * 中文prompt
     */
    @JsonProperty("cnPrompt")
    private String cnPrompt;

    /**
     * 响应格式，通常url"
     */
    @JsonProperty("response_format")
    private String responseFormat = "url";
    
    /**
     * 图片尺寸，如"1024x1024"
     */
    @JsonProperty("size")
    private String size = "1024x1024";
    
    /**
     * 随机种子
     */
    @JsonProperty("seed")
    private Integer seed;
    
    /**
     * 引导尺度
     */
    @JsonProperty("guidance_scale")
    private Double guidanceScale;
    
    /**
     * 是否添加水印
     */
    @JsonProperty("watermark")
    private Boolean watermark = false;
    
    public DoubaoImageGenerationRequest() {
    }
    
    public DoubaoImageGenerationRequest(String model, String prompt) {
        this.model = model;
        this.prompt = prompt;
    }
} 
