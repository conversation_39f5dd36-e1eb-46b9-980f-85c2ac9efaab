package com.wlink.agent.client.model.comfyui;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * ComfyUI 回调事件数据
 */
@Data
public class ComfyUICallbackEventData {
    
    /**
     * 响应代码
     */
    @JsonProperty("code")
    private Integer code;
    
    /**
     * 响应消息
     */
    @JsonProperty("msg")
    private String msg;
    
    /**
     * 数据列表（成功时）或错误详情字符串（失败时）
     */
    @JsonProperty("data")
    private Object data;

    /**
     * 获取成功时的结果数据列表
     */
    @SuppressWarnings("unchecked")
    public List<ComfyUIResultData> getResultDataList() {
        if (code != null && code == 0 && data instanceof List) {
            try {
                // 如果data是List类型，尝试转换为ComfyUIResultData列表
                List<?> dataList = (List<?>) data;
                return dataList.stream()
                        .map(item -> {
                            if (item instanceof ComfyUIResultData) {
                                return (ComfyUIResultData) item;
                            } else {
                                // 如果是Map或其他类型，转换为JSON再解析
                                String json = JSON.toJSONString(item);
                                return JSON.parseObject(json, ComfyUIResultData.class);
                            }
                        })
                        .toList();
            } catch (Exception e) {
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }

    /**
     * 获取失败时的错误详情
     */
    public String getFailureDetails() {
        if (code != null && code != 0 && data != null) {
            if (data instanceof String) {
                return (String) data;
            } else {
                return JSON.toJSONString(data);
            }
        }
        return null;
    }

    /**
     * 结果数据
     */
    @Data
    public static class ComfyUIResultData {
        
        /**
         * 文件URL
         */
        @JsonProperty("fileUrl")
        private String fileUrl;
        
        /**
         * 文件类型
         */
        @JsonProperty("fileType")
        private String fileType;
        
        /**
         * 任务耗时
         */
        @JsonProperty("taskCostTime")
        private Long taskCostTime;
        
        /**
         * 节点ID
         */
        @JsonProperty("nodeId")
        private String nodeId;
    }
}
