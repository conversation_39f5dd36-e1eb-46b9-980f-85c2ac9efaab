package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax文件检索响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniMaxFileRetrieveResponse {
    
    /**
     * 文件信息
     */
    @JsonProperty("file")
    private FileInfo file;
    
    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private BaseResponse baseResp;
    
    /**
     * 文件信息模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileInfo {
        /**
         * 文件ID
         */
        @JsonProperty("file_id")
        private Long fileId;
        
        /**
         * 文件大小（字节）
         */
        @JsonProperty("bytes")
        private Long bytes;
        
        /**
         * 创建时间戳
         */
        @JsonProperty("created_at")
        private Long createdAt;
        
        /**
         * 文件名
         */
        @JsonProperty("filename")
        private String filename;
        
        /**
         * 文件用途
         */
        @JsonProperty("purpose")
        private String purpose;
        
        /**
         * 下载URL
         */
        @JsonProperty("download_url")
        private String downloadUrl;
    }
    
    /**
     * 基础响应模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseResponse {
        /**
         * 状态码
         * 0：请求成功
         */
        @JsonProperty("status_code")
        private Integer statusCode;
        
        /**
         * 状态消息
         */
        @JsonProperty("status_msg")
        private String statusMsg;
    }
}
