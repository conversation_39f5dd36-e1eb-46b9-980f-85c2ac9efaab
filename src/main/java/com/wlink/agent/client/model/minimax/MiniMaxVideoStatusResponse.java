package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax视频生成状态查询响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniMaxVideoStatusResponse {
    
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * 任务状态
     * processing: 生成中
     * success: 成功
     * failed: 失败
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 文件ID（成功时返回）
     */
    @JsonProperty("file_id")
    private String fileId;

    
    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private MiniMaxVideoGenerationResponse.BaseResponse baseResp;
    
    /**
     * 判断任务是否成功完成
     * 
     * @return true表示成功完成，false表示未完成或失败
     */
    public boolean isSuccess() {
        return "success".equalsIgnoreCase(status);
    }
    
    /**
     * 判断任务是否失败
     * 
     * @return true表示失败，false表示未失败
     */
    public boolean isFailed() {
        return "failed".equalsIgnoreCase(status);
    }
    
    /**
     * 判断任务是否正在处理中
     * 
     * @return true表示处理中，false表示不在处理中
     */
    public boolean isProcessing() {
        return "processing".equalsIgnoreCase(status);
    }
    
    /**
     * 判断任务是否已完成（成功或失败）
     * 
     * @return true表示已完成，false表示未完成
     */
    public boolean isCompleted() {
        return isSuccess() || isFailed();
    }
    
    /**
     * 获取错误信息
     * 
     * @return 错误信息，如果没有错误则返回null
     */
    public String getErrorMessage() {
        if (baseResp != null && baseResp.getStatusCode() != null && baseResp.getStatusCode() != 0) {
            return baseResp.getStatusMsg();
        }
        if (isFailed()) {
            return "视频生成失败";
        }
        return null;
    }
    
    /**
     * 获取状态码
     * 
     * @return 状态码
     */
    public Integer getStatusCode() {
        return baseResp != null ? baseResp.getStatusCode() : null;
    }
    
    /**
     * 视频生成状态枚举
     */
    public enum VideoStatus {
        PROCESSING("processing", "生成中"),
        SUCCESS("success", "成功"),
        FAILED("failed", "失败");
        
        private final String value;
        private final String description;
        
        VideoStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static VideoStatus fromValue(String value) {
            if (value == null) {
                return null;
            }
            
            for (VideoStatus status : VideoStatus.values()) {
                if (status.getValue().equalsIgnoreCase(value)) {
                    return status;
                }
            }
            
            throw new IllegalArgumentException("未知的视频状态值: " + value);
        }
    }
}
