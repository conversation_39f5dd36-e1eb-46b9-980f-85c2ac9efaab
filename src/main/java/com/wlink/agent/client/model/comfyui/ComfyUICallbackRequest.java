package com.wlink.agent.client.model.comfyui;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ComfyUI 回调请求
 */
@Data
public class ComfyUICallbackRequest {
    
    /**
     * 事件类型
     */
    @JsonProperty("event")
    private String event;
    
    /**
     * 任务ID
     */
    @JsonProperty("taskId")
    private String taskId;
    
    /**
     * 事件数据（JSON字符串）
     */
    @JsonProperty("eventData")
    private String eventData;
}
