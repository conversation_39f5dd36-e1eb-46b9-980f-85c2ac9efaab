package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax视频生成回调请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniMaxVideoCallbackRequest {
    
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * 任务状态
     * processing: 生成中
     * success: 成功
     * failed: 失败
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 文件ID（成功时返回）
     */
    @JsonProperty("file_id")
    private String fileId;
    
    /**
     * 视频URL（成功时返回）
     */
    @JsonProperty("video_url")
    private String videoUrl;
    
    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private MiniMaxVideoGenerationResponse.BaseResponse baseResp;
    
    /**
     * 挑战值（用于回调URL验证）
     */
    @JsonProperty("challenge")
    private String challenge;
    
    /**
     * 判断是否为验证请求
     * 
     * @return true表示是验证请求，false表示是回调请求
     */
    public boolean isVerificationRequest() {
        return challenge != null && !challenge.trim().isEmpty();
    }
    
    /**
     * 判断任务是否成功完成
     * 
     * @return true表示成功完成，false表示未完成或失败
     */
    public boolean isSuccess() {
        return "success".equalsIgnoreCase(status);
    }
    
    /**
     * 判断任务是否失败
     * 
     * @return true表示失败，false表示未失败
     */
    public boolean isFailed() {
        return "failed".equalsIgnoreCase(status);
    }
    
    /**
     * 判断任务是否正在处理中
     * 
     * @return true表示处理中，false表示不在处理中
     */
    public boolean isProcessing() {
        return "processing".equalsIgnoreCase(status);
    }
    
    /**
     * 获取错误信息
     * 
     * @return 错误信息，如果没有错误则返回null
     */
    public String getErrorMessage() {
        if (baseResp != null && baseResp.getStatusCode() != null && baseResp.getStatusCode() != 0) {
            return baseResp.getStatusMsg();
        }
        if (isFailed()) {
            return "视频生成失败";
        }
        return null;
    }
    
    /**
     * 创建验证响应
     * 
     * @param challenge 挑战值
     * @return 验证响应对象
     */
    public static MiniMaxVideoCallbackRequest createVerificationResponse(String challenge) {
        return MiniMaxVideoCallbackRequest.builder()
                .challenge(challenge)
                .build();
    }
}
