package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LogoInfo {

    @JsonProperty("add_logo")
    private Boolean addLogo;

    @JsonProperty("position")
    private Integer position;

    @JsonProperty("language")
    private Integer language;

    @JsonProperty("opacity")
    private Float opacity;

    @JsonProperty("logo_text_content")
    private String logoTextContent;
}
