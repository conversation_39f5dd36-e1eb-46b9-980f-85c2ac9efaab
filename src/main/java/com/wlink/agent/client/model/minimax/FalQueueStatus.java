package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * FAL 队列状态模型
 * 基于 fal-ai OpenAPI 规范的队列状态响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FalQueueStatus {
    
    /**
     * 队列状态
     * 枚举值: "IN_QUEUE", "IN_PROGRESS", "COMPLETED"
     * 必填字段
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 请求 ID
     * 必填字段
     */
    @JsonProperty("request_id")
    private String requestId;
    
    /**
     * 响应的 URL
     * 当任务完成时，可以通过此 URL 获取结果
     */
    @JsonProperty("response_url")
    private String responseUrl;
    
    /**
     * 状态 URL
     * 用于查询任务状态的 URL
     */
    @JsonProperty("status_url")
    private String statusUrl;
    
    /**
     * 取消 URL
     * 用于取消任务的 URL
     */
    @JsonProperty("cancel_url")
    private String cancelUrl;
    
    /**
     * 日志信息
     * 包含任务执行过程中的日志
     */
    @JsonProperty("logs")
    private Map<String, Object> logs;
    
    /**
     * 指标信息
     * 包含任务执行的性能指标
     */
    @JsonProperty("metrics")
    private Map<String, Object> metrics;
    
    /**
     * 队列位置
     * 当状态为 IN_QUEUE 时，表示在队列中的位置
     */
    @JsonProperty("queue_position")
    private Integer queuePosition;
    
    /**
     * 队列状态枚举
     */
    public enum Status {
        /**
         * 在队列中等待
         */
        IN_QUEUE("IN_QUEUE"),
        
        /**
         * 正在处理中
         */
        IN_PROGRESS("IN_PROGRESS"),
        
        /**
         * 已完成
         */
        COMPLETED("COMPLETED");
        
        private final String value;
        
        Status(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        public static Status fromValue(String value) {
            for (Status status : Status.values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return null;
        }
    }
    
    /**
     * 检查任务是否在队列中
     * 
     * @return 如果在队列中返回 true
     */
    public boolean isInQueue() {
        return Status.IN_QUEUE.getValue().equals(status);
    }
    
    /**
     * 检查任务是否正在处理
     * 
     * @return 如果正在处理返回 true
     */
    public boolean isInProgress() {
        return Status.IN_PROGRESS.getValue().equals(status);
    }
    
    /**
     * 检查任务是否已完成
     * 
     * @return 如果已完成返回 true
     */
    public boolean isCompleted() {
        return Status.COMPLETED.getValue().equals(status);
    }
    
    /**
     * 检查任务是否仍在运行中（队列中或处理中）
     * 
     * @return 如果仍在运行返回 true
     */
    public boolean isRunning() {
        return isInQueue() || isInProgress();
    }
    
    /**
     * 获取状态枚举
     * 
     * @return 状态枚举，如果无法识别返回 null
     */
    public Status getStatusEnum() {
        return Status.fromValue(status);
    }
    
    /**
     * 获取队列位置描述
     * 
     * @return 队列位置描述字符串
     */
    public String getQueuePositionDescription() {
        if (queuePosition == null) {
            return "未知位置";
        }
        return "第 " + queuePosition + " 位";
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态的中文描述
     */
    public String getStatusDescription() {
        return switch (status) {
            case "IN_QUEUE" -> "排队中";
            case "IN_PROGRESS" -> "处理中";
            case "COMPLETED" -> "已完成";
            default -> "未知状态: " + status;
        };
    }
}
