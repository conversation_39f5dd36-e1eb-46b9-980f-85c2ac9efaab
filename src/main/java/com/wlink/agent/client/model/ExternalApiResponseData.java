package com.wlink.agent.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class ExternalApiResponseData {

    private String id;

    @JsonProperty("workflow_id")
    private String workflowId;

    private String status;

    private ExternalApiResponseOutputs outputs;

    private String error;

    @JsonProperty("elapsed_time")
    private double elapsedTime;

    @JsonProperty("total_tokens")
    private int totalTokens;

    @JsonProperty("total_steps")
    private int totalSteps;

    @JsonProperty("created_at")
    private long createdAt;

    @JsonProperty("finished_at")
    private long finishedAt;
} 
