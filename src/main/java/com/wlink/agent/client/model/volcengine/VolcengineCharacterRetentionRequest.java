package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wlink.agent.validation.annotation.ValidStrings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "火山引擎人像IP保留请求参数")
public class VolcengineCharacterRetentionRequest {



    //会话id
    @NotBlank(message = "conversationId is required")
    @Schema(description = "会话id")
    private String conversationId;

    //类型  3-角色  4-分镜
    @NotNull(message = "type is required")
    @Schema(description = "类型  3-角色  4-分镜")
    private Integer type;

    @NotBlank(message = "contentId is required")
    @Schema(description = "内容id")
    private String contentId;


    @Schema(description = "请求类型Key", example = "high_aes_ip_v20")
    @JsonProperty("req_key")
    private String reqKey = "high_aes_ip_v20";

    @NotEmpty(message = "image_urls is required")
    @ValidStrings(message = "图片URL列表中不能包含空字符串或null")
    @Schema(description = "输入图片URL列表，包含人, required = true, example = ", required = true, example = "[")
    @JsonProperty("image_urls")
    private List<String> imageUrls;

    @NotBlank(message = "prompt is required")
    @Schema(description = "图像描述文本，描述期望的场景或风, required = true, example = ", required = true, example = "一个宇航员在月球上")
    @JsonProperty("prompt")
    private String prompt;

    @Schema(description = "是否进行描述回推", example = "true")
    @JsonProperty("desc_pushback")
    private Boolean descPushback = true;

    @Schema(description = "随机种子, -1表示随机", example = "-1")
    @JsonProperty("seed")
    private Integer seed = -1;

    @Schema(description = "相关性，控制图像与输入文本的匹配程度", example = "3.5")
    @JsonProperty("scale")
    private Double scale = 3.5;

    @Schema(description = "DDIM采样步数", example = "9")
    @JsonProperty("ddim_steps")
    private Integer ddimSteps = 9;

    @Schema(description = "图像宽度", example = "512")
    private Integer width  = 512;

    @Schema(description = "图像高度", example = "512")
    private Integer height = 512;

    @Schema(description = "CFG重缩放因, example = ", example = "0.7")
    @JsonProperty("cfg_rescale")
    private Double cfgRescale  = 0.7;

    @Schema(description = "参考IP权重", example = "0.7")
    @JsonProperty("ref_ip_weight")
    private Double refIpWeight = 0.7;

    @Schema(description = "参考ID权重", example = "0.36")
    @JsonProperty("ref_id_weight")
    private Double refIdWeight = 0.36;

    @Schema(description = "是否使用超分辨率", example = "true")
    @JsonProperty("use_sr")
    private Boolean useSr = true;

    @Schema(description = "是否返回图片URL", example = "true")
    @JsonProperty("return_url")
    private Boolean returnUrl = true;

    @Schema(description = "Logo信息")
    @JsonProperty("logo_info")
    private LogoInfo logoInfo;





    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "人像IP保留Logo信息")
    public static class LogoInfo {
        @Schema(description = "是否添加Logo", example = "false")
        @JsonProperty("add_logo")
        private Boolean addLogo;

        @Schema(description = "Logo位置: 0-右下 1-左下 2-左上 3-右上, example = ", example = "0")
        private Integer position;

        @Schema(description = "Logo语言: 0-中文（AI生成 1-英文（Generated by AI, example = ", example = "0")
        private Integer language;

        @Schema(description = "Logo透明, example = ", example = "0.3")
        private Double opacity;

        @Schema(description = "Logo文本内容", example = "AI生成")
        @JsonProperty("logo_text_content")
        private String logoTextContent;
    }
}
