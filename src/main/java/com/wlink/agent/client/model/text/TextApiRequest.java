package com.wlink.agent.client.model.text;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本API请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextApiRequest {
    
    /**
     * 输入参数
     */
    private TextApiInputs inputs;
    
    /**
     * 用户ID
     */
    private String user;
    
    /**
     * 响应模式，默认为blocking
     */
    @JsonProperty("response_mode")
    @Builder.Default
    private String responseMode = "blocking";
} 
