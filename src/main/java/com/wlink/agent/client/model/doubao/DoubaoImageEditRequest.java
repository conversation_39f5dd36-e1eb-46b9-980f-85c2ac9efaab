package com.wlink.agent.client.model.doubao;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 豆包图像编辑API - 请求模型
 * 
 * <AUTHOR> Assistant
 */
@Data
public class DoubaoImageEditRequest {
    
    /**
     * 模型名称 - 目前支持 SeedEdit 3.0 模型，Model ID为 doubao-seededit-3-0-i2i-250628
     */
    @NotBlank(message = "模型名称不能为空")
    @JsonProperty("model")
    private String model;
    
    /**
     * 文本描述，用于编辑图像的提示词
     */
    @NotBlank(message = "提示词不能为空")
    @JsonProperty("prompt")
    private String prompt;
    
    /**
     * 需要编辑的图像，输入图片的 Base64 编码或可访问的 URL
     * 图片URL：请确保图片URL可被访问
     * Base64编码：请遵循此格式data:image/<图片格式>;base64,<Base64编码>
     * 
     * 传入图片需要满足以下条件：
     * - 图片格式：jpeg、png
     * - 宽高比（宽/高）：在范围 (1/3, 3)
     * - 宽高长度（px） > 14
     * - 大小：不超过 10MB
     */
    @NotBlank(message = "图像不能为空")
    @JsonProperty("image")
    private String image;
    
    /**
     * 指定生成图像的返回格式
     * url：以可下载的 jpeg 图片链接形式返回
     * b64_json：以 Base64 编码字符串的 JSON 格式返回图像数据
     */
    @JsonProperty("response_format")
    private String responseFormat = "url";
    
    /**
     * 生成图像的宽高像素
     * adaptive：根据所上传图片的比例，自动选择最合适的宽高比
     */
    @JsonProperty("size")
    private String size = "adaptive";
    
    /**
     * 随机数种子，用于控制模型生成内容的随机性
     * 取值范围为 [-1, 2^31-1]，即 [-1, 2147483647] 之间的整数
     * 如果不提供，则算法自动生成一个随机数作为种子
     */
    @JsonProperty("seed")
    private Integer seed = -1;
    
    /**
     * 文本描述和输入图片对生成图像的影响程度
     * 取值范围：[1, 10] 之间的浮点数
     * 该值越大代表文本描述影响程度越大，且输入图片影响程度越小
     */
    @JsonProperty("guidance_scale")
    private Double guidanceScale = 5.5;
    
    /**
     * 是否在生成的图片中添加水印
     * false：不添加水印
     * true：在图片右下角添加"AI生成"字样的水印标识
     */
    @JsonProperty("watermark")
    private Boolean watermark = false;
    
    public DoubaoImageEditRequest() {
    }
    
    public DoubaoImageEditRequest(String model, String prompt, String image) {
        this.model = model;
        this.prompt = prompt;
        this.image = image;
    }
}
