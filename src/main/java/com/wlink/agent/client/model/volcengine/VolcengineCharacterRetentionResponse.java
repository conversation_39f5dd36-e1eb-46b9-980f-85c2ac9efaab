package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class VolcengineCharacterRetentionResponse {
    private int code;
    private CharacterRetentionData data;
    private String message;
    @JsonProperty("request_id")
    private String requestId;
    private int status;
    @JsonProperty("time_elapsed")
    private String timeElapsed;

    @Data
    public static class CharacterRetentionData {
        @JsonProperty("algorithm_base_resp")
        private AlgorithmBaseResp algorithmBaseResp;

        @JsonProperty("binary_data_base64")
        private List<String> binaryDataBase64;

        @JsonProperty("image_urls")
        private List<String> imageUrls;

        @JsonProperty("pe_result")
        private String peResult;

        @JsonProperty("predict_tags_result")
        private String predictTagsResult;

        @JsonProperty("rephraser_result")
        private String rephraserResult;

        @JsonProperty("request_id")
        private String requestId; // data 内部还有一request_id
    }
}
