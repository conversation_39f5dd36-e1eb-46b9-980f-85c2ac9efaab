package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class VolcengineTextToImageData {

    @JsonProperty("algorithm_base_resp")
    private AlgorithmBaseResp algorithmBaseResp;

    @JsonProperty("binary_data_base64")
    private List<String> binaryDataBase64;

    @JsonProperty("image_urls")
    private List<String> imageUrls;
}
