package com.wlink.agent.client.model.doubao;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 豆包图像生成API - 响应模型
 * 
 * <AUTHOR> Assistant
 */
@Data
public class DoubaoImageGenerationResponse {
    
    /**
     * 使用的模型名
     */
    @JsonProperty("model")
    private String model;
    
    /**
     * 创建时间
     */
    @JsonProperty("created")
    private Long created;
    
    /**
     * 生成的图片数据列
     */
    @JsonProperty("data")
    private List<DoubaoImageData> data;
    
    /**
     * 使用统计信息
     */
    @JsonProperty("usage")
    private DoubaoUsage usage;
    
    public DoubaoImageGenerationResponse() {
    }
    
    public DoubaoImageGenerationResponse(String model, Long created, List<DoubaoImageData> data, DoubaoUsage usage) {
        this.model = model;
        this.created = created;
        this.data = data;
        this.usage = usage;
    }
} 
