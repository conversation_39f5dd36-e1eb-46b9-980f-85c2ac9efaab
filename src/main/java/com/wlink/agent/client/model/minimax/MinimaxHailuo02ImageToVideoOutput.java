package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax Hailuo-02 Standard Image-to-Video 输出结果模型
 * 基于 fal-ai/minimax/hailuo-02/standard/image-to-video OpenAPI 规范
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MinimaxHailuo02ImageToVideoOutput {
    
    /**
     * 生成的视频
     * 必填字段
     */
    @JsonProperty("video")
    private FalFile video;
    
    /**
     * FAL 文件模型
     * 表示生成的视频文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FalFile {
        
        /**
         * 文件的大小（以字节为单位）
         * 示例: 4404019
         */
        @JsonProperty("file_size")
        private Integer fileSize;
        
        /**
         * 文件的名称
         * 如果未提供，将自动生成
         * 示例: "z9RV14K95DvU.png"
         */
        @JsonProperty("file_name")
        private String fileName;
        
        /**
         * 该文件的 MIME 类型
         * 示例: "video/mp4"
         */
        @JsonProperty("content_type")
        private String contentType;
        
        /**
         * 可以下载文件的 URL
         * 必填字段
         * 示例: "https://v3.fal.media/files/monkey/xF9OsLwGjjNURyAxD8RM1_output.mp4"
         */
        @JsonProperty("url")
        private String url;
        
        /**
         * 文件数据（二进制格式）
         * 通常不使用，文件通过 URL 访问
         */
        @JsonProperty("file_data")
        private String fileData;
        
        /**
         * 获取文件大小的可读格式
         * 
         * @return 格式化的文件大小字符串
         */
        public String getFormattedFileSize() {
            if (fileSize == null) {
                return "未知";
            }
            
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else if (fileSize < 1024 * 1024 * 1024) {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            } else {
                return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
            }
        }
        
        /**
         * 检查是否为视频文件
         * 
         * @return 如果是视频文件返回 true
         */
        public boolean isVideoFile() {
            return contentType != null && contentType.startsWith("video/");
        }
        
        /**
         * 获取文件扩展名
         * 
         * @return 文件扩展名（不包含点号）
         */
        public String getFileExtension() {
            if (fileName == null) {
                return null;
            }
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
                return fileName.substring(lastDotIndex + 1).toLowerCase();
            }
            return null;
        }
    }
    
    /**
     * 检查输出是否有效
     * 
     * @return 如果输出有效返回 true
     */
    public boolean isValid() {
        return video != null && video.getUrl() != null && !video.getUrl().trim().isEmpty();
    }
    
    /**
     * 获取视频URL
     * 
     * @return 视频URL，如果不存在返回 null
     */
    public String getVideoUrl() {
        return video != null ? video.getUrl() : null;
    }
    
    /**
     * 获取视频文件名
     * 
     * @return 视频文件名，如果不存在返回 null
     */
    public String getVideoFileName() {
        return video != null ? video.getFileName() : null;
    }
    
    /**
     * 获取视频文件大小
     * 
     * @return 视频文件大小（字节），如果不存在返回 null
     */
    public Integer getVideoFileSize() {
        return video != null ? video.getFileSize() : null;
    }
    
    /**
     * 获取格式化的视频文件大小
     * 
     * @return 格式化的文件大小字符串
     */
    public String getFormattedVideoFileSize() {
        return video != null ? video.getFormattedFileSize() : "未知";
    }
}
