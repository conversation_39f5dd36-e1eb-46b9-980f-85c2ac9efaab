package com.wlink.agent.client.model.comfyui;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ComfyUI 运行响应数据
 */
@Data
public class ComfyUIRunResponseData {
    
    /**
     * WebSocket连接URL
     */
    @JsonProperty("netWssUrl")
    private String netWssUrl;
    
    /**
     * 任务ID
     */
    @JsonProperty("taskId")
    private String taskId;
    
    /**
     * 客户端ID
     */
    @JsonProperty("clientId")
    private String clientId;
    
    /**
     * 任务状态
     */
    @JsonProperty("taskStatus")
    private String taskStatus;
    
    /**
     * 提示信息
     */
    @JsonProperty("promptTips")
    private String promptTips;
}
