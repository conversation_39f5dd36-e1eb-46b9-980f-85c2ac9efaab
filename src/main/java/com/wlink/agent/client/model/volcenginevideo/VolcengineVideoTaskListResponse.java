package com.wlink.agent.client.model.volcenginevideo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 火山引擎视频生成任务列表响应
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VolcengineVideoTaskListResponse {

    /**
     * 总任务数
     */
    private Integer total;

    /**
     * 任务列表
     */
    private List<VideoTaskItem> items;

    /**
     * 视频生成任务
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VideoTaskItem {
        /**
         * 任务ID
         */
        private String id;

        /**
         * 模型名称
         */
        private String model;

        /**
         * 任务状态：queued（排队中）、running（运行中）
         * cancelled（已取消）、succeeded（成功）、failed（失败）
         */
        private String status;

        /**
         * 任务内容（成功时包含视频URL
         */
        private VideoContent content;

        /**
         * Token使用情况
         */
        private Usage usage;

        /**
         * 创建时间（Unix时间戳）
         */
        @JsonProperty("created_at")
        private Long createdAt;

        /**
         * 更新时间（Unix时间戳）
         */
        @JsonProperty("updated_at")
        private Long updatedAt;
    }

    /**
     * 视频内容
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VideoContent {
        /**
         * 视频URL
         */
        @JsonProperty("video_url")
        private String videoUrl;
    }

    /**
     * Token使用情况
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Usage {
        /**
         * 生成令牌
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        /**
         * 总令牌数
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }
} 
