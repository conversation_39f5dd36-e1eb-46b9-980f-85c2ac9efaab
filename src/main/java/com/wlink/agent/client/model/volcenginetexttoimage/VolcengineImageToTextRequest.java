package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolcengineImageToTextRequest {

    @JsonProperty("ServiceId")
    private String serviceId;

    @JsonProperty("StoreUri")
    private String storeUri;
}
