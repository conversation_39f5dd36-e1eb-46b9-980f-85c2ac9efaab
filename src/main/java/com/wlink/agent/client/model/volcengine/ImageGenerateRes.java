package com.wlink.agent.client.model.volcengine;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 火山引擎图片生成结果
 * @date 2025/5/9 15:10
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Schema(description = "火山引擎图片生成结果")
public class ImageGenerateRes {

    @Schema(description = "生成的图片URL", example = "http://example.com/image.png")
    private String imageUrl;

    private String code;

    private String message;


    public ImageGenerateRes(String imageUrl){
        this.imageUrl = imageUrl;
    }

}
