package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class VolcengineImageToTextResponse {

    @JsonProperty("ResponseMetadata")
    private ResponseMetadata responseMetadata;

    @JsonProperty("Data")
    private VolcengineImageToTextResponseData data;
    
    // Helper method to check for errors in the response
    public boolean hasError() {
        return this.responseMetadata != null && this.responseMetadata.getError() != null;
    }

    // Helper method to get error details
    public ErrorDetails getErrorDetails() {
        if (hasError()) {
            return this.responseMetadata.getError();
        }
        return null;
    }
}
