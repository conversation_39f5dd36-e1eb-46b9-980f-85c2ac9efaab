package com.wlink.agent.client.model.volcenginevideo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 视频生成请求参数模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VolcengineVideoGenerationRequest {
    
    /**
     * 模型名称
     */
    @JsonProperty("model")
    private String model;
    
    /**
     * 内容数组，可以包含文本和图片
     */
    @JsonProperty("content")
    private List<ContentItem> content;


    @JsonProperty("callback_url")
    private String callbackUrl;


    /**
     * 内容项基
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static abstract class ContentItem {
        /**
         * 内容类型：text image_url
         */
        @JsonProperty("type")
        private String type;
        
        /**
         * 角色，可用于首尾帧视频生成：first_frame, last_frame
         */
        @JsonProperty("role")
        private String role;
    }
    
    /**
     * 文本内容
     */
    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class TextContentItem extends ContentItem {
        
        /**
         * 文本内容
         */
        @JsonProperty("text")
        private String text;
        
        /**
         * 创建一个文本内容项
         * 
         * @param textContent 文本内容
         */
        public TextContentItem(String textContent) {
            this.setType("text");
            this.text = textContent;
        }
    }
    
    /**
     * 图片内容
     */
    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageUrlContentItem extends ContentItem {
        
        /**
         * 图片URL信息
         */
        @JsonProperty("image_url")
        private ImageUrl imageUrl;
        
        /**
         * 创建一个图片内容项
         * 
         * @param url 图片URL
         */
        public ImageUrlContentItem(String url) {
            this.setType("image_url");
            this.imageUrl = new ImageUrl(url);
        }
        
        /**
         * 创建一个带角色的图片内容项
         * 
         * @param url 图片URL
         * @param role 角色(first_frame/last_frame)
         */
        public ImageUrlContentItem(String url, String role) {
            this.setType("image_url");
            this.setRole(role);
            this.imageUrl = new ImageUrl(url);
        }
        
        /**
         * 图片URL包装
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ImageUrl {
            /**
             * 图片URL
             */
            @JsonProperty("url")
            private String url;
        }
    }
    
    /**
     * 构建文生视频请求
     * 
     * @param model 模型名称
     * @param text 提示词文
     * @return 请求对象
     */
    public static VolcengineVideoGenerationRequest buildTextToVideoRequest(String model, String text,String callbackUrl) {
        TextContentItem textItem = new TextContentItem(text);
        List<ContentItem> contentItems = new ArrayList<>();
        contentItems.add(textItem);
        
        return VolcengineVideoGenerationRequest.builder()
                .model(model)
                .content(contentItems)
                .callbackUrl(callbackUrl)
                .build();
    }
    
    /**
     * 构建图生视频请求
     * 
     * @param model 模型名称
     * @param text 提示词文
     * @param imageUrl 参考图片URL
     * @return 请求对象
     */
    public static VolcengineVideoGenerationRequest buildImageToVideoRequest(String model, String text, String imageUrl, String callbackUrl) {
        TextContentItem textItem = new TextContentItem(text);
        ImageUrlContentItem imageItem = new ImageUrlContentItem(imageUrl);
        List<ContentItem> contentItems = new ArrayList<>();
        contentItems.add(textItem);
        contentItems.add(imageItem);
        
        return VolcengineVideoGenerationRequest.builder()
                .model(model)
                .content(contentItems)
                .callbackUrl(callbackUrl)
                .build();
    }
    
    /**
     * 构建首尾帧生成视频请
     * 
     * @param model 模型名称
     * @param text 提示词文
     * @param firstFrameUrl 首帧图片URL
     * @param lastFrameUrl 尾帧图片URL
     * @return 请求对象
     */
    public static VolcengineVideoGenerationRequest buildFirstLastFrameVideoRequest(String model, String text, String firstFrameUrl, String lastFrameUrl,String callbackUrl) {
        TextContentItem textItem = new TextContentItem(text);
        ImageUrlContentItem firstFrameItem = new ImageUrlContentItem(firstFrameUrl, "first_frame");
        ImageUrlContentItem lastFrameItem = new ImageUrlContentItem(lastFrameUrl, "last_frame");
        
        List<ContentItem> contentItems = new ArrayList<>();
        contentItems.add(textItem);
        contentItems.add(firstFrameItem);
        contentItems.add(lastFrameItem);
        
        return VolcengineVideoGenerationRequest.builder()
                .model(model)
                .content(contentItems)
                .callbackUrl(callbackUrl)
                .build();
    }
} 
