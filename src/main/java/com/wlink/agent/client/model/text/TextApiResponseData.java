package com.wlink.agent.client.model.text;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表示文本API响应中的data部分
 */
@Data
@NoArgsConstructor
public class TextApiResponseData {
    
    /**
     * 工作流运行ID
     */
    private String id;
    
    /**
     * 工作流ID
     */
    @JsonProperty("workflow_id")
    private String workflowId;
    
    /**
     * 状
     */
    private String status;
    
    /**
     * 输出内容
     */
    private TextApiResponseOutputs outputs;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 耗时
     */
    @JsonProperty("elapsed_time")
    private double elapsedTime;
    
    /**
     * 总Token
     */
    @JsonProperty("total_tokens")
    private int totalTokens;
    
    /**
     * 总步
     */
    @JsonProperty("total_steps")
    private int totalSteps;
    
    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private long createdAt;
    
    /**
     * 完成时间
     */
    @JsonProperty("finished_at")
    private long finishedAt;
} 
