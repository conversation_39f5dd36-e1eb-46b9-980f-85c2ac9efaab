package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax视频生成响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniMaxVideoGenerationResponse {
    
    /**
     * 视频生成异步任务的任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private BaseResponse baseResp;
    
    /**
     * 基础响应模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseResponse {
        /**
         * 状态码
         * 0：请求成功
         * 1002：触发限流，请稍后再试
         * 1004：账号鉴权失败，请检查 API-Key 是否填写正确
         * 1008：账号余额不足
         * 1026：视频描述涉及敏感内容，请调整
         * 2013：传入参数异常，请检查入参是否按要求填写
         * 2049：无效的api key，请检查api key
         */
        @JsonProperty("status_code")
        private Integer statusCode;
        
        /**
         * 具体错误详情
         */
        @JsonProperty("status_msg")
        private String statusMsg;
    }
    
    /**
     * 判断请求是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return baseResp != null && baseResp.getStatusCode() != null && baseResp.getStatusCode() == 0;
    }
    
    /**
     * 获取错误信息
     * 
     * @return 错误信息，如果成功则返回null
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return baseResp != null ? baseResp.getStatusMsg() : "未知错误";
    }
    
    /**
     * 获取状态码
     * 
     * @return 状态码
     */
    public Integer getStatusCode() {
        return baseResp != null ? baseResp.getStatusCode() : null;
    }
}
