package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "Logo信息")
public class LogoInfo {

    @Schema(description = "是否添加Logo", example = "false")
    @JsonProperty("add_logo")
    private Boolean addLogo = false;

    @Schema(description = "Logo位置: 0-右下 1-左下 2-左上 3-右上, example = ", example = "0")
    @JsonProperty("position")
    private Integer position = 0; // 0-右下 1-左下 2-左上 3-右上

    @Schema(description = "Logo语言: 0-中文（AI生成 1-英文（Generated by AI, example = ", example = "0")
    @JsonProperty("language")
    private Integer language = 0; // 0-中文（AI生成 1-英文（Generated by AI

    @Schema(description = "Logo透明, example = ", example = "0.3")
    @JsonProperty("opacity")
    private Float opacity = 0.3f;

    @Schema(description = "Logo文本内容", example = "AI生成")
    @JsonProperty("logo_text_content")
    private String logoTextContent;
}
