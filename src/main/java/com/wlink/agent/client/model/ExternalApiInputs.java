package com.wlink.agent.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalApiInputs {

    @JsonProperty("input_Prompt")
    private String inputPrompt;

    /**
     * function
     * type
     * contentID
     * taskID
     * style
     */
    @JsonProperty("function")
    private String function;

    @JsonProperty("type")
    private String type;

    @JsonProperty("contentID")
    private String contentId;

    @JsonProperty("TaskID")
    private String taskId;

    @JsonProperty("style")
    private String style;


    @JsonProperty("img01_URL")
    private String img01Url;

     @JsonProperty("img02_URL")
     private String img02Url;
     @JsonProperty("img03_URL")
     private String img03Url;
     @JsonProperty("img04_URL")
     private String img04Url;


} 
