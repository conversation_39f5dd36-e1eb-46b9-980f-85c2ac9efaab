package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@Data
public class VolcengineImageResponse {

    @JsonProperty("code")
    private int code;

    @JsonProperty("data")
    private ResponseData data;

    @JsonProperty("message")
    private String message;

    @JsonProperty("request_id")
    private String requestId; // This appears twice in the example, using the outer one

    @JsonProperty("status")
    private int status; // Same as 'code' in the example

    @JsonProperty("time_elapsed")
    private String timeElapsed;

    // Get<PERSON> and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public ResponseData getData() {
        return data;
    }

    public void setData(ResponseData data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getTimeElapsed() {
        return timeElapsed;
    }

    public void setTimeElapsed(String timeElapsed) {
        this.timeElapsed = timeElapsed;
    }

    @Override
    public String toString() {
        return "VolcengineImageResponse{" +
                "code=" + code +
                ", data=" + data +
                ", message='" + message + '\'' +
                ", requestId='" + requestId + '\'' +
                ", status=" + status +
                ", timeElapsed='" + timeElapsed + '\'' +
                '}';
    }

    public static class ResponseData {
        @JsonProperty("algorithm_base_resp")
        private AlgorithmBaseResponse algorithmBaseResp;

        @JsonProperty("binary_data_base64")
        private List<String> binaryDataBase64;

        @JsonProperty("image_urls")
        private List<String> imageUrls;

        @JsonProperty("pe_result")
        private String peResult; // Assuming String, adjust if it's a complex object

        @JsonProperty("predict_tags_result")
        private String predictTagsResult; // Assuming String

        @JsonProperty("rephraser_result")
        private String rephraserResult; // Assuming String

        @JsonProperty("request_id")
        private String innerRequestId; // request_id inside data

        // Getters and Setters
        public AlgorithmBaseResponse getAlgorithmBaseResp() {
            return algorithmBaseResp;
        }

        public void setAlgorithmBaseResp(AlgorithmBaseResponse algorithmBaseResp) {
            this.algorithmBaseResp = algorithmBaseResp;
        }

        public List<String> getBinaryDataBase64() {
            return binaryDataBase64;
        }

        public void setBinaryDataBase64(List<String> binaryDataBase64) {
            this.binaryDataBase64 = binaryDataBase64;
        }

        public List<String> getImageUrls() {
            return imageUrls;
        }

        public void setImageUrls(List<String> imageUrls) {
            this.imageUrls = imageUrls;
        }

        public String getPeResult() {
            return peResult;
        }

        public void setPeResult(String peResult) {
            this.peResult = peResult;
        }

        public String getPredictTagsResult() {
            return predictTagsResult;
        }

        public void setPredictTagsResult(String predictTagsResult) {
            this.predictTagsResult = predictTagsResult;
        }

        public String getRephraserResult() {
            return rephraserResult;
        }

        public void setRephraserResult(String rephraserResult) {
            this.rephraserResult = rephraserResult;
        }

        public String getInnerRequestId() {
            return innerRequestId;
        }

        public void setInnerRequestId(String innerRequestId) {
            this.innerRequestId = innerRequestId;
        }

        @Override
        public String toString() {
            return "ResponseData{" +
                    "algorithmBaseResp=" + algorithmBaseResp +
                    ", binaryDataBase64=" + binaryDataBase64 +
                    ", imageUrls=" + imageUrls +
                    ", peResult='" + peResult + '\'' +
                    ", predictTagsResult='" + predictTagsResult + '\'' +
                    ", rephraserResult='" + rephraserResult + '\'' +
                    ", innerRequestId='" + innerRequestId + '\'' +
                    '}';
        }
    }

    public static class AlgorithmBaseResponse {
        @JsonProperty("status_code")
        private int statusCode;

        @JsonProperty("status_message")
        private String statusMessage;

        // Getters and Setters
        public int getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(int statusCode) {
            this.statusCode = statusCode;
        }

        public String getStatusMessage() {
            return statusMessage;
        }

        public void setStatusMessage(String statusMessage) {
            this.statusMessage = statusMessage;
        }

        @Override
        public String toString() {
            return "AlgorithmBaseResponse{" +
                    "statusCode=" + statusCode +
                    ", statusMessage='" + statusMessage + '\'' +
                    '}';
        }
    }
}
