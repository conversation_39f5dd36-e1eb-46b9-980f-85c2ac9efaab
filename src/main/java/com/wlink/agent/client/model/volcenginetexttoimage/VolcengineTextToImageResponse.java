package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class VolcengineTextToImageResponse {

    @JsonProperty("code")
    private int code;

    @JsonProperty("data")
    private VolcengineTextToImageData data;

    // General fields that might be present in Volcengine API responses, though not explicitly in text-to-image doc
    @JsonProperty("message")
    private String message;

    @JsonProperty("request_id")
    private String requestId;
}
