package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MiniMax Hailuo-02 Standard Image-to-Video 输入参数模型
 * 基于 fal-ai/minimax/hailuo-02/standard/image-to-video OpenAPI 规范
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MinimaxHailuo02ImageToVideoInput {
    
    /**
     * 是否使用模型的提示优化器
     * 默认值: true
     */
    @JsonProperty("prompt_optimizer")
    @Builder.Default
    private Boolean promptOptimizer = true;
    
    /**
     * 视频时长（以秒为单位）
     * 枚举值: "6", "10"
     * 默认值: "6"
     * 注意: 1080p 分辨率不支持 10 秒的视频
     */
    @JsonProperty("duration")
    @Builder.Default
    private String duration = "6";
    
    /**
     * 生成视频的描述提示词
     * 最大长度: 2000 字符
     * 必填参数
     */
    @JsonProperty("prompt")
    private String prompt;
    
    /**
     * 图片URL
     * 必填参数
     * 示例: "https://storage.googleapis.com/falserverless/model_tests/minimax/1749891352437225630-389852416840474630_1749891352.png"
     */
    @JsonProperty("image_url")
    private String imageUrl;
    
    /**
     * 构建标准图生视频请求
     * 
     * @param prompt 提示词
     * @param imageUrl 图片URL
     * @return 请求对象
     */
    public static MinimaxHailuo02ImageToVideoInput buildStandardRequest(String prompt, String imageUrl) {
        return MinimaxHailuo02ImageToVideoInput.builder()
                .prompt(prompt)
                .imageUrl(imageUrl)
                .build();
    }
    
    /**
     * 构建标准图生视频请求（指定时长）
     * 
     * @param prompt 提示词
     * @param imageUrl 图片URL
     * @param duration 视频时长（"6" 或 "10"）
     * @return 请求对象
     */
    public static MinimaxHailuo02ImageToVideoInput buildStandardRequest(String prompt, String imageUrl, String duration) {
        return MinimaxHailuo02ImageToVideoInput.builder()
                .prompt(prompt)
                .imageUrl(imageUrl)
                .duration(duration)
                .build();
    }
    
    /**
     * 构建标准图生视频请求（完整参数）
     * 
     * @param prompt 提示词
     * @param imageUrl 图片URL
     * @param duration 视频时长（"6" 或 "10"）
     * @param promptOptimizer 是否使用提示优化器
     * @return 请求对象
     */
    public static MinimaxHailuo02ImageToVideoInput buildStandardRequest(String prompt, String imageUrl, 
                                                                        String duration, Boolean promptOptimizer) {
        return MinimaxHailuo02ImageToVideoInput.builder()
                .prompt(prompt)
                .imageUrl(imageUrl)
                .duration(duration)
                .promptOptimizer(promptOptimizer)
                .build();
    }
    
    /**
     * 设置提示优化器
     * 
     * @param promptOptimizer 是否启用提示优化器
     * @return 当前对象
     */
    public MinimaxHailuo02ImageToVideoInput withPromptOptimizer(Boolean promptOptimizer) {
        this.promptOptimizer = promptOptimizer;
        return this;
    }
    
    /**
     * 设置视频时长
     * 
     * @param duration 视频时长（"6" 或 "10"）
     * @return 当前对象
     */
    public MinimaxHailuo02ImageToVideoInput withDuration(String duration) {
        this.duration = duration;
        return this;
    }
    
    /**
     * 验证请求参数
     * 
     * @throws IllegalArgumentException 如果参数无效
     */
    public void validate() {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("prompt 不能为空");
        }
        if (prompt.length() > 2000) {
            throw new IllegalArgumentException("prompt 长度不能超过 2000 字符");
        }
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("imageUrl 不能为空");
        }
        if (duration != null && !"6".equals(duration) && !"10".equals(duration)) {
            throw new IllegalArgumentException("duration 只能是 \"6\" 或 \"10\"");
        }
    }
}
