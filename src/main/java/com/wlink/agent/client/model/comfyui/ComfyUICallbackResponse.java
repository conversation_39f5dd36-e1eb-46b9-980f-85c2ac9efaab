package com.wlink.agent.client.model.comfyui;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ComfyUI 回调响应
 */
@Data
public class ComfyUICallbackResponse {
    
    /**
     * 响应代码
     */
    @JsonProperty("code")
    private Integer code;
    
    /**
     * 响应消息
     */
    @JsonProperty("msg")
    private String msg;
    
    /**
     * 创建成功响应
     */
    public static ComfyUICallbackResponse success() {
        ComfyUICallbackResponse response = new ComfyUICallbackResponse();
        response.setCode(0);
        response.setMsg("success");
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static ComfyUICallbackResponse failure(String message) {
        ComfyUICallbackResponse response = new ComfyUICallbackResponse();
        response.setCode(-1);
        response.setMsg(message);
        return response;
    }
}
