package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class VolcengineImageToTextResponseData {

    @JsonProperty("Result") // Assuming the generated text is in a field named "Result"
    private String result;
    
    // Add other fields from the 'Data' part of the API response if there are any
    // For example, if there's a task ID or other relevant information:
    // @JsonProperty("TaskId")
    // private String taskId;
}
