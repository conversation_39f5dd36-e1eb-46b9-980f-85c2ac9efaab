package com.wlink.agent.client.model.volcenginetexttoimage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class VolcengineTextToImageRequest {

    @JsonProperty("req_key")
    private String reqKey;

    @JsonProperty("prompt")
    private String prompt;

    @JsonProperty("seed")
    private Integer seed;

    @JsonProperty("scale")
    private Float scale;

    @JsonProperty("ddim_steps")
    private Integer ddimSteps;

    @JsonProperty("width")
    private Integer width;

    @JsonProperty("height")
    private Integer height;

    @JsonProperty("use_pre_llm")
    private Boolean usePreLlm;

    @JsonProperty("use_sr")
    private Boolean useSr;

    @JsonProperty("return_url")
    private Boolean returnUrl;

    @JsonProperty("logo_info")
    private LogoInfo logoInfo;
}
