package com.wlink.agent.client.model.volcengine;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Schema(description = "涂抹编辑请求参数")
public class VolcengineInpaintingEditRequest {

    @NotEmpty(message = "图片URL不能为空")
    @Schema(description = "图片URL列表，[原图, 原图标注后的mask]", required = true)
    private List<String> imageUrls;

    @Schema(required = false)
    private String customPrompt;

    @Schema(description = "请求Key", required = true)
    private String reqKey = "i2i_inpainting_edit";

    @Schema(description = "缩放因子", required = false)
    private Integer scale = 5;

    @Schema(description = "随机种子", required = false)
    private Integer seed = -1;

    @Schema(description = "步数", required = false)
    private Integer steps = 25;
} 
