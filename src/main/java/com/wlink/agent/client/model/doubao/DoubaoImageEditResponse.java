package com.wlink.agent.client.model.doubao;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 豆包图像编辑API - 响应模型
 * 
 * <AUTHOR> Assistant
 */
@Data
public class DoubaoImageEditResponse {
    
    /**
     * 使用的模型名称
     */
    @JsonProperty("model")
    private String model;
    
    /**
     * 创建时间的 Unix 时间戳（秒）
     */
    @JsonProperty("created")
    private Long created;
    
    /**
     * 输出图像的信息，包括图像下载的 URL 或 Base64
     */
    @JsonProperty("data")
    private List<DoubaoImageEditData> data;
    
    /**
     * 本次请求的用量信息
     */
    @JsonProperty("usage")
    private DoubaoImageEditUsage usage;
    
    /**
     * 错误信息（如果发生错误）
     */
    @JsonProperty("error")
    private DoubaoImageEditError error;
    
    public DoubaoImageEditResponse() {
    }
    
    public DoubaoImageEditResponse(String model, Long created, List<DoubaoImageEditData> data, DoubaoImageEditUsage usage) {
        this.model = model;
        this.created = created;
        this.data = data;
        this.usage = usage;
    }
    
    /**
     * 图像数据模型
     */
    @Data
    public static class DoubaoImageEditData {
        /**
         * 图像下载URL（当response_format为url时）
         */
        @JsonProperty("url")
        private String url;
        
        /**
         * Base64编码的图像数据（当response_format为b64_json时）
         */
        @JsonProperty("b64_json")
        private String b64Json;
    }
    
    /**
     * 用量信息模型
     */
    @Data
    public static class DoubaoImageEditUsage {
        /**
         * 模型生成的图片张数
         */
        @JsonProperty("generated_images")
        private Integer generatedImages;
        
        /**
         * 模型生成的图片所用的 token 数量
         * 计算公式为：长*宽/256，四舍五入取整
         */
        @JsonProperty("output_tokens")
        private Integer outputTokens;
        
        /**
         * 本次请求消耗的总token数量
         * 由于输入不进行token计算，故total_tokens和output_tokens一致
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }
    
    /**
     * 错误信息模型
     */
    @Data
    public static class DoubaoImageEditError {
        /**
         * 错误代码
         */
        @JsonProperty("code")
        private String code;
        
        /**
         * 错误提示信息
         */
        @JsonProperty("message")
        private String message;
    }
}
