package com.wlink.agent.client.model.volcenginevideo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频任务查询响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolcengineVideoTaskQueryResponse {
    
    /**
     * 任务ID
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 模型名称
     */
    @JsonProperty("model")
    private String model;
    
    /**
     * 任务状
     * - pending: 任务排队
     * - running: 任务处理
     * - succeeded: 任务成功
     * - canceled: 任务被取
     * - failed: 任务失败
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 视频内容
     */
    @JsonProperty("content")
    private VideoContent content;
    
    /**
     * 使用量统
     */
    @JsonProperty("usage")
    private UsageInfo usage;
    
    /**
     * 创建时间，Unix时间
     */
    @JsonProperty("created_at")
    private Long createdAt;
    
    /**
     * 更新时间，Unix时间
     */
    @JsonProperty("updated_at")
    private Long updatedAt;
    
    /**
     * 错误信息
     */
    @JsonProperty("error")
    private ErrorInfo error;
    
    /**
     * 视频内容
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoContent {
        /**
         * 视频URL
         */
        @JsonProperty("video_url")
        private String videoUrl;
    }
    
    /**
     * 使用量统
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UsageInfo {
        /**
         * 完成所需Token
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        
        /**
         * 总Token
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }
    
    /**
     * 错误信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorInfo {
        /**
         * 错误代码
         */
        @JsonProperty("code")
        private String code;
        
        /**
         * 错误消息
         */
        @JsonProperty("message")
        private String message;
    }
} 
