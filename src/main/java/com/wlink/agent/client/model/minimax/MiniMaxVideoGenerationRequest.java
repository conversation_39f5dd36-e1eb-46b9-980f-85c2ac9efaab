package com.wlink.agent.client.model.minimax;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * MiniMax视频生成请求参数模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MiniMaxVideoGenerationRequest {
    
    /**
     * 调用的算法模型ID
     * 可选项：MiniMax-Hailuo-02, T2V-01-Director, I2V-01-Director, S2V-01, I2V-01, I2V-01-live, T2V-01
     */
    @JsonProperty("model")
    private String model;
    
    /**
     * 生成视频的描述（最大支持2000字符）
     */
    @JsonProperty("prompt")
    private String prompt;
    
    /**
     * 是否自动优化prompt，默认为true
     */
    @JsonProperty("prompt_optimizer")
    private Boolean promptOptimizer;
    
    /**
     * 生成视频时长，单位秒（s）
     * 枚举值：6，10，默认值为6
     */
    @JsonProperty("duration")
    private Integer duration;
    
    /**
     * 生成视频的分辨率
     * 枚举值：768P，1080P，默认值为768P
     */
    @JsonProperty("resolution")
    private String resolution;
    
    /**
     * 首帧图片
     * 支持Base64编码或公网URL
     * 当model选择为I2V-01、I2V-01-Director、I2V-01-live时为必填参数
     */
    @JsonProperty("first_frame_image")
    private String firstFrameImage;
    
    /**
     * 主体参考数组
     * 仅当model选择为S2V-01时可用，目前仅支持单主体参考（数组长度为1）
     */
    @JsonProperty("subject_reference")
    private List<SubjectReference> subjectReference;
    
    /**
     * 回调URL
     * 用于接收任务状态变化通知
     */
    @JsonProperty("callback_url")
    private String callbackUrl;
    
    /**
     * 主体参考模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SubjectReference {
        /**
         * 主体图片URL或Base64编码
         */
        @JsonProperty("image")
        private String image;
        
        /**
         * 主体描述
         */
        @JsonProperty("description")
        private String description;
    }
    
    /**
     * 构建文生视频请求
     * 
     * @param model 模型名称
     * @param prompt 提示词
     * @param duration 视频时长（秒）
     * @param resolution 分辨率
     * @return 请求对象
     */
    public static MiniMaxVideoGenerationRequest buildTextToVideoRequest(String model, String prompt, 
                                                                        Integer duration, String resolution) {
        return MiniMaxVideoGenerationRequest.builder()
                .model(model)
                .prompt(prompt)
                .duration(duration)
                .resolution(resolution)
                .promptOptimizer(true)
                .build();
    }
    
    /**
     * 构建图生视频请求
     * 
     * @param model 模型名称
     * @param prompt 提示词
     * @param firstFrameImage 首帧图片
     * @param duration 视频时长（秒）
     * @param resolution 分辨率
     * @return 请求对象
     */
    public static MiniMaxVideoGenerationRequest buildImageToVideoRequest(String model, String prompt, 
                                                                         String firstFrameImage, Integer duration, String resolution) {
        return MiniMaxVideoGenerationRequest.builder()
                .model(model)
                .prompt(prompt)
                .firstFrameImage(firstFrameImage)
                .duration(duration)
                .resolution(resolution)
                .promptOptimizer(true)
                .build();
    }
    
    /**
     * 构建主体参考视频请求
     * 
     * @param model 模型名称（必须为S2V-01）
     * @param prompt 提示词
     * @param subjectImage 主体图片
     * @param subjectDescription 主体描述
     * @param duration 视频时长（秒）
     * @param resolution 分辨率
     * @return 请求对象
     */
    public static MiniMaxVideoGenerationRequest buildSubjectReferenceVideoRequest(String model, String prompt,
                                                                                  String subjectImage, String subjectDescription,
                                                                                  Integer duration, String resolution) {
        SubjectReference subjectRef = SubjectReference.builder()
                .image(subjectImage)
                .description(subjectDescription)
                .build();
        
        return MiniMaxVideoGenerationRequest.builder()
                .model(model)
                .prompt(prompt)
                .subjectReference(List.of(subjectRef))
                .duration(duration)
                .resolution(resolution)
                .promptOptimizer(true)
                .build();
    }
    
    /**
     * 设置回调URL
     * 
     * @param callbackUrl 回调URL
     * @return 当前对象
     */
    public MiniMaxVideoGenerationRequest withCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
        return this;
    }
    
    /**
     * 设置prompt优化器
     * 
     * @param promptOptimizer 是否启用prompt优化
     * @return 当前对象
     */
    public MiniMaxVideoGenerationRequest withPromptOptimizer(Boolean promptOptimizer) {
        this.promptOptimizer = promptOptimizer;
        return this;
    }
}
