package com.wlink.agent.client.model.completion;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 完成API的响应模
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompletionResponse {
    /**
     * 事件类型
     */
    private String event;
    
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * ID
     */
    private String id;
    
    /**
     * 消息ID
     */
    @JsonProperty("message_id")
    private String messageId;
    
    /**
     * 模式
     */
    private String mode;
    
    /**
     * 答案内容
     */
    private String answer;
    
    /**
     * 元数
     */
    private MetadataDTO metadata;
    
    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private Long createdAt;
    
    /**
     * 元数据内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MetadataDTO {
        /**
         * 使用信息
         */
        private UsageDTO usage;
        
        /**
         * 使用信息内部
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class UsageDTO {
            /**
             * 提示词令牌数
             */
            @JsonProperty("prompt_tokens")
            private Integer promptTokens;
            
            /**
             * 提示词单
             */
            @JsonProperty("prompt_unit_price")
            private String promptUnitPrice;
            
            /**
             * 提示词价格单
             */
            @JsonProperty("prompt_price_unit")
            private String promptPriceUnit;
            
            /**
             * 提示词价
             */
            @JsonProperty("prompt_price")
            private String promptPrice;
            
            /**
             * 完成令牌
             */
            @JsonProperty("completion_tokens")
            private Integer completionTokens;
            
            /**
             * 完成单价
             */
            @JsonProperty("completion_unit_price")
            private String completionUnitPrice;
            
            /**
             * 完成价格单位
             */
            @JsonProperty("completion_price_unit")
            private String completionPriceUnit;
            
            /**
             * 完成价格
             */
            @JsonProperty("completion_price")
            private String completionPrice;
            
            /**
             * 总令牌数
             */
            @JsonProperty("total_tokens")
            private Integer totalTokens;
            
            /**
             * 总价
             */
            @JsonProperty("total_price")
            private String totalPrice;
            
            /**
             * 货币
             */
            private String currency;
            
            /**
             * 延迟
             */
            private Double latency;
        }
    }
} 
