package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/5/9 13:57
 */
@NoArgsConstructor
@Data
public class VolcengineImageEnhanceRequest {

    @JsonProperty("req_key")
    private String reqKey = "high_aes_scheduler_svr_controlnet_v2.0";
    @JsonProperty("image_urls")
    private List<String> imageUrls;
    @JsonProperty("prompt")
    private String prompt;
    @JsonProperty("model_version")
    private String modelVersion = "general_controlnet_v2.0";
    @JsonProperty("seed")
    private Integer seed = -1;
    @JsonProperty("scale")
    private Integer scale = 3;
    @JsonProperty("ddim_steps")
    private Integer ddimSteps = 16;
    @JsonProperty("use_rephraser")
    private Boolean useRephraser = true;
    @JsonProperty("controlnet_args")
    private List<ControlnetArgsDTO> controlnetArgs;
    @JsonProperty("return_url")
    private Boolean returnUrl = true;
    @JsonProperty("logo_info")
    private LogoInfoDTO logoInfo;

    @NoArgsConstructor
    @Data
    public static class LogoInfoDTO {
        @JsonProperty("add_logo")
        private Boolean addLogo;
        @JsonProperty("position")
        private Integer position;
        @JsonProperty("language")
        private Integer language;
        @JsonProperty("logo_text_content")
        private String logoTextContent;
    }

    @NoArgsConstructor
    @Data
    public static class ControlnetArgsDTO {
        @JsonProperty("type")
        private String type;
        @JsonProperty("binary_data_index")
        private Integer binaryDataIndex;
        @JsonProperty("strength")
        private Double strength;
    }
}
