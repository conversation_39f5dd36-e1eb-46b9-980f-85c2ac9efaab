package com.wlink.agent.client.model.text;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 表示文本API的整体响应结
 */
@Data
@NoArgsConstructor
public class TextApiResponse {


    @JsonProperty("task_id")
    private String taskId;
    @JsonProperty("workflow_run_id")
    private String workflowRunId;
    @JsonProperty("data")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("workflow_id")
        private String workflowId;
        @JsonProperty("status")
        private String status;
        @JsonProperty("outputs")
        private OutputsDTO outputs;
        @JsonProperty("error")
        private Object error;
        @JsonProperty("elapsed_time")
        private Double elapsedTime;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
        @JsonProperty("total_steps")
        private Integer totalSteps;
        @JsonProperty("created_at")
        private Integer createdAt;
        @JsonProperty("finished_at")
        private Integer finishedAt;

        @NoArgsConstructor
        @Data
        public static class OutputsDTO {
            @JsonProperty("text")
            private List<TextDTO> text;

            @NoArgsConstructor
            @Data
            public static class TextDTO {
                @JsonProperty("name")
                private String name;
                @JsonProperty("prompt")
                private String prompt;
                @JsonProperty("category")
                private String category;
            }
        }
    }
}
