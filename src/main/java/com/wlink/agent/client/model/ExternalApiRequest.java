package com.wlink.agent.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalApiRequest {

    private ExternalApiInputs inputs;

    @JsonProperty("response_mode")
    private String responseMode = "blocking"; // Default as per example

    private String user;

    // This field seems redundant based on the example structure,
    // input_Prompt is inside 'inputs'. If required, uncomment and map appropriately.
    // @JsonProperty("input_prompt")
    // private String topLevelInputPrompt;
} 
