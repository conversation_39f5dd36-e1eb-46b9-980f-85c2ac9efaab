package com.wlink.agent.client;

import com.wlink.agent.dao.po.FalAiFluxRequestLog;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FalAi Flux API请求上下
 * 用于跟踪请求生命周期
 */
@Data
@Accessors(chain = true)
public class FalAiFluxRequestContext {
    /**
     * 请求日志ID
     */
    private Long requestLogId;
    
    /**
     * 请求类型：TEXT_TO_IMAGE, IMAGE_TO_IMAGE
     */
    private String requestType;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 是否已完
     */
    private boolean completed = false;
    
    /**
     * 是否已失
     */
    private boolean failed = false;
    
    /**
     * 创建文本到图像请求上下文
     * @param prompt 提示
     * @return 请求上下
     */
    public static FalAiFluxRequestContext createTextToImageContext(String prompt) {
        return new FalAiFluxRequestContext()
                .setRequestType("TEXT_TO_IMAGE");
    }
    
    /**
     * 创建图像到图像请求上下文
     * @param prompt 提示
     * @param imageUrl 图像URL
     * @return 请求上下
     */
    public static FalAiFluxRequestContext createImageToImageContext(String prompt, String imageUrl) {
        return new FalAiFluxRequestContext()
                .setRequestType("IMAGE_TO_IMAGE");
    }
} 
