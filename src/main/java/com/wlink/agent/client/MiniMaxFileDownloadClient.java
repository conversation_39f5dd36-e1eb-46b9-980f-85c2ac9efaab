package com.wlink.agent.client;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.client.model.minimax.MiniMaxFileRetrieveResponse;
import com.wlink.agent.utils.OssUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MiniMax文件下载客户端
 * 用于下载MiniMax生成的视频文件
 */
@Slf4j
@Component
public class MiniMaxFileDownloadClient {
    
    private static final String DEFAULT_DOWNLOAD_URL = "https://api.minimaxi.com/v1/files/retrieve";
    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";

    private final OkHttpClient httpClient;

    @Value("${minimax.api.download-url:" + DEFAULT_DOWNLOAD_URL + "}")
    private String downloadUrl;

    @Value("${minimax.api-key:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}")
    private String defaultApiKey;

    @Value("${minimax.group-id:1685416442501041}")
    private String groupId;

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private OssUtils ossUtils;
    
    public MiniMaxFileDownloadClient() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS) // 5分钟读取超时，视频文件可能较大
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 获取文件信息并上传到OSS（使用默认API密钥）
     *
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return OSS文件URL
     */
    public CompletableFuture<String> retrieveFileAndUploadToOss(String fileId, String userId) {
        return retrieveFileAndUploadToOss(fileId, userId, defaultApiKey);
    }

    /**
     * 获取文件信息并上传到OSS
     *
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return OSS文件URL
     */
    public CompletableFuture<String> retrieveFileAndUploadToOss(String fileId, String userId, String apiKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始获取MiniMax文件信息: fileId={}", fileId);

                // 构建请求URL
                HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(downloadUrl)).newBuilder()
                        .addQueryParameter("file_id", fileId);

                HttpUrl requestUrl = urlBuilder.build();

                Request request = new Request.Builder()
                        .url(requestUrl)
                        .get()
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("Content-Type", "application/json")
                        .build();

                log.debug("MiniMax文件信息请求URL: {}", requestUrl);

                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                        log.error("MiniMax文件信息获取失败: fileId={}, code={}, error={}",
                                fileId, response.code(), errorBody);
                        throw new BizException("文件信息获取失败: " + response.code() + " - " + errorBody);
                    }

                    ResponseBody responseBody = response.body();
                    if (responseBody == null) {
                        throw new BizException("响应体为空");
                    }

                    String responseStr = responseBody.string();
                    log.info("MiniMax文件信息响应: {}", responseStr);

                    // 解析响应
                    JSONObject jsonResponse = JSON.parseObject(responseStr);
                    MiniMaxFileRetrieveResponse retrieveResponse = JSON.parseObject(responseStr, MiniMaxFileRetrieveResponse.class);

                    // 检查响应状态
                    if (retrieveResponse.getBaseResp() == null ||
                        retrieveResponse.getBaseResp().getStatusCode() == null ||
                        !retrieveResponse.getBaseResp().getStatusCode().equals(0)) {
                        String errorMsg = retrieveResponse.getBaseResp() != null ?
                                retrieveResponse.getBaseResp().getStatusMsg() : "Unknown error";
                        throw new BizException("文件信息获取失败: " + errorMsg);
                    }

                    // 获取下载URL
                    MiniMaxFileRetrieveResponse.FileInfo fileInfo = retrieveResponse.getFile();
                    if (fileInfo == null || fileInfo.getDownloadUrl() == null) {
                        throw new BizException("文件下载URL为空");
                    }

                    String downloadUrl = fileInfo.getDownloadUrl();
                    String filename = fileInfo.getFilename();
                    log.info("获取到文件下载URL: {}, filename: {}", downloadUrl, filename);

//                    // 确定文件扩展名
//                    String fileExtension = "mp4"; // 默认扩展名
//                    if (filename != null && filename.contains(".")) {
//                        fileExtension = filename.substring(filename.lastIndexOf(".") + 1);
//                    }
//
//                    // 构建OSS路径
//                    String ossPath = OSS_PATH.replace("{env}", env)
//                            .replace("{userId}", userId)
//                            .replace("{type}", "video");
//
//                    // 生成唯一文件名
//                    String ossFileName = IdUtil.fastSimpleUUID() + "." + fileExtension;
//                    String fullOssPath = ossPath + ossFileName;
//
//                    log.info("开始上传文件到OSS: downloadUrl={}, ossPath={}", downloadUrl, fullOssPath);
//
//                    // 上传到OSS
//                    String ossUrl = ossUtils.uploadFile(downloadUrl, fullOssPath);
//
//                    // 获取完整的OSS URL
//                    String fullOssUrl = ossUtils.getOssUrl(ossUrl);
//                    log.info("文件上传到OSS成功: originalUrl={}, ossUrl={}", downloadUrl, fullOssUrl);

                    return downloadUrl;
                }

            } catch (IOException e) {
                log.error("MiniMax文件信息获取异常: fileId={}", fileId, e);
                throw new BizException("文件信息获取异常: " + e.getMessage());
            }
        });
    }

    /**
     * 下载文件（使用默认API密钥）
     *
     * @param fileId 文件ID
     * @return 异步文件输入流
     */
    public CompletableFuture<InputStream> downloadFile(String fileId) {
        return downloadFile(fileId, defaultApiKey);
    }
    
    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @param apiKey API密钥
     * @return 异步文件输入流
     */
    public CompletableFuture<InputStream> downloadFile(String fileId, String apiKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始下载MiniMax文件: fileId={}", fileId);

                // 构建请求URL，使用GET方法，参数放在查询参数中
                // 注意：这里仍使用旧的retrieve_content接口来保持向后兼容
                String oldDownloadUrl = downloadUrl.replace("/retrieve", "/retrieve_content");
                HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(oldDownloadUrl)).newBuilder()
                        .addQueryParameter("file_id", fileId);

                // 添加GroupId参数（如果配置了）
                if (groupId != null && !groupId.isEmpty()) {
                    urlBuilder.addQueryParameter("GroupId", groupId);
                }

                HttpUrl requestUrl = urlBuilder.build();

                Request request = new Request.Builder()
                        .url(requestUrl)
                        .get() // 使用GET方法
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("Content-Type", "application/json")
                        .build();

                log.debug("MiniMax文件下载请求URL: {}", requestUrl);

                Response response = httpClient.newCall(request).execute();
                log.info("MiniMax文件下载响应: {}", JSON.toJSONString(response));
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    response.close(); // 确保关闭响应
                    log.error("MiniMax文件下载失败: fileId={}, code={}, error={}",
                            fileId, response.code(), errorBody);
                    throw new BizException("文件下载失败: " + response.code() + " - " + errorBody);
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    response.close(); // 确保关闭响应
                    throw new BizException("响应体为空");
                }

                log.info("MiniMax文件下载成功: fileId={}, contentLength={}",
                        fileId, responseBody.contentLength());

                // 创建一个包装的InputStream，在关闭时同时关闭Response
                return new ResponseBodyInputStream(response, responseBody.byteStream());

            } catch (IOException e) {
                log.error("MiniMax文件下载异常: fileId={}", fileId, e);
                throw new BizException("文件下载异常: " + e.getMessage());
            }
        });
    }

    /**
     * 包装ResponseBody的InputStream，确保在关闭时同时关闭Response
     */
    private static class ResponseBodyInputStream extends InputStream {
        private final Response response;
        private final InputStream inputStream;

        public ResponseBodyInputStream(Response response, InputStream inputStream) {
            this.response = response;
            this.inputStream = inputStream;
        }

        @Override
        public int read() throws IOException {
            return inputStream.read();
        }

        @Override
        public int read(byte[] b) throws IOException {
            return inputStream.read(b);
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            return inputStream.read(b, off, len);
        }

        @Override
        public long skip(long n) throws IOException {
            return inputStream.skip(n);
        }

        @Override
        public int available() throws IOException {
            return inputStream.available();
        }

        @Override
        public void close() throws IOException {
            try {
                inputStream.close();
            } finally {
                response.close();
            }
        }

        @Override
        public void mark(int readlimit) {
            inputStream.mark(readlimit);
        }

        @Override
        public void reset() throws IOException {
            inputStream.reset();
        }

        @Override
        public boolean markSupported() {
            return inputStream.markSupported();
        }
    }
    
    /**
     * 下载文件并返回字节数组
     * 
     * @param fileId 文件ID
     * @return 异步字节数组
     */
    public CompletableFuture<byte[]> downloadFileAsBytes(String fileId) {
        return downloadFileAsBytes(fileId, defaultApiKey);
    }
    
    /**
     * 下载文件并返回字节数组
     *
     * @param fileId 文件ID
     * @param apiKey API密钥
     * @return 异步字节数组
     */
    public CompletableFuture<byte[]> downloadFileAsBytes(String fileId, String apiKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始下载MiniMax文件: fileId={}", fileId);

                // 构建请求URL，使用GET方法，参数放在查询参数中
                // 注意：这里仍使用旧的retrieve_content接口来保持向后兼容
                String oldDownloadUrl = downloadUrl.replace("/retrieve", "/retrieve_content");
                HttpUrl.Builder urlBuilder = HttpUrl.parse(oldDownloadUrl).newBuilder()
                        .addQueryParameter("file_id", fileId);

                // 添加GroupId参数（如果配置了）
                if (groupId != null && !groupId.isEmpty()) {
                    urlBuilder.addQueryParameter("GroupId", groupId);
                }

                HttpUrl requestUrl = urlBuilder.build();

                Request request = new Request.Builder()
                        .url(requestUrl)
                        .get() // 使用GET方法
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("Content-Type", "application/json")
                        .build();

                log.debug("MiniMax文件下载请求URL: {}", requestUrl);

                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                        log.error("MiniMax文件下载失败: fileId={}, code={}, error={}",
                                fileId, response.code(), errorBody);
                        throw new BizException("文件下载失败: " + response.code() + " - " + errorBody);
                    }
                    ResponseBody responseBody = response.body();
                    if (responseBody == null) {
                        throw new BizException("响应体为空");
                    }

                    // 直接读取字节数组
                    byte[] bytes = responseBody.bytes();
                    log.info("MiniMax文件下载并读取完成: fileId={}, size={}bytes", fileId, bytes.length);

                    return bytes;
                }

            } catch (IOException e) {
                log.error("MiniMax文件下载异常: fileId={}", fileId, e);
                throw new BizException("文件下载异常: " + e.getMessage());
            }
        });
    }
}
