package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.minimax.FalQueueStatus;
import com.wlink.agent.client.model.minimax.MinimaxHailuo02ImageToVideoInput;
import com.wlink.agent.client.model.minimax.MinimaxHailuo02ImageToVideoOutput;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MiniMax FAL API 客户端
 * 基于 fal-ai/minimax/hailuo-02/standard/image-to-video OpenAPI 规范
 * 使用 JDK 17 新特性实现
 */
@Slf4j
@Component
public class MinimaxFalClient {
    
    private static final String API_BASE_URL = "https://queue.fal.run";
    private static final String IMAGE_TO_VIDEO_ENDPOINT = "/fal-ai/minimax/hailuo-02/standard/image-to-video";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    
    private static final int MAX_RETRIES = 3;
    private static final long INITIAL_RETRY_DELAY_MS = 1000;
    private static final double RETRY_DELAY_MULTIPLIER = 2.0;
    private static final long MAX_RETRY_DELAY_MS = 10000;
    private static final long POLL_INTERVAL_MS = 2000; // 2秒轮询间隔
    private static final long MAX_WAIT_TIME_MS = 600000; // 10分钟超时
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String apiKey;
    
    /**
     * 创建 MinimaxFalClient 实例
     * 
     * @param apiKey FAL API 密钥
     */
    public MinimaxFalClient(@Value("${fal.ai.api-key:58371be0-d55d-4542-a7ce-7018b4aa0226:44e655c15df8eca02ab38a127e299b94}") String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            log.warn("FAL API 密钥未配置，请在 application.properties 中设置 fal.ai.api-key");
        }
        this.apiKey = apiKey;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    /**
     * 提交图像到视频生成请求
     * 
     * @param input 图像到视频输入参数
     * @return 队列状态响应
     * @throws IOException 如果 API 调用失败
     */
    public FalQueueStatus submitImageToVideoRequest(MinimaxHailuo02ImageToVideoInput input) throws IOException {
        // 验证输入参数
        input.validate();
        
        String url = API_BASE_URL + IMAGE_TO_VIDEO_ENDPOINT;
        String requestBody = objectMapper.writeValueAsString(input);
        
        log.info("提交 MiniMax 图像到视频请求 URL: {}, 请求体: {}", url, requestBody);
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("图像到视频 API 响应状态: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交图像到视频请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                throw new BizException("提交图像到视频请求失败: " + response.code() + " " + response.message(),
                        String.valueOf(response.code()));
            }
            
            return objectMapper.readValue(responseBody, FalQueueStatus.class);
        }
    }
    
    /**
     * 检查请求状态
     * 
     * @param requestId 请求 ID
     * @return 队列状态响应
     * @throws IOException 如果 API 调用失败
     */
    public FalQueueStatus checkQueueStatus(String requestId) throws IOException {
        String statusEndpoint = IMAGE_TO_VIDEO_ENDPOINT + "/requests/" + requestId + "/status";
        String url = API_BASE_URL + statusEndpoint;
        
        Request request = new Request.Builder()
                .url(url + "?logs=1") // 包含日志信息
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.debug("状态查询 API 响应状态: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("检查状态失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                throw new BizException("检查状态失败: " + response.code() + " " + response.message(),
                        String.valueOf(response.code()));
            }
            
            return objectMapper.readValue(responseBody, FalQueueStatus.class);
        }
    }
    
    /**
     * 获取请求结果
     * 
     * @param requestId 请求 ID
     * @return 生成的视频结果
     * @throws IOException 如果 API 调用失败
     */
    public MinimaxHailuo02ImageToVideoOutput getResult(String requestId) throws IOException {
        String resultEndpoint = IMAGE_TO_VIDEO_ENDPOINT + "/requests/" + requestId;
        String url = API_BASE_URL + resultEndpoint;
        
        log.info("结果查询请求 URL: {}, 请求方法: GET", url);
        
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.info("结果查询 API 响应状态: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("获取结果失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                throw new BizException("获取结果失败: " + response.code() + " " + response.message(),
                        String.valueOf(response.code()));
            }
            
            return objectMapper.readValue(responseBody, MinimaxHailuo02ImageToVideoOutput.class);
        }
    }
    
    /**
     * 取消请求
     * 
     * @param requestId 请求 ID
     * @return 是否成功取消
     * @throws IOException 如果 API 调用失败
     */
    public boolean cancelRequest(String requestId) throws IOException {
        String cancelEndpoint = IMAGE_TO_VIDEO_ENDPOINT + "/requests/" + requestId + "/cancel";
        String url = API_BASE_URL + cancelEndpoint;
        
        log.debug("取消请求 URL: {}, 请求方法: PUT", url);
        
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + apiKey)
                .put(RequestBody.create("", JSON_MEDIA_TYPE))
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "{}";
            log.debug("取消请求 API 响应状态: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("取消请求失败: 状态码={}, 响应体:{}", response.code(), responseBody);
                throw new BizException("取消请求失败: " + response.code() + " " + response.message(),
                        String.valueOf(response.code()));
            }
            
            var responseMap = objectMapper.readValue(responseBody, java.util.Map.class);
            return Boolean.TRUE.equals(responseMap.get("success"));
        }
    }
    
    /**
     * 生成视频（同步方法）
     *
     * @param input 图像到视频输入参数
     * @return 生成的视频结果
     * @throws IOException 如果 API 调用失败
     */
    public MinimaxHailuo02ImageToVideoOutput generateVideo(MinimaxHailuo02ImageToVideoInput input) throws IOException {
        log.info("开始生成视频: prompt={}, imageUrl={}", input.getPrompt(), input.getImageUrl());

        // 提交请求
        FalQueueStatus queueStatus = null;
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                queueStatus = submitImageToVideoRequest(input);
                log.info("提交视频生成请求成功: requestId={}, status={}",
                        queueStatus.getRequestId(), queueStatus.getStatus());
                break;
            } catch (IOException e) {
                if (attempt == MAX_RETRIES) {
                    log.error("提交视频生成请求失败，重试次数用尽: {}", e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("提交视频生成请求失败，将在 {}ms 后重试（{}/{}）: {}",
                        retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }

        if (queueStatus == null) {
            throw new IOException("提交视频生成请求失败，未获取到队列状态");
        }

        String requestId = queueStatus.getRequestId();

        // 轮询状态直到完成
        long startTime = System.currentTimeMillis();
        int pollCount = 0;

        while (System.currentTimeMillis() - startTime < MAX_WAIT_TIME_MS) {
            try {
                Thread.sleep(POLL_INTERVAL_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("等待结果时被中断", e);
            }

            pollCount++;

            // 检查状态（带重试）
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                try {
                    queueStatus = checkQueueStatus(requestId);
                    break;
                } catch (IOException e) {
                    if (attempt == MAX_RETRIES) {
                        log.error("检查状态失败，重试次数用尽: {}", e.getMessage());
                        throw e;
                    }
                    long retryDelay = calculateRetryDelayMs(attempt);
                    log.warn("检查状态失败，将在 {}ms 后重试（{}/{}）: {}",
                            retryDelay, attempt, MAX_RETRIES, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("等待重试时被中断", ie);
                    }
                }
            }

            // 输出状态日志
            if (queueStatus.isInQueue()) {
                log.info("请求在队列中，位置: {}, 轮询次数: {}",
                        queueStatus.getQueuePositionDescription(), pollCount);
            } else if (queueStatus.isInProgress()) {
                log.info("请求正在处理中，轮询次数: {}", pollCount);
            }

            // 检查状态
            if (queueStatus.isCompleted()) {
                log.info("请求已完成，总轮询次数: {}, 总耗时: {}ms",
                        pollCount, System.currentTimeMillis() - startTime);
                break;
            } else if (!queueStatus.isRunning()) {
                log.error("请求失败，状态: {}", queueStatus.getStatus());
                throw new IOException("生成视频失败，未知状态: " + queueStatus.getStatus());
            }
        }

        if (!queueStatus.isCompleted()) {
            log.error("请求超时，已等待 {}ms", MAX_WAIT_TIME_MS);
            throw new IOException("生成视频请求超时，状态: " + queueStatus.getStatus());
        }

        // 获取结果
        log.info("获取生成的视频结果");
        MinimaxHailuo02ImageToVideoOutput result = null;

        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                result = getResult(requestId);
                break;
            } catch (IOException e) {
                if (attempt == MAX_RETRIES) {
                    log.error("获取结果失败，重试次数用尽: {}", e.getMessage());
                    throw e;
                }
                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("获取结果失败，将在 {}ms 后重试（{}/{}）: {}",
                        retryDelay, attempt, MAX_RETRIES, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }

        if (result != null && result.isValid()) {
            log.info("成功生成视频: URL={}, 文件大小={}",
                    result.getVideoUrl(), result.getFormattedVideoFileSize());
        } else {
            log.warn("未生成有效的视频结果");
            throw new IOException("未生成有效的视频结果");
        }

        return result;
    }

    /**
     * 异步生成视频
     *
     * @param input 图像到视频输入参数
     * @return 包含结果的 CompletableFuture
     */
    public CompletableFuture<MinimaxHailuo02ImageToVideoOutput> generateVideoAsync(MinimaxHailuo02ImageToVideoInput input) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateVideo(input);
            } catch (IOException e) {
                throw new RuntimeException("生成视频失败", e);
            }
        });
    }

    /**
     * 计算指数退避的重试延迟时间
     *
     * @param attempt 当前尝试次数（从1开始）
     * @return 重试延迟时间（毫秒）
     */
    private long calculateRetryDelayMs(int attempt) {
        long delay = (long) (INITIAL_RETRY_DELAY_MS * Math.pow(RETRY_DELAY_MULTIPLIER, attempt - 1));
        return Math.min(delay, MAX_RETRY_DELAY_MS);
    }
}
