package com.wlink.agent.client;



import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.client.model.volcengine.VolcengineImageEnhanceData;
import com.wlink.agent.client.model.volcengine.VolcengineImageEnhanceRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageResponse;
import com.wlink.agent.client.model.volcengine.VolcengineImageEnhanceResponse;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditRequest;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditResponse;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionRequest;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionResponse;
import com.wlink.agent.client.model.volcengine.VolcengineInpaintingEditRequest;
import com.wlink.agent.client.model.volcengine.VolcengineInpaintingEditResponse;
import com.wlink.agent.client.util.VolcengineSigner;
import com.wlink.agent.dao.po.VendorAccountPo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class VolcengineImageApiClient {

    private static final String DEFAULT_API_HOST = "visual.volcengineapi.com";
    private static final String API_PATH = "/";
    private static final String ACTION = "CVProcess"; // 假设所有接口使用相同的 Action
    private static final String VERSION = "2022-08-31"; // 假设所有接口使用相同的 Version
    private static final Charset UTF_8 = StandardCharsets.UTF_8;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    VolcengineSigner signer;
    @Value("${volcengine.api.host:" + DEFAULT_API_HOST + "}")
    private String apiHost;
    @Value("${volcengine.api.service:cv}")
    private String serviceName;
    @Value("${volcengine.api.region:cn-north-1}")
    private String regionName;
    @Value("${volcengine.access-key:AKLTZGY2MjFjYWNkZWQ4NDcxZTliMGEzY2E0YjA3ZWEzNWM}")
    private String defaultAccessKey;
    @Value("${volcengine.secret-key:TVRZME5UWTVNRFEyTlRsbE5EUmlZMkkwTjJabFpqVTROMlkyWldRek1ESQ==}")
    private String defaultSecretKey;

    /**
     * 生成图片 - 使用默认凭证
     *
     * @param request 请求参数
     * @return 图片生成结果
     */
    public ImageGenerateRes generateImage(VolcengineImageRequest request) {
        // 使用默认凭证
        return generateImageWithCredentials(request, null);
    }

    /**
     * 使用指定的账号凭证生成图
     *
     * @param request 请求参数
     * @param account 账号信息，如果为null则使用默认凭
     * @return 图片生成结果
     */
    public ImageGenerateRes generateImageWithCredentials(VolcengineImageRequest request, VendorAccountPo account) {
        try {
            // 从账号中提取凭证
            String accessKey = account != null ? account.getAccessKeyId() : defaultAccessKey;
            String secretKey = account != null ? account.getSecretAccessKey() : defaultSecretKey;
            
            String requestBodyJson = objectMapper.writeValueAsString(request);
            byte[] bodyBytes = requestBodyJson.getBytes(UTF_8);
            
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("Action", ACTION);
            queryParams.put("Version", VERSION);

            Map<String, String> headersToSign = new HashMap<>();
            headersToSign.put("Host", this.apiHost);
            headersToSign.put("Content-Type", "application/json; charset=utf-8");
            
            // 使用指定账号的凭证进行签
            Map<String, String> signedHeaders = new HashMap<>();
            if (account != null) {
                log.info("Using custom credentials for account: {}", account.getAccountName());
                signedHeaders = signer.signHeadersWithCredentials(
                    "POST", API_PATH, queryParams, headersToSign, bodyBytes,
                    regionName, serviceName, accessKey, secretKey
                );
            } else {
                log.info("Using default credentials");
                signedHeaders = signer.signHeaders(
                    "POST", API_PATH, queryParams, headersToSign, bodyBytes, 
                    regionName, serviceName
                );
            }
            
            HttpUrl.Builder urlBuilder = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(API_PATH);

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
            HttpUrl url = urlBuilder.build();

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(bodyBytes, MediaType.parse("application/json; charset=utf-8")));

            for (Map.Entry<String, String> header : signedHeaders.entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }
            if (!signedHeaders.containsKey("Content-Type")) {
                requestBuilder.addHeader("Content-Type", "application/json; charset=utf-8");
            }

            Request httpRequest = requestBuilder.build();
            log.info("Request URL for Generate Image: {}", httpRequest.url());
            log.info("Request Headers for Generate Image: {}", httpRequest.headers());
            log.info("Request Body for Generate Image: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for Generate Image: {}", response.code());
                log.info("Response Body for Generate Image: {}", responseBodyString);

                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from Generate Image API");
                }
                
                VolcengineImageResponse volcengineImageResponse = objectMapper.readValue(responseBodyString, VolcengineImageResponse.class);
                if (10000 == volcengineImageResponse.getCode()) {
                    VolcengineImageResponse.ResponseData data = volcengineImageResponse.getData();
                    if (data != null && data.getImageUrls() != null && !data.getImageUrls().isEmpty()) {
                        String imgUrl = data.getImageUrls().get(0);
                        return new ImageGenerateRes(imgUrl);
                    }
                }else {
                    return new ImageGenerateRes(null, String.valueOf(volcengineImageResponse.getCode()), volcengineImageResponse.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error calling Volcengine Generate Image API", e);
            throw new BizException("Error processing Volcengine Generate Image request: " + e.getMessage());
        }
        
        throw new BizException("Failed to generate image");
    }

    public ImageGenerateRes enhanceImage(VolcengineImageEnhanceRequest volcengineImageRequest) {
        try {
            String requestBodyJson = objectMapper.writeValueAsString(volcengineImageRequest);
            byte[] bodyBytes = requestBodyJson.getBytes(UTF_8);
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("Action", ACTION);
            queryParams.put("Version", VERSION);

            Map<String, String> headersToSign = new HashMap<>();
            headersToSign.put("Host", this.apiHost);
            headersToSign.put("Content-Type", "application/json; charset=utf-8");

            Map<String, String> signedHeaders = signer.signHeaders("POST", API_PATH, queryParams, headersToSign, bodyBytes,  regionName, serviceName);

            HttpUrl.Builder urlBuilder = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(API_PATH);

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
            HttpUrl url = urlBuilder.build();

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(bodyBytes, MediaType.parse("application/json; charset=utf-8")));
            for (Map.Entry<String, String> header : signedHeaders.entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }
            if (!signedHeaders.containsKey("Content-Type")) {
                requestBuilder.addHeader("Content-Type", "application/json; charset=utf-8");
            }
            Request request = requestBuilder.build();
            log.info("Request URL for Enhance Image: {}", request.url());
            log.info("Request Headers for Enhance Image: {}", request.headers());
            log.info("Request Body for Enhance Image: {}", requestBodyJson);

            try(Response response = okHttpClient.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for Enhance Image: {}", response.code());
                log.info("Response Body for Enhance Image: {}", responseBodyString);
                if (!response.isSuccessful()) {
                    throw new BizException("Unexpected API response code for Enhance Image " + response.code() + ": " + responseBodyString);
                }
                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from Enhance Image API");
                }
                VolcengineImageEnhanceResponse volcengineImageEnhanceResponse = objectMapper.readValue(responseBodyString, VolcengineImageEnhanceResponse.class);
                if (10000 == volcengineImageEnhanceResponse.getCode()){
                    VolcengineImageEnhanceData data = volcengineImageEnhanceResponse.getData();
                    if (Objects.nonNull(data)){
                        if (CollUtil.isNotEmpty(data.getImageUrls())){
                            String imgUrl = data.getImageUrls().get(0);
                            ImageGenerateRes imageGenerateRes = new ImageGenerateRes(imgUrl);
                            return imageGenerateRes;
                        }
                    }
                }else{
                    throw new BizException("Unexpected API response code for enhance Image " + volcengineImageEnhanceResponse.getCode() + ": " + volcengineImageEnhanceResponse.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error calling Volcengine image enhance API", e);
            throw new BizException("Error processing Volcengine image enhance request");
        }
        return null;
    }

    public ImageGenerateRes seedEditImage(VolcengineSeedEditRequest seedEditRequest) {
        // 使用默认凭证
        return seedEditImageWithCredentials(seedEditRequest, null);
    }

    /**
     * 使用指定的账号凭证进行Seed编辑
     *
     * @param seedEditRequest 请求参数
     * @param account 账号信息，如果为null则使用默认凭
     * @return 图片生成结果
     */
    public ImageGenerateRes seedEditImageWithCredentials(VolcengineSeedEditRequest seedEditRequest, VendorAccountPo account) {
        try {
            // 从账号中提取凭证
            String accessKey = account != null ? account.getAccessKeyId() : defaultAccessKey;
            String secretKey = account != null ? account.getSecretAccessKey() : defaultSecretKey;
            
            String requestBodyJson = objectMapper.writeValueAsString(seedEditRequest);
            byte[] bodyBytes = requestBodyJson.getBytes(UTF_8);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("Action", ACTION);
            queryParams.put("Version", VERSION);

            Map<String, String> headersToSign = new HashMap<>();
            headersToSign.put("Host", this.apiHost);
            headersToSign.put("Content-Type", "application/json; charset=utf-8");

            // 使用指定账号的凭证进行签
            Map<String, String> signedHeaders = new HashMap<>();
            if (account != null) {
                log.info("Using custom credentials for account: {}", account.getAccountName());
                signedHeaders = signer.signHeadersWithCredentials(
                    "POST", API_PATH, queryParams, headersToSign, bodyBytes,
                    regionName, serviceName, accessKey, secretKey
                );
            } else {
                log.info("Using default credentials");
                signedHeaders = signer.signHeaders(
                    "POST", API_PATH, queryParams, headersToSign, bodyBytes, 
                    regionName, serviceName
                );
            }

            HttpUrl.Builder urlBuilder = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(API_PATH);

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
            HttpUrl url = urlBuilder.build();

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(bodyBytes, MediaType.parse("application/json; charset=utf-8")));

            for (Map.Entry<String, String> header : signedHeaders.entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }
            if (!signedHeaders.containsKey("Content-Type")) {
                requestBuilder.addHeader("Content-Type", "application/json; charset=utf-8");
            }

            Request request = requestBuilder.build();
            log.info("Request URL for SeedEdit Image: {}", request.url());
            log.info("Request Headers for SeedEdit Image: {}", request.headers());
            log.info("Request Body for SeedEdit Image: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for SeedEdit Image: {}", response.code());
                log.info("Response Body for SeedEdit Image: {}", responseBodyString);
                if (!response.isSuccessful()) {
                    throw new BizException("Unexpected API response code for SeedEdit Image " + response.code() + ": " + responseBodyString);
                }
                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from SeedEdit Image API");
                }
                VolcengineSeedEditResponse volcengineSeedEditResponse = objectMapper.readValue(responseBodyString, VolcengineSeedEditResponse.class);
                if (10000 == volcengineSeedEditResponse.getCode()){
                    VolcengineSeedEditResponse.SeedEditData data = volcengineSeedEditResponse.getData();
                    if (Objects.nonNull(data)){
                        if (CollUtil.isNotEmpty(data.getImageUrls())){
                            String imgUrl = data.getImageUrls().get(0);
                            return new ImageGenerateRes(imgUrl);
                        }
                    }
                } else {
                    return new ImageGenerateRes(null, String.valueOf(volcengineSeedEditResponse.getCode()), volcengineSeedEditResponse.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error calling Volcengine SeedEdit image API", e);
            throw new BizException("Error processing Volcengine SeedEdit image request: " + e.getMessage());
        }
        
        throw new BizException("Failed to process seed edit image");
    }

    /**
     * 保留角色形象 - 使用默认凭证
     *
     * @param request 请求参数
     * @return 图片生成结果
     */
    public ImageGenerateRes retainCharacterImage(VolcengineCharacterRetentionRequest request) {
        // 使用默认凭证
        return retainCharacterImageWithCredentials(request, null);
    }

    /**
     * 使用指定的账号凭证保留角色形
     *
     * @param request 请求参数
     * @param account 账号信息，如果为null则使用默认凭
     * @return 图片生成结果
     */
    public ImageGenerateRes retainCharacterImageWithCredentials(VolcengineCharacterRetentionRequest request, VendorAccountPo account) {
        try {
            // 从账号中提取凭证
            String accessKey = account != null ? account.getAccessKeyId() : defaultAccessKey;
            String secretKey = account != null ? account.getSecretAccessKey() : defaultSecretKey;
            
            String requestBodyJson = objectMapper.writeValueAsString(request);
            byte[] bodyBytes = requestBodyJson.getBytes(UTF_8);
            
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("Action", ACTION);
            queryParams.put("Version", VERSION);

            Map<String, String> headersToSign = new HashMap<>();
            headersToSign.put("Host", this.apiHost);
            headersToSign.put("Content-Type", "application/json; charset=utf-8");
            
            // 使用指定账号的凭证进行签
            Map<String, String> signedHeaders = new HashMap<>();
            if (account != null) {
                log.info("Using custom credentials for account: {}", account.getAccountName());
                signedHeaders = signer.signHeadersWithCredentials(
                    "POST", API_PATH, queryParams, headersToSign, bodyBytes,
                    regionName, serviceName, accessKey, secretKey
                );
            } else {
                log.info("Using default credentials");
                signedHeaders = signer.signHeaders(
                    "POST", API_PATH, queryParams, headersToSign, bodyBytes, 
                    regionName, serviceName
                );
            }
            
            HttpUrl.Builder urlBuilder = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(API_PATH);

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
            HttpUrl url = urlBuilder.build();

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(bodyBytes, MediaType.parse("application/json; charset=utf-8")));

            for (Map.Entry<String, String> header : signedHeaders.entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }
            if (!signedHeaders.containsKey("Content-Type")) {
                requestBuilder.addHeader("Content-Type", "application/json; charset=utf-8");
            }

            Request httpRequest = requestBuilder.build();
            log.info("Request URL for Retain Character: {}", httpRequest.url());
            log.info("Request Headers for Retain Character: {}", httpRequest.headers());
            log.info("Request Body for Retain Character: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for Retain Character: {}", response.code());
                log.info("Response Body for Retain Character: {}", responseBodyString);
                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from Retain Character API");
                }
                
                VolcengineCharacterRetentionResponse volcengineResponse = objectMapper.readValue(responseBodyString, VolcengineCharacterRetentionResponse.class);
                if (10000 == volcengineResponse.getCode()) {
                    VolcengineCharacterRetentionResponse.CharacterRetentionData data = volcengineResponse.getData();
                    if (data != null && data.getImageUrls() != null && !data.getImageUrls().isEmpty()) {
                        String imgUrl = data.getImageUrls().get(0);
                        return new ImageGenerateRes(imgUrl);
                    }
                } else {
                    return new ImageGenerateRes(null, String.valueOf(volcengineResponse.getCode()), volcengineResponse.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error calling Volcengine Retain Character API", e);
            throw new BizException("Error processing Volcengine Retain Character request: " + e.getMessage());
        }
        
        throw new BizException("Failed to retain character image");
    }

    public ImageGenerateRes paintingEditImage(VolcengineInpaintingEditRequest paintingEditRequest) {
        try {
            String requestBodyJson = objectMapper.writeValueAsString(paintingEditRequest);
            byte[] bodyBytes = requestBodyJson.getBytes(UTF_8);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("Action", ACTION);
            queryParams.put("Version", VERSION);

            Map<String, String> headersToSign = new HashMap<>();
            headersToSign.put("Host", this.apiHost);
            headersToSign.put("Content-Type", "application/json; charset=utf-8");

            Map<String, String> signedHeaders = signer.signHeaders("POST", API_PATH, queryParams, headersToSign, bodyBytes, regionName, serviceName);

            HttpUrl.Builder urlBuilder = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(API_PATH);

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
            }
            HttpUrl url = urlBuilder.build();

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(bodyBytes, MediaType.parse("application/json; charset=utf-8")));

            for (Map.Entry<String, String> header : signedHeaders.entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }
            if (!signedHeaders.containsKey("Content-Type")) {
                requestBuilder.addHeader("Content-Type", "application/json; charset=utf-8");
            }

            Request request = requestBuilder.build();
            log.info("Request URL for Inpainting Edit Image: {}", request.url());
            log.info("Request Headers for Inpainting Edit Image: {}", request.headers());
            log.info("Request Body for Inpainting Edit Image: {}", requestBodyJson);

            try (Response response = okHttpClient.newCall(request).execute()) {
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for Inpainting Edit Image: {}", response.code());
                log.info("Response Body for Inpainting Edit Image: {}", responseBodyString);
                if (!response.isSuccessful()) {
                    throw new BizException("Unexpected API response code for Inpainting Edit Image " + response.code() + ": " + responseBodyString);
                }
                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from Inpainting Edit Image API");
                }
                VolcengineInpaintingEditResponse inpaintingEditResponse = objectMapper.readValue(responseBodyString, VolcengineInpaintingEditResponse.class);
                if (10000 == inpaintingEditResponse.getCode()){
                    VolcengineInpaintingEditResponse.InpaintingEditData data = inpaintingEditResponse.getData();
                    if (Objects.nonNull(data)){
                        if (CollUtil.isNotEmpty(data.getImageUrls())){
                            String imgUrl = data.getImageUrls().get(0);
                            ImageGenerateRes imageGenerateRes = new ImageGenerateRes(imgUrl);
                            return imageGenerateRes;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error calling Volcengine Inpainting Edit image API", e);
            throw new BizException("Error processing Volcengine Inpainting Edit image request");
        }
        throw new BizException("Unexpected API response code for Inpainting Edit Image fail");
    }
}
