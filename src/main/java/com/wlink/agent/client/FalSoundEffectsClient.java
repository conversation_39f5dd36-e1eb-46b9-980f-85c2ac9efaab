package com.wlink.agent.client;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.model.dto.QueueStatus;
import com.wlink.agent.model.dto.SoundEffectsRequest;
import com.wlink.agent.model.dto.SoundEffectsResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * FAL 音效生成客户端
 * 基于 cassetteai/sound-effects-generator API
 * 使用 JDK 17 新特性
 */
@Slf4j
@Component
public class FalSoundEffectsClient {
    
    private static final String BASE_URL = "https://queue.fal.run";
    private static final String ENDPOINT = "/cassetteai/sound-effects-generator";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    
    private final OkHttpClient httpClient;
    private final String apiKey;
    
    public FalSoundEffectsClient(@Value("${fal.api.key:63e02ace-5988-4df0-9492-e6b7258cc5b6:fa9a4ee526b783d30a926a9314b1aba7}") String apiKey) {
        this.apiKey = apiKey;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(30))
                .readTimeout(Duration.ofMinutes(5))
                .writeTimeout(Duration.ofSeconds(30))
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 提交音效生成请求
     *
     * @param request 音效生成请求
     * @return 队列状态
     */
    public CompletableFuture<QueueStatus> submitRequest(SoundEffectsRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String requestJson = JSON.toJSONString(request);
                log.info("提交音效生成请求: {}", requestJson);
                
                RequestBody body = RequestBody.create(requestJson, JSON_MEDIA_TYPE);
                Request httpRequest = new Request.Builder()
                        .url(BASE_URL + ENDPOINT)
                        .post(body)
                        .addHeader("Authorization", "Key " + apiKey)
                        .addHeader("Content-Type", "application/json")
                        .build();
                
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("音效生成请求响应: {}", responseBody);
                    
                    QueueStatus queueStatus = JSON.parseObject(responseBody, QueueStatus.class);
                    log.info("音效生成请求提交成功: requestId={}, status={}", 
                            queueStatus.requestId(), queueStatus.status());
                    
                    return queueStatus;
                }
            } catch (IOException e) {
                log.error("提交音效生成请求失败", e);
                throw new RuntimeException("提交音效生成请求失败", e);
            }
        });
    }
    
    /**
     * 查询请求状态
     *
     * @param requestId 请求ID
     * @param includeLogs 是否包含日志
     * @return 队列状态
     */
    public CompletableFuture<QueueStatus> getStatus(String requestId, boolean includeLogs) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                HttpUrl.Builder urlBuilder = HttpUrl.parse(BASE_URL + ENDPOINT + "/requests/" + requestId + "/status")
                        .newBuilder();
                
                if (includeLogs) {
                    urlBuilder.addQueryParameter("logs", "1");
                }
                
                Request request = new Request.Builder()
                        .url(urlBuilder.build())
                        .get()
                        .addHeader("Authorization", "Key " + apiKey)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("查询状态响应: requestId={}, response={}", requestId, responseBody);
                    
                    return JSON.parseObject(responseBody, QueueStatus.class);
                }
            } catch (IOException e) {
                log.error("查询请求状态失败: requestId={}", requestId, e);
                throw new RuntimeException("查询请求状态失败", e);
            }
        });
    }
    
    /**
     * 获取生成结果
     *
     * @param requestId 请求ID
     * @return 音效生成结果
     */
    public CompletableFuture<SoundEffectsResult> getResult(String requestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Request request = new Request.Builder()
                        .url(BASE_URL + ENDPOINT + "/requests/" + requestId)
                        .get()
                        .addHeader("Authorization", "Key " + apiKey)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("获取结果响应: requestId={}, response={}", requestId, responseBody);
                    
                    SoundEffectsResult result = JSON.parseObject(responseBody, SoundEffectsResult.class);
                    log.info("获取音效生成结果成功: requestId={}, audioUrl={}", 
                            requestId, result.audioFile().url());
                    
                    return result;
                }
            } catch (IOException e) {
                log.error("获取生成结果失败: requestId={}", requestId, e);
                throw new RuntimeException("获取生成结果失败", e);
            }
        });
    }
    
    /**
     * 取消请求
     *
     * @param requestId 请求ID
     * @return 是否取消成功
     */
    public CompletableFuture<Boolean> cancelRequest(String requestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Request request = new Request.Builder()
                        .url(BASE_URL + ENDPOINT + "/requests/" + requestId + "/cancel")
                        .put(RequestBody.create("", JSON_MEDIA_TYPE))
                        .addHeader("Authorization", "Key " + apiKey)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new RuntimeException("HTTP请求失败: " + response.code() + " " + response.message());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("取消请求响应: requestId={}, response={}", requestId, responseBody);
                    
                    var cancelResult = JSON.parseObject(responseBody);
                    boolean success = cancelResult.getBooleanValue("success");
                    
                    log.info("取消音效生成请求: requestId={}, success={}", requestId, success);
                    return success;
                }
            } catch (IOException e) {
                log.error("取消请求失败: requestId={}", requestId, e);
                throw new RuntimeException("取消请求失败", e);
            }
        });
    }
    
    /**
     * 轮询等待结果完成
     * 使用 JDK 17 的 CompletableFuture 和 switch 表达式
     *
     * @param requestId 请求ID
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 音效生成结果
     */
    public CompletableFuture<SoundEffectsResult> waitForCompletion(String requestId, int maxWaitMinutes) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            long maxWaitTime = TimeUnit.MINUTES.toMillis(maxWaitMinutes);
            
            while (System.currentTimeMillis() - startTime < maxWaitTime) {
                try {
                    QueueStatus status = getStatus(requestId, false).join();
                    
                    // 使用 JDK 17 的 switch 表达式
                    var result = switch (status.status()) {
                        case "COMPLETED" -> {
                            log.info("音效生成完成: requestId={}", requestId);
                            yield getResult(requestId).join();
                        }
                        case "IN_QUEUE" -> {
                            log.debug("音效生成排队中: requestId={}, position={}", 
                                    requestId, status.queuePosition());
                            yield null;
                        }
                        case "IN_PROGRESS" -> {
                            log.debug("音效生成进行中: requestId={}", requestId);
                            yield null;
                        }
                        default -> {
                            log.error("音效生成状态异常: requestId={}, status={}", requestId, status.status());
                            throw new RuntimeException("音效生成状态异常: " + status.status());
                        }
                    };
                    
                    if (result != null) {
                        return result;
                    }
                    
                    // 等待 5 秒后重试
                    Thread.sleep(5000);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("等待被中断", e);
                } catch (Exception e) {
                    log.error("轮询状态失败: requestId={}", requestId, e);
                    throw new RuntimeException("轮询状态失败", e);
                }
            }
            
            throw new RuntimeException("等待超时: " + maxWaitMinutes + " 分钟");
        });
    }
    
    /**
     * 一键生成音效（提交请求并等待完成）
     * 使用 JDK 17 的 CompletableFuture 链式调用
     *
     * @param prompt 音效描述
     * @param duration 时长（秒）
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 音效生成结果
     */
    public CompletableFuture<SoundEffectsResult> generateAndWait(String prompt, int duration, int maxWaitMinutes) {
        var request = new SoundEffectsRequest(prompt, duration);
        
        return submitRequest(request)
                .thenCompose(queueStatus -> waitForCompletion(queueStatus.requestId(), maxWaitMinutes))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("音效生成失败: prompt={}, duration={}", prompt, duration, throwable);
                    } else {
                        log.info("音效生成成功: prompt={}, duration={}, audioUrl={}", 
                                prompt, duration, result.audioFile().url());
                    }
                });
    }
}
