package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.volcenginevideo.VolcengineVideoGenerationRequest;
import com.wlink.agent.client.model.volcenginevideo.VolcengineVideoGenerationResponse;
import com.wlink.agent.client.model.volcenginevideo.VolcengineVideoTaskListResponse;
import com.wlink.agent.client.model.volcenginevideo.VolcengineVideoTaskQueryResponse;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 火山引擎视频生成API客户
 */
@Slf4j
@RefreshScope
@Component
public class VolcengineVideoApiClient {

    private static final String DEFAULT_API_HOST = "ark.cn-beijing.volces.com";
    private static final String CREATE_VIDEO_PATH = "/api/v3/contents/generations/tasks";
    private static final String QUERY_VIDEO_TASK_PATH = "/api/v3/contents/generations/tasks/";
    private static final Charset UTF_8 = StandardCharsets.UTF_8;

    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private OkHttpClient okHttpClient;
    
    @Resource
    private AiVideoGenerationRecordMapper aiVideoGenerationRecordMapper;
    
    @Value("${volcengine.api.video.host:" + DEFAULT_API_HOST + "}")
    private String apiHost;
    
    @Value("${volcengine.api.key:f8858401-aa58-49b7-bff9-9876ef8bdf14}")
    private String defaultApiKey;

    /**
     * 回调地址
     */
    @Value("${volcengine.api.callback.url:https://dev.neodomain.cn/agent/video/callback}")
    private String callbackUrl;


    /**
     * 文生视频任务 - 使用默认API Key
     *
     * @param model 模型名称
     * @param text 提示词文
     * @param userId 用户ID，可为null
     * @param vendorAccountId 供应商账号ID，可为null
     * @return 视频生成结果（包含任务ID
     */
    public VolcengineVideoGenerationResponse createTextToVideoTask(String model, String text, Long userId, Long vendorAccountId) {
        // 使用默认API Key
        return createTextToVideoTaskWithApiKey(model, text, null, userId, vendorAccountId);
    }

    /**
     * 文生视频任务 - 使用指定API Key
     *
     * @param model 模型名称
     * @param text 提示词文
     * @param customApiKey 自定义API Key，如果为null则使用默认API Key
     * @param userId 用户ID，可为null
     * @param vendorAccountId 供应商账号ID，可为null
     * @return 视频生成结果（包含任务ID
     */
    public VolcengineVideoGenerationResponse createTextToVideoTaskWithApiKey(String model, String text, String customApiKey, Long userId, Long vendorAccountId) {
        try {
            // 构建请求对象
            VolcengineVideoGenerationRequest request = VolcengineVideoGenerationRequest.buildTextToVideoRequest(model, text,callbackUrl);
            
            // 发送API请求
            VolcengineVideoGenerationResponse response = callGenerationApi(request, customApiKey);
            
            // 保存任务记录到数据库
            if (response != null) {
                saveVideoGenerationRecord(response, "text_to_video", model, text, null, null, null, userId, vendorAccountId);
            }
            
            return response;
        } catch (Exception e) {
            log.error("Error calling Text-to-Video API", e);
            throw new BizException("Error processing Text-to-Video request: " + e.getMessage());
        }
    }
    
    /**
     * 图生视频任务 - 使用默认API Key
     *
     * @param model 模型名称
     * @param text 提示词文
     * @param imageUrl 参考图片URL
     * @param userId 用户ID，可为null
     * @param vendorAccountId 供应商账号ID，可为null
     * @return 视频生成结果（包含任务ID
     */
    public VolcengineVideoGenerationResponse createImageToVideoTask(String model, String text, String imageUrl, Long userId, Long vendorAccountId) {
        // 使用默认API Key
        return createImageToVideoTaskWithApiKey(model, text, imageUrl, null, userId, vendorAccountId);
    }

    /**
     * 图生视频任务 - 使用指定API Key
     *
     * @param model 模型名称
     * @param text 提示词文
     * @param imageUrl 参考图片URL
     * @param customApiKey 自定义API Key，如果为null则使用默认API Key
     * @param userId 用户ID，可为null
     * @param vendorAccountId 供应商账号ID，可为null
     * @return 视频生成结果（包含任务ID
     */
    public VolcengineVideoGenerationResponse createImageToVideoTaskWithApiKey(String model, String text, String imageUrl, String customApiKey, Long userId, Long vendorAccountId) {
        try {
            // 构建请求对象
            VolcengineVideoGenerationRequest request = VolcengineVideoGenerationRequest.buildImageToVideoRequest(model, text, imageUrl,callbackUrl);
            
            // 发送API请求
            VolcengineVideoGenerationResponse response = callGenerationApi(request, customApiKey);
            
            // 保存任务记录到数据库
            if (response != null) {
                saveVideoGenerationRecord(response, "image_to_video", model, text, imageUrl, null, null, userId, vendorAccountId);
            }
            
            return response;
        } catch (Exception e) {
            log.error("Error calling Image-to-Video API", e);
            throw new BizException("Error processing Image-to-Video request: " + e.getMessage());
        }
    }
    
    /**
     * 首尾帧生成视频任- 使用默认API Key
     *
     * @param model 模型名称
     * @param text 提示词文
     * @param firstFrameUrl 首帧图片URL
     * @param lastFrameUrl 尾帧图片URL
     * @param userId 用户ID，可为null
     * @param vendorAccountId 供应商账号ID，可为null
     * @return 视频生成结果（包含任务ID
     */
    public VolcengineVideoGenerationResponse createFirstLastFrameVideoTask(String model, String text, String firstFrameUrl, String lastFrameUrl, Long userId, Long vendorAccountId) {
        // 使用默认API Key
        return createFirstLastFrameVideoTaskWithApiKey(model, text, firstFrameUrl, lastFrameUrl, null, userId, vendorAccountId);
    }

    /**
     * 首尾帧生成视频任- 使用指定API Key
     *
     * @param model 模型名称
     * @param text 提示词文
     * @param firstFrameUrl 首帧图片URL
     * @param lastFrameUrl 尾帧图片URL
     * @param customApiKey 自定义API Key，如果为null则使用默认API Key
     * @param userId 用户ID，可为null
     * @param vendorAccountId 供应商账号ID，可为null
     * @return 视频生成结果（包含任务ID
     */
    public VolcengineVideoGenerationResponse createFirstLastFrameVideoTaskWithApiKey(String model, String text, String firstFrameUrl, String lastFrameUrl, String customApiKey, Long userId, Long vendorAccountId) {
        try {
            // 构建请求对象
            VolcengineVideoGenerationRequest request = VolcengineVideoGenerationRequest.buildFirstLastFrameVideoRequest(
                    model, text, firstFrameUrl, lastFrameUrl,callbackUrl);
            
            // 发送API请求
            VolcengineVideoGenerationResponse response = callGenerationApi(request, customApiKey);
            
            // 保存任务记录到数据库
            if (response != null) {
                saveVideoGenerationRecord(response, "first_last_frame", model, text, null, firstFrameUrl, lastFrameUrl, userId, vendorAccountId);
            }
            
            return response;
        } catch (Exception e) {
            log.error("Error calling First-Last-Frame Video API", e);
            throw new BizException("Error processing First-Last-Frame Video request: " + e.getMessage());
        }
    }

    /**
     * 保存视频生成任务记录到数据库
     * 
     * @param response API响应
     * @param sourceType 源类
     * @param model 模型名称
     * @param prompt 提示
     * @param imageUrl 图片URL
     * @param firstFrameUrl 首帧URL
     * @param lastFrameUrl 尾帧URL
     * @param userId 用户ID
     * @param vendorAccountId 供应商账号ID
     */
    private void saveVideoGenerationRecord(VolcengineVideoGenerationResponse response, String sourceType, 
                                          String model, String prompt, String imageUrl, 
                                          String firstFrameUrl, String lastFrameUrl, 
                                          Long userId, Long vendorAccountId) {
        try {
            AiVideoGenerationRecordPo record = new AiVideoGenerationRecordPo();
            record.setTaskId(response.getId());
            record.setUserId(userId);
            record.setModel(model);
            record.setPrompt(prompt);
            record.setSourceType(sourceType);
            record.setImageUrl(imageUrl);
            record.setFirstFrameUrl(firstFrameUrl);
            record.setLastFrameUrl(lastFrameUrl);
            
            // 设置初始状
            if (response.getId() != null) {
                record.setStatus("queued"); // 假设初始状态为排队
            } else {
                record.setStatus("failed");
                record.setErrorCode(response.getErrorCode());
                record.setErrorMsg(response.getErrorMsg());
            }
            
            record.setVendorAccountId(vendorAccountId);
            
            // 插入记录
            aiVideoGenerationRecordMapper.insert(record);
            log.info("Saved video generation record for task ID: {}", response.getId());
        } catch (Exception e) {
            log.error("Error saving video generation record", e);
        }
    }

    /**
     * 通用API调用方法
     * 
     * @param request 请求对象
     * @param customApiKey 自定义API Key
     * @return 响应对象
     * @throws Exception 如果API调用失败
     */
    private VolcengineVideoGenerationResponse callGenerationApi(VolcengineVideoGenerationRequest request, String customApiKey) throws Exception {
        // 使用提供的API Key或默认API Key
        String apiKey = customApiKey != null ? customApiKey : defaultApiKey;
        
        String requestBodyJson = objectMapper.writeValueAsString(request);
        
        HttpUrl url = new HttpUrl.Builder()
                .scheme("https")
                .host(this.apiHost)
                .encodedPath(CREATE_VIDEO_PATH)
                .build();

        // 构建请求
        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(requestBodyJson.getBytes(UTF_8), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .build();
        
        log.info("Request URL for Video Generation: {}", httpRequest.url());
        log.info("Request Headers for Video Generation: {}", httpRequest.headers());
        log.info("Request Body for Video Generation: {}", requestBodyJson);

        try (Response response = okHttpClient.newCall(httpRequest).execute()) {
            int statusCode = response.code();
            String responseBodyString = response.body() != null ? response.body().string() : null;
            log.info("Response Code for Video Generation: {}", statusCode);
            log.info("Response Body for Video Generation: {}", responseBodyString);

            if (responseBodyString == null || responseBodyString.isEmpty()) {
                throw new BizException("Empty response body from Video Generation API");
            }
            
            // 处理不同的状态码
            if (statusCode >= 200 && statusCode < 300) {
                return objectMapper.readValue(responseBodyString, VolcengineVideoGenerationResponse.class);
            } else {
                // 尝试解析错误响应
                VolcengineVideoGenerationResponse errorResponse = objectMapper.readValue(responseBodyString, VolcengineVideoGenerationResponse.class);
                if (errorResponse.getErrorCode() == null) {
                    errorResponse.setErrorCode(String.valueOf(statusCode));
                }
                if (errorResponse.getErrorMsg() == null) {
                    errorResponse.setErrorMsg("API request failed with status: " + statusCode);
                }

                // 检查是否为敏感内容检测错误
                String errorCode = errorResponse.getErrorCode();
                if (isSensitiveContentError(errorCode)) {
                    String sensitiveErrorMsg = getSensitiveContentErrorMessage(errorCode);
                    log.warn("敏感内容检测错误: errorCode={}, message={}", errorCode, sensitiveErrorMsg);
                    errorResponse.setErrorMsg(sensitiveErrorMsg);
                }

                return errorResponse;
            }
        }
    }

    /**
     * 查询视频生成任务 - 使用默认API Key
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    public VolcengineVideoTaskQueryResponse queryVideoGenerationTask(String taskId) {
        // 使用默认API Key
        return queryVideoGenerationTaskWithApiKey(taskId, null);
    }

    /**
     * 查询视频生成任务 - 使用指定API Key
     *
     * @param taskId 任务ID
     * @param customApiKey 自定义API Key，如果为null则使用默认API Key
     * @return 任务详情
     */
    public VolcengineVideoTaskQueryResponse queryVideoGenerationTaskWithApiKey(String taskId, String customApiKey) {
        try {
            // 使用提供的API Key或默认API Key
            String apiKey = customApiKey != null ? customApiKey : defaultApiKey;
            
            // 构建URL，注意任务ID直接附加到路径后
            HttpUrl url = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(QUERY_VIDEO_TASK_PATH + taskId)
                    .build();

            // 构建请求
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .build();
            
            log.info("Request URL for Query Video Task: {}", httpRequest.url());
            log.info("Request Headers for Query Video Task: {}", httpRequest.headers());

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                int statusCode = response.code();
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for Query Video Task: {}", statusCode);
                log.info("Response Body for Query Video Task: {}", responseBodyString);

                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from Query Video Task API");
                }
                
                // 处理不同的状态码
                VolcengineVideoTaskQueryResponse queryResponse;
                if (statusCode >= 200 && statusCode < 300) {
                    queryResponse = objectMapper.readValue(responseBodyString, VolcengineVideoTaskQueryResponse.class);
                } else {
                    // 处理错误，创建错误响应对
                    queryResponse = new VolcengineVideoTaskQueryResponse();
                    
                    try {
                        // 尝试解析错误响应
                        queryResponse = objectMapper.readValue(responseBodyString, VolcengineVideoTaskQueryResponse.class);
                        if (queryResponse.getError() == null) {
                            VolcengineVideoTaskQueryResponse.ErrorInfo error = new VolcengineVideoTaskQueryResponse.ErrorInfo();
                            error.setCode(String.valueOf(statusCode));
                            error.setMessage("API request failed with status: " + statusCode);
                            queryResponse.setError(error);
                        }

                        // 检查是否为敏感内容检测错误
                        if (queryResponse.getError() != null) {
                            String errorCode = queryResponse.getError().getCode();
                            if (isSensitiveContentError(errorCode)) {
                                String sensitiveErrorMsg = getSensitiveContentErrorMessage(errorCode);
                                log.warn("查询任务时发现敏感内容检测错误: taskId={}, errorCode={}, message={}",
                                        taskId, errorCode, sensitiveErrorMsg);
                                queryResponse.getError().setMessage(sensitiveErrorMsg);
                            }
                        }
                    } catch (Exception e) {
                        // 如果无法解析为标准错误格式，创建一个通用错误
                        VolcengineVideoTaskQueryResponse.ErrorInfo error = new VolcengineVideoTaskQueryResponse.ErrorInfo();
                        error.setCode(String.valueOf(statusCode));
                        error.setMessage("API request failed with status: " + statusCode);
                        queryResponse.setError(error);
                    }
                    
                    queryResponse.setId(taskId);
                    queryResponse.setStatus("failed");
                }
                
                // 更新数据库中的任务状
                updateVideoGenerationRecord(queryResponse);
                
                return queryResponse;
            }
        } catch (Exception e) {
            log.error("Error calling Query Video Task API", e);
            throw new BizException("Error processing Query Video Task request: " + e.getMessage());
        }
    }
    
    /**
     * 更新视频生成任务记录
     * 
     * @param response 任务查询响应
     */
    private void updateVideoGenerationRecord(VolcengineVideoTaskQueryResponse response) {
        try {
            if (response == null || response.getId() == null) {
                return;
            }
            
            // 先查询数据库中是否存在该记录
            AiVideoGenerationRecordPo existingRecord = aiVideoGenerationRecordMapper.selectOne(new LambdaQueryWrapper<AiVideoGenerationRecordPo>()
                    .eq(AiVideoGenerationRecordPo::getTaskId, response.getId()));
            if (existingRecord == null) {
                log.warn("No record found for task ID: {}", response.getId());
                return;
            }
            
            // 更新记录
            AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
            updateRecord.setTaskId(response.getId());
            updateRecord.setStatus(response.getStatus());
            
            // 处理错误信息
            if (response.getError() != null) {
                String errorCode = response.getError().getCode();
                String errorMessage = response.getError().getMessage();

                // 检查是否为敏感内容检测错误，如果是则使用友好的错误信息
                if (isSensitiveContentError(errorCode)) {
                    errorMessage = getSensitiveContentErrorMessage(errorCode);
                    log.warn("更新任务记录时发现敏感内容检测错误: taskId={}, errorCode={}, friendlyMessage={}",
                            response.getId(), errorCode, errorMessage);
                }

                updateRecord.setErrorCode(errorCode);
                updateRecord.setErrorMsg(errorMessage);
            }
            
            // 处理成功情况下的视频URL和Token使用情况
            if ("succeeded".equals(response.getStatus()) && response.getContent() != null) {
                updateRecord.setVideoUrl(response.getContent().getVideoUrl());
                
                if (response.getUsage() != null) {
                    updateRecord.setCompletionTokens(response.getUsage().getCompletionTokens());
                    updateRecord.setTotalTokens(response.getUsage().getTotalTokens());
                }
            }
            // 更新数据
            aiVideoGenerationRecordMapper.update(updateRecord,new LambdaUpdateWrapper<AiVideoGenerationRecordPo>()
                    .eq(AiVideoGenerationRecordPo::getTaskId, updateRecord.getTaskId()));
            log.info("Updated video generation record for task ID: {}", response.getId());
        } catch (Exception e) {
            log.error("Error updating video generation record", e);
        }
    }
    
    /**
     * 检查视频生成任务是否完
     * 
     * @param taskId 任务ID
     * @return 如果任务已完成（成功或失败），返回true；如果仍在处理中，返回false
     */
    public boolean isVideoGenerationTaskCompleted(String taskId) {
        VolcengineVideoTaskQueryResponse response = queryVideoGenerationTask(taskId);
        return "succeeded".equals(response.getStatus()) || "failed".equals(response.getStatus()) || "canceled".equals(response.getStatus());
    }
    
    /**
     * 检查视频生成任务是否成功完成并且有可用的视频URL
     * 
     * @param taskId 任务ID
     * @return 如果任务已成功完成且有视频URL，返回true；否则返回false
     */
    public boolean isVideoGenerationTaskSucceeded(String taskId) {
        VolcengineVideoTaskQueryResponse response = queryVideoGenerationTask(taskId);
        return "succeeded".equals(response.getStatus()) && response.getContent() != null && response.getContent().getVideoUrl() != null;
    }
    
    /**
     * 获取生成的视频URL
     * 
     * @param taskId 任务ID
     * @return 视频URL，如果任务未完成或失败则返回null
     */
    public String getGeneratedVideoUrl(String taskId) {
        VolcengineVideoTaskQueryResponse response = queryVideoGenerationTask(taskId);
        if ("succeeded".equals(response.getStatus()) && response.getContent() != null) {
            return response.getContent().getVideoUrl();
        }
        return null;
    }

    /**
     * 取消或删除视频生成任- 使用默认API Key
     *
     * @param taskId 任务ID
     * @return 如果取消成功返回true，否则返回false
     */
    public boolean cancelVideoGenerationTask(String taskId) {
        // 使用默认API Key
        return cancelVideoGenerationTaskWithApiKey(taskId, null);
    }

    /**
     * 取消或删除视频生成任- 使用指定API Key
     *
     * @param taskId 任务ID
     * @param customApiKey 自定义API Key，如果为null则使用默认API Key
     * @return 如果取消成功返回true，否则返回false
     */
    public boolean cancelVideoGenerationTaskWithApiKey(String taskId, String customApiKey) {
        try {
            // 使用提供的API Key或默认API Key
            String apiKey = customApiKey != null ? customApiKey : defaultApiKey;
            
            // 构建URL，注意任务ID直接附加到路径后
            HttpUrl url = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(QUERY_VIDEO_TASK_PATH + taskId)
                    .build();

            // 构建DELETE请求
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .delete()
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .build();
            
            log.info("Request URL for Cancel Video Task: {}", httpRequest.url());
            log.info("Request Headers for Cancel Video Task: {}", httpRequest.headers());

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                int statusCode = response.code();
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for Cancel Video Task: {}", statusCode);
                log.info("Response Body for Cancel Video Task: {}", responseBodyString);

                boolean success = statusCode >= 200 && statusCode < 300;
                
                // 如果取消成功，更新数据库中的任务状
                if (success) {
                    try {
                        AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
                        updateRecord.setTaskId(taskId);
                        updateRecord.setStatus("cancelled");
                        aiVideoGenerationRecordMapper.update(updateRecord,new LambdaQueryWrapper<AiVideoGenerationRecordPo>().eq(AiVideoGenerationRecordPo::getTaskId, taskId));
                        log.info("Updated video generation record status to cancelled for task ID: {}", taskId);
                    } catch (Exception e) {
                        log.error("Error updating video generation record status to cancelled", e);
                    }
                }
                
                return success;
            }
        } catch (Exception e) {
            log.error("Error calling Cancel Video Task API", e);
            throw new BizException("Error processing Cancel Video Task request: " + e.getMessage());
        }
    }
    
    /**
     * 查询视频生成任务列表 - 使用默认API Key
     *
     * @param pageSize 每页大小，默认为10
     * @param status 可选的状态过滤参数，可以是：queued, running, cancelled, succeeded, failed
     * @return 任务列表响应
     */
    public VolcengineVideoTaskListResponse listVideoGenerationTasks(Integer pageSize, String status) {
        // 使用默认API Key
        return listVideoGenerationTasksWithApiKey(pageSize, status, null);
    }

    /**
     * 查询视频生成任务列表 - 使用指定API Key
     *
     * @param pageSize 每页大小，默认为10
     * @param status 可选的状态过滤参数，可以是：queued, running, cancelled, succeeded, failed
     * @param customApiKey 自定义API Key，如果为null则使用默认API Key
     * @return 任务列表响应
     */
    public VolcengineVideoTaskListResponse listVideoGenerationTasksWithApiKey(Integer pageSize, String status, String customApiKey) {
        try {
            // 使用提供的API Key或默认API Key
            String apiKey = customApiKey != null ? customApiKey : defaultApiKey;
            
            // 构建URL，添加查询参
            HttpUrl.Builder urlBuilder = new HttpUrl.Builder()
                    .scheme("https")
                    .host(this.apiHost)
                    .encodedPath(CREATE_VIDEO_PATH);
            
            // 添加分页大小参数
            if (pageSize != null && pageSize > 0) {
                urlBuilder.addQueryParameter("page_size", String.valueOf(pageSize));
            }
            
            // 添加状态过滤参
            if (StringUtils.hasText(status)) {
                urlBuilder.addQueryParameter("filter.status", status);
            }
            
            HttpUrl url = urlBuilder.build();

            // 构建请求
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json; charset=utf-8")
                    .build();
            
            log.info("Request URL for List Video Tasks: {}", httpRequest.url());
            log.info("Request Headers for List Video Tasks: {}", httpRequest.headers());

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                int statusCode = response.code();
                String responseBodyString = response.body() != null ? response.body().string() : null;
                log.info("Response Code for List Video Tasks: {}", statusCode);
                log.info("Response Body for List Video Tasks: {}", responseBodyString);

                if (responseBodyString == null || responseBodyString.isEmpty()) {
                    throw new BizException("Empty response body from List Video Tasks API");
                }
                
                // 处理不同的状态码
                if (statusCode >= 200 && statusCode < 300) {
                    return objectMapper.readValue(responseBodyString, VolcengineVideoTaskListResponse.class);
                } else {
                    // 处理错误，创建空响应对象
                    log.error("API request failed with status: {}", statusCode);
                    throw new BizException("API request failed with status: " + statusCode);
                }
            }
        } catch (Exception e) {
            log.error("Error calling List Video Tasks API", e);
            throw new BizException("Error processing List Video Tasks request: " + e.getMessage());
        }
    }

    /**
     * 检查是否为敏感内容检测错误
     *
     * @param errorCode 错误代码
     * @return 是否为敏感内容错误
     */
    private boolean isSensitiveContentError(String errorCode) {
        if (errorCode == null) {
            return false;
        }

        return errorCode.contains("SensitiveContentDetected") ||
               errorCode.contains("InputTextSensitiveContentDetected") ||
               errorCode.contains("InputImageSensitiveContentDetected") ||
               errorCode.contains("InputVideoSensitiveContentDetected") ||
               errorCode.contains("OutputTextSensitiveContentDetected") ||
               errorCode.contains("OutputVideoSensitiveContentDetected");
    }

    /**
     * 获取敏感内容检测错误的友好提示信息
     *
     * @param errorCode 错误代码
     * @return 友好的错误提示信息
     */
    private String getSensitiveContentErrorMessage(String errorCode) {
        if (errorCode == null) {
            return "内容检测失败，请重试";
        }

        if (errorCode.contains("SensitiveContentDetected.SevereViolation")) {
            return "输入内容包含严重违规信息，请修改后重试";
        }

        if (errorCode.contains("SensitiveContentDetected.Violence")) {
            return "输入内容包含暴力相关信息，请修改后重试";
        }

        if (errorCode.contains("InputTextSensitiveContentDetected")) {
            return "输入文本包含敏感信息，请修改后重试";
        }

        if (errorCode.contains("InputImageSensitiveContentDetected")) {
            return "输入图像包含敏感信息，请更换图片后重试";
        }

        if (errorCode.contains("InputVideoSensitiveContentDetected")) {
            return "输入视频包含敏感信息，请更换视频后重试";
        }

        if (errorCode.contains("OutputTextSensitiveContentDetected")) {
            return "生成的文字内容包含敏感信息，请修改输入内容后重试";
        }

        if (errorCode.contains("OutputVideoSensitiveContentDetected")) {
            return "生成的视频内容包含敏感信息，请修改输入内容后重试";
        }

        if (errorCode.contains("SensitiveContentDetected")) {
            return "内容包含敏感信息，请修改后重试";
        }

        return "内容检测失败，请重试";
    }
}
