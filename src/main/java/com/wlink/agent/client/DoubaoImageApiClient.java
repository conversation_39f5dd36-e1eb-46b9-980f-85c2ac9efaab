package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationResponse;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 豆包图像生成API客户
 * 
 * <AUTHOR> Assistant
 */

@Slf4j
@Component
public class DoubaoImageApiClient {
    
    private static final String DEFAULT_API_URL = "https://ark.cn-beijing.volces.com/api/v3/images/generations";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private OkHttpClient okHttpClient;
    
    @Value("${doubao.api.url:" + DEFAULT_API_URL + "}")
    private String apiUrl;
    
    @Value("${doubao.api.key:f8858401-aa58-49b7-bff9-9876ef8bdf14}")
    private String defaultApiKey;
    
    /**
     * 使用默认API密钥生成图像
     * 
     * @param request 图像生成请求
     * @return 图像生成结果
     */
    public ImageGenerateRes generateImage(DoubaoImageGenerationRequest request) {
        return generateImageWithApiKey(request, defaultApiKey);
    }
    
    /**
     * 使用指定API密钥生成图像
     * 
     * @param request 图像生成请求
     * @param apiKey API密钥
     * @return 图像生成结果
     */
    public ImageGenerateRes generateImageWithApiKey(DoubaoImageGenerationRequest request, String apiKey) {
        try {
            // 参数验证
            if (request == null) {
                throw new BizException("请求参数不能为空");
            }
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new BizException("API密钥不能为空");
            }
            
            // 构建HTTP请求
            Request httpRequest = buildRequest(request, apiKey);
            
            // 执行HTTP调用
            String responseBody = executeRequest(httpRequest);
            
            // 尝试解析错误响应
            ImageGenerateRes errorResponse = parseErrorResponse(responseBody);
            if (errorResponse != null) {
                return errorResponse;
            }
            
            // 解析正常响应
            return parseResponse(responseBody);
            
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用豆包图像生成API发生异常", e);
            throw new BizException("豆包图像生成API调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建HTTP请求
     */
    private Request buildRequest(DoubaoImageGenerationRequest request, String apiKey) throws Exception {
        // 序列化请求体
        String requestBodyJson = objectMapper.writeValueAsString(request);
        byte[] bodyBytes = requestBodyJson.getBytes(StandardCharsets.UTF_8);
        
        log.info("调用豆包图像生成API, URL: {}, 请求: {}", apiUrl, requestBodyJson);
        
        // 构建请求
        return new Request.Builder()
                .url(apiUrl)
                .post(RequestBody.create(bodyBytes, JSON_MEDIA_TYPE))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .build();
    }
    
    /**
     * 执行HTTP请求
     */
    private String executeRequest(Request httpRequest) throws Exception {
        try (Response response = okHttpClient.newCall(httpRequest).execute()) {
            int statusCode = response.code();
            String responseBody = response.body() != null ? response.body().string() : null;
            
            log.info("豆包API响应: 状态码={}, 响应={}", statusCode, responseBody);
            
            if (responseBody == null || responseBody.trim().isEmpty()) {
                throw new BizException("豆包API响应为空");
            }
            
            return responseBody;
        }
    }
    
    /**
     * 解析响应内容
     */
    private ImageGenerateRes parseResponse(String responseBody) throws Exception {
        try {
            DoubaoImageGenerationResponse response = objectMapper.readValue(responseBody, DoubaoImageGenerationResponse.class);
            
            // 检查响应数
            if (response.getData() == null || response.getData().isEmpty()) {
                throw new BizException("响应中没有图片数据");
            }
            
            // 提取第一个图片URL
            String imageUrl = response.getData().get(0).getUrl();
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                throw new BizException("图片URL为空");
            }
            
            return new ImageGenerateRes(imageUrl);
            
        } catch (Exception e) {
            log.error("解析豆包API响应失败: {}", responseBody, e);
            throw new BizException("响应解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析错误响应
     */
    private ImageGenerateRes parseErrorResponse(String responseBody) {
        try {
            JsonNode rootNode = objectMapper.readTree(responseBody);
            JsonNode errorNode = rootNode.path("error");
            
            if (!errorNode.isMissingNode()) {
                String code = errorNode.path("code").asText();
                String message = errorNode.path("message").asText();
                
                log.warn("豆包API返回错误: code={}, message={}", code, message);
                return new ImageGenerateRes(null, code, message);
            }
            
            return null;
        } catch (Exception e) {
            log.error("解析豆包API错误响应失败: {}", responseBody, e);
            return null;
        }
    }
} 
