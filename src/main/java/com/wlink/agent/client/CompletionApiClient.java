package com.wlink.agent.client;

import com.alibaba.cola.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.completion.CompletionInput;
import com.wlink.agent.client.model.completion.CompletionRequest;
import com.wlink.agent.client.model.completion.CompletionResponse;
import com.wlink.agent.config.ExternalApiConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.time.Duration;

/**
 * 完成API客户端，用于调用外部AI完成接口
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class CompletionApiClient {

    // 定义OkHttp JSON Media Type
    public static final MediaType JSON_UTF8 = MediaType.get("application/json; charset=utf-8");

    private final ObjectMapper objectMapper;
    private final OkHttpClient okHttpClient;
    private final WebClient webClient;
    private final ExternalApiConfig externalApiConfig;

    /**
     * 使用OkHttp同步请求AI完成
     *
     * @param query 查询内容
     * @param userId 用户ID
     * @return 响应中的answer字段
     * @throws IOException 网络IO异常
     */
    public String requestCompletion(String query, String userId,String key){
        CompletionResponse response = null;
        try {
            CompletionRequest request = buildCompletionRequest(query, userId,key);
            response = requestCompletionWithOkHttp(request,key);
        } catch (Exception e) {
            log.error("同步调用失败: {}", e.getMessage(), e);
            return null;
        }
        return response != null ? response.getAnswer() : null;
    }

    /**
     * 使用WebClient异步请求AI完成
     *
     * @param query 查询内容
     * @param userId 用户ID
     * @return 响应中的answer字段的Mono
     */
    public Mono<String> requestCompletionAsync(String query, String userId) {
        CompletionRequest request = buildCompletionRequest(query, userId,null);
        return requestCompletionWithWebClient(request)
                .map(response -> response.getAnswer());
    }

    /**
     * 使用WebClient异步请求AI完成
     *
     * @param request 请求参数
     * @return 响应对象的Mono
     */
    public Mono<CompletionResponse> requestCompletionWithWebClient(CompletionRequest request) {
        try {
            String requestBodyJson = objectMapper.writeValueAsString(request);
            log.info("[WebClient] 序列化请求体: {}", requestBodyJson);
            return webClient
                    .post()
                    .uri(externalApiConfig.getCompletionUrl())
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + externalApiConfig.getCompletionApiKey())
                    .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                    .bodyValue(requestBodyJson)
                    .retrieve()
                    .bodyToMono(CompletionResponse.class)
                    .timeout(Duration.ofSeconds(30))
                    .doOnSuccess(response -> log.info("[WebClient] 成功反序列化响应: {}", response))
                    .doOnError(error -> {
                        if (!(error instanceof JsonProcessingException)) {
                            log.error("[WebClient] 外部API响应错误: URL={}, Error={}", externalApiConfig.getCompletionUrl(), error.getMessage(), error);
                        }
                    });
        } catch (JsonProcessingException e) {
            log.error("[WebClient] 请求序列化为JSON失败: Request={}, Error={}", request, e.getMessage(), e);
            return Mono.error(e);
        }
    }

    /**
     * 使用OkHttp同步请求AI完成
     *
     * @param request 请求参数
     * @return 响应对象
     * @throws IOException 网络IO异常
     */
    public CompletionResponse requestCompletionWithOkHttp(CompletionRequest request,String key){
        String apiUrl = externalApiConfig.getCompletionUrl();
        log.info("[OkHttp] 发送请求到完成API: URL={}, Payload={}", apiUrl, request);
        String requestBodyJson;
        try {
            requestBodyJson = objectMapper.writeValueAsString(request);
            log.debug("[OkHttp] 序列化请求体: {}", requestBodyJson);
        } catch (JsonProcessingException e) {
            log.error("[OkHttp] 请求序列化为JSON失败: Request={}, Error={}", request, e.getMessage(), e);
            throw new BizException("序列化请求失");
        }
        key = StringUtils.isBlank(key) ? externalApiConfig.getCompletionApiKey() : key;
        RequestBody body = RequestBody.create(requestBodyJson, JSON_UTF8);
        Request okHttpRequest = new Request.Builder()
                .url(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + key)
                .post(body)
                .build();

        log.debug("[OkHttp] 执行请求: {}", okHttpRequest);

        // 同步执行
        try (Response response = okHttpClient.newCall(okHttpRequest).execute()) {
            String responseBodyString = response.body() != null ? response.body().string() : null;
            log.info("[OkHttp] 收到原始响应. 状态码: {}, 响应体长 {}", response.code(), 
                    responseBodyString != null ? responseBodyString.length() : 0);
            log.debug("[OkHttp] 响应体内 {}", responseBodyString);

            if (!response.isSuccessful()) {
                log.error("[OkHttp] 完成API返回失败: Code={}, Message={}, Body={}",
                        response.code(), response.message(), responseBodyString);
                throw new BizException("意外的响应码 " + response + " 响应 " + responseBodyString);
            }

            if (responseBodyString == null || responseBodyString.isEmpty()) {
                log.error("[OkHttp] 收到空响应体.");
                return null;
            }
            try {
                CompletionResponse completionResponse = objectMapper.readValue(responseBodyString, CompletionResponse.class);
                log.info("[OkHttp] 成功解析响应，answer长度: {}", 
                        completionResponse != null && completionResponse.getAnswer() != null ? 
                        completionResponse.getAnswer().length() : 0);
                return completionResponse;
            } catch (Exception e) {
                log.error("[OkHttp] 响应JSON解析失败: Error={}, ResponseBody={}", e.getMessage(), responseBodyString, e);
                throw new BizException("响应JSON解析失败: " + e.getMessage(), e);
            }
        } catch (IOException e) {
            log.error("[OkHttp] API调用过程中IO异常: URL={}, Error={}", apiUrl, e.getMessage(), e);
            throw new BizException("IO异常: " + e.getMessage());
        }
    }
    
    /**
     * 构建完成请求对象
     *
     * @param query 查询内容
     * @param userId 用户ID
     * @return 完成请求对象
     */
    public CompletionRequest buildCompletionRequest(String query, String userId,String key) {
        CompletionInput input = CompletionInput.builder()
                .query(query)
                .build();
        if (StringUtils.isNotBlank(key)){
             input = CompletionInput.builder()
                    .inputText(query)
                    .build();
        }
        return CompletionRequest.builder()
                .inputs(input)
                .user(userId)
                .responseMode("blocking")
                .build();
    }
} 
