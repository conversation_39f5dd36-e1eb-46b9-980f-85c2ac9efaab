package com.wlink.agent.example;

import com.wlink.agent.client.DoubaoImageEditApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.model.req.DoubaoImageEditReq;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.service.DoubaoImageEditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 豆包图像编辑使用示例
 */
@Slf4j
@Component
public class DoubaoImageEditExample {

    @Autowired
    private DoubaoImageEditApiClient doubaoImageEditApiClient;

    @Autowired
    private DoubaoImageEditService doubaoImageEditService;

    /**
     * 示例1：直接使用API客户端
     */
    public void example1_DirectApiClient() {
        log.info("=== 示例1：直接使用API客户端 ===");

        try {
            // 构建请求
            DoubaoImageEditRequest request = new DoubaoImageEditRequest();
            request.setModel("doubao-seededit-3-0-i2i-250628");
            request.setPrompt("把图中的红色汽车改成蓝色，保持其他部分不变");
            request.setImage("https://example.com/red-car.jpg");
            request.setResponseFormat("url");
            request.setSize("adaptive");
            request.setSeed(12345);
            request.setGuidanceScale(7.0);
            request.setWatermark(false);

            // 调用API
            ImageGenerateRes result = doubaoImageEditApiClient.editImage(request);
            log.info("图像编辑成功，图片URL: {}", result.getImageUrl());

        } catch (Exception e) {
            log.error("图像编辑失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 示例2：使用服务层
     */
    public void example2_ServiceLayer() {
        log.info("=== 示例2：使用服务层 ===");

        try {
            // 构建请求
            DoubaoImageEditReq req = new DoubaoImageEditReq();
            req.setShotId(123456789L);
            req.setImage("https://example.com/original-image.jpg");
            req.setPrompt("将背景从白色改为蓝天白云");
            req.setModel("doubao-seededit-3-0-i2i-250628");
            req.setGuidanceScale(6.5);
            req.setSeed(54321);
            req.setWatermark(true);

            // 调用服务
            ImageGenerateRes result = doubaoImageEditService.editImage(req);
            log.info("图像编辑成功，图片URL: {}", result.getImageUrl());

        } catch (Exception e) {
            log.error("图像编辑失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 示例3：使用自定义API密钥
     */
    public void example3_CustomApiKey() {
        log.info("=== 示例3：使用自定义API密钥 ===");

        try {
            // 构建请求
            DoubaoImageEditReq req = new DoubaoImageEditReq();
            req.setShotId(987654321L);
            req.setImage("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...");
            req.setPrompt("给人物添加一顶帽子");
            req.setGuidanceScale(8.0);

            // 使用自定义API密钥
            String customApiKey = "your-custom-api-key";
            ImageGenerateRes result = doubaoImageEditService.editImageWithApiKey(req, customApiKey);
            log.info("图像编辑成功，图片URL: {}", result.getImageUrl());

        } catch (Exception e) {
            log.error("图像编辑失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 示例4：批量图像编辑
     */
    public void example4_BatchEdit() {
        log.info("=== 示例4：批量图像编辑 ===");

        String[] imageUrls = {
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        };

        String[] prompts = {
            "将天空改为夕阳",
            "添加彩虹效果",
            "改变季节为秋天"
        };

        for (int i = 0; i < imageUrls.length; i++) {
            try {
                DoubaoImageEditReq req = new DoubaoImageEditReq();
                req.setShotId((long) (100000 + i));
                req.setImage(imageUrls[i]);
                req.setPrompt(prompts[i]);
                req.setGuidanceScale(6.0);

                ImageGenerateRes result = doubaoImageEditService.editImage(req);
                log.info("批量编辑 {}/{} 成功，图片URL: {}", i + 1, imageUrls.length, result.getImageUrl());

                // 添加延迟避免API限流
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("批量编辑 {}/{} 失败: {}", i + 1, imageUrls.length, e.getMessage());
            }
        }
    }

    /**
     * 示例5：不同引导尺度的效果对比
     */
    public void example5_GuidanceScaleComparison() {
        log.info("=== 示例5：不同引导尺度的效果对比 ===");

        String imageUrl = "https://example.com/test-image.jpg";
        String prompt = "将图中的花朵改为玫瑰";
        double[] guidanceScales = {3.0, 5.5, 8.0};

        for (double scale : guidanceScales) {
            try {
                DoubaoImageEditReq req = new DoubaoImageEditReq();
                req.setShotId(200000L + (long) (scale * 10));
                req.setImage(imageUrl);
                req.setPrompt(prompt);
                req.setGuidanceScale(scale);
                req.setSeed(12345); // 使用相同种子确保可比性

                ImageGenerateRes result = doubaoImageEditService.editImage(req);
                log.info("引导尺度 {} 编辑成功，图片URL: {}", scale, result.getImageUrl());

                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("引导尺度 {} 编辑失败: {}", scale, e.getMessage());
            }
        }
    }

    /**
     * 示例6：错误处理演示
     */
    public void example6_ErrorHandling() {
        log.info("=== 示例6：错误处理演示 ===");

        // 测试无效图像URL
        try {
            DoubaoImageEditReq req = new DoubaoImageEditReq();
            req.setImage("invalid-url");
            req.setPrompt("测试提示词");

            doubaoImageEditService.editImage(req);
        } catch (Exception e) {
            log.warn("预期的错误 - 无效图像URL: {}", e.getMessage());
        }

        // 测试空提示词
        try {
            DoubaoImageEditReq req = new DoubaoImageEditReq();
            req.setImage("https://example.com/valid-image.jpg");
            req.setPrompt("");

            doubaoImageEditService.editImage(req);
        } catch (Exception e) {
            log.warn("预期的错误 - 空提示词: {}", e.getMessage());
        }

        // 测试无效引导尺度
        try {
            DoubaoImageEditReq req = new DoubaoImageEditReq();
            req.setImage("https://example.com/valid-image.jpg");
            req.setPrompt("有效提示词");
            req.setGuidanceScale(15.0); // 超出范围

            doubaoImageEditService.editImage(req);
        } catch (Exception e) {
            log.warn("预期的错误 - 无效引导尺度: {}", e.getMessage());
        }
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("开始运行豆包图像编辑示例...");

        example1_DirectApiClient();
        example2_ServiceLayer();
        example3_CustomApiKey();
        example4_BatchEdit();
        example5_GuidanceScaleComparison();
        example6_ErrorHandling();

        log.info("所有示例运行完成！");
    }
}
