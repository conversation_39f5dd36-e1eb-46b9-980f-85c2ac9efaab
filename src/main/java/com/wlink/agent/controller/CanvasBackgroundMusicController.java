package com.wlink.agent.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.CanvasBackgroundMusicReq;
import com.wlink.agent.model.res.CanvasBackgroundMusicRes;
import com.wlink.agent.service.CanvasBackgroundMusicService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 画布背景音乐控制器
 */
@Tag(name = "画布背景音乐管理", description = "画布背景音乐相关接口")
@Slf4j
@RestController
@RequestMapping("/agent/canvas/background-music")
@RequiredArgsConstructor
public class CanvasBackgroundMusicController {
    
    private final CanvasBackgroundMusicService backgroundMusicService;
    
    /**
     * 设置画布背景音乐
     *
     * @param req 背景音乐请求
     * @return 背景音乐ID
     */
    @PostMapping("/set")
    @Operation(summary = "设置画布背景音乐", description = "新增或更新画布背景音乐，一个画布只能有一个背景音乐。当音频URL为空时，会删除背景音乐")
    public SingleResponse<Long> setCanvasBackgroundMusic(@Valid @RequestBody CanvasBackgroundMusicReq req) {
        log.info("设置画布背景音乐: canvasId={}, audioUrl={}", req.getCanvasId(), req.getAudioUrl());
        Long musicId = backgroundMusicService.setCanvasBackgroundMusic(req);
        return SingleResponse.of(musicId);
    }


}
