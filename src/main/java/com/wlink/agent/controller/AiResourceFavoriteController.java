package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.BatchFavoriteReq;
import com.wlink.agent.model.req.BatchRemoveFavoriteReq;
import com.wlink.agent.model.res.ResourceFavoriteRes;
import com.wlink.agent.service.AiResourceFavoriteService;
import com.wlink.agent.utils.UserContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 资源收藏控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/resource/favorite")
@Tag(name = "资源收藏接口", description = "提供资源收藏相关功能")
public class AiResourceFavoriteController {

    @Resource
    private AiResourceFavoriteService aiResourceFavoriteService;

//    /**
//     * 通过资源ID和类型添加收藏
//     *
//     * @param req 简化的收藏请求
//     * @return 操作结果
//     */
//    @PostMapping("/add")
//    @ApiOperation(value = "通过ID添加资源收藏", notes = "通过资源ID和类型添加收藏，图片类型会自动获取详细信息")
//    public Response addFavoriteById(
//            @ApiParam(value = "简化的收藏请求参数", required = true) @Valid @RequestBody SimpleFavoriteReq req) {
//
//        SimpleUserInfo userInfo = UserContext.getUser();
//        if (userInfo == null || userInfo.getUserId() == null) {
//            throw new BizException("用户信息不存在");
//        }
//        String userId = userInfo.getUserId();
//
//        log.info("用户 {} 请求通过ID收藏资源，类型: {}, ID: {}", userId, req.getResourceType(), req.getResourceId());
//        return aiResourceFavoriteService.addFavoriteById(userId, req);
//    }
//
//    /**
//     * 移除资源收藏
//     *
//     * @param resourceCode 资源编码
//     * @return 操作结果
//     */
//    @PostMapping("/remove/{resourceCode}")
//    @ApiOperation(value = "移除资源收藏", notes = "从收藏列表中移除指定的资源")
//    public Response removeFavorite(
//            @ApiParam(value = "资源编码", required = true) @PathVariable String resourceCode) {
//
//        SimpleUserInfo userInfo = UserContext.getUser();
//        if (userInfo == null || userInfo.getUserId() == null) {
//            throw new BizException("用户信息不存在");
//        }
//        String userId = userInfo.getUserId();
//
//        log.info("用户 {} 请求取消收藏资源 {}", userId, resourceCode);
//        return aiResourceFavoriteService.removeFavorite(userId, resourceCode);
//    }

    /**
     * 查询用户收藏的所有资源列表
     *
     * @return 收藏资源列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询资源收藏列表", description = "获取当前用户收藏的所有资源")
    public MultiResponse<ResourceFavoriteRes> listFavorites() {
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求查询收藏资源列表", userId);
        return aiResourceFavoriteService.listUserFavorites(userId);
    }

    /**
     * 查询用户收藏的指定类型资源列表
     *
     * @param resourceType 资源类型
     * @return 收藏资源列表
     */
    @GetMapping("/list/type/{resourceType}")
    @Operation(summary = "查询指定类型的资源收藏列表", description = "获取当前用户收藏的指定类型资源")
    public MultiResponse<ResourceFavoriteRes> listFavoritesByType(
            @Parameter(description = "资源类型", required = true, example = "2") @PathVariable Integer resourceType) {

        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求查询类型为 {} 的收藏资源列表", userId, resourceType);
        return aiResourceFavoriteService.listUserFavoritesByType(userId, resourceType);
    }

    /**
     * 查询用户收藏的指定类型和子类型资源列表
     *
     * @param resourceType 资源类型
     * @param resourceSubtype 资源子类型
     * @return 收藏资源列表
     */
    @GetMapping("/list/type/{resourceType}/subtype/{resourceSubtype}")
    @Operation(summary = "查询指定类型和子类型的资源收藏列表", description = "获取当前用户收藏的指定类型和子类型资源")
    public MultiResponse<ResourceFavoriteRes> listFavoritesByTypeAndSubtype(
            @Parameter(description = "资源类型", required = true, example = "2") @PathVariable Integer resourceType,
            @Parameter(description = "资源子类型", required = true, example = "1") @PathVariable Integer resourceSubtype) {

        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求查询类型为 {}, 子类型为 {} 的收藏资源列表", userId, resourceType, resourceSubtype);
        return aiResourceFavoriteService.listUserFavoritesByTypeAndSubtype(userId, resourceType, resourceSubtype);
    }

    /**
     * 批量添加资源收藏
     *
     * @param req 批量收藏请求
     * @return 操作结果
     */
    @PostMapping("/batch/add")
    @Operation(summary = "批量添加资源收藏", description = "批量添加资源收藏，图片类型会自动获取详细信息")
    public Response batchAddFavoriteById(
            @Parameter(description = "批量收藏请求参数", required = true) @Valid @RequestBody BatchFavoriteReq req) {

        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求批量收藏资源，类型: {}, ID数量: {}", userId, req.getResourceType(), req.getResourceIds().size());
        return aiResourceFavoriteService.batchAddFavoriteById(userId, req);
    }

    /**
     * 批量移除资源收藏
     *
     * @param req 批量移除请求
     * @return 操作结果
     */
    @PostMapping("/batch/remove")
    @Operation(summary = "批量移除资源收藏", description = "批量从收藏列表中移除指定的资源")
    public Response batchRemoveFavorite(
            @Parameter(description = "批量移除请求参数", required = true) @Valid @RequestBody BatchRemoveFavoriteReq req) {

        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求批量取消收藏资源，编码数量: {}", userId, req.getResourceIds().size());
        return aiResourceFavoriteService.batchRemoveFavorite(userId, req);
    }

}