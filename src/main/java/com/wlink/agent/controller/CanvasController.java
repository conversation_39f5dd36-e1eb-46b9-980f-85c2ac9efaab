package com.wlink.agent.controller;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.CanvasUpdateReq;
import com.wlink.agent.model.req.ChapterToCanvasReq;
import com.wlink.agent.model.req.ShotOrderUpdateReq;
import com.wlink.agent.model.res.AiCanvasDetailRes;
import com.wlink.agent.model.res.AiCanvasRes;
import com.wlink.agent.service.AiCanvasService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 画布控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent/canvas")
@Tag(name = "画布接口")
public class CanvasController {

    private final AiCanvasService canvasService;
    
    /**
     * 创建新画布（名称默认生成为"未命名x"）
     * 
     * @return 创建的画布ID
     */
    @PostMapping("/create")
    @Operation(summary = "创建画布")
    public SingleResponse<Long> createCanvas() {
        log.info("Creating new canvas");
        Long canvasId = canvasService.createCanvas();
        return SingleResponse.of(canvasId);
    }
    
    /**
     * 更新画布信息（支持修改名称和封面）
     * 
     * @param req 更新请求
     * @return 成功响应
     */
    @PutMapping("/update")
    @Operation(summary = "更新画布")
    public Response updateCanvas(@Valid @RequestBody CanvasUpdateReq req) {
        log.info("Updating canvas, id: {}", req.getCanvasId());
        canvasService.updateCanvas(req);
        return Response.buildSuccess();
    }
    
    /**
     * 分页查询当前用户的画布列表
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询画布列表")
    public PageResponse<AiCanvasRes> getCanvasList(
            @Parameter(description = "页码（从1开始）", required = true) 
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @Parameter(description = "每页大小", required = true) 
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        log.info("Getting canvas list for pageNum: {}, pageSize: {}", pageNum, pageSize);
        return canvasService.getCanvasList(pageNum, pageSize);
    }
    
    /**
     * 获取画布详情（包含分镜信息）
     * 
     * @param canvasId 画布ID
     * @return 画布详情
     */
    @GetMapping("/detail/{canvasId}")
    @Operation(summary = "获取画布详情")
    public SingleResponse<AiCanvasDetailRes> getCanvasDetail(
            @Parameter(description = "画布ID", required = true) 
            @PathVariable("canvasId") Long canvasId) {
        log.info("Getting canvas detail, id: {}", canvasId);
        AiCanvasDetailRes detail = canvasService.getCanvasDetail(canvasId);
        return SingleResponse.of(detail);
    }

    /**
     * 修改分镜顺序
     *
     * @param req 分镜顺序更新请求
     * @return 成功响应
     */
    @PutMapping("/shot-order")
    @Operation(summary = "修改分镜顺序")
    public Response updateShotOrder(@Valid @RequestBody ShotOrderUpdateReq req) {
        log.info("Updating shot order for canvas: {}, shots count: {}", req.getCanvasId(), req.getShotOrders().size());
        canvasService.updateShotOrder(req);
        return Response.buildSuccess();
    }
    
    /**
     * 将章节转换为画布
     *
     * @param req 章节转画布请求
     * @return 创建的画布ID
     */
    @PostMapping("/convert-from-chapter")
    @Operation(summary = "将章节转换为画布")
    public SingleResponse<Long> convertChapterToCanvas(@Valid @RequestBody ChapterToCanvasReq req) {
        log.info("Converting chapter to canvas: sessionId={}, segmentId={}", req.getSessionId(), req.getSegmentId());
        Long canvasId = canvasService.convertChapterToCanvas(req);
        return SingleResponse.of(canvasId);
    }

    /**
     * 删除画布
     *
     * @param canvasId 画布ID
     * @return 成功响应
     */
    @DeleteMapping("/{canvasId}")
    @Operation(summary = "删除画布")
    public Response deleteCanvas(
            @Parameter(description = "画布ID", required = true)
            @PathVariable("canvasId") Long canvasId) {
        log.info("Deleting canvas: {}", canvasId);
        canvasService.deleteCanvas(canvasId);
        return Response.buildSuccess();
    }

    /**
     * 根据会话ID和章节ID查询画布详情
     *
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @return 画布详情
     */
    @GetMapping("/session/{sessionId}/segment/{segmentId}")
    @Operation(summary = "根据会话ID和章节ID查询画布详情")
    public SingleResponse<AiCanvasDetailRes> getCanvasBySessionAndSegment(
            @Parameter(description = "会话ID", required = true)
            @PathVariable("sessionId") String sessionId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable("segmentId") String segmentId) {
        log.info("Getting canvas by sessionId: {} and segmentId: {}", sessionId, segmentId);
        AiCanvasDetailRes detail = canvasService.getCanvasBySessionAndSegment(sessionId, segmentId);
        return SingleResponse.of(detail);
    }
}