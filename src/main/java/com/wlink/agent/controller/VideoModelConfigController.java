package com.wlink.agent.controller;


import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.VideoModelConfigQueryReq;
import com.wlink.agent.model.res.VideoModelConfigRes;
import com.wlink.agent.service.VideoModelConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频模型配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/video-model-config")
@Tag(name = "视频模型配置管理")
@RequiredArgsConstructor
public class VideoModelConfigController {
    
    private final VideoModelConfigService videoModelConfigService;
    
    /**
     * 查询所有启用的视频模型配置列表
     * 
     * @return 视频模型配置列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询所有启用的视频模型配置列表", description = "获取所有启用状态的视频模型配置信息")
    public MultiResponse<VideoModelConfigRes> listAllEnabledConfigs() {
        log.info("查询所有启用的视频模型配置列表");
        List<VideoModelConfigRes> configs = videoModelConfigService.listAllEnabledConfigs();
        log.info("查询到 {} 个启用的视频模型配置", configs.size());
        return MultiResponse.of(configs);
    }
    
    /**
     * 根据条件查询视频模型配置列表
     * 
     * @param queryReq 查询条件
     * @return 视频模型配置列表
     */
    @PostMapping("/search")
    @Operation(summary = "根据条件查询视频模型配置列表", description = "根据提供商、模型类型等条件筛选视频模型配置")
    public MultiResponse<VideoModelConfigRes> searchConfigs(@RequestBody VideoModelConfigQueryReq queryReq) {
        log.info("根据条件查询视频模型配置列表: {}", queryReq);
        List<VideoModelConfigRes> configs = videoModelConfigService.listConfigsByCondition(queryReq);
        log.info("根据条件查询到 {} 个视频模型配置", configs.size());
        return MultiResponse.of(configs);
    }
    
    /**
     * 根据提供商查询视频模型配置列表
     * 
     * @param provider 提供商
     * @return 视频模型配置列表
     */
    @GetMapping("/provider/{provider}")
    @Operation(summary = "根据提供商查询视频模型配置列表", description = "根据指定提供商查询其支持的视频模型配置")
    public MultiResponse<VideoModelConfigRes> listConfigsByProvider(
            @Parameter(description = "提供商(MINIMAX, DOUBAO)", required = true) 
            @PathVariable String provider) {
        log.info("根据提供商查询视频模型配置列表: provider={}", provider);
        List<VideoModelConfigRes> configs = videoModelConfigService.listConfigsByProvider(provider);
        log.info("提供商 {} 有 {} 个视频模型配置", provider, configs.size());
        return MultiResponse.of(configs);
    }
    
    /**
     * 根据模型类型查询视频模型配置列表
     * 
     * @param modelType 模型类型
     * @return 视频模型配置列表
     */
    @GetMapping("/model-type/{modelType}")
    @Operation(summary = "根据模型类型查询视频模型配置列表", description = "根据指定模型类型查询支持该类型的视频模型配置")
    public MultiResponse<VideoModelConfigRes> listConfigsByModelType(
            @Parameter(description = "模型类型(T2V-文生视频, I2V-图生视频, FLF2V-首尾帧图生视频)", required = true) 
            @PathVariable String modelType) {
        log.info("根据模型类型查询视频模型配置列表: modelType={}", modelType);
        List<VideoModelConfigRes> configs = videoModelConfigService.listConfigsByModelType(modelType);
        log.info("模型类型 {} 有 {} 个视频模型配置", modelType, configs.size());
        return MultiResponse.of(configs);
    }
    
    /**
     * 根据模型名称获取视频模型配置详情
     * 
     * @param modelName 模型名称
     * @return 视频模型配置详情
     */
    @GetMapping("/detail/{modelName}")
    @Operation(summary = "根据模型名称获取视频模型配置详情", description = "根据指定模型名称获取详细的配置信息")
    public SingleResponse<VideoModelConfigRes> getConfigByModelName(
            @Parameter(description = "模型名称", required = true) 
            @PathVariable String modelName) {
        log.info("根据模型名称获取视频模型配置详情: modelName={}", modelName);
        VideoModelConfigRes config = videoModelConfigService.getConfigByModelName(modelName);
        if (config != null) {
            log.info("找到模型配置: {}", config.getModelDisplayName());
            return SingleResponse.of(config);
        } else {
            log.warn("未找到模型配置: modelName={}", modelName);
            return SingleResponse.of(null);
        }
    }
    
    /**
     * 根据提供商和模型类型查询视频模型配置列表
     * 
     * @param provider 提供商
     * @param modelType 模型类型
     * @return 视频模型配置列表
     */
    @GetMapping("/provider/{provider}/model-type/{modelType}")
    @Operation(summary = "根据提供商和模型类型查询视频模型配置列表", 
               description = "根据指定提供商和模型类型查询匹配的视频模型配置")
    public MultiResponse<VideoModelConfigRes> listConfigsByProviderAndModelType(
            @Parameter(description = "提供商(MINIMAX, DOUBAO)", required = true) 
            @PathVariable String provider,
            @Parameter(description = "模型类型(T2V-文生视频, I2V-图生视频, FLF2V-首尾帧图生视频)", required = true) 
            @PathVariable String modelType) {
        log.info("根据提供商和模型类型查询视频模型配置列表: provider={}, modelType={}", provider, modelType);
        List<VideoModelConfigRes> configs = videoModelConfigService.listConfigsByProviderAndModelType(provider, modelType);
        log.info("提供商 {} 的模型类型 {} 有 {} 个视频模型配置", provider, modelType, configs.size());
        return MultiResponse.of(configs);
    }
    
    /**
     * 根据图片数量查询视频模型配置列表
     *
     * @param imageCount 图片数量
     * @return 视频模型配置列表
     */
    @GetMapping("/image-count/{imageCount}")
    @Operation(summary = "根据图片数量查询视频模型配置列表",
               description = "根据指定图片数量查询支持该数量的视频模型配置(0-不支持图片,1-支持首帧,2-支持首尾帧)")
    public MultiResponse<VideoModelConfigRes> listConfigsByImageCount(
            @Parameter(description = "图片数量(0-不支持图片,1-支持首帧,2-支持首尾帧)", required = true)
            @PathVariable Integer imageCount) {
        log.info("根据图片数量查询视频模型配置列表: imageCount={}", imageCount);
        List<VideoModelConfigRes> configs = videoModelConfigService.listConfigsByImageCount(imageCount);
        log.info("图片数量 {} 有 {} 个视频模型配置", imageCount, configs.size());
        return MultiResponse.of(configs);
    }

    /**
     * 获取支持的提供商列表
     *
     * @return 提供商列表
     */
    @GetMapping("/providers")
    @Operation(summary = "获取支持的提供商列表", description = "获取系统支持的所有视频模型提供商")
    public MultiResponse<String> getSupportedProviders() {
        log.info("获取支持的提供商列表");
        List<String> providers = List.of("MINIMAX", "DOUBAO");
        return MultiResponse.of(providers);
    }

    /**
     * 获取支持的模型类型列表
     *
     * @return 模型类型列表
     */
    @GetMapping("/model-types")
    @Operation(summary = "获取支持的模型类型列表", description = "获取系统支持的所有视频模型类型")
    public MultiResponse<String> getSupportedModelTypes() {
        log.info("获取支持的模型类型列表");
        List<String> modelTypes = List.of("T2V", "I2V", "FLF2V");
        return MultiResponse.of(modelTypes);
    }
}
