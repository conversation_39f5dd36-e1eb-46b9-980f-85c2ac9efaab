package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.dao.po.PayOrderPo;
import com.wlink.agent.enums.PayStatusEnum;
import com.wlink.agent.service.PayCallbackService;
import com.wlink.agent.user.model.PayOrderStatusRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付回调控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent/pay/callback")
@Tag(name = "支付回调接口")
public class PayCallbackController {

    private final PayCallbackService payCallbackService;

    /**
     * 微信支付回调
     */
    @PostMapping("/wx")
    @Operation(summary = "微信支付回调", description = "接收微信支付结果通知")
    public String wxPayCallback(@RequestBody String xmlData) {
        log.info("接收到微信支付回调请求");
        return payCallbackService.handleWxPayCallback(xmlData);
    }

    /**
     * 主动查询订单支付状态
     */
    @GetMapping("/check/{orderNo}")
    @Operation(summary = "主动查询订单支付状态", description = "主动查询微信支付订单状态并同步本地订单")
    public SingleResponse<PayOrderStatusRes> checkOrderStatus(
            @Parameter(description = "订单编号", required = true)
            @PathVariable("orderNo") String orderNo) {

        PayOrderPo order = payCallbackService.checkOrderStatus(orderNo);

        // 转换为轻量级响应对象
        PayOrderStatusRes statusRes = new PayOrderStatusRes();
        statusRes.setOrderNo(order.getOrderNo());
        statusRes.setAmount(order.getAmount() / 100.0); // 分转元
        statusRes.setSubject(order.getSubject());
        statusRes.setStatus(order.getStatus());
        // 获取状态描述
        PayStatusEnum.OrderStatus orderStatus = PayStatusEnum.OrderStatus.getByCode(order.getStatus());
        statusRes.setStatusDesc(orderStatus != null ? orderStatus.getDesc() : "未知状态");
        statusRes.setCreateTime(order.getCreateTime());
        statusRes.setPayTime(order.getPayTime());

        return SingleResponse.of(statusRes);
    }
}