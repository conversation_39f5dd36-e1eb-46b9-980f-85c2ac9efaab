package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.wlink.agent.model.res.ImageStyleRes;
import com.wlink.agent.model.res.TagRes;
import com.wlink.agent.service.ContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/agent")
@Tag(name = "内容管理")
@Validated
@RequiredArgsConstructor
public class ContentController {

    private final ContentService contentService; // 注入 ContentService

    @GetMapping("/image/style/list")
    @Operation(summary = "获取可用图片风格列表", description = "查询并返回所有可用的图片风格")
    public MultiResponse<ImageStyleRes> getImageStyles() {
        log.info("Request to list image styles.");
        List<ImageStyleRes> styles = contentService.getAllActiveStyles();
        log.info("Returning {} image styles.", styles.size());
        return MultiResponse.of(styles);
    }


    @GetMapping("/tag/list")
    @Operation(summary = "获取可用标签列表", description = "查询并返回所有可用的标签")
    public MultiResponse<TagRes> getTags() {
        log.info("Request to list tags.");
        List<TagRes> tags = contentService.getAllTags();
        log.info("Returning {} tags.", tags.size());
        return MultiResponse.of(tags);
    }
}
