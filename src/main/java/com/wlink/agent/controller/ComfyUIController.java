package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackRequest;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackResponse;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.wlink.agent.event.ComfyUICallbackEvent;
import com.wlink.agent.service.ComfyUIService;
import org.springframework.context.ApplicationEventPublisher;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * ComfyUI 控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/comfyui")
@Tag(name = "ComfyUI 接口")
@RequiredArgsConstructor
public class ComfyUIController {

    private final ApplicationEventPublisher eventPublisher;



    /**
     * ComfyUI 结果回调处理接口
     *
     * @param request 回调请求
     * @return 回调响应
     */
    @PostMapping("/callback")
    @Operation(summary = "ComfyUI 结果回调", description = "接收 ComfyUI 的任务结果回调")
    public ComfyUICallbackResponse handleCallback(@Valid @RequestBody ComfyUICallbackRequest request) {
        log.info("接收到 ComfyUI 回调: event={}, taskId={}, eventData={}", request.getEvent(), request.getTaskId(), JSON.toJSONString(request.getEventData()) );
        try {
            // 发布异步事件进行处理
            ComfyUICallbackEvent event = new ComfyUICallbackEvent(this, request);
            eventPublisher.publishEvent(event);

            log.info("ComfyUI 回调事件已发布: event={}, taskId={}", request.getEvent(), request.getTaskId());
            // 立即返回成功响应
            return ComfyUICallbackResponse.success();

        } catch (Exception e) {
            log.error("处理 ComfyUI 回调异常: event={}, taskId={}, error={}",
                    request.getEvent(), request.getTaskId(), e.getMessage(), e);
            return ComfyUICallbackResponse.failure("处理回调异常: " + e.getMessage());
        }
    }

    /**
     * 测试回调处理接口（仅用于开发测试）
     *
     * @param taskId 任务ID
     * @param fileUrl 文件URL
     * @param fileType 文件类型
     * @return 测试结果
     */
    @PostMapping("/test-callback")
    @Operation(summary = "测试回调处理", description = "测试不同类型任务的回调处理逻辑，通过查询数据库区分任务类型")
    public SingleResponse<String> testCallback(
            @RequestParam String taskId,
            @RequestParam String fileUrl,
            @RequestParam(required = false) String fileType) {

        log.info("测试回调处理: taskId={}, fileUrl={}, fileType={}", taskId, fileUrl, fileType);

        try {
            // 构造测试回调请求
            ComfyUICallbackRequest testRequest = new ComfyUICallbackRequest();
            testRequest.setEvent("TASK_END");
            testRequest.setTaskId(taskId);

            // 构造事件数据
            String eventData = String.format(
                "{\"code\":0,\"msg\":\"success\",\"data\":[{\"fileUrl\":\"%s\",\"fileType\":\"%s\",\"taskCostTime\":5000}]}",
                fileUrl, fileType != null ? fileType : "unknown"
            );
            testRequest.setEventData(eventData);

            // 发布事件
            ComfyUICallbackEvent event = new ComfyUICallbackEvent(this, testRequest);
            eventPublisher.publishEvent(event);

            return SingleResponse.of("测试回调事件已发布，将通过查询数据库判断任务类型");

        } catch (Exception e) {
            log.error("测试回调处理异常: taskId={}, error={}", taskId, e.getMessage(), e);
            return SingleResponse.buildFailure("测试失败:", e.getMessage());
        }
    }
}
