package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.annotation.LogRequest;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.RoleFavoriteReq;
import com.wlink.agent.model.res.RoleFavoriteRes;
import com.wlink.agent.service.AiRoleFavoriteService;
import com.wlink.agent.utils.UserContext;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 角色收藏控制 */
@Slf4j
@RestController
@RequestMapping("/agent/role/favorite")
@Tag(name = "角色收藏控制")
public class AiRoleFavoriteController {

    @Resource
    private AiRoleFavoriteService aiRoleFavoriteService;

    /**
     * 添加角色收藏
     *
     * @param req 收藏请求
     * @return 操作结果
     */
    @LogRequest(description = "添加角色收藏")
    @PostMapping("/add")
    @Operation(summary = "添加角色收藏", description = "将指定会话中的角色添加到收藏列表")
    public Response addFavorite(
            @Parameter(description = "收藏请求参数", required = true) @Valid @RequestBody RoleFavoriteReq req) {
        
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求收藏角色，会话ID: {}, 角色ID: {}", userId, req.getSessionId(), req.getRoleId());
        return aiRoleFavoriteService.addFavorite(userId, req);
    }

    /**
     * 移除角色收藏
     *
     * @param roleCode 角色编码
     * @return 操作结果
     */
    @LogRequest(description = "移除角色收藏")
    @PostMapping("/remove/{roleId}")
@Operation(summary = "Remove Favorite")
    public Response removeFavorite(
            @Parameter(description = "角色ID", required = true) @PathVariable String roleCode) {
        
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求取消收藏角色 {}", userId, roleCode);
        return aiRoleFavoriteService.removeFavorite(userId, roleCode);
    }

    /**
     * 查询用户收藏的角色列     *
     * @return 收藏角色列表
     */
    @LogRequest(description = "查询角色收藏列表")
    @GetMapping("/list")
@Operation(summary = "鏌ヨFavorites鍒楄〃")
    public MultiResponse<RoleFavoriteRes> listFavorites() {
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        String userId = userInfo.getUserId();

        log.info("用户 {} 请求查询收藏角色列表", userId);
        return aiRoleFavoriteService.listUserFavorites(userId);
    }
} 
