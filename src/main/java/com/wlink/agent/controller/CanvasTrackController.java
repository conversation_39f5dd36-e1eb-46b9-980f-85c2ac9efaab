package com.wlink.agent.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.CanvasTrackCreateReq;
import com.wlink.agent.model.req.TrackAudioCreateReq;
import com.wlink.agent.model.res.CanvasTrackRes;
import com.wlink.agent.model.res.TrackAudioRes;
import com.wlink.agent.service.CanvasTrackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 画布音轨控制器
 */
@Tag(name = "画布音轨管理", description = "画布音轨和音频管理相关接口")
@Slf4j
@RestController
@RequestMapping("/agent/canvas/track")
@RequiredArgsConstructor
public class CanvasTrackController {
    
    private final CanvasTrackService canvasTrackService;
    
    /**
     * 创建画布音轨
     *
     * @param req 创建请求
     * @return 音轨ID
     */
    @PostMapping("/create")
    @Operation(summary = "创建画布音轨")
    public SingleResponse<Long> createCanvasTrack(@Valid @RequestBody CanvasTrackCreateReq req) {
        log.info("创建画布音轨: canvasId={}, trackName={}", req.getCanvasId(), req.getTrackName());
        Long trackId = canvasTrackService.createCanvasTrack(req);
        return SingleResponse.of(trackId);
    }
    
    /**
     * 获取画布音轨列表
     *
     * @param canvasId 画布ID
     * @return 音轨列表
     */
    @GetMapping("/list/{canvasId}")
    @Operation(summary = "获取画布音轨列表")
    public SingleResponse<List<CanvasTrackRes>> getCanvasTracks(
            @Parameter(description = "画布ID", required = true)
            @PathVariable("canvasId") Long canvasId) {
        log.info("获取画布音轨列表: canvasId={}", canvasId);
        List<CanvasTrackRes> tracks = canvasTrackService.getCanvasTracks(canvasId);
        return SingleResponse.of(tracks);
    }
    
    /**
     * 获取音轨详情
     *
     * @param trackId 音轨ID
     * @return 音轨详情
     */
    @GetMapping("/detail/{trackId}")
    @Operation(summary = "获取音轨详情")
    public SingleResponse<CanvasTrackRes> getTrackDetail(
            @Parameter(description = "音轨ID", required = true)
            @PathVariable("trackId") Long trackId) {
        log.info("获取音轨详情: trackId={}", trackId);
        CanvasTrackRes track = canvasTrackService.getTrackDetail(trackId);
        return SingleResponse.of(track);
    }
    
    /**
     * 更新音轨信息
     *
     * @param trackId 音轨ID
     * @param req 更新请求
     * @return 更新结果
     */
    @PutMapping("/update/{trackId}")
    @Operation(summary = "更新音轨信息")
    public Response updateCanvasTrack(
            @Parameter(description = "音轨ID", required = true)
            @PathVariable("trackId") Long trackId,
            @Valid @RequestBody CanvasTrackCreateReq req) {
        log.info("更新音轨信息: trackId={}, trackName={}", trackId, req.getTrackName());
        canvasTrackService.updateCanvasTrack(trackId, req);
        return Response.buildSuccess();
    }
    
    /**
     * 删除音轨
     *
     * @param trackId 音轨ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{trackId}")
    @Operation(summary = "删除音轨")
    public Response deleteCanvasTrack(
            @Parameter(description = "音轨ID", required = true)
            @PathVariable("trackId") Long trackId) {
        log.info("删除音轨: trackId={}", trackId);
        canvasTrackService.deleteCanvasTrack(trackId);
        return Response.buildSuccess();
    }
    
    /**
     * 添加音频到音轨
     *
     * @param req 添加请求
     * @return 音频ID
     */
    @PostMapping("/audio/add")
    @Operation(summary = "添加音频到音轨")
    public SingleResponse<Long> addTrackAudio(@Valid @RequestBody TrackAudioCreateReq req) {
        log.info("添加音频到音轨: trackId={}, audioName={}", req.getTrackId(), req.getAudioName());
        Long audioId = canvasTrackService.addTrackAudio(req);
        return SingleResponse.of(audioId);
    }
    
    /**
     * 获取音轨音频列表
     *
     * @param trackId 音轨ID
     * @return 音频列表
     */
    @GetMapping("/audio/list/{trackId}")
    @Operation(summary = "获取音轨音频列表")
    public SingleResponse<List<TrackAudioRes>> getTrackAudios(
            @Parameter(description = "音轨ID", required = true)
            @PathVariable("trackId") Long trackId) {
        log.info("获取音轨音频列表: trackId={}", trackId);
        List<TrackAudioRes> audios = canvasTrackService.getTrackAudios(trackId);
        return SingleResponse.of(audios);
    }
    
    /**
     * 获取音频详情
     *
     * @param audioId 音频ID
     * @return 音频详情
     */
    @GetMapping("/audio/detail/{audioId}")
    @Operation(summary = "获取音频详情")
    public SingleResponse<TrackAudioRes> getAudioDetail(
            @Parameter(description = "音频ID", required = true)
            @PathVariable("audioId") Long audioId) {
        log.info("获取音频详情: audioId={}", audioId);
        TrackAudioRes audio = canvasTrackService.getAudioDetail(audioId);
        return SingleResponse.of(audio);
    }
    
    /**
     * 更新音频信息
     *
     * @param audioId 音频ID
     * @param req 更新请求
     * @return 更新结果
     */
    @PutMapping("/audio/update/{audioId}")
    @Operation(summary = "更新音频信息")
    public Response updateTrackAudio(
            @Parameter(description = "音频ID", required = true)
            @PathVariable("audioId") Long audioId,
            @Valid @RequestBody TrackAudioCreateReq req) {
        log.info("更新音频信息: audioId={}, audioName={}", audioId, req.getAudioName());
        canvasTrackService.updateTrackAudio(audioId, req);
        return Response.buildSuccess();
    }
    
    /**
     * 删除音频
     *
     * @param audioId 音频ID
     * @return 删除结果
     */
    @DeleteMapping("/audio/delete/{audioId}")
    @Operation(summary = "删除音频")
    public Response deleteTrackAudio(
            @Parameter(description = "音频ID", required = true)
            @PathVariable("audioId") Long audioId) {
        log.info("删除音频: audioId={}", audioId);
        canvasTrackService.deleteTrackAudio(audioId);
        return Response.buildSuccess();
    }
}
