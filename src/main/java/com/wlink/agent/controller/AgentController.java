package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.wlink.agent.model.req.DeleteSoundReq;
import com.wlink.agent.model.req.SoundQueryReq;
import com.wlink.agent.model.req.UpdateSoundNameReq;
import com.wlink.agent.model.res.SoundRes;
import com.wlink.agent.service.AgentSoundService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import javax.annotation.Resource;


@Tag(name = "智能体管理")
@Slf4j
@RestController
@RequestMapping("/agent/v1/")
public class AgentController {


    @Resource
    AgentSoundService agentSoundService;


    @Operation(description = "查询声音列表")
    @GetMapping("/sound/list")
    public MultiResponse<SoundRes> listSounds(SoundQueryReq req) {
        return MultiResponse.of(agentSoundService.listSounds(req));
    }

    @Operation(summary = "修改定制声音名称", description = "根据声音ID修改定制声音的名称")
    @PutMapping("/sound/update-name")
    public Response updateSoundName(@Valid @RequestBody UpdateSoundNameReq req) {
        log.info("修改定制声音名称请求: soundId={}, newName={}", req.getSoundId(), req.getName());

        try {
            agentSoundService.updateSoundName(req);
            log.info("修改定制声音名称成功: soundId={}, newName={}", req.getSoundId(), req.getName());
            return Response.buildSuccess();
        } catch (Exception e) {
            log.error("修改定制声音名称失败: soundId={}, newName={}", req.getSoundId(), req.getName(), e);
            return Response.buildFailure("UPDATE_SOUND_NAME_FAILED", e.getMessage());
        }
    }

    @Operation(summary = "删除定制声音", description = "根据声音ID删除定制声音（逻辑删除）")
    @DeleteMapping("/sound/delete/{soundId}")
    public Response deleteSound(
            @Parameter(description = "声音ID", required = true, example = "123456789")
            @PathVariable("soundId") Long soundId) {
        log.info("删除定制声音请求: soundId={}", soundId);

        try {
            DeleteSoundReq req = new DeleteSoundReq();
            req.setSoundId(soundId);
            agentSoundService.deleteSound(req);
            log.info("删除定制声音成功: soundId={}", soundId);
            return Response.buildSuccess();
        } catch (Exception e) {
            log.error("删除定制声音失败: soundId={}", soundId, e);
            return Response.buildFailure("DELETE_SOUND_FAILED", e.getMessage());
        }
    }
}
