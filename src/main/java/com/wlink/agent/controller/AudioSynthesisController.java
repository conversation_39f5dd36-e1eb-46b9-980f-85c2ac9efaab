package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.dto.AudioSynthesisRequest;
import com.wlink.agent.model.dto.AudioSynthesisResult;
import com.wlink.agent.service.AudioSynthesisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 音频合成控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/audio-synthesis")
@Tag(name = "音频合成", description = "音频合成相关接口")
@RequiredArgsConstructor
public class AudioSynthesisController {

    private final AudioSynthesisService audioSynthesisService;

    /**
     * 提交音频合成任务
     */
    @PostMapping("/submit")
    @Operation(summary = "提交音频合成任务", description = "提交音频合成任务到ComfyUI进行处理")
    public SingleResponse<AudioSynthesisResult> submitSynthesisTask(@RequestBody AudioSynthesisRequest request) {
        try {
            log.info("提交音频合成任务: shotId={}, audioCount={}", request.getShotId(), 
                    request.getAudioUrls() != null ? request.getAudioUrls().size() : 0);

            AudioSynthesisResult result = audioSynthesisService.submitSynthesisTask(request);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("提交音频合成任务失败: shotId={}", request.getShotId(), e);
            return SingleResponse.buildFailure("SUBMIT_FAILED", "提交音频合成任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询音频合成任务状态
     */
    @GetMapping("/status/{taskId}")
    @Operation(summary = "查询音频合成任务状态", description = "根据任务ID查询音频合成任务的状态")
    public SingleResponse<AudioSynthesisResult> getTaskStatus(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            log.info("查询音频合成任务状态: taskId={}", taskId);

            AudioSynthesisResult result = audioSynthesisService.getTaskStatus(taskId);
            if (result == null) {
                return SingleResponse.buildFailure("TASK_NOT_FOUND", "任务不存在: " + taskId);
            }

            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("查询音频合成任务状态失败: taskId={}", taskId, e);
            return SingleResponse.buildFailure("GET_STATUS_FAILED", "查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 根据分镜ID查询最新的音频合成任务
     */
    @GetMapping("/latest-by-shot/{shotId}")
    @Operation(summary = "查询分镜最新音频合成任务", description = "根据分镜ID查询最新的音频合成任务")
    public SingleResponse<AudioSynthesisResult> getLatestTaskByShot(
            @Parameter(description = "分镜ID") @PathVariable Long shotId) {
        try {
            log.info("查询分镜最新音频合成任务: shotId={}", shotId);

            AudioSynthesisResult result = audioSynthesisService.getLatestTaskByShot(shotId);
            if (result == null) {
                return SingleResponse.buildFailure("SHOT_NO_TASK", "分镜没有音频合成任务: " + shotId);
            }

            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("查询分镜最新音频合成任务失败: shotId={}", shotId, e);
            return SingleResponse.buildFailure("GET_SHOT_TASK_FAILED", "查询分镜音频合成任务失败: " + e.getMessage());
        }
    }

    /**
     * 取消音频合成任务
     */
    @PostMapping("/cancel/{taskId}")
    @Operation(summary = "取消音频合成任务", description = "取消指定的音频合成任务")
    public SingleResponse<Boolean> cancelTask(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            log.info("取消音频合成任务: taskId={}", taskId);

            boolean success = audioSynthesisService.cancelTask(taskId);
            if (success) {
                return SingleResponse.of(true);
            } else {
                return SingleResponse.buildFailure("CANCEL_FAILED", "取消任务失败，任务可能不存在或已完成: " + taskId);
            }

        } catch (Exception e) {
            log.error("取消音频合成任务失败: taskId={}", taskId, e);
            return SingleResponse.buildFailure("CANCEL_ERROR", "取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 测试音频合成（使用测试数据）
     */
    @PostMapping("/test")
    @Operation(summary = "测试音频合成", description = "使用测试数据进行音频合成测试")
    public SingleResponse<AudioSynthesisResult> testAudioSynthesis(
            @Parameter(description = "分镜ID") @RequestParam Long shotId,
            @Parameter(description = "用户ID") @RequestParam String userId,
            @Parameter(description = "音频URL列表") @RequestParam List<String> audioUrls) {
        try {
            log.info("测试音频合成: shotId={}, userId={}, audioCount={}", shotId, userId, audioUrls.size());

            AudioSynthesisRequest request = new AudioSynthesisRequest();
            request.setShotId(shotId);
            request.setUserId(userId);
            request.setAudioUrls(audioUrls);

            AudioSynthesisResult result = audioSynthesisService.submitSynthesisTask(request);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("测试音频合成失败: shotId={}, userId={}", shotId, userId, e);
            return SingleResponse.buildFailure("TEST_FAILED", "测试音频合成失败: " + e.getMessage());
        }
    }
}
