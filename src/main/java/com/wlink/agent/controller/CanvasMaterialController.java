package com.wlink.agent.controller;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wlink.agent.annotation.LogRequest;
import com.wlink.agent.model.req.CanvasMaterialPageReq;
import com.wlink.agent.model.req.CanvasMaterialCreateReq;
import com.wlink.agent.model.req.ImageGenerateReq;
import com.wlink.agent.model.req.ShotLipSyncReq;
import com.wlink.agent.model.req.VideoGenerationReq;
import com.wlink.agent.model.res.CanvasMaterialRes;
import com.wlink.agent.model.res.ImageGenerateRes;
import com.wlink.agent.model.res.ShotLipSyncRes;
import com.wlink.agent.service.AiCanvasMaterialService;
import com.wlink.agent.service.ShotImageEditService;
import com.wlink.agent.service.ShotLipSyncService;
import com.wlink.agent.service.VideoGenerationQueueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 画布素材控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent/canvas/material")
@Tag(name = "画布素材管理接口")
public class CanvasMaterialController {

    private final AiCanvasMaterialService canvasMaterialService;
    private final VideoGenerationQueueService queueService;
    private final ShotLipSyncService shotLipSyncService;
    /**
     * 分页查询画布素材
     * 
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询画布素材")
    public PageResponse<CanvasMaterialRes> pageQueryMaterials(@Valid @RequestBody CanvasMaterialPageReq req) {
        log.info("Page querying canvas materials for canvas: {}, page: {}, size: {}", 
                req.getCanvasId(), req.getPageNum(), req.getPageSize());
        
        Page<CanvasMaterialRes> page = canvasMaterialService.pageQueryMaterials(req);

        return PageResponse.of(page.getRecords(), (int) page.getTotal(), req.getPageNum(), req.getPageSize());
    }

    /**
     * 新增画布素材
     *
     * @param req 新增素材请求
     * @return 新创建的素材ID
     */
    @PostMapping("/add")
    @Operation(summary = "新增画布素材")
    public SingleResponse<Long> createMaterial(@Valid @RequestBody CanvasMaterialCreateReq req) {
        log.info("Creating material for canvas: {}, type: {}, url: {}",
                req.getCanvasId(), req.getMaterialType(), req.getMaterialUrl());

        Long materialId = canvasMaterialService.createMaterial(req);
        return SingleResponse.of(materialId);
    }

    /**
     * 生成图片
     *
     * @param req 图片生成请求
     * @return 图片生成响应
     */
    @PostMapping("/generate-image")
    @Operation(summary = "生成图片")
    public SingleResponse<ImageGenerateRes> generateImage(@Valid @RequestBody ImageGenerateReq req) {
        log.info("Generating image for shot: {}, prompt: {}, hasReference: {}",
                req.getShotId(), req.getPrompt(), req.getReferenceImageUrl() != null);
        ImageGenerateRes result = canvasMaterialService.generateImage(req);
        return SingleResponse.of(result);
    }

    /**
     * 提交视频生成任务
     *
     * @param req 视频生成请求
     * @return 生成记录ID
     */
    @PostMapping("/generate-video")
    @Operation(summary = "提交视频生成任务")
    public SingleResponse<Long> generateVideo(@Valid @RequestBody VideoGenerationReq req) {
        log.info("用户提交视频生成任务: shotId={}, prompt={}, resolution={}, duration={}s",
                req.getShotId(),req.getPrompt(), req.getResolution(), req.getDuration());
        Long taskId = queueService.submitVideoGeneration(req);
        return SingleResponse.of(taskId);
    }





    /**
     * 删除画布素材
     *
     * @param materialId 素材ID
     * @return 成功响应
     */
    @DeleteMapping("/{materialId}")
    @Operation(summary = "删除画布素材")
    public Response deleteMaterial(
            @Parameter(description = "素材ID", required = true)
            @PathVariable("materialId") Long materialId) {
        log.info("Deleting canvas material: {}", materialId);
        canvasMaterialService.deleteMaterial(materialId);
        return Response.buildSuccess();
    }
    /**
     * 提交分镜对口型任务
     *
     * @param req 对口型请求
     * @return 对口型响应
     */
    @PostMapping("/lip-sync")
    @Operation(summary = "提交分镜对口型任务", description = "根据分镜ID查询音频和图片数据，提交对口型任务到 ComfyUI 进行处理")
    public SingleResponse<ShotLipSyncRes> submitLipSync(@Valid @RequestBody ShotLipSyncReq req) {
        log.info("提交分镜对口型任务: shotId={}", req.getShotId());

        ShotLipSyncRes result = shotLipSyncService.submitLipSync(req);
        return SingleResponse.of(result);
    }


}
