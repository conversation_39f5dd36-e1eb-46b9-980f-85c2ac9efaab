package com.wlink.agent.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.service.PayService;
import com.wlink.agent.user.model.CreateOrderReq;
import com.wlink.agent.user.model.PayOrderRes;
import com.wlink.agent.user.model.PayOrderStatusRes;
import com.wlink.agent.dao.po.PayOrderPo;
import com.wlink.agent.enums.PayStatusEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 支付控制
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent/pay")
@Tag(name = "支付相关接口")
public class PayController {

    private final PayService payService;

    /**
     * 创建支付订单
     */
    @PostMapping("/order/create")
    @Operation(summary = "创建支付订单")
    public SingleResponse<PayOrderRes> createOrder(@Valid @RequestBody CreateOrderReq request) {
        PayOrderRes orderRes = payService.createOrder(request);
        return SingleResponse.of(orderRes);
    }

    /**
     * 查询订单
     */
    @GetMapping("/order/query")
    @Operation(summary = "查询订单")
    public SingleResponse<PayOrderPo> queryOrder(
            @Parameter(description = "订单编号", required = true)
            @RequestParam("orderNo") String orderNo) {
        PayOrderPo order = payService.queryOrder(orderNo);
        return SingleResponse.of(order);
    }

    /**
     * 轮询查询支付状
     */
    @GetMapping("/order/status")
@Operation(summary = "鏌ヨPay Status")
    public SingleResponse<PayOrderStatusRes> queryPayStatus(
            @Parameter(description = "订单编号", required = true)
            @RequestParam("orderNo") String orderNo) {
        PayOrderPo order = payService.queryOrder(orderNo);
        // 转换为轻量级响应对象
        PayOrderStatusRes statusRes = new PayOrderStatusRes();
        statusRes.setOrderNo(order.getOrderNo());
        statusRes.setAmount(order.getAmount() / 100.0); // 分转
        statusRes.setSubject(order.getSubject());
        statusRes.setStatus(order.getStatus());
        // 获取状态描
        PayStatusEnum.OrderStatus orderStatus = PayStatusEnum.OrderStatus.getByCode(order.getStatus());
        statusRes.setStatusDesc(orderStatus != null ? orderStatus.getDesc() : "未知状态");
        statusRes.setCreateTime(order.getCreateTime());
        statusRes.setPayTime(order.getPayTime());

        return SingleResponse.of(statusRes);
    }

    /**
     * 关闭订单
     */
    @PostMapping("/order/close")
    @Operation(summary = "关闭订单")
    public Response closeOrder(
            @Parameter(description = "订单编号", required = true)
            @RequestParam("orderNo") String orderNo) {
        payService.closeOrder(orderNo);
        return Response.buildSuccess();
    }



    /**
     * 主动同步订单支付状态（从微信支付查询）
     */
    @GetMapping("/order/sync")
    @Operation(summary = "主动同步订单支付状态（从微信支付查询）")
    public SingleResponse<PayOrderStatusRes> syncOrderStatus(
            @Parameter(description = "订单编号", required = true)
            @RequestParam("orderNo") String orderNo) {
        // 主动查询微信支付订单状态并同步本地订单
        PayOrderPo order = payService.syncOrderStatus(orderNo);
        
        // 转换为轻量级响应对象
        PayOrderStatusRes statusRes = new PayOrderStatusRes();
        statusRes.setOrderNo(order.getOrderNo());
        statusRes.setAmount(order.getAmount() / 100.0); // 分转
        statusRes.setSubject(order.getSubject());
        statusRes.setStatus(order.getStatus());
        // 获取状态描
        PayStatusEnum.OrderStatus orderStatus = PayStatusEnum.OrderStatus.getByCode(order.getStatus());
        statusRes.setStatusDesc(orderStatus != null ? orderStatus.getDesc() : "未知状态");
        statusRes.setCreateTime(order.getCreateTime());
        statusRes.setPayTime(order.getPayTime());

        return SingleResponse.of(statusRes);
    }
} 
