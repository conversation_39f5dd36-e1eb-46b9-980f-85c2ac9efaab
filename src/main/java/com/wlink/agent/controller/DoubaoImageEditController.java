package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.DoubaoImageEditReq;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.service.DoubaoImageEditService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 豆包图像编辑控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/doubao/image-edit")
@Tag(name = "豆包图像编辑接口")
@RequiredArgsConstructor
public class DoubaoImageEditController {

    private final DoubaoImageEditService doubaoImageEditService;

    /**
     * 编辑图像
     *
     * @param req 编辑请求
     * @return 编辑结果
     */
    @PostMapping("/edit")
    @Operation(summary = "编辑图像", description = "使用豆包SeedEdit模型编辑图像")
    public SingleResponse<ImageGenerateRes> editImage(
            @Parameter(description = "图像编辑请求", required = true)
            @Valid @RequestBody DoubaoImageEditReq req) {
        
        log.info("接收到豆包图像编辑请求: shotId={}, prompt={}", req.getShotId(), req.getPrompt());

        try {
            ImageGenerateRes result = doubaoImageEditService.editImage(req);
            log.info("豆包图像编辑成功: shotId={}", req.getShotId());
            return SingleResponse.of(result);
        } catch (Exception e) {
            log.error("豆包图像编辑失败: shotId={}, error={}", req.getShotId(), e.getMessage(), e);
            return SingleResponse.buildFailure("IMAGE_EDIT_FAILED", e.getMessage());
        }
    }

    /**
     * 使用指定API密钥编辑图像
     *
     * @param req 编辑请求
     * @param apiKey API密钥
     * @return 编辑结果
     */
    @PostMapping("/edit-with-key")
    @Operation(summary = "使用指定API密钥编辑图像", description = "使用指定的API密钥调用豆包SeedEdit模型编辑图像")
    public SingleResponse<ImageGenerateRes> editImageWithApiKey(
            @Parameter(description = "图像编辑请求", required = true)
            @Valid @RequestBody DoubaoImageEditReq req,
            @Parameter(description = "API密钥", required = true)
            @RequestHeader("X-API-Key") String apiKey) {
        
        log.info("接收到豆包图像编辑请求(指定API密钥): shotId={}, prompt={}", req.getShotId(), req.getPrompt());

        try {
            ImageGenerateRes result = doubaoImageEditService.editImageWithApiKey(req, apiKey);
            log.info("豆包图像编辑成功(指定API密钥): shotId={}", req.getShotId());
            return SingleResponse.of(result);
        } catch (Exception e) {
            log.error("豆包图像编辑失败(指定API密钥): shotId={}, error={}", req.getShotId(), e.getMessage(), e);
            return SingleResponse.buildFailure("IMAGE_EDIT_FAILED", e.getMessage());
        }
    }
}
