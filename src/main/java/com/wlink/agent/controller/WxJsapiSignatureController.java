package com.wlink.agent.controller;

import jakarta.validation.Valid;

import com.alibaba.cola.dto.SingleResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wlink.agent.model.WxJsapiSignature;
import com.wlink.agent.model.WxJsapiSignatureRequest;
import com.wlink.agent.service.WxJsapiSignatureService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信JS-SDK签名接口
 */
@Slf4j
@RestController
@RequestMapping("/agent/wechat/jssdk")
@Tag(name = "微信JS-SDK接口")
public class WxJsapiSignatureController {

    @Autowired
    private WxJsapiSignatureService wxJsapiSignatureService;

    /**
     * 获取微信JS-SDK签名
     *
     * @param request 签名请求参数
     * @return 签名结果
     */
    @PostMapping("/signature")
    @Operation(summary = "获取微信JS-SDK签名")
    public SingleResponse<WxJsapiSignature> getSignature(@RequestBody @Valid WxJsapiSignatureRequest request) {
        log.info("Requesting WxJsapiSignature for URL: {}", request.getUrl());
        WxJsapiSignature signature = wxJsapiSignatureService.createJsapiSignature(request.getUrl());
        log.info("Generated signature: {}", signature);
        return SingleResponse.of(signature);
    }
} 
