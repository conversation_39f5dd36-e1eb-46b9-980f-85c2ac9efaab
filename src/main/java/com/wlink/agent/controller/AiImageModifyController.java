package com.wlink.agent.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.ImageConfirmReq;
import com.wlink.agent.model.req.ImageModifyReq;
import com.wlink.agent.model.req.ImageUploadReq;
import com.wlink.agent.model.res.ImageModifyRes;
import com.wlink.agent.model.res.ImageModifyResultRes;
import com.wlink.agent.model.res.ImageUploadRes;
import com.wlink.agent.model.res.ModifyImageUrlInfoRes;
import com.wlink.agent.service.AiImageModifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/agent/ai-image") //
@Tag(name = "图片修改")
@Validated
@RequiredArgsConstructor
public class AiImageModifyController {

    private final AiImageModifyService imageModifyService;

    /**
     * 提交图片修改请求
     * @param req 图片修改请求参数
     * @return 包含记录ID和操作编码的响应
     */
    @PostMapping("/modify") // 接口路径为 /agent/ai-image/modify/
    @Operation(summary = "提交图片修改请求", description = "接收图片修改参数，调用服务进行处理，并返回创建的记录ID和操作编码")
    public SingleResponse<ImageModifyRes> modifyImage( // 修改返回类型
                                                       @Parameter(description = "图片修改请求体", required = true) @Valid @RequestBody ImageModifyReq req) {
        ImageModifyRes result = imageModifyService.modifyImage(req); // 接收新的响应对象
        return SingleResponse.of(result);
    }

    /**
     * 根据 modifyCode 查询图片修改结果
     * @param modifyCode 修改操作的唯一编码
     * @return 图片修改结果详情
     */
    @GetMapping("/modify/result/{modifyCode}") // 定义 GET 接口和路径变    @Operation(summary = "查询图片修改结果", description = "根据 modifyCode 查询图片修改的处理状态和结果")
    public SingleResponse<ImageModifyResultRes> getModifyResult(
@Parameter(description = "Parameter")
            @PathVariable @NotBlank(message = "modifyCode 不能为空") String modifyCode) { // 使用 @PathVariable 获取路径变量
        log.info("Controller: Received query request for modifyCode: {}", modifyCode);
        ImageModifyResultRes result = imageModifyService.getModifyResultByCode(modifyCode);
        log.info("Controller: Found image modification result for modifyCode: {}", modifyCode);
        return SingleResponse.of(result);
    }

    /**
     * 根据会话ID、一级ID和二级ID查询源图片URL列表
     *
     * @param conversationId 会话ID
     * @param primaryId 一级ID
     * @param secondaryId 二级ID (可
     * @return 包含源图片URL和修改编码的对象列表
     */
    @GetMapping("/modify/source-images") // Maps to /agent/ai-image/source-images
@Operation(summary = "鑾峰彇Source Image Urls")
    public SingleResponse<ModifyImageUrlInfoRes> getSourceImageUrls( // Update return type
                                                                    @Parameter(description = "会话ID", required = true) @RequestParam String conversationId,
                                                                    @Parameter(description = "一级ID", required = true) @RequestParam String primaryId,
                                                                    @Parameter(description = "二级ID", required = false) @RequestParam(required = false) String secondaryId) {

        log.info("Controller: Received request for source image infos with sessionId: {}, primaryId: {}, secondaryId: {}", conversationId, primaryId, secondaryId);
        ModifyImageUrlInfoRes sourceImageUrlsBySessionAndIds = imageModifyService.getSourceImageUrlsBySessionAndIds(conversationId, primaryId, secondaryId);
        return SingleResponse.of(sourceImageUrlsBySessionAndIds); // Return the list of DTOs
    }

    /**
     * 确认图片修改结果并将新图片更新到对应的内容记录
     *
     * @param req 包含 modifyCode 的请求体
     * @return 通用响应
     */
    @PostMapping("/confirm")
    @Operation(summary = "确认图片修改", description = "根据 modifyCode 确认图片修改结果，并将新图片更新到关联的内容记录中")
    public Response confirmImageModification(
            @Parameter(description = "图片确认请求体", required = true) @Valid @RequestBody ImageConfirmReq req) {
        log.info("Controller: Received image confirmation request for modifyCode: {}", req.getModifyCode());
        imageModifyService.confirmImageModification(req.getModifyCode()); // Call the service method
        log.info("Controller: Image modification confirmed for modifyCode: {}", req.getModifyCode());
        return Response.buildSuccess(); // Return a successful response
    }
    /**
     * 上传图片
     * @param req 图片上传请求参数
     * @return 上传结果
     */
    @PostMapping("/upload")
    @Operation(summary = "上传图片", description = "上传图片并存储到ai_image_modify_record表")
    public SingleResponse<ImageUploadRes> uploadImage(@Valid @RequestBody ImageUploadReq req) {
        log.info("Controller: Received image upload request for session: {}, contentId: {}", req.getConversationId(), req.getContentId());
        ImageUploadRes result = imageModifyService.uploadImage(req);
        log.info("Controller: Image upload processed successfully, modifyCode: {}", result.getModifyCode());
        return SingleResponse.of(result);
    }

}
