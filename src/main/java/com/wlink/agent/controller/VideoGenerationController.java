package com.wlink.agent.controller;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.client.MiniMaxFileDownloadClient;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import com.wlink.agent.exception.VideoGenerationCallbackException;
import com.wlink.agent.model.req.MiniMaxVideoCallbackReq;
import com.wlink.agent.model.req.VideoGenerationCallbackReq;
import com.wlink.agent.service.MiniMaxVideoCallbackService;
import com.wlink.agent.service.VideoGenerationQueueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

/**
 * 视频生成控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent/video")
@Tag(name = "视频生成接口")
public class VideoGenerationController {

    private final VideoGenerationQueueService queueService;
    private final MiniMaxVideoCallbackService miniMaxCallbackService;
    private final MiniMaxFileDownloadClient miniMaxFileDownloadClient;
    private final AiVideoGenerationRecordMapper videoGenerationRecordMapper;

    /**
     * 视频生成回调接口
     * 
     * @param req 回调请求
     * @return 处理结果
     */
    @PostMapping("/callback")
    @Operation(summary = "视频生成回调接口")
    public ResponseEntity<Boolean> videoGenerationCallback(@Valid @RequestBody VideoGenerationCallbackReq req) {
        log.info("收到视频生成回调: taskId={}, status={}", req.getId(), req.getStatus());
        
        try {
            // 验证外部任务ID是否存在
            AiVideoGenerationPo task = queueService.findTaskByExternalId(req.getId());
            if (task == null) {
                log.error("未找到对应的内部任务: externalId={}", req.getId());
                throw new VideoGenerationCallbackException("未找到对应的内部任务: " + req.getId());
            }
            
            // 异步更新ai_video_generation_record表
            String videoUrl = req.getContent() != null ? req.getContent().getVideoUrl() : null;
            Integer completionTokens = req.getUsage() != null ? req.getUsage().getCompletionTokens() : null;
            Integer totalTokens = req.getUsage() != null ? req.getUsage().getTotalTokens() : null;
            String errorMessage = "failed".equals(req.getStatus()) ? "视频生成失败，状态: " + req.getStatus() : null;
            
            queueService.asyncUpdateVideoGenerationRecord(
                req.getId(),                  // 外部任务ID
                req.getStatus(),             // 任务状态
                videoUrl,                    // 视频URL
                errorMessage,                // 错误信息
                completionTokens,            // 完成token数
                totalTokens                  // 总token数
            );
            
            Long internalTaskId = task.getId();
            log.info("找到对应的内部任务: externalId={}, internalId={}", req.getId(), internalTaskId);
            // 判断状态是否为成功
            if ("succeeded".equals(req.getStatus())) {
                // 获取视频URL
                if (videoUrl != null) {
                    // 更新任务状态为完成
                    queueService.completeTask(internalTaskId, videoUrl);
                    return ResponseEntity.ok(true);
                } else {
                    log.error("视频生成回调缺少视频URL: taskId={}", req.getId());
                    throw new VideoGenerationCallbackException("视频生成回调缺少视频URL: " + req.getId());
                }
            } else if ("failed".equals(req.getStatus())){
                // 更新任务状态为失败
                String errorMsg = "视频生成失败，状态: " + req.getStatus();
                queueService.failTask(internalTaskId, errorMsg);
                return ResponseEntity.ok(false);
            }
            return ResponseEntity.ok(true);
        } catch (Exception e) {
            log.error("处理视频生成回调异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    /**
     * MiniMax视频生成回调接口
     *
     * @param req 回调请求
     * @return 处理结果
     */
    @PostMapping("/minimax/callback")
    @Operation(summary = "MiniMax视频生成回调接口")
    public ResponseEntity<String> miniMaxVideoGenerationCallback(@Valid @RequestBody MiniMaxVideoCallbackReq req) {
        log.info("收到MiniMax视频生成回调: {}", JSON.toJSONString(req));
        try {
            String challenge = req.getChallenge();
            if (StringUtils.isNotBlank(challenge)) {
                log.info("收到MiniMax视频生成回调: challenge={}", challenge);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("challenge", challenge);
                return ResponseEntity.ok(jsonObject.toJSONString());
            }
            boolean success = miniMaxCallbackService.handleCallback(req);
            if (success) {
                log.info("MiniMax视频生成回调处理成功: taskId={}", req.getTaskId());
                return ResponseEntity.ok("success");
            } else {
                log.warn("MiniMax视频生成回调处理失败: taskId={}", req.getTaskId());
                return ResponseEntity.ok("failed");
            }
        } catch (Exception e) {
            log.error("处理MiniMax视频生成回调异常: taskId={}", req.getTaskId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("error");
        }
    }

    /**
     * 下载MiniMax生成的视频文件
     *
     * @param taskId 任务ID
     * @param fileId 文件ID
     * @return 视频文件流
     */
    @GetMapping("/minimax/download")
    @Operation(summary = "下载MiniMax生成的视频文件")
    public CompletableFuture<ResponseEntity<InputStreamResource>> downloadMiniMaxVideo(
            @Parameter(description = "任务ID", required = true)
            @RequestParam String taskId,
            @Parameter(description = "文件ID", required = true)
            @RequestParam String fileId) {

        log.info("开始下载MiniMax视频文件: taskId={}, fileId={}", taskId, fileId);

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 验证任务ID是否存在
                AiVideoGenerationRecordPo record = findVideoGenerationRecord(taskId);
                if (record == null) {
                    log.error("未找到对应的视频生成记录: taskId={}", taskId);
                    return ResponseEntity.notFound().build();
                }


                // 3. 调用MiniMaxFileDownloadClient下载文件
                InputStream inputStream = miniMaxFileDownloadClient.downloadFile(fileId).get();

                // 4. 构建响应
                InputStreamResource resource = new InputStreamResource(inputStream);

                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"video_" + taskId + ".mp4\"");
                headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

                log.info("MiniMax视频文件下载成功: taskId={}, fileId={}", taskId, fileId);

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(resource);

            } catch (Exception e) {
                log.error("下载MiniMax视频文件失败: taskId={}, fileId={}", taskId, fileId, e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        });
    }

    /**
     * 下载MiniMax生成的视频文件（字节数组方式）
     *
     * @param taskId 任务ID
     * @param fileId 文件ID
     * @return 视频文件字节数组
     */
    @GetMapping("/minimax/download/bytes")
    @Operation(summary = "下载MiniMax生成的视频文件（字节数组）")
    public CompletableFuture<ResponseEntity<byte[]>> downloadMiniMaxVideoAsBytes(
            @Parameter(description = "任务ID", required = true)
            @RequestParam String taskId,
            @Parameter(description = "文件ID", required = true)
            @RequestParam String fileId) {

        log.info("开始下载MiniMax视频文件（字节数组）: taskId={}, fileId={}", taskId, fileId);

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 验证任务ID是否存在
                AiVideoGenerationRecordPo record = findVideoGenerationRecord(taskId);
                if (record == null) {
                    log.error("未找到对应的视频生成记录: taskId={}", taskId);
                    return ResponseEntity.notFound().build();
                }

                // 2. 获取API密钥
                String apiKey = getApiKeyForRecord(record);
                if (StringUtils.isBlank(apiKey)) {
                    log.error("无法获取API密钥: taskId={}", taskId);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
                }

                // 3. 调用MiniMaxFileDownloadClient下载文件
                byte[] videoBytes = miniMaxFileDownloadClient.downloadFileAsBytes(fileId, apiKey).get();

                // 4. 构建响应
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"video_" + taskId + ".mp4\"");
                headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
                headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(videoBytes.length));

                log.info("MiniMax视频文件下载成功（字节数组）: taskId={}, fileId={}, size={}bytes",
                        taskId, fileId, videoBytes.length);

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(videoBytes);

            } catch (Exception e) {
                log.error("下载MiniMax视频文件失败（字节数组）: taskId={}, fileId={}", taskId, fileId, e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        });
    }

    /**
     * 查找视频生成记录
     *
     * @param taskId 任务ID
     * @return 视频生成记录
     */
    private AiVideoGenerationRecordPo findVideoGenerationRecord(String taskId) {
        try {
            return videoGenerationRecordMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<AiVideoGenerationRecordPo>()
                            .eq(AiVideoGenerationRecordPo::getTaskId, taskId)
                            .orderByDesc(AiVideoGenerationRecordPo::getCreateTime)
                            .last("LIMIT 1")
            );
        } catch (Exception e) {
            log.error("查询视频生成记录失败: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 获取记录对应的API密钥
     * 这里需要根据实际业务逻辑来实现
     *
     * @param record 视频生成记录
     * @return API密钥
     */
    private String getApiKeyForRecord(AiVideoGenerationRecordPo record) {
        // TODO: 根据实际业务逻辑获取API密钥
        // 可能需要根据用户ID、模型类型等信息来获取对应的API密钥
        // 这里暂时返回null，需要根据实际情况实现

        // 示例实现：
        // if ("MiniMax-Hailuo-02".equals(record.getModel())) {
        //     return miniMaxApiKeyService.getApiKeyByUserId(record.getUserId());
        // }

        log.warn("需要实现getApiKeyForRecord方法来获取API密钥: taskId={}, model={}",
                record.getTaskId(), record.getModel());
        return null;
    }
}
