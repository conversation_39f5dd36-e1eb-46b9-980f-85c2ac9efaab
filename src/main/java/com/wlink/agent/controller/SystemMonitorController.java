package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.service.impl.DatabaseConcurrencyControlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统监控控制器
 * 提供系统状态查询和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/system/monitor")
@Tag(name = "系统监控", description = "系统状态监控和管理接口")
public class SystemMonitorController {

    @Autowired
    private DatabaseConcurrencyControlService concurrencyControlService;

    /**
     * 获取系统并发状态
     */
    @GetMapping("/concurrency-status")
    @Operation(summary = "获取系统并发状态", description = "查看当前系统的并发处理状态")
    public SingleResponse<DatabaseConcurrencyControlService.ConcurrencyStatus> getConcurrencyStatus() {
        try {
            DatabaseConcurrencyControlService.ConcurrencyStatus status = 
                concurrencyControlService.getStatus();
            
            log.info("查询系统并发状态: {}", status);
            return SingleResponse.of(status);
            
        } catch (Exception e) {
            log.error("获取系统并发状态失败", e);
            return SingleResponse.buildFailure("SYSTEM_ERROR", "获取系统状态失败: " + e.getMessage());
        }
    }

    /**
     * 手动清理超时任务
     */
    @PostMapping("/cleanup-timeout-tasks")
    @Operation(summary = "手动清理超时任务", description = "立即执行超时任务清理操作")
    public SingleResponse<String> cleanupTimeoutTasks() {
        try {
            log.info("手动触发超时任务清理");
            
            // 获取清理前状态
            DatabaseConcurrencyControlService.ConcurrencyStatus beforeStatus = 
                concurrencyControlService.getStatus();
            
            // 执行清理
            int cleanedCount = concurrencyControlService.cleanupTimeoutTasks();
            
            // 获取清理后状态
            DatabaseConcurrencyControlService.ConcurrencyStatus afterStatus = 
                concurrencyControlService.getStatus();
            
            String result = String.format(
                "清理完成！清理了 %d 个超时任务。清理前: %s，清理后: %s", 
                cleanedCount, beforeStatus, afterStatus
            );
            
            log.info("手动清理超时任务完成: {}", result);
            return SingleResponse.of(result);
            
        } catch (Exception e) {
            log.error("手动清理超时任务失败", e);
            return SingleResponse.buildFailure("CLEANUP_ERROR", "清理超时任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取系统健康状态", description = "检查系统是否正常运行")
    public SingleResponse<SystemHealthInfo> getSystemHealth() {
        try {
            DatabaseConcurrencyControlService.ConcurrencyStatus status = 
                concurrencyControlService.getStatus();
            
            SystemHealthInfo healthInfo = new SystemHealthInfo();
            healthInfo.setStatus("HEALTHY");
            healthInfo.setMaxConcurrent(status.getMaxConcurrent());
            healthInfo.setCurrentProcessing(status.getCurrentProcessing());
            healthInfo.setAvailable(status.getAvailable());
            healthInfo.setPendingTasks(status.getPendingTasks());
            
            // 检查是否有异常情况
            if (status.getCurrentProcessing() == status.getMaxConcurrent() && status.getPendingTasks() > 50) {
                healthInfo.setStatus("WARNING");
                healthInfo.setMessage("系统可能存在任务堵塞，建议检查");
            } else if (status.getPendingTasks() > 200) {
                healthInfo.setStatus("WARNING");
                healthInfo.setMessage("待处理任务数量较多，建议关注系统负载");
            } else {
                healthInfo.setMessage("系统运行正常");
            }
            
            return SingleResponse.of(healthInfo);
            
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            
            SystemHealthInfo errorInfo = new SystemHealthInfo();
            errorInfo.setStatus("ERROR");
            errorInfo.setMessage("系统状态检查失败: " + e.getMessage());
            
            return SingleResponse.of(errorInfo);
        }
    }

    /**
     * 系统健康信息
     */
    public static class SystemHealthInfo {
        private String status;
        private String message;
        private int maxConcurrent;
        private int currentProcessing;
        private int available;
        private int pendingTasks;

        // Getters and Setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getMaxConcurrent() { return maxConcurrent; }
        public void setMaxConcurrent(int maxConcurrent) { this.maxConcurrent = maxConcurrent; }
        
        public int getCurrentProcessing() { return currentProcessing; }
        public void setCurrentProcessing(int currentProcessing) { this.currentProcessing = currentProcessing; }
        
        public int getAvailable() { return available; }
        public void setAvailable(int available) { this.available = available; }
        
        public int getPendingTasks() { return pendingTasks; }
        public void setPendingTasks(int pendingTasks) { this.pendingTasks = pendingTasks; }
    }
}
