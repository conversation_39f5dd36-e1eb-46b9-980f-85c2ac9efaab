package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.UniversalImageGenerationRequest;
import com.wlink.agent.model.res.UniversalImageGenerationResponse;
import com.wlink.agent.model.res.UserResourceStatusResponse;
import com.wlink.agent.service.UniversalImageGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通用图片生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/universal/image")
@RequiredArgsConstructor
@Tag(name = "通用图片生成", description = "通用图片生成相关接口")
public class UniversalImageGenerationController {

    private final UniversalImageGenerationService universalImageGenerationService;

    /**
     * 生成图片
     */
    @PostMapping("/generate")
    @Operation(summary = "生成图片", description = "根据参数选择合适的模型和方法生成图片")
    public SingleResponse<UniversalImageGenerationResponse> generateImage(
            @Parameter(description = "图片生成请求参数", required = true)
            @Valid @RequestBody UniversalImageGenerationRequest request) {

        log.info("接收到通用图片生成请求: modelType={}, prompt={}, imageUrls={}",
                request.getModelType(), request.getPrompt(), request.getImageUrls());

        try {
            UniversalImageGenerationResponse response = universalImageGenerationService.generateImage(request);
            log.info("图片生成任务提交成功，资源编码: {}", response.getCode());
            return SingleResponse.of(response);
        } catch (Exception e) {
            log.error("图片生成任务提交失败: {}", e.getMessage(), e);
            return SingleResponse.buildFailure("GENERATION_FAILED", e.getMessage());
        }
    }

    /**
     * 查询资源状态
     */
    @GetMapping("/status/{code}")
    @Operation(summary = "查询资源状态", description = "根据资源编码查询生成状态和结果")
    public SingleResponse<UserResourceStatusResponse> getResourceStatus(
            @Parameter(description = "资源编码", required = true)
            @PathVariable String code) {

        log.info("查询资源状态，编码: {}", code);
        UserResourceStatusResponse response = universalImageGenerationService.getResourceStatus(code);
        return SingleResponse.of(response);

    }

    /**
     * 批量查询资源状态
     */
    @PostMapping("/status/batch")
    @Operation(summary = "批量查询资源状态", description = "根据资源编码列表批量查询生成状态和结果")
    public MultiResponse<UserResourceStatusResponse> getResourceStatusBatch(
            @Parameter(description = "资源编码列表", required = true)
            @RequestBody List<String> codes) {

        log.info("批量查询资源状态，编码数量: {}", codes.size());
        List<UserResourceStatusResponse> responses = universalImageGenerationService.getResourceStatusBatch(codes);
        return MultiResponse.of(responses);

    }

    /**
     * 查询用户资源列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "查询用户资源列表", description = "查询指定用户的资源列表")
    public SingleResponse<List<UserResourceStatusResponse>> getUserResources(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId,
            @Parameter(description = "资源类型(1-图片,2-视频,3-音频)")
            @RequestParam(required = false) Integer resourceType,
            @Parameter(description = "限制数量，默认20")
            @RequestParam(defaultValue = "20") Integer limit) {

        log.info("查询用户资源列表，用户ID: {}, 资源类型: {}, 限制数量: {}", userId, resourceType, limit);


        List<UserResourceStatusResponse> responses = universalImageGenerationService.getUserResources(userId, resourceType, limit);
        return SingleResponse.of(responses);

    }
}
