package com.wlink.agent.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.client.VolcengineImageApiClient;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.client.model.volcengine.VolcengineInpaintingEditRequest;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditRequest;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.service.ImageTaskQueueService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



@Slf4j
@RestController
@RequestMapping("/agent/image/volcengine")
@Tag(name = "火山引擎图片服务接口", description = "提供火山引擎图片生成、编辑等功能")
@Validated
@RequiredArgsConstructor
public class VolcengineImageController {

    private final VolcengineImageApiClient volcengineImageApiClient;
    private final ImageTaskQueueService imageTaskQueueService;

    @PostMapping("/generate")
    @Operation(summary = "文生图", description = "根据文本描述生成图片，使用队列系统控制并发")
    public Response textGenerateImage(
            @Parameter(description = "文生图请求参数", required = true) @RequestBody @Valid VolcengineImageRequest request) {
        log.info("Received text-to-image generation request for session: {}", request.getConversationId());
        imageTaskQueueService.queueGenerateTask(request.getConversationId(), request, TaskType.GENERATE.getValue());
        return SingleResponse.buildSuccess();

    }

    @PostMapping("/edit")
    @Operation(summary = "图生图（Seed编辑）", description = "根据输入图片和文本描述编辑图片")
    public SingleResponse<ImageGenerateRes> imageEdit(
            @Parameter(description = "图生图（Seed编辑）请求参数", required = true) @RequestBody @Valid VolcengineSeedEditRequest request) {
        return SingleResponse.of(volcengineImageApiClient.seedEditImage(request));
    }

    @PostMapping("/retain")
    @Operation(summary = "人像IP保留", description = "根据输入的人像图片和文本描述，生成保留人像特征的新图片，使用队列系统控制并发")
    public Response imageUnanimous(
            @Parameter(description = "人像IP保留请求参数", required = true) @RequestBody @Valid VolcengineCharacterRetentionRequest request) {
        log.info("Received character retention request for session: {}", request.getConversationId());
        imageTaskQueueService.queueRetainTask(request.getConversationId(), request);
        return SingleResponse.buildSuccess();

    }

    @PostMapping("/painting")
    @Operation(summary = "涂抹编辑", description = "根据输入图片和涂抹掩码以及文本描述，生成编辑后的图片")
    public SingleResponse<ImageGenerateRes> paintingEdit(
            @Parameter(description = "涂抹编辑请求参数", required = true) @RequestBody @Valid VolcengineInpaintingEditRequest request) {
        return SingleResponse.of(volcengineImageApiClient.paintingEditImage(request));
    }
}