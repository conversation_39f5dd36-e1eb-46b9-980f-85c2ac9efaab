package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.wlink.agent.config.OSSConfig;
import com.wlink.agent.model.res.StsCredentialsRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "STS接口", description = "提供STS相关功能")
@Slf4j
@RestController
@RequestMapping("/agent/sts/oss")
public class StsController {

    private static final Logger logger = LoggerFactory.getLogger(StsController.class);

    @Value("${aliyun.sts.endpoint:sts.cn-shanghai.aliyuncs.com}")
    private String endpoint;

    @Value("${aliyun.accessKeyId:LTAI5t65BFnZzFTEzwDTDf6G}")
    private String accessKeyId;

    @Value("${aliyun.accessKeySecret:******************************}")
    private String accessKeySecret;

    @Value("${aliyun.roleArn:acs:ram::1308734115947803:role/sts}")
    private String roleArn;

    @Value("${aliyun.sts.roleSessionName:OssSession}") // Default value if not set
    private String roleSessionName;

    @Value("${aliyun.sts.durationSeconds:3600}") // Default value if not set
    private Long durationSeconds;

    @Value("${spring.profiles.active}")
    String env;

    @Resource
    OSSConfig ossConfig;
    @Operation(summary = "获取OSS的STS令牌", description = "根据配置生成OSS的STS令牌")
    @GetMapping("/token")
    public SingleResponse<StsCredentialsRes> getOssStsToken() {
        String policy = "{\"Version\": \"1\", \"Statement\": [{\"Action\": [\"oss:PutObject\"], \"Resource\": [\"acs:oss:*:*:wlpaas/*\"], \"Effect\": \"Allow\"}]}";
        String regionId = "oss-cn-shanghai"; // Default region
        // Add endpoint (assuming Java SDK 3.12.0+)
        DefaultProfile.addEndpoint(regionId, "Sts", endpoint);

        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);

        final AssumeRoleRequest request = new AssumeRoleRequest();
        //request.setSysMethod(MethodType.POST); // Use SysMethod for SDK 3.12.0+
        request.setRoleArn(roleArn);
        request.setRoleSessionName(roleSessionName);
        request.setPolicy(policy);
        request.setDurationSeconds(durationSeconds);

        final AssumeRoleResponse response;
        try {
            response = client.getAcsResponse(request);
        } catch (ClientException e) {
            logger.error("Error generating STS token: {}", e.getMessage());
            throw new RuntimeException(e);
        }
        StsCredentialsRes stsCredentialsRes = new StsCredentialsRes();
        stsCredentialsRes.setAccessKeyId(response.getCredentials().getAccessKeyId());
        stsCredentialsRes.setAccessKeySecret(response.getCredentials().getAccessKeySecret());
        stsCredentialsRes.setSecurityToken(response.getCredentials().getSecurityToken());
        stsCredentialsRes.setExpiration(String.valueOf(durationSeconds));
        stsCredentialsRes.setRequestId(response.getRequestId());
        stsCredentialsRes.setBucketName("wlpaas");
        stsCredentialsRes.setEnv(env);
        logger.info("Successfully generated STS token. RequestId: {}", response.getRequestId());
        return SingleResponse.of(stsCredentialsRes);
    }
}