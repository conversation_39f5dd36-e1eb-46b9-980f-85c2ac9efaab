package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.model.req.ShotImageEditReq;
import com.wlink.agent.model.res.ShotImageEditRes;
import com.wlink.agent.service.ShotImageEditService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分镜图片编辑控制器
 */
@Slf4j
@RestController
@RequestMapping("/agent/shot-image-edit")
@Tag(name = "分镜图片编辑接口")
@RequiredArgsConstructor
public class ShotImageEditController {

    private final ShotImageEditService shotImageEditService;

    /**
     * 提交分镜图片编辑任务
     *
     * @param req 编辑请求
     * @return 编辑响应
     */
    @PostMapping("/submit")
    @Operation(summary = "提交分镜图片编辑任务", description = "提交分镜图片编辑任务到 ComfyUI 进行处理")
    public SingleResponse<ShotImageEditRes> submitImageEdit(@Valid @RequestBody ShotImageEditReq req) {
        log.info("提交分镜图片编辑任务: shotId={}, prompt={}", req.getShotId(), req.getPrompt());

        ShotImageEditRes result = shotImageEditService.submitImageEdit(req);
        return SingleResponse.of(result);
    }

    /**
     * 根据任务ID查询编辑记录
     *
     * @param taskId 任务ID
     * @return 编辑记录
     */
    @GetMapping("/task/{taskId}")
    @Operation(summary = "根据任务ID查询编辑记录", description = "根据 ComfyUI 任务ID查询编辑记录详情")
    public SingleResponse<ShotImageEditRes> getEditRecordByTaskId(
            @Parameter(description = "任务ID", required = true) @PathVariable String taskId) {
        log.info("根据任务ID查询编辑记录: taskId={}", taskId);

        ShotImageEditRes result = shotImageEditService.getEditRecordByTaskId(taskId);
        return SingleResponse.of(result);
    }

    /**
     * 根据分镜ID查询编辑记录列表
     *
     * @param shotId 分镜ID
     * @return 编辑记录列表
     */
    @GetMapping("/shot/{shotId}")
    @Operation(summary = "根据分镜ID查询编辑记录列表", description = "根据分镜ID查询该分镜的所有编辑记录")
    public MultiResponse<ShotImageEditRes> getEditRecordsByShotId(
            @Parameter(description = "分镜ID", required = true) @PathVariable Long shotId) {
        log.info("根据分镜ID查询编辑记录列表: shotId={}", shotId);

        List<ShotImageEditRes> result = shotImageEditService.getEditRecordsByShotId(shotId);
        return MultiResponse.of(result);
    }
}
