package com.wlink.agent.controller;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.UserImageQueryReq;
import com.wlink.agent.model.req.UserVideoQueryReq;
import com.wlink.agent.model.res.UserImageRecordRes;
import com.wlink.agent.model.res.UserVideoRecordRes;
import com.wlink.agent.service.UserResourceRecordService;
import com.wlink.agent.utils.UserContext;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 用户图片记录查询控制
 */
@Slf4j
@RestController
@RequestMapping("/agent/user")
@Tag(name = "用户资源记录查询")
public class UserResourceRecordController {

    @Resource
    private UserResourceRecordService userImageRecordService;

    /**
     * 分页查询用户图片记录
     *
     * @param req 查询请求（包含可选的sessionId参数）
     * @return 分页结果
     */
    @PostMapping("/image/query")
    @Operation(summary = "查询用户图片记录", description = "分页查询用户图片记录，支持按类型和会话ID过滤")
    public PageResponse<UserImageRecordRes> queryUserImageRecords(
            @Parameter(description = "查询请求参数", required = true) @Valid @RequestBody UserImageQueryReq req) {
        
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        
        // 使用当前登录用户ID，忽略请求中的userId
        req.setUserId(userInfo.getUserId());
        
        log.info("用户 {} 请求查询图片记录，类 {}", userInfo.getUserId(), req.getType());
        return userImageRecordService.queryUserImageRecords(req);
    }

    /**
     * 分页查询用户视频记录
     *
     * @param req 查询请求
     * @return 分页结果
     */
    @PostMapping("/video/query")
    @Operation(summary = "查询用户视频记录", description = "分页查询用户视频记录，仅查询状态为2的数据")
    public PageResponse<UserVideoRecordRes> queryUserVideoRecords(
            @Parameter(description = "查询请求参数", required = true) @Valid @RequestBody UserVideoQueryReq req) {
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || userInfo.getUserId() == null) {
            throw new BizException("用户信息不存在");
        }
        req.setUserId(userInfo.getUserId());
        log.info("用户 {} 请求查询视频记录", userInfo.getUserId());
        return userImageRecordService.queryUserVideoRecords(req);
    }
} 
