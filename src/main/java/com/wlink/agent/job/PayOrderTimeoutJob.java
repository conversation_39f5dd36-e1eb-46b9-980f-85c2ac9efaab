package com.wlink.agent.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wlink.agent.dao.mapper.PayOrderMapper;
import com.wlink.agent.dao.po.PayOrderPo;
import com.wlink.agent.enums.PayStatusEnum;
import com.wlink.agent.service.PayService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 支付订单超时关闭定时任务
 * 定时扫描支付中状态的订单，超过两分钟未支付的关闭订单
 */
@Slf4j
@Component
public class PayOrderTimeoutJob {

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Autowired
    private PayService payService;

    /**
     * 扫描支付中状态的订单，关闭超时订单
     * 每1分钟执行一次
     */
    @XxlJob("payOrderTimeoutJobHandler")
    public ReturnT<String> execute(String param) {
        log.info("开始执行支付订单超时关闭定时任务");
        try {
            // 计算超时时间点（当前时间减去2分钟）
            Date timeoutTime = new Date(System.currentTimeMillis() - 3 * 60 * 1000);

            // 查询状态为"支付中"且创建时间超过2分钟的订单
            LambdaQueryWrapper<PayOrderPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PayOrderPo::getStatus, PayStatusEnum.OrderStatus.PAYING.getCode())
                    .lt(PayOrderPo::getUpdateTime, timeoutTime)
                    .orderByAsc(PayOrderPo::getCreateTime)
                    .last("limit 50"); // 每次最多处理50条，避免一次处理太多

            List<PayOrderPo> timeoutOrders = payOrderMapper.selectList(queryWrapper);

            if (timeoutOrders.isEmpty()) {
                log.info("没有需要关闭的超时订单");
                return ReturnT.SUCCESS;
            }

            log.info("发现{}个超时未支付订单，准备关闭", timeoutOrders.size());

            // 逐个关闭超时订单
            int successCount = 0;
            int failCount = 0;
            for (PayOrderPo order : timeoutOrders) {
                try {
                    Transaction transaction = payService.queryOrderFromWechat(order.getOrderNo());
                    String tradeState = transaction.getTradeState().name();
                    if ("SUCCESS".equals(tradeState)) {
                        log.info("订单已支付: orderNo={}, 创建时间={}",
                                order.getOrderNo(), order.getCreateTime());
                        payService.updateOrderStatusByTransaction(transaction);
                        continue;
                    } else if ("CLOSED".equals(tradeState)) {
                        log.info("订单已关闭: orderNo={}, 创建时间={}",
                                order.getOrderNo(), order.getCreateTime());
                        continue;
                    }
                    log.info("关闭超时订单: orderNo={}, 创建时间={}",
                            order.getOrderNo(), order.getCreateTime());
                    payService.closeOrder(order.getOrderNo());
                    successCount++;
                } catch (Exception e) {
                    log.error("关闭订单失败: orderNo={}, error={}",
                            order.getOrderNo(), e.getMessage(), e);
                    failCount++;
                }
            }
            log.info("超时订单关闭完成，成功: {}，失败: {}", successCount, failCount);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("支付订单超时关闭定时任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }
} 