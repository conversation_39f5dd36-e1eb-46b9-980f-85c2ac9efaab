package com.wlink.agent.job;

import com.alibaba.cola.exception.BizException;
import com.wlink.agent.dao.mapper.AiPayRefundMapper;
import com.wlink.agent.dao.po.AiPayRefundPo;
import com.wlink.agent.enums.RefundStatusEnum;
import com.wlink.agent.service.PayService;
import com.wlink.agent.service.RefundService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 支付退款处理定时任务
 * 定时扫描处理中状态的退款记录，调用退款接口进行处理
 */
@Slf4j
@Component
public class PayRefundJob {

    @Autowired
    private AiPayRefundMapper aiPayRefundMapper;

    @Autowired
    private PayService payService;

    @Autowired
    private RefundService refundService;

    /**
     * 处理待退款的记录
     * 每5分钟执行一次
     */
    @XxlJob("payRefundJobHandler")
    public ReturnT<String> execute(String param) {
        log.info("开始执行支付退款处理定时任务");
        try {
            // 查询状态为"处理中"的退款记录，每次最多处理10条
            List<AiPayRefundPo> pendingRefunds = aiPayRefundMapper.selectPendingRefunds(RefundStatusEnum.Status.PROCESSING.getCode(), 10);

            if (pendingRefunds.isEmpty()) {
                log.info("没有需要处理的退款记录");
                return ReturnT.SUCCESS;
            }

            log.info("发现{}个待处理退款记录，准备处理", pendingRefunds.size());

            // 逐个处理退款记录
            int successCount = 0;
            int failCount = 0;

            for (AiPayRefundPo refund : pendingRefunds) {
                try {
                    log.info("处理退款记录: refundNo={}, orderNo={}, amount={}", 
                            refund.getRefundNo(), refund.getOrderNo(), refund.getAmount());
                    // 调用退款服务
                    payService.refund(refund.getOrderNo(), refund.getReason(),  refund.getDeductPoints());
                    
                    // 更新退款记录状态为成功
                    refund.setStatus(RefundStatusEnum.Status.SUCCESS.getCode());
                    refund.setFinishTime(new Date());
                    refund.setUpdateTime(new Date());
                    aiPayRefundMapper.updateById(refund);
                    
                    log.info("退款处理成功: refundNo={}", refund.getRefundNo());
                    successCount++;
                } catch (Exception e) {
                    log.error("退款处理失败: refundNo={}, error={}", refund.getRefundNo(), e.getMessage(), e);
                    // 更新退款记录状态为失败
                    refund.setStatus(RefundStatusEnum.Status.FAILURE.getCode());
                    refund.setRemark("退款处理失败: " + e.getMessage());
                    refund.setUpdateTime(new Date());
                    aiPayRefundMapper.updateById(refund);
                    failCount++;
                }
            }
            log.info("退款处理完成，成功: {}，失败: {}", successCount, failCount);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("退款处理定时任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }
} 