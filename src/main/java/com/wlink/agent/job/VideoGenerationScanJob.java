package com.wlink.agent.job;

import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.service.impl.VideoGenerationQueueServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 视频生成任务扫描定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoGenerationScanJob {

    private final AiVideoGenerationMapper videoGenerationMapper;
    private final VideoGenerationQueueServiceImpl videoGenerationQueueService;
    
    /**
     * 最大并发处理数
     */
    private static final int MAX_CONCURRENT_PROCESSING = 5;

    /**
     * 定时扫描视频生成任务
     * 每30秒执行一次
     */
    @XxlJob("videoGenerationScanJobHandler")
    public ReturnT<String> scanVideoGenerationTasks(String param) {
        log.info("开始扫描视频生成任务...");
        XxlJobHelper.log("开始扫描视频生成任务...");
        
        try {
            // 查询当前处理中的任务数量
            int processingCount = videoGenerationMapper.getProcessingCount();
            log.info("当前处理中的视频生成任务数量: {}", processingCount);
            XxlJobHelper.log("当前处理中的视频生成任务数量: {}", processingCount);
            
            // 如果处理中的任务数量已达到上限，则不处理新任务
            if (processingCount >= MAX_CONCURRENT_PROCESSING) {
                log.info("处理中的任务数量已达到上限({}), 不处理新任务", MAX_CONCURRENT_PROCESSING);
                XxlJobHelper.log("处理中的任务数量已达到上限({}), 不处理新任务", MAX_CONCURRENT_PROCESSING);
                return ReturnT.SUCCESS;
            }
            
            // 计算可以处理的新任务数量
            int availableSlots = MAX_CONCURRENT_PROCESSING - processingCount;
            log.info("可以处理{}个新任务", availableSlots);
            XxlJobHelper.log("可以处理{}个新任务", availableSlots);
            
            // 获取排队中的任务
            List<AiVideoGenerationPo> queuedTasks = videoGenerationMapper.getQueuedTasks(availableSlots);
            log.info("获取到{}个排队中的任务", queuedTasks.size());
            XxlJobHelper.log("获取到{}个排队中的任务", queuedTasks.size());
            
            // 处理任务
            for (AiVideoGenerationPo task : queuedTasks) {
                log.info("开始处理排队中的视频生成任务: taskId={}", task.getId());
                XxlJobHelper.log("开始处理排队中的视频生成任务: taskId={}", task.getId());
                try {
                    videoGenerationQueueService.processVideoGenerationTask(task.getId());
                } catch (Exception e) {
                    log.error("处理视频生成任务失败: taskId={}", task.getId(), e);
                    XxlJobHelper.log("处理视频生成任务失败: taskId={}, 错误: {}", task.getId(), e.getMessage());
                }
            }
            
            log.info("视频生成任务扫描完成");
            XxlJobHelper.log("视频生成任务扫描完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("扫描视频生成任务异常", e);
            XxlJobHelper.log("扫描视频生成任务异常: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "扫描视频生成任务异常: " + e.getMessage());
        }
    }
} 