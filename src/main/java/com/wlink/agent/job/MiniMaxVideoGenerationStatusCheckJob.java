package com.wlink.agent.job;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.client.MiniMaxFileDownloadClient;
import com.wlink.agent.client.MiniMaxVideoApiClient;
import com.wlink.agent.client.model.minimax.MiniMaxVideoStatusResponse;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationRecordMapper;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiCanvasVideoPo;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.dao.po.AiVideoGenerationRecordPo;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.service.impl.VideoGenerationQueueServiceImpl;
import com.wlink.agent.utils.OssUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * MiniMax视频生成任务状态检查定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MiniMaxVideoGenerationStatusCheckJob {

    private final AiVideoGenerationRecordMapper videoGenerationRecordMapper;
    private final AiVideoGenerationMapper videoGenerationMapper;
    private final AiCanvasShotMapper aiCanvasShotMapper;
    private final AiCanvasVideoMapper aiCanvasVideoMapper;
    private final VideoGenerationQueueServiceImpl videoGenerationQueueService;
    private final MiniMaxVideoApiClient miniMaxVideoApiClient;
    private final MiniMaxFileDownloadClient miniMaxFileDownloadClient;
    private final OssUtils ossUtils;

    @Value("${app.env:dev}")
    private String env;

    /**
     * MiniMax模型名称
     */
    private static final String MINIMAX_MODEL = "MiniMax-Hailuo-02";
    
    /**
     * 任务超时时间（分钟）- 5分钟
     */
    private static final long TASK_TIMEOUT_MINUTES = 5;
    
    /**
     * OSS路径模板
     */
    private static final String OSS_PATH_TEMPLATE = "{env}/video/{userId}/";

    /**
     * 定时检查MiniMax视频生成任务状态
     * 每5分钟执行一次
     */
    @XxlJob("miniMaxVideoGenerationStatusCheckJobHandler")
    public ReturnT<String> checkMiniMaxVideoGenerationTaskStatus(String param) {
        log.info("开始检查MiniMax视频生成任务状态...");
        XxlJobHelper.log("开始检查MiniMax视频生成任务状态...");
        
        try {
            // 获取5分钟前的时间点
            LocalDateTime timeoutBefore = LocalDateTime.now().minusMinutes(TASK_TIMEOUT_MINUTES);
            
            // 查询MiniMax模型的未完成任务
            List<AiVideoGenerationRecordPo> uncompletedTasks = videoGenerationRecordMapper
                    .getUncompletedTasksByModel(MINIMAX_MODEL, timeoutBefore);
            
            log.info("发现{}个MiniMax未完成的任务", uncompletedTasks.size());
            XxlJobHelper.log("发现{}个MiniMax未完成的任务", uncompletedTasks.size());
            
            // 检查每个任务的状态
            for (AiVideoGenerationRecordPo task : uncompletedTasks) {
                log.info("检查MiniMax任务状态: taskId={}", task.getTaskId());
                XxlJobHelper.log("检查MiniMax任务状态: taskId={}", task.getTaskId());
                
                // 如果没有任务ID，则标记为失败
                if (!StringUtils.hasText(task.getTaskId())) {
                    log.error("MiniMax任务没有任务ID: recordId={}", task.getId());
                    XxlJobHelper.log("MiniMax任务没有任务ID: recordId={}", task.getId());
                    updateTaskAsFailed(task, "任务没有任务ID");
                    continue;
                }
                
                try {
                    // 调用MiniMax API查询任务状态
                    MiniMaxVideoStatusResponse response = miniMaxVideoApiClient.getVideoStatus(task.getTaskId()).get();
                    log.info("MiniMax任务状态查询结果: taskId={}, status={}", task.getTaskId(), response.getStatus());
                    XxlJobHelper.log("MiniMax任务状态查询结果: taskId={}, status={}", task.getTaskId(), response.getStatus());
                    
                    // 处理任务状态
                    processTaskStatus(task, response);
                } catch (Exception e) {
                    log.error("查询MiniMax任务状态失败: taskId={}", task.getTaskId(), e);
                    XxlJobHelper.log("查询MiniMax任务状态失败: taskId={}, 错误: {}", task.getTaskId(), e.getMessage());
                }
            }
            
            log.info("MiniMax视频生成任务状态检查完成");
            XxlJobHelper.log("MiniMax视频生成任务状态检查完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("检查MiniMax视频生成任务状态异常", e);
            XxlJobHelper.log("检查MiniMax视频生成任务状态异常: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "检查MiniMax视频生成任务状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理任务状态
     * 
     * @param task 任务记录
     * @param response API响应
     */
    private void processTaskStatus(AiVideoGenerationRecordPo task, MiniMaxVideoStatusResponse response) {
        String status = response.getStatus();
        
        if ("Success".equalsIgnoreCase(status)) {
            // 任务成功完成
            handleSuccessTask(task, response);
        } else if ("Fail".equalsIgnoreCase(status)) {
            // 任务失败
            String errorMessage = response.getErrorMessage();
            if (!StringUtils.hasText(errorMessage)) {
                errorMessage = "MiniMax视频生成失败";
            }
            updateTaskAsFailed(task, errorMessage);
            log.error("MiniMax任务处理失败: taskId={}, error={}", task.getTaskId(), errorMessage);
            XxlJobHelper.log("MiniMax任务处理失败: taskId={}, error={}", task.getTaskId(), errorMessage);
        } else if ("Processing".equalsIgnoreCase(status) || "Preparing".equalsIgnoreCase(status) || "Queueing".equalsIgnoreCase(status)) {
            // 任务仍在处理中，更新状态但不做其他处理
            updateTaskStatus(task, status);
            log.info("MiniMax任务仍在处理中: taskId={}, status={}", task.getTaskId(), status);
            XxlJobHelper.log("MiniMax任务仍在处理中: taskId={}, status={}", task.getTaskId(), status);
        }
    }
    
    /**
     * 处理成功的任务
     * 
     * @param task 任务记录
     * @param response API响应
     */
    private void handleSuccessTask(AiVideoGenerationRecordPo task, MiniMaxVideoStatusResponse response) {
        try {
            String fileId = response.getFileId();
            if (!StringUtils.hasText(fileId)) {
                log.error("MiniMax任务成功但没有文件ID: taskId={}", task.getTaskId());
                updateTaskAsFailed(task, "任务成功但没有文件ID");
                return;
            }
            
            log.info("开始下载MiniMax视频文件: taskId={}, fileId={}", task.getTaskId(), fileId);
            XxlJobHelper.log("开始下载MiniMax视频文件: taskId={}, fileId={}", task.getTaskId(), fileId);
            
            // 下载文件
            byte[] videoBytes = miniMaxFileDownloadClient.downloadFileAsBytes(fileId).get();
            log.info("MiniMax视频下载完成: taskId={}, fileId={}, size={}bytes", 
                    task.getTaskId(), fileId, videoBytes.length);
            
            // 生成OSS文件路径
            String ossPath = OSS_PATH_TEMPLATE
                    .replace("{env}", env)
                    .replace("{userId}", String.valueOf(task.getUserId()));
            String fileName = IdUtil.fastSimpleUUID() + ".mp4";
            String fullPath = ossPath + fileName;
            
            // 上传到OSS
            String videoUrl = ossUtils.uploadStream(new ByteArrayInputStream(videoBytes), fullPath);
            log.info("MiniMax视频上传OSS成功: taskId={}, fileId={}, videoUrl={}", 
                    task.getTaskId(), fileId, videoUrl);
            XxlJobHelper.log("MiniMax视频上传OSS成功: taskId={}, fileId={}, videoUrl={}", 
                    task.getTaskId(), fileId, videoUrl);
            
            // 更新任务状态为成功
            updateTaskAsSuccess(task, videoUrl, response);
            
            log.info("MiniMax视频处理完成: taskId={}, fileId={}, videoUrl={}", 
                    task.getTaskId(), fileId, videoUrl);
            XxlJobHelper.log("MiniMax视频处理完成: taskId={}, fileId={}, videoUrl={}", 
                    task.getTaskId(), fileId, videoUrl);
            
        } catch (Exception e) {
            log.error("处理MiniMax成功任务失败: taskId={}", task.getTaskId(), e);
            XxlJobHelper.log("处理MiniMax成功任务失败: taskId={}, 错误: {}", task.getTaskId(), e.getMessage());
            updateTaskAsFailed(task, "视频下载或上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新任务状态
     *
     * @param task 任务记录
     * @param status 新状态
     */
    private void updateTaskStatus(AiVideoGenerationRecordPo task, String status) {
        try {
            // 更新ai_video_generation_record表
            AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
            updateRecord.setId(task.getId());
            updateRecord.setStatus(status);
            updateRecord.setUpdateTime(LocalDateTime.now());
            videoGenerationRecordMapper.updateById(updateRecord);
            log.info("更新任务状态成功: taskId={}, status={}", task.getTaskId(), status);
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", task.getTaskId(), e);
        }
    }
    
    /**
     * 更新任务为成功状态
     *
     * @param task 任务记录
     * @param videoUrl 视频URL
     * @param response API响应
     */
    private void updateTaskAsSuccess(AiVideoGenerationRecordPo task, String videoUrl, MiniMaxVideoStatusResponse response) {
        try {
            // 更新ai_video_generation_record表
            AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
            updateRecord.setId(task.getId());
            updateRecord.setStatus("succeeded");
            updateRecord.setVideoUrl(videoUrl);
            updateRecord.setUpdateTime(LocalDateTime.now());
            videoGenerationRecordMapper.updateById(updateRecord);

            // 查找对应的ai_video_generation记录并更新
            updateVideoGenerationTable(task.getTaskId(), videoUrl, 2); // 2-处理完成

            // 更新分镜状态
            updateShotStatus(task.getTaskId(), ShotStatus.COMPLETED.getValue());

            // 更新ai_canvas_video表
            updateCanvasVideoTable(task.getTaskId(), videoUrl);

            log.info("更新任务为成功状态完成: taskId={}, videoUrl={}", task.getTaskId(), videoUrl);
        } catch (Exception e) {
            log.error("更新任务为成功状态失败: taskId={}", task.getTaskId(), e);
        }
    }
    
    /**
     * 更新任务为失败状态
     *
     * @param task 任务记录
     * @param errorMessage 错误信息
     */
    private void updateTaskAsFailed(AiVideoGenerationRecordPo task, String errorMessage) {
        try {
            // 更新ai_video_generation_record表
            AiVideoGenerationRecordPo updateRecord = new AiVideoGenerationRecordPo();
            updateRecord.setId(task.getId());
            updateRecord.setStatus("failed");
            updateRecord.setErrorMsg(errorMessage);
            updateRecord.setUpdateTime(LocalDateTime.now());
            videoGenerationRecordMapper.updateById(updateRecord);

            // 查找对应的ai_video_generation记录并更新
            updateVideoGenerationTable(task.getTaskId(), null, 3); // 3-处理失败

            // 更新分镜状态
            updateShotStatus(task.getTaskId(), ShotStatus.FAILED.getValue());

            log.info("更新任务为失败状态完成: taskId={}, errorMessage={}", task.getTaskId(), errorMessage);
        } catch (Exception e) {
            log.error("更新任务为失败状态失败: taskId={}", task.getTaskId(), e);
        }
    }

    /**
     * 更新ai_video_generation表
     *
     * @param taskId 外部任务ID
     * @param videoUrl 视频URL
     * @param status 状态
     */
    private void updateVideoGenerationTable(String taskId, String videoUrl, Integer status) {
        try {
            LambdaQueryWrapper<AiVideoGenerationPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiVideoGenerationPo::getGenerateTaskId, taskId)
                    .eq(AiVideoGenerationPo::getDelFlag, 0)
                    .orderByDesc(AiVideoGenerationPo::getCreateTime)
                    .last("LIMIT 1");

            AiVideoGenerationPo videoGeneration = videoGenerationMapper.selectOne(queryWrapper);
            if (videoGeneration != null) {
                LambdaUpdateWrapper<AiVideoGenerationPo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(AiVideoGenerationPo::getId, videoGeneration.getId())
                        .set(AiVideoGenerationPo::getStatus, status)
                        .set(AiVideoGenerationPo::getUpdateTime, new Date());

                if (StringUtils.hasText(videoUrl)) {
                    updateWrapper.set(AiVideoGenerationPo::getVideoUrl, videoUrl);
                    updateWrapper.set(AiVideoGenerationPo::getCompleteTime, new Date());
                }

                videoGenerationMapper.update(updateWrapper);
                log.info("更新ai_video_generation表成功: taskId={}, status={}", taskId, status);
            } else {
                log.warn("未找到对应的ai_video_generation记录: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新ai_video_generation表失败: taskId={}", taskId, e);
        }
    }

    /**
     * 更新分镜状态
     *
     * @param taskId 外部任务ID
     * @param shotStatus 分镜状态
     */
    private void updateShotStatus(String taskId, Integer shotStatus) {
        try {
            // 通过ai_video_generation表找到shotId
            LambdaQueryWrapper<AiVideoGenerationPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiVideoGenerationPo::getGenerateTaskId, taskId)
                    .eq(AiVideoGenerationPo::getDelFlag, 0)
                    .orderByDesc(AiVideoGenerationPo::getCreateTime)
                    .last("LIMIT 1");

            AiVideoGenerationPo videoGeneration = videoGenerationMapper.selectOne(queryWrapper);
            if (videoGeneration != null && videoGeneration.getShotId() != null) {
                LambdaUpdateWrapper<AiCanvasShotPo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(AiCanvasShotPo::getId, videoGeneration.getShotId())
                        .set(AiCanvasShotPo::getShotStatus, shotStatus)
                        .set(AiCanvasShotPo::getUpdateTime, new Date());

                aiCanvasShotMapper.update(updateWrapper);
                log.info("更新分镜状态成功: taskId={}, shotId={}, status={}", taskId, videoGeneration.getShotId(), shotStatus);
            } else {
                log.warn("未找到对应的分镜记录: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新分镜状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 更新ai_canvas_video表
     *
     * @param taskId 外部任务ID
     * @param videoUrl 视频URL
     */
    private void updateCanvasVideoTable(String taskId, String videoUrl) {
        try {
            // 通过ai_video_generation表找到shotId，再通过shotId找到分镜编码
            LambdaQueryWrapper<AiVideoGenerationPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiVideoGenerationPo::getGenerateTaskId, taskId)
                    .eq(AiVideoGenerationPo::getDelFlag, 0)
                    .orderByDesc(AiVideoGenerationPo::getCreateTime)
                    .last("LIMIT 1");

            AiVideoGenerationPo videoGeneration = videoGenerationMapper.selectOne(queryWrapper);
            if (videoGeneration != null && videoGeneration.getShotId() != null) {
                // 查找分镜信息获取分镜编码
                AiCanvasShotPo shotPo = aiCanvasShotMapper.selectById(videoGeneration.getShotId());
                if (shotPo != null) {
                    String shotCode = shotPo.getCode();
                    Long canvasId = shotPo.getCanvasId();

                    // 查询或创建ai_canvas_video记录
                    LambdaQueryWrapper<AiCanvasVideoPo> videoQueryWrapper = new LambdaQueryWrapper<>();
                    videoQueryWrapper.eq(AiCanvasVideoPo::getCanvasId, canvasId)
                            .eq(AiCanvasVideoPo::getShotCode, shotCode);

                    AiCanvasVideoPo canvasVideo = aiCanvasVideoMapper.selectOne(videoQueryWrapper);
                    if (canvasVideo != null) {
                        // 更新现有记录
                        LambdaUpdateWrapper<AiCanvasVideoPo> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(AiCanvasVideoPo::getId, canvasVideo.getId())
                                .set(AiCanvasVideoPo::getVideoUrl, videoUrl)
                                .set(AiCanvasVideoPo::getVideoStatus, "completed")
                                .set(AiCanvasVideoPo::getUpdateTime, new Date());

                        aiCanvasVideoMapper.update(updateWrapper);
                        log.info("更新ai_canvas_video表成功: taskId={}, shotCode={}, videoUrl={}", taskId, shotCode, videoUrl);
                    } else {
                        log.warn("未找到对应的ai_canvas_video记录: taskId={}, shotCode={}", taskId, shotCode);
                    }
                } else {
                    log.warn("未找到对应的分镜信息: taskId={}, shotId={}", taskId, videoGeneration.getShotId());
                }
            } else {
                log.warn("未找到对应的视频生成记录: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新ai_canvas_video表失败: taskId={}", taskId, e);
        }
    }
}
