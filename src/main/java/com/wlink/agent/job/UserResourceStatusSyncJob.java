package com.wlink.agent.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.client.FalAiFluxKontextClient;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextResponse;
import com.wlink.agent.client.FalAiFluxKontextClient.KontextImage;
import com.wlink.agent.client.FalAiFluxKontextClient.QueueStatus;
import com.wlink.agent.dao.mapper.AiUserResourceMapper;
import com.wlink.agent.dao.po.AiUserResourcePo;
import com.wlink.agent.enums.UserResourceGenerationStatus;
import com.wlink.agent.model.req.UniversalImageGenerationRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户资源状态同步定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserResourceStatusSyncJob {

    private final AiUserResourceMapper userResourceMapper;
    private final FalAiFluxKontextClient falAiFluxKontextClient;

    // 支持的模型类型常量
    private static final String MODEL_KONTEXT_PRO = "kontext-pro";
    private static final String MODEL_KONTEXT_MAX = "kontext-max";

    // 对应的端点
    private static final String KONTEXT_TEXT_TO_IMAGE_ENDPOINT = "/fal-ai/flux-pro/kontext/text-to-image";
    private static final String KONTEXT_MAX_TEXT_TO_IMAGE_ENDPOINT = "/fal-ai/flux-pro/kontext/max/text-to-image";
    private static final String KONTEXT_ENDPOINT = "/fal-ai/flux-pro/kontext";
    private static final String KONTEXT_MAX_ENDPOINT = "/fal-ai/flux-pro/kontext/max";
    private static final String KONTEXT_MAX_MULTI_ENDPOINT = "/fal-ai/flux-pro/kontext/max/multi";

    /**
     * 任务超时时间（毫秒）- 10分钟
     */
    private static final long TASK_TIMEOUT_MS = 10 * 60 * 1000;

    /**
     * 定时同步用户资源状态
     * 每2分钟执行一次
     */
    @XxlJob("userResourceStatusSyncJobHandler")
    public ReturnT<String> syncUserResourceStatus(String param) {
        log.info("开始执行用户资源状态同步任务...");
        XxlJobHelper.log("开始执行用户资源状态同步任务...");

        try {
            // 查询所有生成中的资源
            List<AiUserResourcePo> pendingResources = getPendingResources();

            if (pendingResources.isEmpty()) {
                log.info("没有需要同步状态的资源");
                XxlJobHelper.log("没有需要同步状态的资源");
                return ReturnT.SUCCESS;
            }

            log.info("找到 {} 个需要同步状态的资源", pendingResources.size());
            XxlJobHelper.log("找到 {} 个需要同步状态的资源", pendingResources.size());

            int successCount = 0;
            int failedCount = 0;

            for (AiUserResourcePo resource : pendingResources) {
                try {
                    boolean updated = syncSingleResourceStatus(resource);
                    if (updated) {
                        successCount++;
                    }
                } catch (Exception e) {
                    failedCount++;
                    log.error("同步资源状态失败: resourceId={}, code={}, externalRequestId={}", 
                            resource.getId(), resource.getCode(), resource.getExternalRequestId(), e);
                    XxlJobHelper.log("同步资源状态失败: resourceId={}, code={}, 错误: {}", 
                            resource.getId(), resource.getCode(), e.getMessage());
                }
            }

            log.info("用户资源状态同步完成，成功: {}, 失败: {}", successCount, failedCount);
            XxlJobHelper.log("用户资源状态同步完成，成功: {}, 失败: {}", successCount, failedCount);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("用户资源状态同步任务执行失败", e);
            XxlJobHelper.log("用户资源状态同步任务执行失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 获取需要同步状态的资源
     */
    private List<AiUserResourcePo> getPendingResources() {
        LambdaQueryWrapper<AiUserResourcePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserResourcePo::getGenerationStatus, UserResourceGenerationStatus.PENDING.getValue())
                .isNotNull(AiUserResourcePo::getExternalRequestId)
                .eq(AiUserResourcePo::getDelFlag, 0)
                .orderByAsc(AiUserResourcePo::getCreateTime)
                .last("LIMIT 50"); // 每次最多处理50个

        return userResourceMapper.selectList(queryWrapper);
    }

    /**
     * 同步单个资源状态
     */
    @Transactional(rollbackFor = Exception.class)
    private boolean syncSingleResourceStatus(AiUserResourcePo resource) throws Exception {
        String externalRequestId = resource.getExternalRequestId();
        String modelType = resource.getModelType();

        if (!StringUtils.hasText(externalRequestId) || !StringUtils.hasText(modelType)) {
            log.warn("资源缺少必要信息: resourceId={}, externalRequestId={}, modelType={}", 
                    resource.getId(), externalRequestId, modelType);
            return false;
        }

        // 检查是否超时
        if (isResourceTimeout(resource)) {
            markResourceAsFailed(resource, "生成任务超时");
            return true;
        }

        // 根据模型类型和参数确定端点
        String endpoint = determineEndpoint(resource);
        if (endpoint == null) {
            log.warn("无法确定API端点: resourceId={}, modelType={}", resource.getId(), modelType);
            return false;
        }

        try {
            // 查询任务状态
            QueueStatus queueStatus = falAiFluxKontextClient.checkKontextQueueStatus(externalRequestId, endpoint);
            log.info("查询到资源状态: resourceId={}, status={}, queuePosition={}", 
                    resource.getId(), queueStatus.getStatus(), queueStatus.getQueuePosition());

            if ("COMPLETED".equals(queueStatus.getStatus())) {
                // 任务完成，获取结果
                KontextResponse kontextResponse = falAiFluxKontextClient.getKontextResult(externalRequestId, endpoint);
                updateResourceWithResult(resource, kontextResponse);
                return true;
            } else if ("FAILED".equals(queueStatus.getStatus()) || "CANCELLED".equals(queueStatus.getStatus())) {
                // 任务失败
                markResourceAsFailed(resource, "外部API任务失败，状态: " + queueStatus.getStatus());
                return true;
            } else {
                // 任务仍在进行中，更新时间
                resource.setUpdateTime(new Date());
                userResourceMapper.updateById(resource);
                log.info("资源仍在生成中: resourceId={}, status={}", resource.getId(), queueStatus.getStatus());
                return false;
            }

        } catch (Exception e) {
            log.error("查询外部API状态失败: resourceId={}, externalRequestId={}", 
                    resource.getId(), externalRequestId, e);
            throw e;
        }
    }

    /**
     * 检查资源是否超时
     */
    private boolean isResourceTimeout(AiUserResourcePo resource) {
        long createTime = resource.getCreateTime().getTime();
        long currentTime = System.currentTimeMillis();
        return (currentTime - createTime) > TASK_TIMEOUT_MS;
    }

    /**
     * 根据资源信息确定API端点
     */
    private String determineEndpoint(AiUserResourcePo resource) {
        String modelType = resource.getModelType();
        
        try {
            // 解析生成参数
            UniversalImageGenerationRequest originalRequest = JSON.parseObject(
                    resource.getGenerationParams(), UniversalImageGenerationRequest.class);
            
            boolean hasImages = originalRequest.getImageUrls() != null && !originalRequest.getImageUrls().isEmpty();
            int imageCount = hasImages ? originalRequest.getImageUrls().size() : 0;

            if (MODEL_KONTEXT_PRO.equalsIgnoreCase(modelType)) {
                if (hasImages && imageCount == 1) {
                    return KONTEXT_ENDPOINT; // 图像到图像
                } else if (!hasImages) {
                    return KONTEXT_TEXT_TO_IMAGE_ENDPOINT; // 文本到图像
                }
            } else if (MODEL_KONTEXT_MAX.equalsIgnoreCase(modelType)) {
                if (hasImages && imageCount > 1) {
                    return KONTEXT_MAX_MULTI_ENDPOINT; // 多图像处理
                } else if (hasImages && imageCount == 1) {
                    return KONTEXT_MAX_ENDPOINT; // 单图像到图像
                } else if (!hasImages) {
                    return KONTEXT_MAX_TEXT_TO_IMAGE_ENDPOINT; // 文本到图像
                }
            }
        } catch (Exception e) {
            log.warn("解析生成参数失败: resourceId={}", resource.getId(), e);
        }

        return null;
    }

    /**
     * 更新资源为成功状态并保存结果
     */
    private void updateResourceWithResult(AiUserResourcePo resource, KontextResponse kontextResponse) {
        if (kontextResponse == null || kontextResponse.getImages() == null || kontextResponse.getImages().isEmpty()) {
            markResourceAsFailed(resource, "外部API返回空结果");
            return;
        }

        List<String> imageUrls = new ArrayList<>();
        long totalSize = 0;
        Integer width = null;
        Integer height = null;

        for (KontextImage image : kontextResponse.getImages()) {
            imageUrls.add(image.getUrl());
            if (image.getFileSize() != null) {
                totalSize += image.getFileSize();
            }
            if (width == null && image.getWidth() != null) {
                width = image.getWidth();
            }
            if (height == null && image.getHeight() != null) {
                height = image.getHeight();
            }
        }

        resource.setGenerationStatus(UserResourceGenerationStatus.SUCCESS.getValue());
        resource.setResourceUrls(JSON.toJSONString(imageUrls));
        resource.setResourceSize(totalSize > 0 ? totalSize : null);
        resource.setWidth(width);
        resource.setHeight(height);
        resource.setUpdateTime(new Date());

        userResourceMapper.updateById(resource);
        log.info("资源生成成功: resourceId={}, imageCount={}, totalSize={}", 
                resource.getId(), imageUrls.size(), totalSize);
    }

    /**
     * 标记资源为失败状态
     */
    private void markResourceAsFailed(AiUserResourcePo resource, String errorMessage) {
        resource.setGenerationStatus(UserResourceGenerationStatus.FAILED.getValue());
        resource.setErrorMessage(errorMessage);
        resource.setUpdateTime(new Date());
        userResourceMapper.updateById(resource);
        log.warn("资源生成失败: resourceId={}, error={}", resource.getId(), errorMessage);
    }
}
