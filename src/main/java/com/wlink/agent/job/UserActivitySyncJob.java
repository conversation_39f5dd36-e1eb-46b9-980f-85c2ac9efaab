package com.wlink.agent.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.dao.mapper.AiUserActivityMapper;
import com.wlink.agent.dao.po.AiUserActivityPo;
import com.wlink.agent.utils.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Set;

/**
 * 用户活跃度数据同步定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserActivitySyncJob {

    private final RedissonClient redissonClient;
    private final AiUserActivityMapper aiUserActivityMapper;

    /**
     * 同步前一天的用户活跃数据到数据库
     * 每天凌晨1点执行
     */
    @XxlJob("userActivitySyncJobHandler")
    public ReturnT<String> syncUserActivity() {
        try {
            // 获取当天的日期
            String today = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            log.info("开始同步 {} 的用户活跃数据", today);

            // 构建Redis key
            String activeUsersKey = RedisKeyConstant.getKey(RedisKeyConstant.UserActivity.DAILY_ACTIVE_USERS, today);

            // 获取Redis中的活跃用户集合
            RSet<String> activeUsersSet = redissonClient.getSet(activeUsersKey);
            Set<String> activeUsers = activeUsersSet.readAll();

            if (activeUsers.isEmpty()) {
                log.info("{} 没有活跃用户数据", today);
                return ReturnT.SUCCESS;
            }

            log.info("{} 有 {} 个活跃用户", today, activeUsers.size());

            // 遍历活跃用户，更新或插入数据库记录
            for (String userId : activeUsers) {
                try {
                    // 查询是否已存在记录
                    AiUserActivityPo existingLog = aiUserActivityMapper.selectOne(
                            new LambdaQueryWrapper<AiUserActivityPo>()
                                    .eq(AiUserActivityPo::getUserId, userId)
                                    .eq(AiUserActivityPo::getActivityDate, today)
                    );

                    if (existingLog == null) {
                        // 不存在则插入新记录
                        AiUserActivityPo newLog = new AiUserActivityPo();
                        newLog.setUserId(userId);
                        newLog.setActivityDate(today);
                        newLog.setLastActiveTime(new Date());
                        newLog.setRequestCount(1);
                        newLog.setCreateTime(new Date());
                        newLog.setUpdateTime(new Date());
                        aiUserActivityMapper.insert(newLog);
                    }
                } catch (Exception e) {
                    log.error("同步用户 {} 的活跃数据失败: {}", userId, e.getMessage(), e);
                }
            }

            log.info("{} 的用户活跃数据同步完成", today);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("用户活跃数据同步任务执行失败: {}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }
} 