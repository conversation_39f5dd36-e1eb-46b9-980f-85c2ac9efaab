package com.wlink.agent.job;

import com.wlink.agent.service.impl.DatabaseConcurrencyControlService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 任务超时清理定时任务
 * 替代Redis信号量的修复机制
 */
@Slf4j
@Component
public class TaskTimeoutCleanupJob {

    @Autowired
    private DatabaseConcurrencyControlService concurrencyControlService;

    /**
     * 清理超时任务
     * 每2分钟执行一次
     */
    @XxlJob("taskTimeoutCleanup")
    public ReturnT<String> cleanupTimeoutTasks(String param) {
        try {
            log.info("开始执行任务超时清理...");
            
            // 获取清理前的状态
            DatabaseConcurrencyControlService.ConcurrencyStatus beforeStatus = 
                concurrencyControlService.getStatus();
            log.info("清理前状态: {}", beforeStatus);
            
            // 执行清理
            int cleanedCount = concurrencyControlService.cleanupTimeoutTasks();
            
            // 获取清理后的状态
            DatabaseConcurrencyControlService.ConcurrencyStatus afterStatus = 
                concurrencyControlService.getStatus();
            log.info("清理后状态: {}", afterStatus);
            
            String result = String.format("任务超时清理完成，清理了 %d 个超时任务", cleanedCount);
            log.info(result);
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("任务超时清理失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务超时清理失败: " + e.getMessage());
        }
    }

    /**
     * 系统状态监控
     * 每5分钟执行一次，用于监控系统状态
     */
    @XxlJob("systemStatusMonitor")
    public ReturnT<String> monitorSystemStatus(String param) {
        try {
            DatabaseConcurrencyControlService.ConcurrencyStatus status = 
                concurrencyControlService.getStatus();
            
            log.info("系统并发状态监控: {}", status);
            
            // 检查是否有异常情况
            if (status.getCurrentProcessing() == status.getMaxConcurrent() && status.getPendingTasks() > 0) {
                log.warn("系统可能存在任务堵塞，所有并发槽位都被占用，但还有 {} 个待处理任务", 
                    status.getPendingTasks());
            }
            
            if (status.getPendingTasks() > 100) {
                log.warn("待处理任务数量较多: {} 个，请检查系统负载", status.getPendingTasks());
            }
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("系统状态监控失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统状态监控失败: " + e.getMessage());
        }
    }
}
