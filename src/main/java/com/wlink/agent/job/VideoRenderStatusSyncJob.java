package com.wlink.agent.job;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.dao.mapper.AiVideoRenderExportMapper;
import com.wlink.agent.dao.po.AiVideoRenderExportPo;
import com.wlink.agent.enums.ExternalTaskStatus;
import com.wlink.agent.model.dto.TaskStatusResponse;
import com.wlink.agent.utils.OssUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 视频渲染状态同步定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoRenderStatusSyncJob {
    
    private final AiVideoRenderExportMapper videoRenderExportMapper;
    private final OkHttpClient okHttpClient;
    private final OssUtils ossUtils;


    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";
    @Value("${spring.profiles.active}")
    String env;
    /**
     * 最大查询失败次数
     */
    private static final int MAX_QUERY_FAIL_COUNT = 3;
    
    /**
     * 外部任务查询API基础URL
     */
    private static final String EXTERNAL_API_BASE_URL = "http://47.117.139.188:9990/api/tasks/";
    
    /**
     * 每5分钟执行一次状态同步
     */
    @XxlJob("videoRenderStatusCheckJobHandler")
    public ReturnT<String> syncVideoRenderStatus(String param) {
        log.info("开始执行视频渲染状态同步任务");
        
        try {
            // 查询排队中(0)和渲染中(1)的任务
            LambdaQueryWrapper<AiVideoRenderExportPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(AiVideoRenderExportPo::getStatus, 0, 1)
                    .eq(AiVideoRenderExportPo::getDelFlag, 0)
                    .isNotNull(AiVideoRenderExportPo::getRenderTaskId)
                    .orderByAsc(AiVideoRenderExportPo::getCreateTime);
            
            List<AiVideoRenderExportPo> pendingTasks = videoRenderExportMapper.selectList(queryWrapper);
            
            if (pendingTasks.isEmpty()) {
                log.info("没有需要同步状态的任务");
                return ReturnT.SUCCESS;
            }
            
            log.info("找到 {} 个需要同步状态的任务", pendingTasks.size());
            
            for (AiVideoRenderExportPo task : pendingTasks) {
                try {
                    syncSingleTaskStatus(task);
                } catch (Exception e) {
                    log.error("同步任务状态失败: taskId={}, renderTaskId={}, error={}", 
                            task.getId(), task.getRenderTaskId(), e.getMessage(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("视频渲染状态同步任务执行失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "检查视频生成任务状态异常: " + e.getMessage());
        }
        log.info("视频渲染状态同步任务执行完成");
        return ReturnT.SUCCESS;
    }
    
    /**
     * 同步单个任务状态
     */
    private void syncSingleTaskStatus(AiVideoRenderExportPo task) {
        String renderTaskId = task.getRenderTaskId();
        log.debug("开始同步任务状态: taskId={}, renderTaskId={}", task.getId(), renderTaskId);
        
        try {
            // 调用外部API查询任务状态
            String apiUrl = EXTERNAL_API_BASE_URL + renderTaskId;
            Request request = new Request.Builder()
                    .url(apiUrl)
                    .get()
                    .build();
            
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("外部API响应: taskId={}, response={}", task.getId(), responseBody);
                    
                    // 解析响应
                    TaskStatusResponse statusResponse = JSON.parseObject(responseBody, TaskStatusResponse.class);
                    
                    if (statusResponse != null && Boolean.TRUE.equals(statusResponse.getSuccess()) 
                            && statusResponse.getData() != null) {
                        
                        // 重置查询失败次数
                        resetQueryFailCount(task.getId());
                        
                        // 处理状态更新
                        handleStatusUpdate(task, statusResponse.getData());
                        
                    } else {
                        log.warn("外部API返回失败: taskId={}, response={}", task.getId(), statusResponse);
                        incrementQueryFailCount(task);
                    }
                } else {
                    log.warn("外部API HTTP请求失败: taskId={}, code={}, message={}", 
                            task.getId(), response.code(), response.message());
                    incrementQueryFailCount(task);
                }
            }
            
        } catch (Exception e) {
            log.error("查询外部任务状态异常: taskId={}, renderTaskId={}, error={}", 
                    task.getId(), renderTaskId, e.getMessage(), e);
            incrementQueryFailCount(task);
        }
    }
    
    /**
     * 处理状态更新
     */
    private void handleStatusUpdate(AiVideoRenderExportPo task, TaskStatusResponse.TaskData taskData) {
        String externalStatus = taskData.getStatus();
        Integer newInternalStatus = ExternalTaskStatus.getInternalStatus(externalStatus);
        
        if (newInternalStatus == null) {
            log.warn("未知的外部任务状态: taskId={}, externalStatus={}", task.getId(), externalStatus);
            return;
        }
        
        // 如果状态相同，不做处理
        if (newInternalStatus.equals(task.getStatus())) {
            log.debug("任务状态未变化: taskId={}, status={}", task.getId(), newInternalStatus);
            return;
        }
        
        log.info("任务状态发生变化: taskId={}, oldStatus={}, newStatus={}, externalStatus={}", 
                task.getId(), task.getStatus(), newInternalStatus, externalStatus);
        
        // 更新任务状态
        LambdaUpdateWrapper<AiVideoRenderExportPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiVideoRenderExportPo::getId, task.getId())
                .set(AiVideoRenderExportPo::getStatus, newInternalStatus)
                .set(AiVideoRenderExportPo::getUpdateTime, new Date());
        
        // 根据状态设置相应字段
        if (newInternalStatus == 1) { // 渲染中
            if (task.getStartTime() == null) {
                updateWrapper.set(AiVideoRenderExportPo::getStartTime, new Date());
            }
        } else if (newInternalStatus == 2) { // 已完成
            updateWrapper.set(AiVideoRenderExportPo::getCompleteTime, new Date());
            updateWrapper.set(AiVideoRenderExportPo::getErrorMessage, null);
            
            // 设置视频URL
            if (taskData.getOutputFile() != null && StringUtils.hasText(taskData.getOutputFile().getDownloadUrl())) {
                String videoUrl = taskData.getOutputFile().getDownloadUrl();
                String video = ossUtils.uploadFile(videoUrl, OSS_PATH.replace("{env}", env)
                        .replace("{userId}", task.getUserId())
                        .replace("{type}", "video") + IdUtil.fastSimpleUUID() + ".mp4");
                updateWrapper.set(AiVideoRenderExportPo::getVideoUrl, video);
                updateWrapper.set(AiVideoRenderExportPo::getVideoDuration, taskData.getOutputFile().getDuration());
            }
        } else if (newInternalStatus == 3) { // 失败
            updateWrapper.set(AiVideoRenderExportPo::getCompleteTime, new Date());
            
            // 设置错误信息
            String errorMessage = taskData.getErrorMessage();
            if (!StringUtils.hasText(errorMessage)) {
                errorMessage = "任务执行失败，状态: " + externalStatus;
            }
            updateWrapper.set(AiVideoRenderExportPo::getErrorMessage, errorMessage);
        }
        
        int updateCount = videoRenderExportMapper.update(null, updateWrapper);
        if (updateCount > 0) {
            log.info("任务状态更新成功: taskId={}, newStatus={}", task.getId(), newInternalStatus);
        } else {
            log.error("任务状态更新失败: taskId={}, newStatus={}", task.getId(), newInternalStatus);
        }
    }
    
    /**
     * 增加查询失败次数
     */
    private void incrementQueryFailCount(AiVideoRenderExportPo task) {
        int currentFailCount = task.getQueryFailCount() != null ? task.getQueryFailCount() : 0;
        int newFailCount = currentFailCount + 1;
        
        LambdaUpdateWrapper<AiVideoRenderExportPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiVideoRenderExportPo::getId, task.getId())
                .set(AiVideoRenderExportPo::getQueryFailCount, newFailCount)
                .set(AiVideoRenderExportPo::getUpdateTime, new Date());
        
        // 如果查询失败次数达到上限，标记任务为失败
        if (newFailCount >= MAX_QUERY_FAIL_COUNT) {
            updateWrapper.set(AiVideoRenderExportPo::getStatus, 3) // 失败
                    .set(AiVideoRenderExportPo::getCompleteTime, new Date())
                    .set(AiVideoRenderExportPo::getErrorMessage, 
                            "任务查询失败次数超过限制(" + MAX_QUERY_FAIL_COUNT + "次)，标记为失败");
            
            log.warn("任务查询失败次数达到上限，标记为失败: taskId={}, failCount={}", 
                    task.getId(), newFailCount);
        }
        
        videoRenderExportMapper.update(null, updateWrapper);
    }
    
    /**
     * 重置查询失败次数
     */
    private void resetQueryFailCount(Long taskId) {
        LambdaUpdateWrapper<AiVideoRenderExportPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiVideoRenderExportPo::getId, taskId)
                .set(AiVideoRenderExportPo::getQueryFailCount, 0)
                .set(AiVideoRenderExportPo::getUpdateTime, new Date());
        
        videoRenderExportMapper.update(null, updateWrapper);
    }
}
