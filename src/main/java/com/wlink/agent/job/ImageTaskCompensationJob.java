package com.wlink.agent.job;

import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.config.AliyunRocketMQProperties;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.mq.MqTag;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 图片任务补偿定时任务
 * 定时扫描PENDING状态的任务，如果创建时间超过2分钟，则重新投递到MQ
 */
@Slf4j
@Component
public class ImageTaskCompensationJob {

    @Autowired
    private AiImageTaskQueueMapper imageTaskQueueMapper;

    @Autowired
    private ImageTaskMQHandler imageTaskMQHandler;

    @Resource
    private AliyunRocketMQProperties properties;
    
    /**
     * 补偿图片任务
     * 定时扫描PENDING状态的REDRAW和GENERATE类型任务，如果创建时间超过2分钟，则重新投递到MQ
     * 
     * @return 执行结果
     */
    @XxlJob("imageTaskCompensationJobHandler")
    public ReturnT<String> compensateImageTasks() {
        log.info("开始执行图片任务补偿定时任务");
        XxlJobHelper.log("开始执行图片任务补偿定时任务");
        
        try {
            // 计算2分钟前的时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MINUTE, -2);
            Date twoMinutesAgo = calendar.getTime();
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            log.info("查询创建时间早于 {} 的待处理任务", sdf.format(twoMinutesAgo));
            XxlJobHelper.log("查询创建时间早于 {} 的待处理任务", sdf.format(twoMinutesAgo));
            
            // 查询符合条件的任务：PENDING状态 + (REDRAW或GENERATE类型) + 创建时间小于2分钟前
            LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                    .in(AiImageTaskQueuePo::getTaskType, Arrays.asList(TaskType.REDRAW.getValue(), TaskType.GENERATE.getValue()))
                    .lt(AiImageTaskQueuePo::getCreateTime, twoMinutesAgo);
            
            List<AiImageTaskQueuePo> pendingTasks = imageTaskQueueMapper.selectList(queryWrapper);
            
            log.info("找到 {} 个需要补偿的任务", pendingTasks.size());
            XxlJobHelper.log("找到 {} 个需要补偿的任务", pendingTasks.size());
            
            // 处理找到的任务
            int compensatedCount = 0;
            int failedCount = 0;
            
            for (AiImageTaskQueuePo task : pendingTasks) {
                try {
                    // 根据image_model区分处理方式
                    String taskId = String.valueOf(task.getId());
                    String imageModel = task.getImageModel();
                    String taskType = task.getTaskType();
                    String mqTaskType;
                    if ("DOUBAO".equalsIgnoreCase(imageModel)) {
                        mqTaskType = TaskType.GENERATE_DOUBAO.getValue();
                    } else {
                        mqTaskType = TaskType.GENERATE_FLUX.getValue();
                    }
                    // 重新投递到MQ
                    imageTaskMQHandler.sendTaskMessage(taskId, mqTaskType);
                    
                    String logMessage = String.format("已补偿任务：ID=%s, 类型=%s, 图片模型=%s, MQ标签=%s, 创建时间=%s",
                            taskId, taskType, imageModel, mqTaskType, sdf.format(task.getCreateTime()));
                    log.info(logMessage);
                    XxlJobHelper.log(logMessage);
                    
                    compensatedCount++;
                } catch (Exception e) {
                    failedCount++;
                    String errorMessage = String.format("补偿任务失败：ID=%s, 错误=%s", task.getId(), e.getMessage());
                    log.error(errorMessage, e);
                    XxlJobHelper.log(errorMessage);
                }
            }
            
            String resultMessage = String.format("图片任务补偿定时任务执行完成，共补偿 %d 个任务，失败 %d 个任务", 
                    compensatedCount, failedCount);
            log.info(resultMessage);
            XxlJobHelper.log(resultMessage);
            
            return ReturnT.SUCCESS;
        } catch (BizException e) {
            String errorMessage = String.format("图片任务补偿定时任务执行异常: %s", e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, "业务异常: " + e.getMessage());
        } catch (Exception e) {
            String errorMessage = String.format("图片任务补偿定时任务执行异常: %s", e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }
    
    /**
     * 根据图片模型和任务类型确定MQ标签
     * 
     * @param imageModel 图片模型
     * @param taskType 任务类型
     * @return MQ标签
     */
    private String determineMqTag(String imageModel, String taskType) {
        // 如果是REDRAW类型，直接使用REDRAW标签
        if (TaskType.REDRAW.getValue().equals(taskType)) {
            return TaskType.REDRAW.getValue();
        }
        
        // 如果是GENERATE类型，根据imageModel区分
        if (TaskType.GENERATE.getValue().equals(taskType)) {
            if ("DOUBAO".equals(imageModel)) {
                return MqTag.TAG_GENERATE_DOUBAO;
            } else if ("FLUX".equals(imageModel)) {
                return MqTag.TAG_GENERATE_FLUX;
            }
        }
        
        // 默认返回GENERATE标签
        return TaskType.GENERATE.getValue();
    }
} 