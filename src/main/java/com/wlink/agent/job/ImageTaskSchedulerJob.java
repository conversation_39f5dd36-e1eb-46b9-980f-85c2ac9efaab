package com.wlink.agent.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.config.ConcurrencyControlConfig;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.service.impl.DatabaseConcurrencyControlService;
import com.wlink.agent.service.impl.StableImageGenerationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 图片任务调度器
 * 替代MQ，使用定时任务扫描数据库处理图片生成任务
 */
@Slf4j
@Component
public class ImageTaskSchedulerJob {

    @Autowired
    private AiImageTaskQueueMapper taskQueueMapper;

    @Autowired
    private DatabaseConcurrencyControlService concurrencyControlService;

    @Autowired
    private StableImageGenerationService imageGenerationService;

    @Autowired
    private ConcurrencyControlConfig config;

    // 线程池用于异步处理任务
    private final ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(
        10, // 核心线程数
        20, // 最大线程数
        60L, TimeUnit.SECONDS, // 线程空闲时间
        new LinkedBlockingQueue<>(100), // 队列容量
        new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "ImageTask-" + threadNumber.getAndIncrement());
                t.setDaemon(false);
                return t;
            }
        }
    );

    /**
     * 图片任务调度器 - 主任务
     * 每30秒执行一次，扫描待处理的生成任务（排除编辑任务）
     */
    @XxlJob("imageTaskScheduler")
    public ReturnT<String> scheduleImageTasks(String param) {
        try {
            log.debug("开始扫描图片生成任务...");

            // 获取生成任务的系统状态（排除编辑任务）
            DatabaseConcurrencyControlService.ConcurrencyStatus status =
                getGenerationTaskStatus();

            if (status.getAvailable() <= 0) {
                log.debug("当前无可用并发槽位，跳过本次扫描。状态: {}", status);
                return ReturnT.SUCCESS;
            }

            // 查询待处理的生成任务，排除编辑任务
            List<AiImageTaskQueuePo> pendingTasks = getPendingGenerationTasks(status.getAvailable());

            if (pendingTasks.isEmpty()) {
                log.debug("暂无待处理的生成任务");
                return ReturnT.SUCCESS;
            }

            log.info("发现 {} 个待处理的生成任务，当前可用槽位: {}",
                pendingTasks.size(), status.getAvailable());

            // 异步处理任务
            int processedCount = 0;
            for (AiImageTaskQueuePo task : pendingTasks) {
                if (processedCount >= status.getAvailable()) {
                    break; // 不超过可用槽位数
                }

                // 异步提交任务处理
                CompletableFuture.runAsync(() -> {
                    try {
                        imageGenerationService.processImageTask(String.valueOf(task.getId()));
                    } catch (Exception e) {
                        log.error("异步处理图片生成任务失败: {}", task.getId(), e);
                    }
                }, taskExecutor);

                processedCount++;
            }

            String result = String.format("图片生成任务调度完成，提交了 %d 个任务进行处理", processedCount);
            log.info(result);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("图片生成任务调度失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "图片生成任务调度失败: " + e.getMessage());
        }
    }

    /**
     * 编辑任务调度器
     * 每20秒执行一次，专门处理EDIT和CANVAS_EDIT类型的任务
     */
    @XxlJob("editImageTaskScheduler")
    public ReturnT<String> scheduleEditTasks(String param) {
        try {
            log.debug("开始扫描图片编辑任务...");

            // 获取编辑任务的系统状态
            DatabaseConcurrencyControlService.ConcurrencyStatus status =
                getEditTaskStatus();

            if (status.getAvailable() <= 0) {
                log.debug("当前无可用并发槽位，跳过编辑任务扫描");
                return ReturnT.SUCCESS;
            }

            // 查询编辑类型的待处理任务
            List<AiImageTaskQueuePo> editTasks = getEditTasks(status.getAvailable());

            if (editTasks.isEmpty()) {
                log.debug("暂无待处理的编辑任务");
                return ReturnT.SUCCESS;
            }

            log.info("发现 {} 个编辑任务", editTasks.size());

            // 异步处理编辑任务
            int processedCount = 0;
            for (AiImageTaskQueuePo task : editTasks) {
                if (processedCount >= status.getAvailable()) {
                    break;
                }

                CompletableFuture.runAsync(() -> {
                    try {
                        // 编辑任务可能需要特殊处理逻辑，这里先使用通用处理
                        imageGenerationService.processImageTask(String.valueOf(task.getId()));
                    } catch (Exception e) {
                        log.error("异步处理编辑任务失败: {}", task.getId(), e);
                    }
                }, taskExecutor);

                processedCount++;
            }

            log.info("编辑任务调度完成，提交了 {} 个任务", processedCount);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("编辑任务调度失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "编辑任务调度失败: " + e.getMessage());
        }
    }

    /**
     * 高优先级任务调度器
     * 每10秒执行一次，处理高优先级的生成任务（排除编辑任务）
     */
    @XxlJob("highPriorityImageTaskScheduler")
    public ReturnT<String> scheduleHighPriorityTasks(String param) {
        try {
            log.debug("开始扫描高优先级图片生成任务...");

            // 获取生成任务的系统状态（排除编辑任务）
            DatabaseConcurrencyControlService.ConcurrencyStatus status =
                getGenerationTaskStatus();

            if (status.getAvailable() <= 0) {
                log.debug("当前无可用并发槽位，跳过高优先级生成任务扫描");
                return ReturnT.SUCCESS;
            }

            // 查询高优先级待处理的生成任务（重试次数少的任务优先，排除编辑任务）
            List<AiImageTaskQueuePo> highPriorityTasks = getHighPriorityGenerationTasks(status.getAvailable());

            if (highPriorityTasks.isEmpty()) {
                log.debug("暂无高优先级待处理的生成任务");
                return ReturnT.SUCCESS;
            }

            log.info("发现 {} 个高优先级生成任务", highPriorityTasks.size());

            // 异步处理高优先级任务
            int processedCount = 0;
            for (AiImageTaskQueuePo task : highPriorityTasks) {
                if (processedCount >= status.getAvailable()) {
                    break;
                }

                CompletableFuture.runAsync(() -> {
                    try {
                        imageGenerationService.processImageTask(String.valueOf(task.getId()));
                    } catch (Exception e) {
                        log.error("异步处理高优先级图片生成任务失败: {}", task.getId(), e);
                    }
                }, taskExecutor);

                processedCount++;
            }

            log.info("高优先级生成任务调度完成，提交了 {} 个任务", processedCount);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("高优先级生成任务调度失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "高优先级生成任务调度失败: " + e.getMessage());
        }
    }

    /**
     * 获取编辑任务的系统状态（只包含编辑任务）
     */
    private DatabaseConcurrencyControlService.ConcurrencyStatus getEditTaskStatus() {
        try {
            // 查询当前正在处理的编辑任务数量
            LambdaQueryWrapper<AiImageTaskQueuePo> processingQuery = new LambdaQueryWrapper<>();
            processingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue())
                          .in(AiImageTaskQueuePo::getTaskType,
                              TaskType.EDIT.getValue(),
                              TaskType.EDIT_CANVAS.getValue());
            int processingCount = Math.toIntExact(taskQueueMapper.selectCount(processingQuery));

            // 查询待处理的编辑任务数量
            LambdaQueryWrapper<AiImageTaskQueuePo> pendingQuery = new LambdaQueryWrapper<>();
            pendingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                       .in(AiImageTaskQueuePo::getTaskType,
                           TaskType.EDIT.getValue(),
                           TaskType.EDIT_CANVAS.getValue());
            int pendingCount = Math.toIntExact(taskQueueMapper.selectCount(pendingQuery));

            // 编辑任务可以有独立的并发数配置，这里先使用相同的配置
            int maxConcurrent = config.getMaxConcurrentTasks();
            int available = Math.max(0, maxConcurrent - processingCount);

            return new DatabaseConcurrencyControlService.ConcurrencyStatus(
                maxConcurrent, processingCount, available, pendingCount);

        } catch (Exception e) {
            log.error("获取编辑任务系统状态失败", e);
            // 返回保守的状态
            int maxConcurrent = config.getMaxConcurrentTasks();
            return new DatabaseConcurrencyControlService.ConcurrencyStatus(maxConcurrent, 0, maxConcurrent, 0);
        }
    }

    /**
     * 获取生成任务的系统状态（排除编辑任务）
     */
    private DatabaseConcurrencyControlService.ConcurrencyStatus getGenerationTaskStatus() {
        try {
            // 查询当前正在处理的生成任务数量（排除编辑任务）
            LambdaQueryWrapper<AiImageTaskQueuePo> processingQuery = new LambdaQueryWrapper<>();
            processingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue())
                          .notIn(AiImageTaskQueuePo::getTaskType,
                                 TaskType.EDIT.getValue(),
                                 TaskType.EDIT_CANVAS.getValue());
            int processingCount = Math.toIntExact(taskQueueMapper.selectCount(processingQuery));

            // 查询待处理的生成任务数量
            LambdaQueryWrapper<AiImageTaskQueuePo> pendingQuery = new LambdaQueryWrapper<>();
            pendingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                       .notIn(AiImageTaskQueuePo::getTaskType,
                              TaskType.EDIT.getValue(),
                              TaskType.EDIT_CANVAS.getValue());
            int pendingCount = Math.toIntExact(taskQueueMapper.selectCount(pendingQuery));

            // 获取最大并发数（生成任务独立的并发控制）
            int maxConcurrent = config.getMaxConcurrentTasks();
            int available = Math.max(0, maxConcurrent - processingCount);

            return new DatabaseConcurrencyControlService.ConcurrencyStatus(
                maxConcurrent, processingCount, available, pendingCount);

        } catch (Exception e) {
            log.error("获取生成任务系统状态失败", e);
            // 返回保守的状态
            int maxConcurrent = config.getMaxConcurrentTasks();
            return new DatabaseConcurrencyControlService.ConcurrencyStatus(maxConcurrent, 0, maxConcurrent, 0);
        }
    }

    /**
     * 获取待处理的生成任务
     * 按全局队列位置排序，优先处理早提交的任务
     * 过滤掉EDIT和CANVAS_EDIT类型的任务
     */
    private List<AiImageTaskQueuePo> getPendingGenerationTasks(int limit) {
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                   .notIn(AiImageTaskQueuePo::getTaskType,
                          TaskType.EDIT.getValue(),
                          TaskType.EDIT_CANVAS.getValue())
                   .orderByAsc(AiImageTaskQueuePo::getGlobalQueuePosition)
                   .last("LIMIT " + limit);

        return taskQueueMapper.selectList(queryWrapper);
    }

    /**
     * 获取高优先级生成任务
     * 优先处理重试次数少的任务，然后按时间排序
     * 过滤掉EDIT和CANVAS_EDIT类型的任务
     */
    private List<AiImageTaskQueuePo> getHighPriorityGenerationTasks(int limit) {
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                   .notIn(AiImageTaskQueuePo::getTaskType,
                          TaskType.EDIT.getValue(),
                          TaskType.EDIT_CANVAS.getValue())
                   .orderByAsc(AiImageTaskQueuePo::getRetryCount) // 重试次数少的优先
                   .orderByAsc(AiImageTaskQueuePo::getCreateTime) // 然后按创建时间
                   .last("LIMIT " + limit);

        return taskQueueMapper.selectList(queryWrapper);
    }

    /**
     * 获取编辑类型任务
     * 专门处理EDIT和CANVAS_EDIT类型的任务
     */
    private List<AiImageTaskQueuePo> getEditTasks(int limit) {
        LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                   .in(AiImageTaskQueuePo::getTaskType,
                       TaskType.EDIT.getValue(),
                       TaskType.EDIT_CANVAS.getValue())
                   .orderByAsc(AiImageTaskQueuePo::getRetryCount) // 重试次数少的优先
                   .orderByAsc(AiImageTaskQueuePo::getCreateTime) // 然后按创建时间
                   .last("LIMIT " + limit);

        return taskQueueMapper.selectList(queryWrapper);
    }

    /**
     * 任务统计和监控
     * 每5分钟执行一次
     */
    @XxlJob("imageTaskStatistics")
    public ReturnT<String> generateTaskStatistics(String param) {
        try {
            // 统计各状态任务数量
            LambdaQueryWrapper<AiImageTaskQueuePo> pendingQuery = new LambdaQueryWrapper<>();
            pendingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue());
            long pendingCount = taskQueueMapper.selectCount(pendingQuery);

            // 统计待处理的生成任务（排除编辑任务）
            LambdaQueryWrapper<AiImageTaskQueuePo> pendingGenerateQuery = new LambdaQueryWrapper<>();
            pendingGenerateQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                               .notIn(AiImageTaskQueuePo::getTaskType,
                                      TaskType.EDIT.getValue(),
                                      TaskType.EDIT_CANVAS.getValue());
            long pendingGenerateCount = taskQueueMapper.selectCount(pendingGenerateQuery);

            // 统计待处理的编辑任务
            LambdaQueryWrapper<AiImageTaskQueuePo> pendingEditQuery = new LambdaQueryWrapper<>();
            pendingEditQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PENDING.getValue())
                           .in(AiImageTaskQueuePo::getTaskType,
                               TaskType.EDIT.getValue(),
                               TaskType.EDIT_CANVAS.getValue());
            long pendingEditCount = taskQueueMapper.selectCount(pendingEditQuery);

            LambdaQueryWrapper<AiImageTaskQueuePo> processingQuery = new LambdaQueryWrapper<>();
            processingQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.PROCESSING.getValue());
            long processingCount = taskQueueMapper.selectCount(processingQuery);

            LambdaQueryWrapper<AiImageTaskQueuePo> completedQuery = new LambdaQueryWrapper<>();
            completedQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.COMPLETED.getValue());
            long completedCount = taskQueueMapper.selectCount(completedQuery);

            LambdaQueryWrapper<AiImageTaskQueuePo> failedQuery = new LambdaQueryWrapper<>();
            failedQuery.eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.FAILED.getValue());
            long failedCount = taskQueueMapper.selectCount(failedQuery);

            // 获取线程池状态
            String threadPoolStatus = String.format(
                "ThreadPool[Active: %d, Pool: %d, Queue: %d]",
                taskExecutor.getActiveCount(),
                taskExecutor.getPoolSize(),
                taskExecutor.getQueue().size()
            );

            String statistics = String.format(
                "图片任务统计 - 待处理: %d (生成: %d, 编辑: %d), 处理中: %d, 已完成: %d, 失败: %d, %s",
                pendingCount, pendingGenerateCount, pendingEditCount, processingCount, completedCount, failedCount, threadPoolStatus
            );

            log.info(statistics);

            // 检查异常情况
            if (pendingCount > 100) {
                log.warn("待处理任务数量过多: {} (生成: {}, 编辑: {}), 建议检查系统负载",
                    pendingCount, pendingGenerateCount, pendingEditCount);
            }

            if (pendingEditCount > 50) {
                log.warn("待处理编辑任务数量过多: {}, 建议检查编辑任务调度器", pendingEditCount);
            }

            if (processingCount > 10) {
                log.warn("处理中任务数量异常: {}, 可能存在任务堵塞", processingCount);
            }

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("生成任务统计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务统计失败: " + e.getMessage());
        }
    }

    /**
     * 清理历史任务数据
     * 每天凌晨2点执行一次
     */
    @XxlJob("cleanupHistoryTasks")
    public ReturnT<String> cleanupHistoryTasks(String param) {
        try {
            log.info("开始清理历史任务数据...");
            
            // 这里可以添加清理逻辑，比如删除30天前的已完成任务
            // 具体实现根据业务需求来定
            
            log.info("历史任务数据清理完成");
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("清理历史任务数据失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "清理历史数据失败: " + e.getMessage());
        }
    }
}
