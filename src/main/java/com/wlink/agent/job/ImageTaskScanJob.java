package com.wlink.agent.job;


import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.DoubaoImageEditApiClient;
import com.wlink.agent.client.model.doubao.DoubaoImageEditRequest;
import com.wlink.agent.config.XxlJobConfig;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.mq.ImageTaskMQHandler;
import com.wlink.agent.service.ImageTaskQueueService;
import com.wlink.agent.service.VendorAccountService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 图片任务扫描定时任务
 * 定时扫描待处理的图片任务并发送到MQ
 */
@Slf4j
@Component
public class ImageTaskScanJob {


    @Autowired
    private AiImageTaskQueueMapper imageTaskQueueMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private XxlJobConfig xxlJobConfig;

    @Autowired
    private VendorAccountService vendorAccountService;

    @Autowired
    private ImageTaskQueueService imageTaskQueueService;

    @Autowired
    private ImageTaskMQHandler imageTaskMQHandler;

    @Autowired
    private DoubaoImageEditApiClient doubaoImageEditApiClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    // 任务状态和任务锁定前缀常量
    private static final String TASK_STATUS_PENDING = "PENDING";
    private static final String TASK_STATUS_PROCESSING = "PROCESSING";
    private static final String TASK_LOCK_PREFIX = "task-execution-lock:";

    /**
     * 扫描并发送待处理的图片任务
     * <p>
     * 参数格式示例：
     * - 不带参数：扫描所有待处理任务，使用默认状态"PENDING"
     * - 带任务状态：直接输入状态值如"PENDING"、"PROCESSING"等
     *
     * @param param 任务参数，可以通过XXL-Job管理界面配置
     * @return 执行结果
     */
    @XxlJob("imageTaskScanJobHandler")
    public ReturnT<String> execute(String param) {
        log.info("开始执行图片任务扫描定时任务，参数: {}", param);
        param = XxlJobHelper.getJobParam();
        try {
            // 默认使用PENDING状态
            String taskStatus = "PROCESSING";

            // 解析参数 - 直接将param作为taskStatus
            if (StringUtils.isNotBlank(param)) {
                log.info("使用输入的任务状态: {}", param);
                taskStatus = param.trim();
            } else {
                log.info("未提供参数或参数为空，使用默认任务状态: {}", taskStatus);
            }

            try {
                // 调用MQ处理器的扫描方法，传入任务状态
                log.info("扫描任务状态为 [{}] 的任务", taskStatus);
            } catch (Exception e) {
                log.error("调用scanAndSendPendingTasks方法异常: {}", e.getMessage(), e);
                throw new BizException("调用扫描方法失败: " + e.getMessage());
            }

            log.info("图片任务扫描定时任务执行完成");
            return ReturnT.SUCCESS;
        } catch (BizException e) {
            log.error("图片任务扫描定时任务执行异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("图片任务扫描定时任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 执行CANVAS_EDIT类型的图像编辑任务
     * <p>
     * 流程:
     * 1. 查询最近5条CANVAS_EDIT类型的待处理任务
     * 2. 异步执行每个任务，调用DoubaoImageEditApiClient
     * 3. 更新任务状态
     *
     * @return 执行结果
     */
    @XxlJob("singleTaskExecuteJobHandler")
    public ReturnT<String> executeSingleTask() {
        log.info("开始执行CANVAS_EDIT类型的图像编辑任务");

        try {
            // 1. 查询最近5条CANVAS_EDIT类型的待处理任务
            LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TASK_STATUS_PENDING)
                    .and(wrapper -> wrapper.eq(AiImageTaskQueuePo::getTaskType, TaskType.EDIT_CANVAS.getValue())
                            .or().eq(AiImageTaskQueuePo::getTaskType, TaskType.EDIT.getValue()))
                    .orderByAsc(AiImageTaskQueuePo::getGlobalQueuePosition)
                    .last("limit 5"); // 取最近5条记录

            List<AiImageTaskQueuePo> pendingTasks = imageTaskQueueMapper.selectList(queryWrapper);
            if (pendingTasks.isEmpty()) {
                log.info("没有待处理的CANVAS_EDIT任务");
                return ReturnT.SUCCESS;
            }

            log.info("获取到{}个待处理的CANVAS_EDIT任务", pendingTasks.size());

            // 2. 异步执行每个任务
            for (AiImageTaskQueuePo task : pendingTasks) {
                taskExecutor.execute(() -> processCanvasEditTask(task));
            }

            log.info("已提交{}个CANVAS_EDIT任务到异步执行线程池", pendingTasks.size());
            return ReturnT.SUCCESS;
        } catch (BizException e) {
            log.error("执行单个任务处理时发生业务异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("执行单个任务处理时发生系统异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 根据自定义条件扫描和发送任务
     *
     * @param taskStatus 任务状态
     * @param taskType   任务类型
     */
    private void scanAndSendTasksByCondition(String taskStatus, String taskType) {
        log.info("根据条件扫描任务，状态: {}, 类型: {}", taskStatus, taskType);

        try {
            // 构建查询条件
            LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, taskStatus);
            if (StringUtils.isNotBlank(taskType)) {
                queryWrapper.eq(AiImageTaskQueuePo::getTaskType, taskType);
            }
            queryWrapper.orderByAsc(AiImageTaskQueuePo::getGlobalQueuePosition);

            // 查询符合条件的任务
            List<AiImageTaskQueuePo> tasks = imageTaskQueueMapper.selectList(queryWrapper);

            if (tasks.isEmpty()) {
                log.info("未找到符合条件的任务（状态: {}, 类型: {}）", taskStatus, taskType);
                return;
            }

            int count = 0;
            for (AiImageTaskQueuePo task : tasks) {
                // 检查任务是否已在处理中
                String lockKey = "pending-check:" + task.getId();
                RLock checkLock = redissonClient.getLock(lockKey);

                if (checkLock.tryLock(5, 5, TimeUnit.SECONDS)) {
                    try {
                        // 根据任务类型发送不同标签的消息
//                        if ("GENERATE".equals(task.getTaskType())) {
//                            imageTaskMQHandler.sendGenerateTaskMessage(task.getId());
//                        } else if ("RETAIN".equals(task.getTaskType())) {
//                            imageTaskMQHandler.sendRetainTaskMessage(task.getId());
//                        }
                        count++;
                    } finally {
                        checkLock.unlock();
                    }
                }
            }

            log.info("已发送 {} 个任务到RocketMQ", count);
        } catch (Exception e) {
            log.error("扫描任务异常", e);
            throw new BizException("扫描任务异常: " + e.getMessage());
        }
    }

    /**
     * 检查并重置超时任务
     * <p>
     * 检查状态为PROCESSING但执行时间超过1分钟的任务，并重置任务状态为PENDING，并解锁账号
     *
     * @return 执行结果
     */
//    @XxlJob("checkTimeoutTasksJobHandler")
    public ReturnT<String> checkTimeoutTasks() {
        log.info("开始检查并重置超时任务");

        try {
            // 1. 查询所有PROCESSING状态的任务
            LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiImageTaskQueuePo::getTaskStatus, TASK_STATUS_PROCESSING);
            List<AiImageTaskQueuePo> processingTasks = imageTaskQueueMapper.selectList(queryWrapper);

            if (processingTasks.isEmpty()) {
                log.info("没有正在处理的任务");
                return ReturnT.SUCCESS;
            }

            log.info("找到 {} 个正在处理的任务，检查是否超时", processingTasks.size());

            // 当前时间
            long currentTimeMillis = System.currentTimeMillis();
            // 超时时间（10分钟）
            long timeoutMillis = 60 * 1000;
            int resetCount = 0;

            for (AiImageTaskQueuePo task : processingTasks) {
                // 获取任务更新时间
                Date updateTime = task.getUpdateTime();
                if (updateTime == null) {
                    log.warn("任务 {} 没有更新时间，跳过检查", task.getId());
                    continue;
                }

                // 计算任务已执行时间
                long taskExecutionTime = currentTimeMillis - updateTime.getTime();

                // 如果执行时间超过1分钟，重置任务状态为PENDING，并解锁账号
                if (taskExecutionTime > timeoutMillis) {
                    log.info("任务 {} 执行时间超过1分钟，重置任务状态为PENDING，并解锁账号", task.getId());

                    // 解锁账号（如果已分配）
                    if (task.getVendorAccountId() != null) {
                        try {
                            log.info("解锁账号 {} 并递减任务数", task.getVendorAccountId());
                            // 递减账号正在执行的任务数
                            vendorAccountService.decreaseRunningTaskCount(task.getVendorAccountId());
                        } catch (Exception e) {
                            log.error("解锁账号 {} 或递减任务数时发生异常", task.getVendorAccountId(), e);
                        }
                    }

                    // 重置任务状态为PENDING，并移除账号
                    task.setTaskStatus(TASK_STATUS_PENDING);
                    task.setVendorAccountId(null);
                    imageTaskQueueMapper.updateById(task);
                    resetCount++;
                }
            }

            log.info("重置了 {} 个超时任务", resetCount);
            return ReturnT.SUCCESS;
        } catch (BizException e) {
            log.error("检查并重置超时任务时发生业务异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("检查并重置超时任务时发生系统异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 处理单个CANVAS_EDIT任务
     *
     * @param task 待处理的任务
     */
    private void processCanvasEditTask(AiImageTaskQueuePo task) {
        log.info("开始处理CANVAS_EDIT任务: taskId={}, requestParams={}", task.getId(), task.getRequestParams());

        try {
            // 1. 更新任务状态为处理中
            imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.PROCESSING.getValue(), null, null, null);

            // 2. 解析请求参数
            DoubaoImageEditRequest editRequest = parseRequestParams(task.getRequestParams());
            if (editRequest == null) {
                log.error("解析请求参数失败: taskId={}, requestParams={}", task.getId(), task.getRequestParams());
                imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), null, "解析请求参数失败", null);
                return;
            }

            // 3. 调用豆包图像编辑API
            com.wlink.agent.client.model.volcengine.ImageGenerateRes editResult = doubaoImageEditApiClient.editImage(editRequest);

            // 4. 处理结果
            if (editResult != null && editResult.getImageUrl() != null && !editResult.getImageUrl().trim().isEmpty()) {
                // 成功
                log.info("CANVAS_EDIT任务处理成功: taskId={}, imageUrl={}", task.getId(), editResult.getImageUrl());
                imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.COMPLETED.getValue(), editResult, null, null);
            } else {
                // 失败
                String errorMessage = editResult != null ? editResult.getMessage() : "豆包图像编辑API返回结果为空";
                log.error("CANVAS_EDIT任务处理失败: taskId={}, error={}", task.getId(), errorMessage);
                imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), editResult, errorMessage, null);
            }

        } catch (Exception e) {
            log.error("处理CANVAS_EDIT任务时发生异常: taskId={}", task.getId(), e);
            imageTaskQueueService.updateTaskStatus(task.getId(), TaskStatus.FAILED.getValue(), null, "处理任务时发生异常: " + e.getMessage(), null);
        }
    }

    /**
     * 解析请求参数为DoubaoImageEditRequest
     *
     * @param requestParams JSON格式的请求参数
     * @return 解析后的请求对象
     */
    private DoubaoImageEditRequest parseRequestParams(String requestParams) {
        try {
            if (requestParams == null || requestParams.trim().isEmpty()) {
                log.error("请求参数为空");
                return null;
            }

            // 尝试直接解析为DoubaoImageEditRequest
            DoubaoImageEditRequest request = objectMapper.readValue(requestParams, DoubaoImageEditRequest.class);

            // 验证必要字段
            if (request.getModel() == null || request.getModel().trim().isEmpty()) {
                log.warn("请求参数中model字段为空，使用默认值");
                request.setModel("doubao-seededit-3-0-i2i-250628");
            }

            if (request.getPrompt() == null || request.getPrompt().trim().isEmpty()) {
                log.error("请求参数中prompt字段为空");
                return null;
            }

            if (request.getImage() == null || request.getImage().trim().isEmpty()) {
                log.error("请求参数中image字段为空");
                return null;
            }

            // 设置默认值
            if (request.getResponseFormat() == null) {
                request.setResponseFormat("url");
            }
            if (request.getSize() == null) {
                request.setSize("adaptive");
            }
            if (request.getSeed() == null) {
                request.setSeed(-1);
            }
            if (request.getGuidanceScale() == null) {
                request.setGuidanceScale(5.5);
            }
            if (request.getWatermark() == null) {
                request.setWatermark(true);
            }

            log.info("成功解析请求参数: model={}, prompt={}, image={}",
                    request.getModel(), request.getPrompt(),
                    request.getImage().length() > 100 ? request.getImage().substring(0, 100) + "..." : request.getImage());

            return request;

        } catch (Exception e) {
            log.error("解析请求参数时发生异常: requestParams={}", requestParams, e);
            return null;
        }
    }
}