package com.wlink.agent.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiCanvasVideoPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.enums.ResourceStatus;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 画布分镜状态同步定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CanvasShotStatusSyncJob {
    
    private final AiCanvasShotMapper canvasShotMapper;
    private final AiCanvasMapper canvasMapper;
    private final AiImageTaskQueueMapper imageTaskQueueMapper;
    private final AiVideoGenerationMapper videoGenerationMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final AiCanvasVideoMapper canvasVideoMapper;
    
    /**
     * 超时时间：5分钟
     */
    private static final long TIMEOUT_MINUTES = 5;
    private static final long TIMEOUT_MILLIS = TIMEOUT_MINUTES * 60 * 1000;
    
    /**
     * 每5分钟执行一次状态同步
     */
    @XxlJob("syncCanvasShotStatusJobHandler")// 5分钟
    public ReturnT<String> syncCanvasShotStatus() {
        log.info("开始执行画布分镜状态同步任务");
        
        try {
            // 查询状态为1（处理中）且更新时间超过5分钟的分镜记录
            List<AiCanvasShotPo> timeoutShots = getTimeoutProcessingShots();
            
            if (timeoutShots.isEmpty()) {
                log.info("没有需要同步状态的分镜记录");
                return ReturnT.SUCCESS;
            }
            
            log.info("找到 {} 个需要同步状态的分镜记录", timeoutShots.size());
            
            for (AiCanvasShotPo shot : timeoutShots) {
                try {
                    syncSingleShotStatus(shot);
                } catch (Exception e) {
                    log.error("同步分镜状态失败: shotId={}, shotCode={}, type={}", 
                            shot.getId(), shot.getCode(), shot.getType(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("画布分镜状态同步任务执行失败", e);
            return ReturnT.FAIL;
        }
        
        log.info("画布分镜状态同步任务执行完成");
        return ReturnT.SUCCESS;
    }
    
    /**
     * 获取超时的处理中分镜记录
     */
    private List<AiCanvasShotPo> getTimeoutProcessingShots() {
        Date timeoutThreshold = new Date(System.currentTimeMillis() - TIMEOUT_MILLIS);
        
        LambdaQueryWrapper<AiCanvasShotPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasShotPo::getShotStatus, 1) // 状态为1（处理中）
                .lt(AiCanvasShotPo::getUpdateTime, timeoutThreshold) // 更新时间超过5分钟
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .orderByAsc(AiCanvasShotPo::getUpdateTime);
        
        return canvasShotMapper.selectList(queryWrapper);
    }
    
    /**
     * 同步单个分镜状态
     */
    private void syncSingleShotStatus(AiCanvasShotPo shot) {
        log.debug("开始同步分镜状态: shotId={}, shotCode={}, type={}", 
                shot.getId(), shot.getCode(), shot.getType());
        
        if ("image".equals(shot.getType())) {
            syncImageShotStatus(shot);
        } else if ("video".equals(shot.getType())) {
            syncVideoShotStatus(shot);
        } else {
            log.warn("未知的分镜类型: shotId={}, type={}", shot.getId(), shot.getType());
        }
    }
    
    /**
     * 同步图片分镜状态
     */
    private void syncImageShotStatus(AiCanvasShotPo shot) {
        try {
            // 1. 根据画布ID获取画布code
            AiCanvasPo canvas = canvasMapper.selectById(shot.getCanvasId());
            if (canvas == null) {
                log.error("画布不存在: canvasId={}", shot.getCanvasId());
                return;
            }
            
            // 2. 查询图片任务状态
            LambdaQueryWrapper<AiImageTaskQueuePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiImageTaskQueuePo::getSessionId, canvas.getCode())
                    .eq(AiImageTaskQueuePo::getContentId, shot.getCode())
                    .eq(AiImageTaskQueuePo::getDelFlag, 0)
                    .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                    .last("limit 1");
            
            AiImageTaskQueuePo imageTask = imageTaskQueueMapper.selectOne(queryWrapper);
            if (imageTask == null) {
                log.warn("未找到对应的图片任务，标记为失败: canvasCode={}, shotCode={}", canvas.getCode(), shot.getCode());
                // 找不到任务就认为失败，创建一个虚拟的失败任务对象
                AiImageTaskQueuePo failedTask = new AiImageTaskQueuePo();
                failedTask.setTaskStatus("FAILED");
                failedTask.setErrorReason("未找到对应的图片生成任务");
                updateImageShotToFailed(shot, failedTask);
                return;
            }

            String taskStatus = imageTask.getTaskStatus();
            log.debug("图片任务状态: shotId={}, taskStatus={}", shot.getId(), taskStatus);

            // 3. 根据任务状态更新分镜和图片记录
            if ("COMPLETED".equals(taskStatus)) {
                updateImageShotToSuccess(shot, imageTask);
            } else if ("FAILED".equals(taskStatus)) {
                updateImageShotToFailed(shot, imageTask);
            } else {
                log.debug("图片任务仍在处理中，跳过更新: shotId={}, taskStatus={}", shot.getId(), taskStatus);
            }
            
        } catch (Exception e) {
            log.error("同步图片分镜状态失败: shotId={}", shot.getId(), e);
        }
    }
    
    /**
     * 同步视频分镜状态
     */
    private void syncVideoShotStatus(AiCanvasShotPo shot) {
        try {
            // 1. 查询视频生成任务状态
            LambdaQueryWrapper<AiVideoGenerationPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiVideoGenerationPo::getShotId, shot.getId())
                    .eq(AiVideoGenerationPo::getDelFlag, 0)
                    .orderByDesc(AiVideoGenerationPo::getCreateTime)
                    .last("limit 1");
            
            AiVideoGenerationPo videoGeneration = videoGenerationMapper.selectOne(queryWrapper);
            if (videoGeneration == null) {
                log.warn("未找到对应的视频生成任务，标记为失败: shotId={}", shot.getId());
                // 找不到任务就认为失败，创建一个虚拟的失败任务对象
                AiVideoGenerationPo failedGeneration = new AiVideoGenerationPo();
                failedGeneration.setStatus(3); // 3-失败
                failedGeneration.setErrorMessage("未找到对应的视频生成任务");
                updateVideoShotToFailed(shot, failedGeneration);
                return;
            }

            Integer generationStatus = videoGeneration.getStatus();
            log.debug("视频生成任务状态: shotId={}, status={}", shot.getId(), generationStatus);

            // 2. 根据生成状态更新分镜和视频记录
            if (Integer.valueOf(2).equals(generationStatus)) { // 2-处理完成
                updateVideoShotToSuccess(shot, videoGeneration);
            } else if (Integer.valueOf(3).equals(generationStatus)) { // 3-处理失败
                updateVideoShotToFailed(shot, videoGeneration);
            } else {
                log.debug("视频生成任务仍在处理中，跳过更新: shotId={}, status={}", shot.getId(), generationStatus);
            }
            
        } catch (Exception e) {
            log.error("同步视频分镜状态失败: shotId={}", shot.getId(), e);
        }
    }
    
    /**
     * 更新图片分镜为成功状态
     */
    private void updateImageShotToSuccess(AiCanvasShotPo shot, AiImageTaskQueuePo imageTask) {
        Date now = new Date();
        
        // 1. 更新分镜状态
        LambdaUpdateWrapper<AiCanvasShotPo> shotUpdateWrapper = new LambdaUpdateWrapper<>();
        shotUpdateWrapper.eq(AiCanvasShotPo::getId, shot.getId())
                .set(AiCanvasShotPo::getShotStatus, 2) // 成功
                .set(AiCanvasShotPo::getUpdateTime, now);
        canvasShotMapper.update(null, shotUpdateWrapper);
        
        // 2. 更新画布图片状态
        LambdaUpdateWrapper<AiCanvasImagePo> imageUpdateWrapper = new LambdaUpdateWrapper<>();
        imageUpdateWrapper.eq(AiCanvasImagePo::getShotCode, shot.getCode())
                .set(AiCanvasImagePo::getImageStatus, ResourceStatus.SUCCESS.getValue())
                .set(AiCanvasImagePo::getUpdateTime, now);
        
        // 如果有图片结果，也一并更新
        if (imageTask.getImageResult() != null) {
            imageUpdateWrapper.set(AiCanvasImagePo::getImageUrl, imageTask.getImageResult());
        }
        
        canvasImageMapper.update(null, imageUpdateWrapper);
        
        log.info("图片分镜状态更新为成功: shotId={}, shotCode={}", shot.getId(), shot.getCode());
    }
    
    /**
     * 更新图片分镜为失败状态
     */
    private void updateImageShotToFailed(AiCanvasShotPo shot, AiImageTaskQueuePo imageTask) {
        Date now = new Date();
        
        // 1. 更新分镜状态
        LambdaUpdateWrapper<AiCanvasShotPo> shotUpdateWrapper = new LambdaUpdateWrapper<>();
        shotUpdateWrapper.eq(AiCanvasShotPo::getId, shot.getId())
                .set(AiCanvasShotPo::getShotStatus, 3) // 失败
                .set(AiCanvasShotPo::getUpdateTime, now);
        canvasShotMapper.update(null, shotUpdateWrapper);
        
        // 2. 更新画布图片状态
        LambdaUpdateWrapper<AiCanvasImagePo> imageUpdateWrapper = new LambdaUpdateWrapper<>();
        imageUpdateWrapper.eq(AiCanvasImagePo::getShotCode, shot.getCode())
                .set(AiCanvasImagePo::getImageStatus, ResourceStatus.FAILED.getValue())
                .set(AiCanvasImagePo::getUpdateTime, now);
        
        canvasImageMapper.update(null, imageUpdateWrapper);
        
        log.info("图片分镜状态更新为失败: shotId={}, shotCode={}, error={}", 
                shot.getId(), shot.getCode(), imageTask.getErrorReason());
    }
    
    /**
     * 更新视频分镜为成功状态
     */
    private void updateVideoShotToSuccess(AiCanvasShotPo shot, AiVideoGenerationPo videoGeneration) {
        Date now = new Date();
        
        // 1. 更新分镜状态
        LambdaUpdateWrapper<AiCanvasShotPo> shotUpdateWrapper = new LambdaUpdateWrapper<>();
        shotUpdateWrapper.eq(AiCanvasShotPo::getId, shot.getId())
                .set(AiCanvasShotPo::getShotStatus, 2) // 成功
                .set(AiCanvasShotPo::getUpdateTime, now);
        canvasShotMapper.update(null, shotUpdateWrapper);
        
        // 2. 更新画布视频状态
        LambdaUpdateWrapper<AiCanvasVideoPo> videoUpdateWrapper = new LambdaUpdateWrapper<>();
        videoUpdateWrapper.eq(AiCanvasVideoPo::getShotCode, shot.getCode())
                .set(AiCanvasVideoPo::getVideoStatus, ResourceStatus.SUCCESS.getValue())
                .set(AiCanvasVideoPo::getUpdateTime, now);
        
        // 如果有视频URL，也一并更新
        if (videoGeneration.getVideoUrl() != null) {
            videoUpdateWrapper.set(AiCanvasVideoPo::getVideoUrl, videoGeneration.getVideoUrl());
        }
        
        canvasVideoMapper.update(null, videoUpdateWrapper);
        
        log.info("视频分镜状态更新为成功: shotId={}, shotCode={}", shot.getId(), shot.getCode());
    }
    
    /**
     * 更新视频分镜为失败状态
     */
    private void updateVideoShotToFailed(AiCanvasShotPo shot, AiVideoGenerationPo videoGeneration) {
        Date now = new Date();
        
        // 1. 更新分镜状态
        LambdaUpdateWrapper<AiCanvasShotPo> shotUpdateWrapper = new LambdaUpdateWrapper<>();
        shotUpdateWrapper.eq(AiCanvasShotPo::getId, shot.getId())
                .set(AiCanvasShotPo::getShotStatus, 3) // 失败
                .set(AiCanvasShotPo::getUpdateTime, now);
        canvasShotMapper.update(null, shotUpdateWrapper);
        
        // 2. 更新画布视频状态
        LambdaUpdateWrapper<AiCanvasVideoPo> videoUpdateWrapper = new LambdaUpdateWrapper<>();
        videoUpdateWrapper.eq(AiCanvasVideoPo::getShotCode, shot.getCode())
                .set(AiCanvasVideoPo::getVideoStatus,ResourceStatus.FAILED.getValue())
                .set(AiCanvasVideoPo::getUpdateTime, now);
        
        canvasVideoMapper.update(null, videoUpdateWrapper);
        
        log.info("视频分镜状态更新为失败: shotId={}, shotCode={}, error={}", 
                shot.getId(), shot.getCode(), videoGeneration.getErrorMessage());
    }
}
