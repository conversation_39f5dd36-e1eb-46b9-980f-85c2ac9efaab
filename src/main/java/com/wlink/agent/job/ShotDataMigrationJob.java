package com.wlink.agent.job;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.model.req.ShotSaveReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 分镜数据迁移定时任务
 * 定时将旧格式的分镜数据转换为新表结构(ai_chapter和ai_shot表)
 */
@Slf4j
@Component
public class ShotDataMigrationJob {

    @Autowired
    private AiCreationContentMapper aiCreationContentMapper;
    
    @Autowired
    private AiCreationSessionMapper aiCreationSessionMapper;
    
    @Autowired
    private AiChapterMapper aiChapterMapper;
    
    @Autowired
    private AiShotMapper aiShotMapper;
    
    /**
     * 分镜数据迁移任务处理
     * 定时将旧格式的分镜数据转换为新表结构(ai_chapter和ai_shot表)
     * 
     * @return 执行结果
     */
    @XxlJob("shotDataMigrationJobHandler")
    public ReturnT<String> migrateHistoricalShotData() {
        log.info("开始执行分镜数据迁移定时任务");
        XxlJobHelper.log("开始执行分镜数据迁移定时任务");
        
        try {
            // 获取分页参数
            String param = XxlJobHelper.getJobParam();
            int pageSize = 100; // 默认每页处理100条
            int batchSize = 500; // 默认每批次处理500条
            
            if (StringUtils.isNotBlank(param)) {
                try {
                    String[] params = param.split(",");
                    if (params.length >= 1) {
                        pageSize = Integer.parseInt(params[0].trim());
                    }
                    if (params.length >= 2) {
                        batchSize = Integer.parseInt(params[1].trim());
                    }
                } catch (Exception e) {
                    log.warn("解析分页参数失败，使用默认参数: pageSize={}, batchSize={}", pageSize, batchSize);
                }
            }
            
            log.info("使用参数: pageSize={}, batchSize={}", pageSize, batchSize);
            XxlJobHelper.log("使用参数: pageSize={}, batchSize={}", pageSize, batchSize);
            
            // 查询所有内容类型为4（分镜内容）的记录
            LambdaQueryWrapper<AiCreationContentPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiCreationContentPo::getContentType, 4)
                    .eq(AiCreationContentPo::getDelFlag, 0)
                    .orderByDesc(AiCreationContentPo::getUpdateTime);
            
            // 查询总记录数
            Long totalCount = aiCreationContentMapper.selectCount(queryWrapper);
            if (totalCount == null || totalCount == 0) {
                String message = "未找到需要迁移的分镜数据";
                log.info(message);
                XxlJobHelper.log(message);
                return ReturnT.SUCCESS;
            }
            
            log.info("找到 {} 条需要迁移的分镜数据", totalCount);
            XxlJobHelper.log("找到 {} 条需要迁移的分镜数据", totalCount);
            
            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            log.info("共分 {} 页处理", totalPages);
            XxlJobHelper.log("共分 {} 页处理", totalPages);
            
            // 统计处理结果
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger skipCount = new AtomicInteger(0);
            
            // 分页处理数据
            for (int page = 0; page < totalPages; page++) {
                int offset = page * pageSize;
                log.info("处理第 {} 页数据，偏移量: {}", page + 1, offset);
                XxlJobHelper.log("处理第 {} 页数据，偏移量: {}", page + 1, offset);
                
                // 分页查询数据
                // 自定义分页查询，因为MyBatis-Plus的分页插件可能没有配置
                queryWrapper.last("LIMIT " + pageSize + " OFFSET " + offset);
                List<AiCreationContentPo> contentList = aiCreationContentMapper.selectList(queryWrapper);
                if (CollectionUtils.isEmpty(contentList)) {
                    continue;
                }
                
                log.info("当前页查询到 {} 条数据", contentList.size());
                XxlJobHelper.log("当前页查询到 {} 条数据", contentList.size());
                
                // 处理每一条数据
                for (int i = 0; i < contentList.size(); i++) {
                    AiCreationContentPo content = contentList.get(i);
                    try {
                        boolean result = processContent(content);
                        if (result) {
                            successCount.incrementAndGet();
                        } else {
                            skipCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        // 遇到异常时记录日志并跳过，继续处理下一条数据
                        skipCount.incrementAndGet();
                        String errorMessage = String.format("处理分镜数据异常，已跳过: contentId=%s, sessionId=%s, error=%s", 
                                content.getId(), content.getSessionId(), e.getMessage());
                        log.error(errorMessage, e);
                        XxlJobHelper.log(errorMessage);
                    }
                    
                    // 每处理指定批次，输出一次日志
                    if ((i + 1) % batchSize == 0 || i == contentList.size() - 1) {
                        String progressMessage = String.format("已处理 %d/%d 页, %d/%d 条, 成功: %d, 跳过: %d", 
                                page + 1, totalPages, offset + i + 1, totalCount, 
                                successCount.get(), skipCount.get());
                        log.info(progressMessage);
                        XxlJobHelper.log(progressMessage);
                    }
                }
            }
            
            String resultMessage = String.format("分镜数据迁移定时任务执行完成，总计: %d, 成功: %d, 跳过: %d", 
                    totalCount, successCount.get(), skipCount.get());
            log.info(resultMessage);
            XxlJobHelper.log(resultMessage);
            
            return ReturnT.SUCCESS;
        } catch (BizException e) {
            String errorMessage = String.format("分镜数据迁移定时任务执行异常: %s", e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, "业务异常: " + e.getMessage());
        } catch (Exception e) {
            String errorMessage = String.format("分镜数据迁移定时任务执行异常: %s", e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, "系统异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理单条分镜内容数据
     * 
     * @param content 分镜内容
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean processContent(AiCreationContentPo content) {
        String sessionId = content.getSessionId();
        String contentData = content.getContentData();
        
        // 检查会话是否存在
        AiCreationSessionPo session = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, sessionId)
                        .eq(AiCreationSessionPo::getDelFlag, 0)
        );
        
        if (session == null) {
            log.warn("会话不存在，跳过处理: sessionId={}", sessionId);
            return false;
        }
        
        // 解析分镜数据
        ShotSaveReq shotSaveReq = null;
        try {
            shotSaveReq = JSON.parseObject(contentData, ShotSaveReq.class);
        } catch (Exception e) {
            log.error("解析分镜数据失败: sessionId={}, error={}", sessionId, e.getMessage());
            // 解析失败时直接返回false，不抛出异常
            return false;
        }
        
        if (shotSaveReq == null || CollectionUtils.isEmpty(shotSaveReq.getShotGroups())) {
            log.warn("分镜数据为空，跳过处理: sessionId={}", sessionId);
            return false;
        }
        
        // 检查是否已经迁移过
        Long existingCount = aiChapterMapper.selectCount(
                new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, sessionId)
        );
        
        if (existingCount != null && existingCount > 0) {
            // 已经迁移过，检查分镜数量是否一致
            Long shotCount = aiShotMapper.selectCount(
                    new LambdaQueryWrapper<AiShotPo>()
                            .eq(AiShotPo::getSessionId, sessionId)
            );
            
            // 计算旧数据中的分镜总数
            int oldShotCount = 0;
            for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
                if (CollectionUtils.isNotEmpty(shotGroup.getShots())) {
                    oldShotCount += shotGroup.getShots().size();
                }
            }
            
            // 如果分镜数量一致，说明已经完全迁移，跳过处理
            if (shotCount != null && shotCount == oldShotCount) {
                log.info("数据已完全迁移，跳过处理: sessionId={}, chapterCount={}, shotCount={}", 
                        sessionId, existingCount, shotCount);
                return false;
            }
            
            // 数量不一致，删除已有数据重新迁移
            log.info("数据迁移不完整，重新迁移: sessionId={}, 旧分镜数={}, 新分镜数={}", 
                    sessionId, oldShotCount, shotCount);
            
            // 删除已有数据
            aiShotMapper.delete(
                    new LambdaQueryWrapper<AiShotPo>()
                            .eq(AiShotPo::getSessionId, sessionId)
            );
            
            aiChapterMapper.delete(
                    new LambdaQueryWrapper<AiChapterPo>()
                            .eq(AiChapterPo::getSessionId, sessionId)
            );
        }
        
        // 开始迁移数据
        Date now = new Date();
        
        // 统计章节信息（按segmentId分组）
        Map<String, Map<String, Object>> chapterMap = new HashMap<>();
        
        // 处理所有分镜组
        for (ShotSaveReq.ShotGroupsDTO shotGroup : shotSaveReq.getShotGroups()) {
            String segmentId = shotGroup.getSegmentId();
            String segmentName = shotGroup.getSegmentName();
            String sceneId = shotGroup.getSceneId();
            String sceneName = shotGroup.getSceneName();
            
            // 收集章节信息
            if (!chapterMap.containsKey(segmentId)) {
                Map<String, Object> chapterInfo = new HashMap<>();
                chapterInfo.put("segmentName", segmentName);
                chapterInfo.put("scenes", new HashMap<String, Boolean>());
                chapterMap.put(segmentId, chapterInfo);
            }
            
            // 记录场景
            @SuppressWarnings("unchecked")
            Map<String, Boolean> scenes = (Map<String, Boolean>) chapterMap.get(segmentId).get("scenes");
            scenes.put(sceneId, true);
            
            // 处理分镜数据
            List<ShotSaveReq.ShotGroupsDTO.ShotsDTO> shots = shotGroup.getShots();
            if (CollectionUtils.isEmpty(shots)) {
                continue;
            }
            
            for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : shots) {
                // 创建分镜记录
                AiShotPo shotPo = new AiShotPo();
                shotPo.setSessionId(sessionId);
                shotPo.setSegmentId(segmentId);
                shotPo.setSceneId(sceneId);
                shotPo.setSceneName(sceneName);
                shotPo.setShotId(shot.getId());
                shotPo.setShotData(JSON.toJSONString(shot));
                shotPo.setDelFlag(0);
                shotPo.setCreateTime(now);
                shotPo.setUpdateTime(now);
                
                // 保存分镜记录
                aiShotMapper.insert(shotPo);
            }
        }
        
        // 保存章节记录
        for (Map.Entry<String, Map<String, Object>> entry : chapterMap.entrySet()) {
            String segmentId = entry.getKey();
            Map<String, Object> chapterInfo = entry.getValue();
            String segmentName = (String) chapterInfo.get("segmentName");
            
            @SuppressWarnings("unchecked")
            Map<String, Boolean> scenes = (Map<String, Boolean>) chapterInfo.get("scenes");
            int sceneCount = scenes.size();
            
            // 创建章节记录
            AiChapterPo chapterPo = new AiChapterPo();
            chapterPo.setSessionId(sessionId);
            chapterPo.setSegmentId(segmentId);
            chapterPo.setSegmentName(segmentName);
            chapterPo.setSceneCount(sceneCount);
            chapterPo.setDelFlag(0);
            chapterPo.setCreateTime(now);
            chapterPo.setUpdateTime(now);
            
            // 保存章节记录
            aiChapterMapper.insert(chapterPo);
        }
        
        log.info("成功迁移分镜数据: sessionId={}, 章节数={}, 分镜组数={}", 
                sessionId, chapterMap.size(), shotSaveReq.getShotGroups().size());
        
        return true;
    }
} 