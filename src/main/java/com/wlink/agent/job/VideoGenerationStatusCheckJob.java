package com.wlink.agent.job;

import com.wlink.agent.client.MiniMaxFileDownloadClient;
import com.wlink.agent.client.MiniMaxVideoApiClient;
import com.wlink.agent.client.VolcengineVideoApiClient;
import com.wlink.agent.client.model.minimax.MiniMaxVideoStatusResponse;
import com.wlink.agent.client.model.volcenginevideo.VolcengineVideoTaskQueryResponse;
import com.wlink.agent.dao.mapper.AiVideoGenerationMapper;
import com.wlink.agent.dao.po.AiVideoGenerationPo;
import com.wlink.agent.service.impl.VideoGenerationQueueServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 视频生成任务状态检查定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoGenerationStatusCheckJob {

    private final AiVideoGenerationMapper videoGenerationMapper;
    private final VideoGenerationQueueServiceImpl videoGenerationQueueService;
    private final VolcengineVideoApiClient volcengineVideoApiClient;
    private final MiniMaxVideoApiClient miniMaxVideoApiClient;
    private final MiniMaxFileDownloadClient miniMaxFileDownloadClient;
    
    /**
     * 任务超时时间（毫秒）- 5分钟
     */
    private static final long TASK_TIMEOUT_MS = 5 * 60 * 1000;

    /**
     * MiniMax模型名称
     */
    private static final String MINIMAX_MODEL = "MiniMax-Hailuo-02";

    /**
     * 定时检查处理中的视频生成任务状态
     * 每1分钟执行一次
     */
    @XxlJob("videoGenerationStatusCheckJobHandler")
    public ReturnT<String> checkVideoGenerationTaskStatus(String param) {
        log.info("开始检查处理中的视频生成任务状态...");
        XxlJobHelper.log("开始检查处理中的视频生成任务状态...");
        
        try {
            // 获取所有处理中且开始时间超过5分钟的任务
            Date timeoutBefore = new Date(System.currentTimeMillis() - TASK_TIMEOUT_MS);
            List<AiVideoGenerationPo> timeoutTasks = videoGenerationMapper.getTimeoutTasks(timeoutBefore);
            log.info("发现{}个处理时间超过5分钟的任务", timeoutTasks.size());
            XxlJobHelper.log("发现{}个处理时间超过5分钟的任务", timeoutTasks.size());
            
            // 检查每个任务的状态
            for (AiVideoGenerationPo task : timeoutTasks) {
                log.info("检查任务状态: taskId={}, generateTaskId={}", task.getId(), task.getGenerateTaskId());
                XxlJobHelper.log("检查任务状态: taskId={}, generateTaskId={}", task.getId(), task.getGenerateTaskId());
                
                // 如果没有生成任务ID，则标记为失败
                if (!StringUtils.hasText(task.getGenerateTaskId())) {
                    log.error("任务没有生成任务ID: taskId={}", task.getId());
                    XxlJobHelper.log("任务没有生成任务ID: taskId={}", task.getId());
                    videoGenerationQueueService.failTask(task.getId(), "任务没有生成任务ID");
                    continue;
                }
                
                try {
                    // 根据模型类型选择不同的处理方式
                    if (MINIMAX_MODEL.equals(task.getModel())) {
                        // 调用MiniMax API查询任务状态
                        MiniMaxVideoStatusResponse response = miniMaxVideoApiClient.getVideoStatus(task.getGenerateTaskId()).get();
                        log.info("MiniMax任务状态查询结果: taskId={}, status={}", task.getId(), response.getStatus());
                        XxlJobHelper.log("MiniMax任务状态查询结果: taskId={}, status={}", task.getId(), response.getStatus());

                        // 处理MiniMax任务状态
                        processMiniMaxTaskStatus(task, response);
                    } else {
                        // 调用火山引擎API查询任务状态
                        VolcengineVideoTaskQueryResponse response = volcengineVideoApiClient.queryVideoGenerationTask(task.getGenerateTaskId());
                        log.info("火山引擎任务状态查询结果: taskId={}, status={}", task.getId(), response.getStatus());
                        XxlJobHelper.log("火山引擎任务状态查询结果: taskId={}, status={}", task.getId(), response.getStatus());

                        // 处理火山引擎任务状态
                        processVolcengineTaskStatus(task, response);
                    }
                } catch (Exception e) {
                    log.error("查询任务状态失败: taskId={}", task.getId(), e);
                    XxlJobHelper.log("查询任务状态失败: taskId={}, 错误: {}", task.getId(), e.getMessage());
                }
            }
            
            log.info("视频生成任务状态检查完成");
            XxlJobHelper.log("视频生成任务状态检查完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("检查视频生成任务状态异常", e);
            XxlJobHelper.log("检查视频生成任务状态异常: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "检查视频生成任务状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理火山引擎任务状态
     *
     * @param task 任务
     * @param response API响应
     */
    private void processVolcengineTaskStatus(AiVideoGenerationPo task, VolcengineVideoTaskQueryResponse response) {
        // 如果任务已完成
        if ("succeeded".equals(response.getStatus())) {
            // 获取视频URL
            String videoUrl = null;
            if (response.getContent() != null) {
                videoUrl = response.getContent().getVideoUrl();
            }
            
            if (StringUtils.hasText(videoUrl)) {
                // 更新任务状态为完成
                videoGenerationQueueService.completeTask(task.getId(), videoUrl);
                log.info("任务已完成: taskId={}, videoUrl={}", task.getId(), videoUrl);
                XxlJobHelper.log("任务已完成: taskId={}, videoUrl={}", task.getId(), videoUrl);
            } else {
                // 没有视频URL，标记为失败
                videoGenerationQueueService.failTask(task.getId(), "任务完成但没有视频URL");
                log.error("任务完成但没有视频URL: taskId={}", task.getId());
                XxlJobHelper.log("任务完成但没有视频URL: taskId={}", task.getId());
            }
        } 
        // 如果任务已失败
        else if ("failed".equals(response.getStatus()) || "canceled".equals(response.getStatus())) {
            // 获取错误信息
            String errorMessage = "任务处理失败";
            if (response.getError() != null) {
                errorMessage = response.getError().getMessage();
            }
            
            // 更新任务状态为失败
            videoGenerationQueueService.failTask(task.getId(), errorMessage);
            log.error("任务处理失败: taskId={}, error={}", task.getId(), errorMessage);
            XxlJobHelper.log("任务处理失败: taskId={}, error={}", task.getId(), errorMessage);
        }
        // 如果任务仍在处理中，不做处理
    }

    /**
     * 处理MiniMax任务状态
     *
     * @param task 任务
     * @param response MiniMax API响应
     */
    private void processMiniMaxTaskStatus(AiVideoGenerationPo task, MiniMaxVideoStatusResponse response) {
        // 如果任务已完成
        if (response.isSuccess()) {
            // 获取文件ID
            String fileId = response.getFileId();

            if (StringUtils.hasText(fileId)) {
                try {
                    // 使用文件ID下载文件并上传到OSS
                    String videoUrl = miniMaxFileDownloadClient.retrieveFileAndUploadToOss(fileId, task.getUserId()).get();

                    // 更新任务状态为完成
                    videoGenerationQueueService.completeTask(task.getId(), videoUrl);
                    log.info("MiniMax任务已完成: taskId={}, fileId={}, videoUrl={}", task.getId(), fileId, videoUrl);
                    XxlJobHelper.log("MiniMax任务已完成: taskId={}, fileId={}, videoUrl={}", task.getId(), fileId, videoUrl);
                } catch (Exception e) {
                    // 文件下载失败，标记任务为失败
                    String errorMessage = "文件下载失败: " + e.getMessage();
                    videoGenerationQueueService.failTask(task.getId(), errorMessage);
                    log.error("MiniMax任务文件下载失败: taskId={}, fileId={}, error={}", task.getId(), fileId, e.getMessage());
                    XxlJobHelper.log("MiniMax任务文件下载失败: taskId={}, fileId={}, error={}", task.getId(), fileId, e.getMessage());
                }
            } else {
                // 没有文件ID，标记为失败
                videoGenerationQueueService.failTask(task.getId(), "任务完成但没有文件ID");
                log.error("MiniMax任务完成但没有文件ID: taskId={}", task.getId());
                XxlJobHelper.log("MiniMax任务完成但没有文件ID: taskId={}", task.getId());
            }
        }
        // 如果任务已失败
        else if (response.isFailed()) {
            // 获取错误信息
            String errorMessage = response.getErrorMessage();
            if (!StringUtils.hasText(errorMessage)) {
                errorMessage = "MiniMax任务处理失败";
            }

            // 更新任务状态为失败
            videoGenerationQueueService.failTask(task.getId(), errorMessage);
            log.error("MiniMax任务处理失败: taskId={}, error={}", task.getId(), errorMessage);
            XxlJobHelper.log("MiniMax任务处理失败: taskId={}, error={}", task.getId(), errorMessage);
        }
        // 如果任务仍在处理中，不做处理
    }
}