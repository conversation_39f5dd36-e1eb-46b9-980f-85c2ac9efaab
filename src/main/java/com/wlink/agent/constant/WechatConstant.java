package com.wlink.agent.constant;

/**
 * 微信开放平台相关常
 */
public class WechatConstant {

    /**
     * 微信登录状
     */
    public static class LoginStatus {
        /**
         * 未扫
         */
        public static final int NOT_SCAN = 0;
        
        /**
         * 已扫码未确认
         */
        public static final int SCANNED = 1;
        
        /**
         * 已确认登
         */
        public static final int CONFIRMED = 2;
        
        /**
         * 已取拒绝
         */
        public static final int CANCELED = 3;
        
        /**
         * 已过
         */
        public static final int EXPIRED = 4;
    }

    /**
     * 微信API URL
     */
    public static class ApiUrl {
        /**
         * 获取二维码的URL
         */
        public static final String QRCONNECT = "https://open.weixin.qq.com/connect/qrconnect";
        
        /**
         * 获取访问令牌的URL
         */
        public static final String ACCESS_TOKEN = "https://api.weixin.qq.com/sns/oauth2/access_token";
        
        /**
         * 刷新访问令牌的URL
         */
        public static final String REFRESH_TOKEN = "https://api.weixin.qq.com/sns/oauth2/refresh_token";
        
        /**
         * 获取用户信息的URL
         */
        public static final String USER_INFO = "https://api.weixin.qq.com/sns/userinfo";
        
        /**
         * 验证访问令牌是否有效的URL
         */
        public static final String CHECK_TOKEN = "https://api.weixin.qq.com/sns/auth";
    }

    /**
     * 微信API参数
     */
    public static class ApiParam {
        public static final String APPID = "appid";
        public static final String SECRET = "secret";
        public static final String CODE = "code";
        public static final String GRANT_TYPE = "grant_type";
        public static final String REDIRECT_URI = "redirect_uri";
        public static final String RESPONSE_TYPE = "response_type";
        public static final String SCOPE = "scope";
        public static final String STATE = "state";
        public static final String ACCESS_TOKEN = "access_token";
        public static final String OPENID = "openid";
        public static final String REFRESH_TOKEN = "refresh_token";
        public static final String LANG = "lang";
    }

    /**
     * 微信API响应字段
     */
    public static class ApiResponse {
        public static final String ACCESS_TOKEN = "access_token";
        public static final String EXPIRES_IN = "expires_in";
        public static final String REFRESH_TOKEN = "refresh_token";
        public static final String OPENID = "openid";
        public static final String SCOPE = "scope";
        public static final String UNIONID = "unionid";
        public static final String ERRCODE = "errcode";
        public static final String ERRMSG = "errmsg";
        public static final String NICKNAME = "nickname";
        public static final String SEX = "sex";
        public static final String PROVINCE = "province";
        public static final String CITY = "city";
        public static final String COUNTRY = "country";
        public static final String HEADIMGURL = "headimgurl";
        public static final String PRIVILEGE = "privilege";
    }

    /**
     * 微信API授权类型
     */
    public static class GrantType {
        public static final String AUTHORIZATION_CODE = "authorization_code";
        public static final String REFRESH_TOKEN = "refresh_token";
    }

    /**
     * 微信API响应类型
     */
    public static class ResponseType {
        public static final String CODE = "code";
    }
} 
