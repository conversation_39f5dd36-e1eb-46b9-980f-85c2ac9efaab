package com.wlink.agent.common.dto; // 假设放在 common.dto 包下

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页响应结果")
public class PageRes<T> {

    @Schema(description = "当前页码")
    private int pageNum;

    @Schema(description = "每页数量")
    private int pageSize;

    @Schema(description = "总记录数")
    private long total;

    @Schema(description = "总页")
    private int totalPages;

    @Schema(description = "当前页数据列")
    private List<T> list;

    // 可以添加一个静态工厂方法方便创
    public static <T> PageRes<T> of(int pageNum, int pageSize, long total, List<T> list) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        return new PageRes<>(pageNum, pageSize, total, totalPages, list);
    }
}
