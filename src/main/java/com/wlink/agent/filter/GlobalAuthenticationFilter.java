package com.wlink.agent.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.config.AuthProperties;
import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.utils.DateUtils;
import com.wlink.agent.utils.I18nMessageUtils;
import com.wlink.agent.utils.JwtUtil;
import com.wlink.agent.utils.UserContext;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StopWatch;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;


import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.charset.UnsupportedCharsetException;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
@Order(1)
@RequiredArgsConstructor
public class GlobalAuthenticationFilter extends OncePerRequestFilter {

    private final AuthProperties authProperties;
    private final JwtUtil jwtUtil;
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;
    private final AiUsersMapper aiUsersMapper;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // Define token expiration consistent with LoginService (or get from AuthProperties)
    private static final long TOKEN_EXPIRATION_DAYS = 7;

    // 日志分类常量
    private static final String LOG_PREFIX_REQUEST = "[REQUEST] ";
    private static final String LOG_PREFIX_RESPONSE = "[RESPONSE] ";
    private static final String LOG_PREFIX_AUTH = "[AUTH] ";
    private static final String LOG_PREFIX_TOKEN = "[TOKEN] ";
    private static final String LOG_PREFIX_USER = "[USER] ";
    private static final String LOG_PREFIX_ERROR = "[ERROR] ";

    // MDC键值常量
    private static final String MDC_TRACE_ID = "traceId";
    private static final String MDC_USER_ID = "userId";
    private static final String MDC_URI = "uri";
    private static final String MDC_METHOD = "method";
    private static final String MDC_CLIENT_IP = "clientIp";

    // 用户状态常量
    private static final int USER_STATUS_INACTIVE = 0;  // 未激活
    private static final int USER_STATUS_ACTIVE = 1;    // 已激活
    private static final int USER_STATUS_DISABLED = 2;  // 已禁用

    // 免检查用户状态的路径
    private static final List<String> STATUS_CHECK_EXCLUDED_PATHS = Arrays.asList(
            "/user/login/activate/*",
            "/user/mobile/bind"
    );

    // 添加请求体大小限制配置常量
    private static final int MAX_REQUEST_BODY_LOG_SIZE = 4096; // 4KB
    private static final int MAX_RESPONSE_BODY_LOG_SIZE = 4096; // 4KB

    /**
     * 获取带有traceId的日志前缀
     * @param prefix 日志类别前缀
     * @return 组合了traceId的前缀
     */
    private String getLogPrefix(String prefix) {
        String traceId = MDC.get(MDC_TRACE_ID);
        if (StringUtils.isNotBlank(traceId)) {
            return "[[" + traceId + "]] " + prefix;
        }
        return prefix;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        // 生成traceId并设置到MDC
        String traceId = UUID.randomUUID().toString().replace("-", "");
        MDC.put(MDC_TRACE_ID, traceId);
        MDC.put(MDC_URI, requestURI);

        // 检查是否是SSE请求，如果是则直接通过过滤器而不包装响应
        if (isSSERequest(request)) {
            log.info("{}{} 检测到SSE请求，跳过响应包装", getLogPrefix(LOG_PREFIX_REQUEST), requestURI);
            // 只处理认证，不包装响应
            if (authenticateRequest(request, response)) {
                filterChain.doFilter(request, response);
            }
            // 请求处理完成后清理MDC
            MDC.clear();
            return;
        }

        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);

        String clientIp = getClientIp(wrappedRequest);
        String method = wrappedRequest.getMethod();

        // 设置更多MDC属性
        MDC.put(MDC_METHOD, method);
        MDC.put(MDC_CLIENT_IP, clientIp);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            // 记录基本请求信息（不包括请求体）
            log.info("{}{} {} 来自IP: {}", getLogPrefix(LOG_PREFIX_REQUEST), method, requestURI, clientIp);
            String queryString = wrappedRequest.getQueryString();
            if (StringUtils.isNotBlank(queryString)) {
                log.info("{}请求参数: {}", getLogPrefix(LOG_PREFIX_REQUEST), queryString);
            }

            // 1. 检查 Token 白名单
            if (isWhitelisted(requestURI, authProperties.getTokenWhitelist())) {
                log.debug("{}URI [{}] 在Token白名单中，跳过token验证", getLogPrefix(LOG_PREFIX_AUTH), requestURI);
                filterChain.doFilter(wrappedRequest, wrappedResponse);
                return;
            }

            // 2. 检查 IP 白名单
            if (isWhitelisted(requestURI, authProperties.getIpWhitelistPaths())) {
                log.debug("{}URI [{}] 需要IP验证", getLogPrefix(LOG_PREFIX_AUTH), requestURI);
                if (isValidIp(clientIp, authProperties.getAllowedIps())) {
                    log.debug("{}客户端IP [{}] 允许访问", getLogPrefix(LOG_PREFIX_AUTH), clientIp);
                    filterChain.doFilter(wrappedRequest, wrappedResponse);
                } else {
                    log.warn("{}客户端IP [{}] 不允许访问", getLogPrefix(LOG_PREFIX_AUTH), clientIp);
                    sendErrorResponse(wrappedResponse, ErrorCodeEnum.ACCESS_DENIED_IP.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.ACCESS_DENIED_IP.getMsg()));
                }
                return;
            }

            // 3. Token 校验
            final String token = wrappedRequest.getHeader(authProperties.getTokenHeader());
            if (StringUtils.isBlank(token)) {
                log.warn("{}缺少token", getLogPrefix(LOG_PREFIX_TOKEN));
                sendErrorResponse(wrappedResponse, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return;
            }

            String userId;
            try {
                io.jsonwebtoken.Claims claims = jwtUtil.extractAllClaims(token);
                userId = claims.get("userId", String.class);
                // 将userId放入MDC
                MDC.put(MDC_USER_ID, userId);

                // 检查用户状态（排除特定路径）
                if (!isExcludedFromStatusCheck(requestURI)) {
                    if (!checkUserStatus(userId, wrappedResponse)) {
                        return; // 用户状态检查未通过
                    }
                }

                // 记录用户活跃信息
                recordUserActivity(userId);

            } catch (ExpiredJwtException e) {
                log.warn("{}JWT token已过期: {}", getLogPrefix(LOG_PREFIX_TOKEN), e.getMessage());
                sendErrorResponse(wrappedResponse, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return;
            } catch (JwtException e) {
                log.warn("{}JWT token无效: {}", getLogPrefix(LOG_PREFIX_TOKEN), e.getMessage());
                sendErrorResponse(wrappedResponse, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return;
            } catch (Exception e) {
                log.error("{}Token验证过程中出错: {} - {}", getLogPrefix(LOG_PREFIX_ERROR), e.getClass().getSimpleName(), e.getMessage(), e);
                sendErrorResponse(wrappedResponse, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return;
            }

            if (StringUtils.isBlank(userId)) {
                log.error("{}提取的userId为空!", getLogPrefix(LOG_PREFIX_ERROR));
                sendErrorResponse(wrappedResponse, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return;
            }

            // 保存用户上下文
            SimpleUserInfo simpleUserInfo = new SimpleUserInfo();
            simpleUserInfo.setUserId(userId);
            UserContext.setUser(simpleUserInfo);
            log.debug("{}用户 [{}] 认证成功", getLogPrefix(LOG_PREFIX_AUTH), userId);

            filterChain.doFilter(wrappedRequest, wrappedResponse);

        } catch (Exception e) {
            log.error("{}请求处理过程中异常: {} - {}", getLogPrefix(LOG_PREFIX_ERROR), e.getClass().getSimpleName(), e.getMessage(), e);
            if (!wrappedResponse.isCommitted()) {
                sendErrorResponse(wrappedResponse, ErrorCodeEnum.FILTER_INTERNAL_ERROR.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.FILTER_INTERNAL_ERROR.getMsg()));
            }
        } finally {
            stopWatch.stop();
            long durationMs = stopWatch.getTotalTimeMillis();
            
            // 在请求处理完成后记录请求体
            String requestBody = getRequestBody(wrappedRequest);
            if (StringUtils.isNotBlank(requestBody)) {
                log.info("{}请求体: {}", getLogPrefix(LOG_PREFIX_REQUEST), requestBody);
            }
            
            int status = wrappedResponse.getStatus();
            String responseBody = getResponseBody(wrappedResponse);

            log.info("{}状态: {}, 处理时间: {}ms, URI: {} {}", getLogPrefix(LOG_PREFIX_RESPONSE), status, durationMs, method, requestURI);
            if (StringUtils.isNotBlank(responseBody)) {
                log.info("{}响应体: {}", getLogPrefix(LOG_PREFIX_RESPONSE), responseBody);
            }

            UserContext.clearUserInfo();
            wrappedResponse.copyBodyToResponse();
            log.debug("{}已清理用户上下文", getLogPrefix(LOG_PREFIX_AUTH));

            // 请求处理完成后清理MDC
            MDC.clear();
        }
    }

    /**
     * 检查是否为排除用户状态检查的路径
     * @param requestURI 请求路径
     * @return 是否排除用户状态检查
     */
    private boolean isExcludedFromStatusCheck(String requestURI) {
        return STATUS_CHECK_EXCLUDED_PATHS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, requestURI));
    }

    /**
     * 检查用户状态
     * @param userId 用户ID
     * @param response 响应对象
     * @return 状态检查是否通过
     */
    private boolean checkUserStatus(String userId, HttpServletResponse response) throws IOException {
        // 查询用户信息
        AiUsersPo user = aiUsersMapper.selectOne(
                new LambdaQueryWrapper<AiUsersPo>().eq(AiUsersPo::getUserId, userId)
        );

        // 检查用户是否存在
        if (user == null) {
            log.error("{}用户 [{}] 在数据库中未找到", getLogPrefix(LOG_PREFIX_USER), userId);
            sendErrorResponse(response, ErrorCodeEnum.USER_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_FOUND.getMsg()));
            return false;
        }

        // 检查用户状态 - 按照最可能的顺序排列，提高性能
        if (user.getStatus() == USER_STATUS_ACTIVE) {
            // 用户已激活，可以继续访问
            return true;
        } else if (user.getStatus() == USER_STATUS_DISABLED) {
            // 用户已禁用
            log.warn("{}用户 [{}] 已禁用 (状态: {}). 拒绝访问", getLogPrefix(LOG_PREFIX_USER), userId, user.getStatus());
            sendErrorResponse(response, ErrorCodeEnum.USER_ACCOUNT_DISABLED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getMsg()));
            return false;
        } else if (user.getStatus() == USER_STATUS_INACTIVE) {
            // 用户未激活
            log.warn("{}用户 [{}] 未激活 (状态: {}). 拒绝访问", getLogPrefix(LOG_PREFIX_USER), userId, user.getStatus());
            sendErrorResponse(response, ErrorCodeEnum.USER_NOT_ACTIVATED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_ACTIVATED.getMsg()));
            return false;
        } else {
            // 未知状态，拒绝访问
            log.warn("{}用户 [{}] 状态未知 (状态: {}). 拒绝访问", getLogPrefix(LOG_PREFIX_USER), userId, user.getStatus());
            sendErrorResponse(response, ErrorCodeEnum.USER_ACCOUNT_DISABLED.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getMsg()));
            return false;
        }
    }

    private boolean isSSERequest(HttpServletRequest request) {
        // 检查Accept头或请求URI路径
        String acceptHeader = request.getHeader("Accept");
        if (acceptHeader != null && acceptHeader.contains("text/event-stream")) {
            return true;
        }

        // 或者检查请求参数中是否有streaming模式标记
        String responseMode = request.getParameter("response_mode");
        if ("streaming".equals(responseMode)) {
            return true;
        }

        // 也可以根据请求路径判断
        String requestURI = request.getRequestURI();
        return requestURI != null && requestURI.contains("/agent/chat/messages");
    }

    private boolean authenticateRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        String requestURI = request.getRequestURI();
        String clientIp = getClientIp(request);
        String method = request.getMethod();

        // 设置MDC属性
        MDC.put(MDC_TRACE_ID, traceId);
        MDC.put(MDC_URI, requestURI);
        MDC.put(MDC_METHOD, method);
        MDC.put(MDC_CLIENT_IP, clientIp);

        try {
            // 1. 检查 Token 白名单
            if (isWhitelisted(requestURI, authProperties.getTokenWhitelist())) {
                log.debug("{}URI [{}] 在Token白名单中，跳过token验证", getLogPrefix(LOG_PREFIX_AUTH), requestURI);
                return true;
            }

            // 2. 检查 IP 白名单
            if (isWhitelisted(requestURI, authProperties.getIpWhitelistPaths())) {
                log.debug("{}URI [{}] 需要IP验证", getLogPrefix(LOG_PREFIX_AUTH), requestURI);
                if (isValidIp(clientIp, authProperties.getAllowedIps())) {
                    log.debug("{}客户端IP [{}] 允许访问", getLogPrefix(LOG_PREFIX_AUTH), clientIp);
                    return true;
                } else {
                    log.warn("{}客户端IP [{}] 不允许访问", getLogPrefix(LOG_PREFIX_AUTH), clientIp);
                    sendErrorResponse(response, ErrorCodeEnum.ACCESS_DENIED_IP.getCode(),
                            I18nMessageUtils.getMessage(ErrorCodeEnum.ACCESS_DENIED_IP.getMsg()));
                    return false;
                }
            }

            // 3. Token 校验
            final String token = request.getHeader(authProperties.getTokenHeader());
            if (StringUtils.isBlank(token)) {
                log.warn("{}缺少token", getLogPrefix(LOG_PREFIX_TOKEN));
                sendErrorResponse(response, ErrorCodeEnum.LOGIN_FAIL.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return false;
            }

            String userId;
            try {
                io.jsonwebtoken.Claims claims = jwtUtil.extractAllClaims(token);
                userId = claims.get("userId", String.class);
                // 将userId放入MDC
                MDC.put(MDC_USER_ID, userId);

                // 检查用户状态（排除特定路径）
                if (!isExcludedFromStatusCheck(requestURI)) {
                    if (!checkUserStatus(userId, response)) {
                        return false; // 用户状态检查未通过
                    }
                }

                // 记录用户活跃信息
                recordUserActivity(userId);

            } catch (ExpiredJwtException e) {
                log.warn("{}JWT token已过期: {}", getLogPrefix(LOG_PREFIX_TOKEN), e.getMessage());
                sendErrorResponse(response, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return false;
            } catch (JwtException e) {
                log.warn("{}JWT token无效: {}", getLogPrefix(LOG_PREFIX_TOKEN), e.getMessage());
                sendErrorResponse(response, ErrorCodeEnum.LOGIN_FAIL.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return false;
            } catch (Exception e) {
                log.error("{}认证过程中异常: {} - {}", getLogPrefix(LOG_PREFIX_ERROR), e.getClass().getSimpleName(), e.getMessage(), e);
                if (!response.isCommitted()) {
                    sendErrorResponse(response, ErrorCodeEnum.FILTER_INTERNAL_ERROR.getCode(),
                            I18nMessageUtils.getMessage(ErrorCodeEnum.FILTER_INTERNAL_ERROR.getMsg()));
                }
                // 清理MDC
                MDC.clear();
                return false;
            }

            if (StringUtils.isBlank(userId)) {
                log.error("{}提取的userId为空!", getLogPrefix(LOG_PREFIX_ERROR));
                sendErrorResponse(response, ErrorCodeEnum.LOGIN_FAIL.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
                return false;
            }

            // 保存用户上下文
            SimpleUserInfo simpleUserInfo = new SimpleUserInfo();
            simpleUserInfo.setUserId(userId);
            UserContext.setUser(simpleUserInfo);
            log.debug("{}用户 [{}] 认证成功", getLogPrefix(LOG_PREFIX_AUTH), userId);

            return true;

        } catch (Exception e) {
            log.error("[{}] Exception during authentication: {} - {}", traceId, e.getClass().getSimpleName(), e.getMessage(), e);
            if (!response.isCommitted()) {
                sendErrorResponse(response, ErrorCodeEnum.FILTER_INTERNAL_ERROR.getCode(),
                        I18nMessageUtils.getMessage(ErrorCodeEnum.FILTER_INTERNAL_ERROR.getMsg()));
            }
            // 清理MDC
            MDC.clear();
            return false;
        }
    }

    private boolean isWhitelisted(String requestURI, List<String> whitelist) {
        if (whitelist == null || whitelist.isEmpty()) {
            return false;
        }
        return whitelist.stream().anyMatch(pattern -> pathMatcher.match(pattern, requestURI));
    }

    private boolean isValidIp(String clientIp, List<String> allowedIps) {
        if (allowedIps == null || allowedIps.isEmpty()) {
            log.warn("{}IP白名单未配置", getLogPrefix(LOG_PREFIX_AUTH));
            return false;
        }
        if (clientIp == null) {
            log.warn("{}客户端IP为null", getLogPrefix(LOG_PREFIX_AUTH));
            return false;
        }
        return allowedIps.contains(clientIp);
    }

    private String getClientIp(HttpServletRequest request) {
        // 按优先级顺序检查各种代理头，获取最原始的客户端IP
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR",
            "X-Cluster-Client-IP"
        };
        
        for (String header : headers) {
            String ip = request.getHeader(header);
            if (isValidIpHeader(ip)) {
                // 对于X-Forwarded-For等可能包含多个IP的头，取第一个（最原始的客户端IP）
                if (ip.contains(",")) {
                    String[] ips = ip.split(",");
                    for (String singleIp : ips) {
                        String trimmedIp = singleIp.trim();
                        if (isValidIpAddress(trimmedIp)) {
                            log.debug("{}从头 [{}] 获取到客户端IP: {}", getLogPrefix(LOG_PREFIX_AUTH), header, trimmedIp);
                            return trimmedIp;
                        }
                    }
                } else if (isValidIpAddress(ip)) {
                    log.debug("{}从头 [{}] 获取到客户端IP: {}", getLogPrefix(LOG_PREFIX_AUTH), header, ip);
                    return ip;
                }
            }
        }
        
        // 如果所有代理头都没有有效IP，使用直连IP
        String remoteAddr = request.getRemoteAddr();
        log.debug("{}使用直连IP作为客户端IP: {}", getLogPrefix(LOG_PREFIX_AUTH), remoteAddr);
        return remoteAddr;
    }
    
    /**
     * 检查IP头是否有效（非空且不是unknown）
     */
    private boolean isValidIpHeader(String ip) {
        return ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip);
    }
    
    /**
     * 检查IP地址格式是否有效，排除内网代理IP
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        // 基本格式检查
        if (!ip.matches("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$") && 
            !ip.matches("^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$") &&
            !ip.contains(":")) { // 简单的IPv6检查
            return false;
        }
        
        // 排除明显的内网IP和保留IP（可选，根据业务需求）
        if (ip.startsWith("127.") ||           // 本地回环
            ip.startsWith("10.") ||            // A类私网
            ip.startsWith("192.168.") ||       // C类私网
            ip.startsWith("172.")) {           // B类私网（部分）
            // 如果需要记录内网IP，可以注释掉这个检查
            log.debug("{}检测到内网IP: {}", getLogPrefix(LOG_PREFIX_AUTH), ip);
            // 根据业务需求决定是否接受内网IP
             return false; // 如果只要公网IP，取消注释这行
        }
        
        return true;
    }

    private void sendErrorResponse(HttpServletResponse response, String status, String message) throws IOException {
        if (!response.isCommitted()) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType("application/json;charset=UTF-8");
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("success", false);
            errorDetails.put("errCode", status);
            errorDetails.put("errMessage", message);
            response.getWriter().write(objectMapper.writeValueAsString(errorDetails));
        } else {
            log.warn("{}响应已提交，无法发送错误信息: {} - {}", getLogPrefix(LOG_PREFIX_ERROR), status, message);
        }
    }

    private String getRequestBody(ContentCachingRequestWrapper request) {
        byte[] buf = request.getContentAsByteArray();
        if (buf.length > 0) {
            try {
                String body = new String(buf, 0, buf.length, request.getCharacterEncoding());
                // 限制日志大小
                if (body.length() > MAX_REQUEST_BODY_LOG_SIZE) {
                    body = body.substring(0, MAX_REQUEST_BODY_LOG_SIZE) + "... [截断，完整长度: " + body.length() + "]";
                }
                return body;
            } catch (Exception ex) {
                log.warn("{}读取请求体失败: {}", getLogPrefix(LOG_PREFIX_ERROR), ex.getMessage());
            }
        }
        return null;
    }

    private String getResponseBody(ContentCachingResponseWrapper response) {
        byte[] buf = response.getContentAsByteArray();
        if (buf.length > 0) {
            Charset charsetToUse = null;
            String actualEncoding = null;
            try {
                // 尝试获取响应头中声明的编码
                actualEncoding = response.getCharacterEncoding();
                if (actualEncoding != null) {
                    try {
                        charsetToUse = Charset.forName(actualEncoding);
                    } catch (UnsupportedCharsetException e) {
                        log.warn("{}响应头中指定的编码不受支持: {}. 将使用备用编码", getLogPrefix(LOG_PREFIX_ERROR), actualEncoding);
                        // 标记为null，后续逻辑会处理
                        charsetToUse = null;
                    }
                }

                // === 关键逻辑：修正日志记录时使用的编码 ===
                // 如果响应头没有指定编码，或者是可疑的 ISO-8859-1，
                // 并且内容类型看起来是文本（JSON, text, XML, HTML 等），
                // 则【为了日志记录】，我们强烈倾向于使用 UTF-8 解码，因为实际字节流很可能是 UTF-8。
                if (charsetToUse == null || charsetToUse.equals(StandardCharsets.ISO_8859_1)) {
                    String responseContentType = response.getContentType();
                    if (responseContentType != null &&
                            (responseContentType.toLowerCase().contains("json") ||
                                    responseContentType.toLowerCase().contains("text") ||
                                    responseContentType.toLowerCase().contains("xml") ||
                                    responseContentType.toLowerCase().contains("html")))
                    {
                        if (charsetToUse == null) {
                            log.debug("{}响应编码为null, 内容类型: '{}'. 日志记录假定为UTF-8", getLogPrefix(LOG_PREFIX_RESPONSE), responseContentType);
                        } else {
                            log.debug("{}响应编码为ISO-8859-1, 内容类型: '{}'. 日志记录假定为UTF-8", getLogPrefix(LOG_PREFIX_RESPONSE), responseContentType);
                        }
                        // 【强制】使用 UTF-8 进行日志解码
                        charsetToUse = StandardCharsets.UTF_8;
                    } else if (charsetToUse == null){
                        // 如果不是常见文本类型且编码未指定，日志可能仍然有问题，但至少尝试用默认的
                        log.warn("{}响应编码为null, 内容类型 ('{}') 不是明确的文本类型. 日志记录使用平台默认编码", getLogPrefix(LOG_PREFIX_RESPONSE), responseContentType);
                        charsetToUse = Charset.defaultCharset(); // 或者坚持 ISO-8859-1，或尝试 UTF-8 作为最后的猜测
                    }
                    // 如果 charsetToUse 是 ISO-8859-1 且不是文本类型，就保留它，按响应头（虽然可能是错的）来尝试解码
                }

                // 如果经过上述逻辑，charsetToUse 仍然是 null (极少情况)，给一个最终的默认值
                if (charsetToUse == null) {
                    charsetToUse = StandardCharsets.UTF_8; // 或者 StandardCharsets.ISO_8859_1，取决于你想怎么处理完全未知的情况
                }

                // 使用最终确定的编码进行转换
                String responseBody = new String(buf, 0, buf.length, charsetToUse);
                
                // 限制日志大小
                if (responseBody.length() > MAX_RESPONSE_BODY_LOG_SIZE) {
                    responseBody = responseBody.substring(0, MAX_RESPONSE_BODY_LOG_SIZE) + "... [截断，完整长度: " + responseBody.length() + "]";
                }
                
                return responseBody;

            } catch (Exception ex) {
                // 增加更多上下文
                log.warn("{}读取响应体失败 (用于日志的编码: {}, 实际报告的编码: {}, 内容类型: {}): {}",
                        getLogPrefix(LOG_PREFIX_ERROR),
                        charsetToUse != null ? charsetToUse.name() : "N/A",
                        actualEncoding,
                        response.getContentType(),
                        ex.getMessage(),
                        ex); // 打印完整异常栈
                // 可以尝试用 UTF-8 硬解一次作为后备信息
                try {
                    return "[备用UTF-8解码] " + new String(buf, StandardCharsets.UTF_8);
                } catch (Exception ignored) {}
                return "[解码响应体失败]";
            }
        }
        return null;
    }

    // 添加私有方法记录用户活跃信息
    private void recordUserActivity(String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return;
            }
            
            // 获取当前日期，格式为yyyy-MM-dd
            String today = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            
            // 构建Redis key
            String activeUsersKey = RedisKeyConstant.getKey(RedisKeyConstant.UserActivity.DAILY_ACTIVE_USERS, today);
            
            // 将用户ID添加到当天的活跃用户集合中
            RSet<String> activeUsersSet = redissonClient.getSet(activeUsersKey);
            activeUsersSet.add(userId);
            
            // 设置过期时间
            activeUsersSet.expire(Duration.ofSeconds(RedisKeyConstant.UserActivity.ACTIVE_USERS_EXPIRE_SECONDS));
            
            log.debug("{}记录用户 [{}] 活跃信息成功", getLogPrefix(LOG_PREFIX_USER), userId);
        } catch (Exception e) {
            log.error("{}记录用户 [{}] 活跃信息失败: {}", getLogPrefix(LOG_PREFIX_ERROR), userId, e.getMessage(), e);
        }
    }
}
