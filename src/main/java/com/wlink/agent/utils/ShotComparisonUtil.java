package com.wlink.agent.utils;

import com.wlink.agent.model.req.ShotSaveReq;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Shot数据比对工具
 * 用于比对两个ShotData对象中narration字段的差
 */
public class ShotComparisonUtil {

    /**
     * 比对结果
     */
    public static class ComparisonResult {
        private List<String> differentIds = new ArrayList<>();
        private List<String> missingInFirst = new ArrayList<>();
        private List<String> missingInSecond = new ArrayList<>();
        private Map<String, NarrationDiff> differences = new HashMap<>();

        // getters and setters
        public List<String> getDifferentIds() { return differentIds; }
        public void setDifferentIds(List<String> differentIds) { this.differentIds = differentIds; }

        public List<String> getMissingInFirst() { return missingInFirst; }
        public void setMissingInFirst(List<String> missingInFirst) { this.missingInFirst = missingInFirst; }

        public List<String> getMissingInSecond() { return missingInSecond; }
        public void setMissingInSecond(List<String> missingInSecond) { this.missingInSecond = missingInSecond; }

        public Map<String, NarrationDiff> getDifferences() { return differences; }
        public void setDifferences(Map<String, NarrationDiff> differences) { this.differences = differences; }
    }

    /**
     * 旁白差异详情
     */
    public static class NarrationDiff {
        private String id;
        private String firstNarration;
        private String secondNarration;

        public NarrationDiff(String id, String firstNarration, String secondNarration) {
            this.id = id;
            this.firstNarration = firstNarration;
            this.secondNarration = secondNarration;
        }

        // getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getFirstNarration() { return firstNarration; }
        public void setFirstNarration(String firstNarration) { this.firstNarration = firstNarration; }

        public String getSecondNarration() { return secondNarration; }
        public void setSecondNarration(String secondNarration) { this.secondNarration = secondNarration; }
    }




    /**
     * 方法3：使用Stream API的函数式比对
     */
    public static List<String> compareNarrationStream(ShotSaveReq first, ShotSaveReq second) {
        Map<String, String> firstNarrations = extractNarrationMap(first);
        Map<String, String> secondNarrations = extractNarrationMap(second);

        return firstNarrations.entrySet().stream()
                .filter(entry -> {
                    String id = entry.getKey();
                    String firstNarration = entry.getValue();
                    String secondNarration = secondNarrations.get(id);
                    return !Objects.equals(firstNarration, secondNarration);
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 辅助方法：将ShotData转换为Map<String, Shot>
     */
    private static Map<String, ShotSaveReq.ShotGroupsDTO.ShotsDTO> convertToShotMap(ShotSaveReq shotData) {
        Map<String, ShotSaveReq.ShotGroupsDTO.ShotsDTO> shotMap = new HashMap<>();

        if (shotData != null && shotData.getShotGroups() != null) {
            for (ShotSaveReq.ShotGroupsDTO group : shotData.getShotGroups()) {
                if (group.getShots() != null) {
                    for (ShotSaveReq.ShotGroupsDTO.ShotsDTO shot : group.getShots()) {
                        shotMap.put(shot.getId(), shot);
                    }
                }
            }
        }

        return shotMap;
    }

    /**
     * 辅助方法：提取所有shot的narration到Map
     */
    private static Map<String, String> extractNarrationMap(ShotSaveReq shotData) {
        Map<String, String> narrationMap = new HashMap<>();

        if (shotData != null && shotData.getShotGroups() != null) {
            shotData.getShotGroups().stream()
                    .filter(group -> group.getShots() != null)
                    .flatMap(group -> group.getShots().stream())
                    .forEach(shot -> narrationMap.put(shot.getId(), shot.getNarration()));
        }

        return narrationMap;
    }

}
