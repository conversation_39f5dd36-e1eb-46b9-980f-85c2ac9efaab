package com.wlink.agent.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.dao.DataAccessException;

import java.util.function.Supplier;

/**
 * 数据库操作重试工具类
 */
@Slf4j
public class DatabaseRetryUtils {

    /**
     * 默认最大重试次数
     */
    private static final int DEFAULT_MAX_RETRIES = 3;

    /**
     * 默认重试间隔（毫秒）
     */
    private static final long DEFAULT_RETRY_DELAY = 1000L;

    /**
     * 执行数据库操作，带重试机制
     *
     * @param operation 数据库操作
     * @param operationName 操作名称（用于日志）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName) {
        return executeWithRetry(operation, operationName, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_DELAY);
    }

    /**
     * 执行数据库操作，带重试机制
     *
     * @param operation 数据库操作
     * @param operationName 操作名称（用于日志）
     * @param maxRetries 最大重试次数
     * @param retryDelay 重试间隔（毫秒）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName, 
                                        int maxRetries, long retryDelay) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount <= maxRetries) {
            try {
                T result = operation.get();
                if (retryCount > 0) {
                    log.info("数据库操作重试成功: operation={}, retryCount={}", operationName, retryCount);
                }
                return result;
                
            } catch (CannotAcquireLockException e) {
                lastException = e;
                retryCount++;
                
                if (retryCount > maxRetries) {
                    log.error("数据库锁等待超时，已达到最大重试次数: operation={}, maxRetries={}, error={}", 
                            operationName, maxRetries, e.getMessage());
                    break;
                }
                
                log.warn("数据库锁等待超时，准备重试: operation={}, retryCount={}/{}, error={}", 
                        operationName, retryCount, maxRetries, e.getMessage());
                
                waitBeforeRetry(retryDelay * retryCount); // 递增等待时间
                
            } catch (DataAccessException e) {
                // 检查是否是可重试的数据库异常
                if (isRetryableException(e)) {
                    lastException = e;
                    retryCount++;
                    
                    if (retryCount > maxRetries) {
                        log.error("数据库操作异常，已达到最大重试次数: operation={}, maxRetries={}, error={}", 
                                operationName, maxRetries, e.getMessage());
                        break;
                    }
                    
                    log.warn("数据库操作异常，准备重试: operation={}, retryCount={}/{}, error={}", 
                            operationName, retryCount, maxRetries, e.getMessage());
                    
                    waitBeforeRetry(retryDelay * retryCount);
                } else {
                    // 不可重试的异常，直接抛出
                    log.error("数据库操作不可重试异常: operation={}, error={}", operationName, e.getMessage());
                    throw e;
                }
                
            } catch (Exception e) {
                // 其他异常，直接抛出
                log.error("数据库操作未知异常: operation={}, error={}", operationName, e.getMessage());
                throw e;
            }
        }

        // 重试次数用完，抛出最后一个异常
        if (lastException instanceof RuntimeException) {
            throw (RuntimeException) lastException;
        } else {
            throw new RuntimeException("数据库操作重试失败: " + operationName, lastException);
        }
    }

    /**
     * 执行数据库操作（无返回值），带重试机制
     *
     * @param operation 数据库操作
     * @param operationName 操作名称（用于日志）
     */
    public static void executeWithRetry(Runnable operation, String operationName) {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, operationName);
    }

    /**
     * 判断是否是可重试的异常
     *
     * @param e 异常
     * @return 是否可重试
     */
    private static boolean isRetryableException(DataAccessException e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("lock wait timeout") ||
               lowerMessage.contains("deadlock") ||
               lowerMessage.contains("connection") ||
               lowerMessage.contains("timeout");
    }

    /**
     * 等待指定时间
     *
     * @param delayMs 等待时间（毫秒）
     */
    private static void waitBeforeRetry(long delayMs) {
        try {
            Thread.sleep(delayMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("重试等待被中断", e);
        }
    }
}
