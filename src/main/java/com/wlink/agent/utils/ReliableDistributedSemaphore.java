package com.wlink.agent.utils;

import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.net.InetAddress;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/5/26 10:28
 */
@Component
@Slf4j
public class ReliableDistributedSemaphore {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AiImageTaskQueueMapper aiImageTaskQueueMapper;

    private static final String SEMAPHORE_NAME = "image_generation_semaphore";
    private static final String TASK_SEMAPHORE_MAPPING = "task_semaphore_mapping";
    private static final int MAX_PERMITS = 5;
    private static final long TIMEOUT_SECONDS = 300; // 5分钟超时

    /**
     * 尝试获取信号量并记录映射关系
     */
    public boolean tryAcquireWithTracking(String taskId) {
        RSemaphore semaphore = redissonClient.getSemaphore(SEMAPHORE_NAME);
        RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);

        try {
            // 初始化信号量许可数
            if (!semaphore.isExists()) {
                semaphore.trySetPermits(MAX_PERMITS);
            }

            // 检查任务是否已经持有信号量
            if (taskMapping.containsKey(taskId)) {
                log.warn("任务 {} 已经持有信号量，跳过获取", taskId);
                return true;
            }

            // 尝试获取许可
            boolean acquired = semaphore.tryAcquire(5, TimeUnit.SECONDS);
            if (acquired) {
                // 记录任务与信号量的映射关系
                SemaphoreRecord record = new SemaphoreRecord();
                record.setTaskId(taskId);
                record.setAcquireTime(System.currentTimeMillis());
                record.setThreadName(Thread.currentThread().getName());
                record.setHostname(getHostname());

                taskMapping.put(taskId, record);
                taskMapping.expire(Duration.ofSeconds(TIMEOUT_SECONDS + 120)); // 比任务超时时间多2分钟

                log.info("任务 {} 成功获取信号量许可，当前可用许可数: {}", taskId, semaphore.availablePermits());
                return true;
            } else {
                log.info("任务 {} 获取信号量许可失败，当前可用许可数: {}", taskId, semaphore.availablePermits());
                return false;
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取信号量被中断, taskId: {}", taskId, e);
            return false;
        } catch (Exception e) {
            log.error("获取分布式信号量失败, taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 释放信号量并清理映射关系
     */
    public boolean releaseWithTracking(String taskId) {
        RSemaphore semaphore = redissonClient.getSemaphore(SEMAPHORE_NAME);
        RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);

        try {
            // 检查任务是否真的持有信号量
            SemaphoreRecord record = taskMapping.get(taskId);
            if (record == null) {
                log.warn("任务 {} 没有信号量记录，可能已经释放或从未获取", taskId);
                return false;
            }

            // 释放信号量
            semaphore.release();

            // 移除映射关系
            taskMapping.remove(taskId);

            log.info("任务 {} 成功释放信号量许可，当前可用许可数: {}", taskId, semaphore.availablePermits());
            return true;

        } catch (Exception e) {
            log.error("释放分布式信号量失败, taskId: {}", taskId, e);
            // 即使出现异常，也要尝试清理映射关系
            try {
                RMap<String, SemaphoreRecord> taskMapping2 = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);
                taskMapping2.remove(taskId);
            } catch (Exception ex) {
                log.error("清理任务映射关系失败", ex);
            }
            return false;
        }
    }

    /**
     * 强制释放任务的信号量（用于异常恢复）
     */
    public boolean forceRelease(String taskId, String reason) {
        RSemaphore semaphore = redissonClient.getSemaphore(SEMAPHORE_NAME);
        RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);

        try {
            SemaphoreRecord record = taskMapping.get(taskId);
            if (record != null) {
                semaphore.release();
                taskMapping.remove(taskId);
                log.warn("强制释放任务 {} 的信号量，原因: {}", taskId, reason);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("强制释放信号量失败, taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 检查并清理超时任务的信号量
     */
    public List<String> checkAndCleanupTimeoutTasks() {
        RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);
        List<String> timeoutTasks = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        long timeoutThreshold = TIMEOUT_SECONDS * 1000;

        try {
            for (Map.Entry<String, SemaphoreRecord> entry : taskMapping.entrySet()) {
                String taskId = entry.getKey();
                SemaphoreRecord record = entry.getValue();

                if (record != null && (currentTime - record.getAcquireTime()) > timeoutThreshold) {
                    // 检查数据库中任务的真实状态
                    AiImageTaskQueuePo dbTask = aiImageTaskQueueMapper.selectById(taskId);
                    if (dbTask != null) {
                        if (TaskStatus.COMPLETED.getValue().equals(dbTask.getTaskStatus()) ||
                                TaskStatus.FAILED.getValue().equals(dbTask.getTaskStatus())) {
                            // 任务已完成但信号量未释放
                            forceRelease(taskId, "任务已完成但信号量未释放");
                            timeoutTasks.add(taskId);
                        } else if (TaskStatus.PROCESSING.getValue().equals(dbTask.getTaskStatus())) {
                            // 检查是否真的超时
                            if (dbTask.getUpdateTime() != null &&
                                    (currentTime - dbTask.getUpdateTime().getTime()) > timeoutThreshold) {
                                forceRelease(taskId, "任务处理超时");
                                timeoutTasks.add(taskId);

                                // 更新任务状态为失败
                                dbTask.setTaskStatus(TaskStatus.FAILED.getValue());
                                dbTask.setErrorReason("任务处理超时，信号量已释放");
                                dbTask.setUpdateTime(new Date());
                                aiImageTaskQueueMapper.updateById(dbTask);
                            }
                        }
                    } else {
                        // 数据库中没有找到任务，可能是脏数据
                        forceRelease(taskId, "数据库中未找到对应任务");
                        timeoutTasks.add(taskId);
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查超时任务失败", e);
        }

        return timeoutTasks;
    }

    /**
     * 系统启动时的信号量状态修复
     */
    public void repairSemaphoreOnStartup() {
        try {
            log.info("开始修复信号量状态...");

            RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);
            RSemaphore semaphore = redissonClient.getSemaphore(SEMAPHORE_NAME);

            // 获取所有持有信号量的任务
            Set<String> taskIds = new HashSet<>(taskMapping.keySet());
            int releasedCount = 0;

            for (String taskId : taskIds) {
                AiImageTaskQueuePo dbTask = aiImageTaskQueueMapper.selectById(taskId);
                if (dbTask == null ||
                        TaskStatus.COMPLETED.getValue().equals(dbTask.getTaskStatus()) ||
                        TaskStatus.FAILED.getValue().equals(dbTask.getTaskStatus())) {
                    // 任务不存在或已完成，释放信号量
                    forceRelease(taskId, "系统启动修复：任务已完成或不存在");
                    releasedCount++;
                } else if (TaskStatus.PROCESSING.getValue().equals(dbTask.getTaskStatus())) {
                    // 处理中的任务，检查是否超时
                    if (dbTask.getUpdateTime() != null) {
                        long processingTime = System.currentTimeMillis() - dbTask.getUpdateTime().getTime();
                        if (processingTime > TIMEOUT_SECONDS * 1000) {
                            forceRelease(taskId, "系统启动修复：处理超时");
                            releasedCount++;

                            // 更新任务状态
                            dbTask.setTaskStatus(TaskStatus.FAILED.getValue());
                            dbTask.setErrorReason("系统重启时发现任务超时");
                            dbTask.setUpdateTime(new Date());
                            aiImageTaskQueueMapper.updateById(dbTask);
                        }
                    }
                }
            }

            log.info("信号量修复完成，释放了 {} 个信号量", releasedCount);

            // 确保信号量许可数正确
            if (!semaphore.isExists()) {
                semaphore.trySetPermits(MAX_PERMITS);
                log.info("重新初始化信号量，设置 {} 个许可", MAX_PERMITS);
            }

        } catch (Exception e) {
            log.error("修复信号量状态失败", e);
        }
    }

    /**
     * 获取信号量状态
     */
    public SemaphoreStatus getDetailedStatus() {
        RSemaphore semaphore = redissonClient.getSemaphore(SEMAPHORE_NAME);
        RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);

        try {
            SemaphoreStatus status = new SemaphoreStatus();
            status.setMaxPermits(MAX_PERMITS);
            status.setAvailablePermits(semaphore.availablePermits());
            status.setUsedPermits(MAX_PERMITS - status.getAvailablePermits());
            // RSemaphore没有getQueuedThreads方法，设置为0
            status.setQueueLength(0);

            // 获取详细的持有者信息
            List<SemaphoreRecord> holders = new ArrayList<>();
            for (SemaphoreRecord record : taskMapping.values()) {
                holders.add(record);
            }
            status.setCurrentHolders(holders);

            // 检查数据一致性
            int mappingCount = taskMapping.size();
            if (mappingCount != status.getUsedPermits()) {
                log.warn("信号量数据不一致：映射记录数={}, 实际使用数={}", mappingCount, status.getUsedPermits());
                status.setDataConsistent(false);
            } else {
                status.setDataConsistent(true);
            }

            return status;
        } catch (Exception e) {
            log.error("获取信号量状态失败", e);
            return new SemaphoreStatus();
        }
    }

    private String getHostname() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown";
        }
    }
    /**
     * 信号量记录
     */
    @Data
    public static class SemaphoreRecord implements Serializable {
        private String taskId;
        private long acquireTime;
        private String threadName;
        private String hostname;
    }

    /**
     * 信号量状态
     */
    @Data
    public static class SemaphoreStatus {
        private int maxPermits;
        private int usedPermits;
        private int availablePermits;
        private int queueLength;
        private List<SemaphoreRecord> currentHolders;
        private boolean dataConsistent;
    }

    /**
     * 强制重置信号量状态结果
     * 完全删除当前信号量并重建，释放所有占用的信号量
     */
    public record ResetResult(boolean success, int releasedCount, String message) {}

    /**
     * 判断是否需要强制重置信号量
     * @param status 当前信号量状态
     * @return 是否需要强制重置，以及原因
     */
    public record ForceResetCheck(boolean resetRequired, String reason) {}

    /**
     * 检查是否需要强制重置信号量
     * @param status 信号量状态
     * @return 检查结果
     */
    public ForceResetCheck isForceResetRequired(SemaphoreStatus status) {
        // 如果数据严重不一致（映射数和使用数差异超过阈值）
        int mappingSize = status.getCurrentHolders().size();
        int usedPermits = status.getUsedPermits();
        int absDiff = Math.abs(mappingSize - usedPermits);

        // 如果差异超过总许可的50%，认为严重不一致
        if (absDiff > MAX_PERMITS / 2) {
            return new ForceResetCheck(true,
                    "严重数据不一致: 映射数=" + mappingSize + ", 使用数=" + usedPermits);
        }

        // 如果全部信号量被占用，但实际映射中没有记录（幽灵占用）
        if (usedPermits == MAX_PERMITS && mappingSize == 0) {
            return new ForceResetCheck(true, "全部信号量被占用，但映射记录为空");
        }

        // 检查是否有过期的映射（超过30分钟，远超正常超时时间）
        long currentTime = System.currentTimeMillis();
        long extremeTimeoutThreshold = TIMEOUT_SECONDS * 6 * 1000; // 6倍超时时间

        boolean hasExtremelyOldRecords = status.getCurrentHolders().stream()
                .anyMatch(record -> (currentTime - record.getAcquireTime()) > extremeTimeoutThreshold);

        if (hasExtremelyOldRecords) {
            return new ForceResetCheck(true, "存在严重超时的信号量记录");
        }

        return new ForceResetCheck(false, "信号量状态正常或可自动修复");
    }

    /**
     * 强制重置信号量
     * @param reason 重置原因
     * @return 重置结果
     */
    public ResetResult forceResetSemaphore(String reason) {
        RSemaphore semaphore = redissonClient.getSemaphore(SEMAPHORE_NAME);
        RMap<String, SemaphoreRecord> taskMapping = redissonClient.getMap(TASK_SEMAPHORE_MAPPING);

        try {
            log.warn("开始强制重置信号量，原因: {}", reason);

            // 获取当前持有信号量的任务
            Set<String> taskIds = new HashSet<>(taskMapping.keySet());
            int releasedCount = taskIds.size();

            // 更新所有处理中任务的状态为失败
            for (String taskId : taskIds) {
                AiImageTaskQueuePo dbTask = aiImageTaskQueueMapper.selectById(taskId);
                if (dbTask != null && TaskStatus.PROCESSING.getValue().equals(dbTask.getTaskStatus())) {
                    dbTask.setTaskStatus(TaskStatus.FAILED.getValue());
                    dbTask.setErrorReason("信号量异常强制重置：" + reason);
                    dbTask.setUpdateTime(new Date());
                    aiImageTaskQueueMapper.updateById(dbTask);
                }
            }

            // 删除映射关系
            taskMapping.delete();

            // 删除并重建信号量
            semaphore.delete();
            semaphore.trySetPermits(MAX_PERMITS);

            log.warn("信号量强制重置完成，释放了 {} 个信号量", releasedCount);
            return new ResetResult(true, releasedCount, "重置成功，释放了 " + releasedCount + " 个信号量");
        } catch (Exception e) {
            log.error("强制重置信号量失败", e);
            return new ResetResult(false, 0, "重置失败: " + e.getMessage());
        }
    }
}