package com.wlink.agent.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期工具
 */
public class DateUtils {
    
    /**
     * 格式化日
     * @param date 日期
     * @param pattern 格式
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null || pattern == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }
    
    /**
     * 获取当前日期字符串，格式为yyyy-MM-dd
     * @return 当前日期字符
     */
    public static String getCurrentDateStr() {
        return formatDate(new Date(), "yyyy-MM-dd");
    }
} 
