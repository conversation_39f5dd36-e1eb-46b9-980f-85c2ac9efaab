package com.wlink.agent.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.enums.TaskStatus;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/9 22:33
 */
@Component
@Slf4j
public class ReliableDistributedSemaphoreV2 {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AiImageTaskQueueMapper aiImageTaskQueueMapper;

    @Autowired
    private ObjectMapper objectMapper; // For converting map to object safely

    // Key names for Redis data structures
    private static final String SEMAPHORE_HOLDER_SET = "image_generation_semaphore:holders"; // ZSET: member=taskId, score=acquireTime
    private static final String TASK_SEMAPHORE_MAPPING = "image_generation_semaphore:task_mapping"; // HASH: key=taskId, value=SemaphoreRecord
    private static final int MAX_PERMITS = 5;
    private static final long TIMEOUT_SECONDS = 300; // 5分钟超时

    // --- LUA Scripts for Atomicity ---

    /**
     * Lua script to acquire a semaphore permit.
     * Atomically cleans expired leases, checks for available permits, and acquires one if possible.
     * KEYS[1]: The ZSET key for semaphore holders.
     * ARGV[1]: Max number of permits.
     * ARGV[2]: Current time in milliseconds (for lease expiration check and score).
     * ARGV[3]: Lease timeout in milliseconds.
     * ARGV[4]: The unique ID of the member trying to acquire (e.g., taskId).
     * @return 1 if acquired, 0 if not.
     */
    private static final String LUA_ACQUIRE_SCRIPT =
            "redis.call('ZREMRANGEBYSCORE', KEYS[1], 0, ARGV[2] - ARGV[3]); " +
                    "if redis.call('ZCARD', KEYS[1]) < tonumber(ARGV[1]) then " +
                    "    redis.call('ZADD', KEYS[1], ARGV[2], ARGV[4]); " +
                    "    return 1; " +
                    "end; " +
                    "return 0;";

    /**
     * Lua script to release a semaphore permit.
     * KEYS[1]: The ZSET key for semaphore holders.
     * ARGV[1]: The unique ID of the member to release.
     * @return 1 if released, 0 if member was not found.
     */
    private static final String LUA_RELEASE_SCRIPT =
            "return redis.call('ZREM', KEYS[1], ARGV[1]);";


    /**
     * 尝试获取信号量并记录映射关系
     */
    public boolean tryAcquireWithTracking(String taskId) {
        try {
            // 检查任务是否已经持有信号量
            if (redisUtil.hhasKey(TASK_SEMAPHORE_MAPPING, taskId)) {
                log.warn("任务 {} 已经持有信号量，跳过获取", taskId);
                return true;
            }

            // 尝试获取许可, 轮询等待最多5秒
            long waitTimeout = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(5);
            boolean acquired = false;
            while (System.currentTimeMillis() < waitTimeout) {
                Long result = redisUtil.execute(
                        LUA_ACQUIRE_SCRIPT,
                        Long.class,
                        Collections.singletonList(SEMAPHORE_HOLDER_SET),
                        MAX_PERMITS,
                        System.currentTimeMillis(),
                        TimeUnit.SECONDS.toMillis(TIMEOUT_SECONDS),
                        taskId
                );

                if (result != null && result == 1L) {
                    acquired = true;
                    break;
                }
                // 短暂休眠避免CPU空转
                Thread.sleep(200);
            }

            if (acquired) {
                // 记录任务与信号量的映射关系
                SemaphoreRecord record = new SemaphoreRecord();
                record.setTaskId(taskId);
                record.setAcquireTime(System.currentTimeMillis());
                record.setThreadName(Thread.currentThread().getName());
                record.setHostname(getHostname());

                redisUtil.hset(TASK_SEMAPHORE_MAPPING, taskId, record);
                // 设置整个哈希表的过期时间，每次操作都会刷新
                redisUtil.expire(TASK_SEMAPHORE_MAPPING, TIMEOUT_SECONDS + 120, TimeUnit.SECONDS);

                log.info("任务 {} 成功获取信号量许可", taskId);
                return true;
            } else {
                log.info("任务 {} 获取信号量许可失败", taskId);
                return false;
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取信号量被中断, taskId: {}", taskId, e);
            return false;
        } catch (Exception e) {
            log.error("获取分布式信号量失败, taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 释放信号量并清理映射关系
     */
    public boolean releaseWithTracking(String taskId) {
        try {
            if (!redisUtil.hhasKey(TASK_SEMAPHORE_MAPPING, taskId)) {
                log.warn("任务 {} 没有信号量记录，可能已经释放或从未获取。尝试强制清理持有者...", taskId);
                redisUtil.execute(LUA_RELEASE_SCRIPT, Long.class, Collections.singletonList(SEMAPHORE_HOLDER_SET), taskId);
                return false;
            }

            // 释放信号量
            redisUtil.execute(LUA_RELEASE_SCRIPT, Long.class, Collections.singletonList(SEMAPHORE_HOLDER_SET), taskId);

            // 移除映射关系
            redisUtil.hdel(TASK_SEMAPHORE_MAPPING, taskId);

            log.info("任务 {} 成功释放信号量许可", taskId);
            return true;
        } catch (Exception e) {
            log.error("释放分布式信号量失败, taskId: {}", taskId, e);
            // 即使出现异常，也要尝试清理映射关系
            try {
                redisUtil.hdel(TASK_SEMAPHORE_MAPPING, taskId);
            } catch (Exception ex) {
                log.error("清理任务映射关系失败", ex);
            }
            return false;
        }
    }

    /**
     * 强制释放任务的信号量（用于异常恢复）
     */
    public boolean forceRelease(String taskId, String reason) {
        try {
            boolean hadMapping = redisUtil.hhasKey(TASK_SEMAPHORE_MAPPING, taskId);

            // 释放信号量持有者
            Long released = redisUtil.execute(LUA_RELEASE_SCRIPT, Long.class, Collections.singletonList(SEMAPHORE_HOLDER_SET), taskId);
            // 删除映射
            redisUtil.hdel(TASK_SEMAPHORE_MAPPING, taskId);

            if (hadMapping || (released != null && released > 0)) {
                log.warn("强制释放任务 {} 的信号量，原因: {}", taskId, reason);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("强制释放信号量失败, taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 检查并清理超时任务的信号量
     */
    public List<String> checkAndCleanupTimeoutTasks() {
        Map<String, Object> taskMapping = redisUtil.hgetAll(TASK_SEMAPHORE_MAPPING);
        List<String> timeoutTasks = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        long timeoutThreshold = TIMEOUT_SECONDS * 1000;

        try {
            for (Map.Entry<String, Object> entry : taskMapping.entrySet()) {
                String taskId = entry.getKey();
                SemaphoreRecord record = objectMapper.convertValue(entry.getValue(), SemaphoreRecord.class);

                if (record != null && (currentTime - record.getAcquireTime()) > timeoutThreshold) {
                    // 检查数据库中任务的真实状态
                    AiImageTaskQueuePo dbTask = aiImageTaskQueueMapper.selectById(taskId);
                    if (dbTask != null) {
                        if (TaskStatus.COMPLETED.getValue().equals(dbTask.getTaskStatus()) ||
                                TaskStatus.FAILED.getValue().equals(dbTask.getTaskStatus())) {
                            forceRelease(taskId, "任务已完成但信号量未释放");
                            timeoutTasks.add(taskId);
                        } else if (TaskStatus.PROCESSING.getValue().equals(dbTask.getTaskStatus())) {
                            if (dbTask.getUpdateTime() != null &&
                                    (currentTime - dbTask.getUpdateTime().getTime()) > timeoutThreshold) {
                                forceRelease(taskId, "任务处理超时");
                                timeoutTasks.add(taskId);

                                // 更新任务状态为失败
                                dbTask.setTaskStatus(TaskStatus.FAILED.getValue());
                                dbTask.setErrorReason("任务处理超时，信号量已释放");
                                dbTask.setUpdateTime(new Date());
                                aiImageTaskQueueMapper.updateById(dbTask);
                            }
                        }
                    } else {
                        forceRelease(taskId, "数据库中未找到对应任务");
                        timeoutTasks.add(taskId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查超时任务失败", e);
        }

        return timeoutTasks;
    }

    /**
     * 系统启动时的信号量状态修复
     */
    public void repairSemaphoreOnStartup() {
        try {
            log.info("开始修复信号量状态...");

            // 1. Clean up expired leases directly in Redis based on score
            long cleaned = redisUtil.zremrangebyscore(SEMAPHORE_HOLDER_SET, 0, System.currentTimeMillis() - TimeUnit.SECONDS.toMillis(TIMEOUT_SECONDS));
            if (cleaned > 0) {
                log.info("启动修复：通过ZSET分数清除了 {} 个过期的信号量持有者", cleaned);
            }

            // 2. Cross-check remaining mapping entries with DB state
            Set<String> taskIds = redisUtil.hkeys(TASK_SEMAPHORE_MAPPING);
            int releasedCount = 0;

            for (String taskId : taskIds) {
                AiImageTaskQueuePo dbTask = aiImageTaskQueueMapper.selectById(taskId);
                if (dbTask == null ||
                        TaskStatus.COMPLETED.getValue().equals(dbTask.getTaskStatus()) ||
                        TaskStatus.FAILED.getValue().equals(dbTask.getTaskStatus())) {
                    if (forceRelease(taskId, "系统启动修复：任务已完成或不存在")) {
                        releasedCount++;
                    }
                } else if (TaskStatus.PROCESSING.getValue().equals(dbTask.getTaskStatus())) {
                    if (dbTask.getUpdateTime() != null && (System.currentTimeMillis() - dbTask.getUpdateTime().getTime()) > TIMEOUT_SECONDS * 1000) {
                        if (forceRelease(taskId, "系统启动修复：处理超时")) {
                            releasedCount++;
                        }
                        // 更新任务状态
                        dbTask.setTaskStatus(TaskStatus.FAILED.getValue());
                        dbTask.setErrorReason("系统重启时发现任务超时");
                        dbTask.setUpdateTime(new Date());
                        aiImageTaskQueueMapper.updateById(dbTask);
                    }
                }
            }

            log.info("信号量修复完成，通过DB检查释放了 {} 个信号量", releasedCount);

        } catch (Exception e) {
            log.error("修复信号量状态失败", e);
        }
    }

    /**
     * 获取信号量状态
     */
    public SemaphoreStatus getDetailedStatus() {
        try {
            Long usedPermitsLong = redisUtil.zcard(SEMAPHORE_HOLDER_SET);
            int usedPermits = usedPermitsLong != null ? usedPermitsLong.intValue() : 0;

            Map<String, Object> taskMappingRaw = redisUtil.hgetAll(TASK_SEMAPHORE_MAPPING);
            List<SemaphoreRecord> holders = taskMappingRaw.values().stream()
                    .map(obj -> objectMapper.convertValue(obj, SemaphoreRecord.class))
                    .collect(Collectors.toList());

            SemaphoreStatus status = new SemaphoreStatus();
            status.setMaxPermits(MAX_PERMITS);
            status.setUsedPermits(usedPermits);
            status.setAvailablePermits(MAX_PERMITS - usedPermits);
            status.setQueueLength(0); // This implementation does not have a wait queue
            status.setCurrentHolders(holders);

            int mappingCount = taskMappingRaw.size();
            status.setDataConsistent(mappingCount == usedPermits);
            if (!status.isDataConsistent()) {
                log.warn("信号量数据不一致：映射记录数={}, 实际使用数={}", mappingCount, usedPermits);
            }

            return status;
        } catch (Exception e) {
            log.error("获取信号量状态失败", e);
            // Return a default empty status on error
            SemaphoreStatus errorStatus = new SemaphoreStatus();
            errorStatus.setMaxPermits(MAX_PERMITS);
            return errorStatus;
        }
    }

    /**
     * 强制重置信号量
     */
    public ResetResult forceResetSemaphore(String reason) {
        try {
            log.warn("开始强制重置信号量，原因: {}", reason);

            Set<String> taskIds = redisUtil.hkeys(TASK_SEMAPHORE_MAPPING);
            int releasedCount = taskIds.size();

            for (String taskId : taskIds) {
                AiImageTaskQueuePo dbTask = aiImageTaskQueueMapper.selectById(taskId);
                if (dbTask != null && TaskStatus.PROCESSING.getValue().equals(dbTask.getTaskStatus())) {
                    dbTask.setTaskStatus(TaskStatus.FAILED.getValue());
                    dbTask.setErrorReason("信号量异常强制重置：" + reason);
                    dbTask.setUpdateTime(new Date());
                    aiImageTaskQueueMapper.updateById(dbTask);
                }
            }

            // Atomically delete both keys
            redisUtil.delete(TASK_SEMAPHORE_MAPPING);
            redisUtil.delete(SEMAPHORE_HOLDER_SET);

            log.warn("信号量强制重置完成，释放了 {} 个信号量", releasedCount);
            return new ResetResult(true, releasedCount, "重置成功，释放了 " + releasedCount + " 个信号量");
        } catch (Exception e) {
            log.error("强制重置信号量失败", e);
            return new ResetResult(false, 0, "重置失败: " + e.getMessage());
        }
    }

    // --- Helper methods and inner classes (unchanged from original) ---

    private String getHostname() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown";
        }
    }

    @Data
    public static class SemaphoreRecord implements Serializable {
        private String taskId;
        private long acquireTime;
        private String threadName;
        private String hostname;
    }

    @Data
    public static class SemaphoreStatus {
        private int maxPermits;
        private int usedPermits;
        private int availablePermits;
        private int queueLength;
        private List<SemaphoreRecord> currentHolders = new ArrayList<>();
        private boolean dataConsistent;
    }

    public record ResetResult(boolean success, int releasedCount, String message) {}

    public record ForceResetCheck(boolean resetRequired, String reason) {}

    public ForceResetCheck isForceResetRequired(SemaphoreStatus status) {
        int mappingSize = status.getCurrentHolders().size();
        int usedPermits = status.getUsedPermits();
        int absDiff = Math.abs(mappingSize - usedPermits);

        // Difference is more than 1 (allowing for minor race conditions) and more than 50% of capacity
        if (absDiff > 1 && absDiff > MAX_PERMITS / 2) {
            return new ForceResetCheck(true, "严重数据不一致: 映射数=" + mappingSize + ", 使用数=" + usedPermits);
        }

        if (usedPermits >= MAX_PERMITS && mappingSize == 0) {
            return new ForceResetCheck(true, "全部信号量被占用，但映射记录为空（幽灵占用）");
        }

        long currentTime = System.currentTimeMillis();
        long extremeTimeoutThreshold = TIMEOUT_SECONDS * 6 * 1000;

        boolean hasExtremelyOldRecords = status.getCurrentHolders().stream()
                .anyMatch(record -> (currentTime - record.getAcquireTime()) > extremeTimeoutThreshold);

        if (hasExtremelyOldRecords) {
            return new ForceResetCheck(true, "存在严重超时的信号量记录");
        }

        return new ForceResetCheck(false, "信号量状态正常或可自动修复");
    }
}
