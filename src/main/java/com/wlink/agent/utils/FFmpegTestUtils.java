package com.wlink.agent.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

/**
 * FFmpeg测试工具类
 */
@Slf4j
public class FFmpegTestUtils {

    /**
     * 测试FFmpeg是否可用
     *
     * @param ffmpegPath FFmpeg可执行文件路径
     * @return 是否可用
     */
    public static boolean testFFmpegAvailable(String ffmpegPath) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(ffmpegPath, "-version");
            Process process = processBuilder.start();
            
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.error("FFmpeg版本检查超时");
                return false;
            }
            
            int exitCode = process.exitValue();
            if (exitCode == 0) {
                log.info("FFmpeg可用，版本信息:\n{}", output.toString());
                return true;
            } else {
                log.error("FFmpeg版本检查失败，退出码: {}", exitCode);
                return false;
            }
            
        } catch (Exception e) {
            log.error("FFmpeg可用性测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 测试音频文件信息
     *
     * @param ffmpegPath FFmpeg可执行文件路径
     * @param audioFilePath 音频文件路径
     * @return 音频文件信息
     */
    public static String getAudioFileInfo(String ffmpegPath, String audioFilePath) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    ffmpegPath, "-i", audioFilePath, "-f", "null", "-"
            );
            
            Process process = processBuilder.start();
            
            StringBuilder output = new StringBuilder();
            StringBuilder error = new StringBuilder();
            
            // 读取标准输出
            Thread outputReader = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                    }
                } catch (IOException e) {
                    log.warn("读取FFmpeg标准输出失败", e);
                }
            });
            
            // 读取错误输出（FFmpeg的信息通常在错误流中）
            Thread errorReader = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        error.append(line).append("\n");
                    }
                } catch (IOException e) {
                    log.warn("读取FFmpeg错误输出失败", e);
                }
            });
            
            outputReader.start();
            errorReader.start();
            
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            
            try {
                outputReader.join(5000);
                errorReader.join(5000);
            } catch (InterruptedException e) {
                log.warn("等待输出读取完成被中断", e);
            }
            
            if (!finished) {
                process.destroyForcibly();
                return "音频文件信息获取超时";
            }
            
            String result = "标准输出:\n" + output.toString() + "\n错误输出:\n" + error.toString();
            log.info("音频文件信息: {}\n{}", audioFilePath, result);
            return result;
            
        } catch (Exception e) {
            String errorMsg = "获取音频文件信息失败: " + e.getMessage();
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    /**
     * 测试简单的音频转换
     *
     * @param ffmpegPath FFmpeg可执行文件路径
     * @param inputPath 输入文件路径
     * @param outputPath 输出文件路径
     * @return 是否成功
     */
    public static boolean testSimpleConversion(String ffmpegPath, String inputPath, String outputPath) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    ffmpegPath, "-i", inputPath, "-y", outputPath
            );
            
            Process process = processBuilder.start();
            
            StringBuilder error = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    error.append(line).append("\n");
                }
            }
            
            boolean finished = process.waitFor(60, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.error("音频转换超时");
                return false;
            }
            
            int exitCode = process.exitValue();
            if (exitCode == 0) {
                log.info("音频转换成功: {} -> {}", inputPath, outputPath);
                return true;
            } else {
                log.error("音频转换失败，退出码: {}, 错误信息:\n{}", exitCode, error.toString());
                return false;
            }
            
        } catch (Exception e) {
            log.error("音频转换测试失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
