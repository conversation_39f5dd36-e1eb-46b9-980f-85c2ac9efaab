package com.wlink.agent.utils;

import com.wlink.agent.model.dto.SimpleUserInfo; // 确保导入正确的用户信息类
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserContext {

    private static final ThreadLocal<SimpleUserInfo> userThreadLocal = new ThreadLocal<>();

    public static void setUser(SimpleUserInfo userInfo) {
        userThreadLocal.set(userInfo);
        log.info("User set in ThreadLocal: {}", userInfo);
    }

    public static SimpleUserInfo getUser() {
        SimpleUserInfo user = userThreadLocal.get();
        log.info("User retrieved from ThreadLocal: {}", user);
        return user;
    }

    public static void clearUserInfo() {
        userThreadLocal.remove();
        log.info("User cleared from ThreadLocal.");
    }
}
