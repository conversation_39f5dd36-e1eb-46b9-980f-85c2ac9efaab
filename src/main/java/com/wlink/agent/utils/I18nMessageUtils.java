package com.wlink.agent.utils;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Locale;

/**
 * 国际化消息工具类
 */
@Component
public class I18nMessageUtils {

    private static MessageSource messageSource;

    @Resource
    public void setMessageSource(MessageSource messageSource) {
        I18nMessageUtils.messageSource = messageSource;
    }

    /**
     * 获取国际化消
     * @param code 消息key
     * @return 对应语言的消
     */
    public static String getMessage(String code) {
        return getMessage(code, null);
    }

    /**
     * 获取国际化消
     * @param code 消息key
     * @param args 参数
     * @return 对应语言的消
     */
    public static String getMessage(String code, Object[] args) {
        return getMessage(code, args, "");
    }

    /**
     * 获取国际化消
     * @param code 消息key
     * @param args 参数
     * @param defaultMessage 默认消息
     * @return 对应语言的消
     */
    public static String getMessage(String code, Object[] args, String defaultMessage) {
        // 获取当前请求的语言环境
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, args, defaultMessage, locale);
    }

    /**
     * 获取国际化消
     * @param code 消息key
     * @param args 参数
     * @param locale 指定语言
     * @return 对应语言的消
     */
    public static String getMessage(String code, Object[] args, Locale locale) {
        return messageSource.getMessage(code, args, locale);
    }
}
