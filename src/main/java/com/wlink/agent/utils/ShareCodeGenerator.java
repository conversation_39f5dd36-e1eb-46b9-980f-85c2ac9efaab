package com.wlink.agent.utils;

import com.wlink.agent.dao.mapper.AiVideoRenderExportMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.UUID;

/**
 * 分享码生成器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShareCodeGenerator {

    private final AiVideoRenderExportMapper videoRenderExportMapper;

    private static final SecureRandom random = new SecureRandom();
    private static final int MAX_RETRIES = 10; // 设置最大重试次数，防止死循环
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CODE_LENGTH = 8;

    /**
     * 生成全局唯一的分享码
     * @return 全局唯一的分享码
     */
    public String generateUniqueShareCode() {
        for (int i = 0; i < MAX_RETRIES; i++) {
            String code = generateRandomCode();
            if (!isCodeExists(code)) {
                log.info("Generated unique share code: {}", code);
                return code;
            }
            log.warn("Generated code {} already exists, retrying... ({}/{})", code, i + 1, MAX_RETRIES);
        }
        
        // 如果达到最大重试次数仍未生成唯一编码，使用UUID作为后备方案
        log.error("Failed to generate a unique share code after {} retries, using UUID fallback.", MAX_RETRIES);
        String fallbackCode = "SHARE-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        log.info("Generated fallback share code: {}", fallbackCode);
        return fallbackCode;
    }

    /**
     * 生成随机分享码
     * @return 随机分享码
     */
    private String generateRandomCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return code.toString();
    }

    /**
     * 检查分享码是否已存在
     * @param code 分享码
     * @return 是否存在
     */
    private boolean isCodeExists(String code) {
        try {
            return videoRenderExportMapper.selectByShareCode(code) != null;
        } catch (Exception e) {
            log.warn("检查分享码是否存在时发生异常: {}", e.getMessage());
            // 发生异常时，为了安全起见，认为编码已存在
            return true;
        }
    }
}
