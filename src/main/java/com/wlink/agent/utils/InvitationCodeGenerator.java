package com.wlink.agent.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.dao.mapper.AiInvitationCodesMapper; // 新增导入
import com.wlink.agent.dao.po.AiInvitationCodesPo; // 新增导入
import lombok.RequiredArgsConstructor; // 新增导入
import lombok.extern.slf4j.Slf4j; // 新增导入
import org.springframework.stereotype.Component;
import java.security.SecureRandom;
import java.util.Base64;

@Slf4j // 添加日志记录
@Component
@RequiredArgsConstructor // 使用 Lombok 自动注入 final 字段
public class InvitationCodeGenerator {

    private final AiInvitationCodesMapper aiInvitationCodesMapper; // 注入 Mapper

    private static final SecureRandom random = new SecureRandom();
    private static final Base64.Encoder encoder = Base64.getUrlEncoder().withoutPadding();
    private static final int CODE_LENGTH_BYTES = 8; // 生成码的字节长度
    private static final int MAX_RETRIES = 10; // 设置最大重试次数，防止死循

    /**
     * 生成一个全局唯一的邀请码.
     * 会查询数据库确保唯一
     * @return 全局唯一的邀请码字符
     * @throws RuntimeException 如果在最大重试次数内无法生成唯一
     */
    public String generateUniqueCode() {
        for (int i = 0; i < MAX_RETRIES; i++) {
            String code = generateRandomCode();
            if (!isCodeExists(code)) {
                log.info("Generated unique invitation code: {}", code);
                return code;
            }
            log.warn("Generated code {} already exists, retrying... ({}/{})", code, i + 1, MAX_RETRIES);
        }
        // 如果达到最大重试次数仍未生成唯一
        log.error("Failed to generate a unique invitation code after {} retries.", MAX_RETRIES);
        throw new RuntimeException("无法生成唯一的邀请码，请稍后重试或检查数据库状");
    }

    // 内部方法：生成随机码
    private String generateRandomCode() {
        byte[] buffer = new byte[CODE_LENGTH_BYTES];
        random.nextBytes(buffer);
        return encoder.encodeToString(buffer);
    }

    // 内部方法：检查数据库中是否存在该
    private boolean isCodeExists(String code) {
        LambdaQueryWrapper<AiInvitationCodesPo> queryWrapper = new LambdaQueryWrapper<>();
        // 仅根code 字段查询，因为我们需要全局唯一，不考虑 delFlag
        queryWrapper.eq(AiInvitationCodesPo::getCode, code);
        // 使用 selectCount 优化查询，我们只需要知道是否存
        Long count = aiInvitationCodesMapper.selectCount(queryWrapper);
        return count != null && count > 0;
    }
}
