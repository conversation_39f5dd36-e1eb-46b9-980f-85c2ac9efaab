package com.wlink.agent.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 文本拆分工具类
 */
@Slf4j
public class TextSplitUtils {
    
    /**
     * 支持的拆分标点符号
     */
    private static final String SPLIT_PUNCTUATION = "，。！？｜";
    
    /**
     * 拆分正则表达式
     */
    private static final Pattern SPLIT_PATTERN = Pattern.compile("[" + SPLIT_PUNCTUATION + "]");
    
    /**
     * 按标点符号拆分文本
     * 拆分标点符号：，。！？｜
     * 注意：拆分后的文本段落不包含用于拆分的标点符号
     *
     * @param text 要拆分的文本
     * @return 拆分后的文本段落列表（不包含拆分标点符号）
     */
    public static List<String> splitTextByPunctuation(String text) {
        List<String> segments = new ArrayList<>();

        if (!StringUtils.hasText(text)) {
            log.warn("输入文本为空，返回空列表");
            return segments;
        }

        log.info("开始拆分文本，原文本长度: {}", text.length());

        // 使用正则表达式拆分，不保留分隔符
        String[] parts = SPLIT_PATTERN.split(text);

        // 直接处理拆分后的文本段落，不添加标点符号
        for (String part : parts) {
            String trimmedPart = part.trim();
            if (!trimmedPart.isEmpty()) {
                segments.add(trimmedPart);
                log.debug("添加文本段落: {}", trimmedPart);
            }
        }

        log.info("文本拆分完成，共拆分为 {} 个段落", segments.size());
        return segments;
    }
    
    /**
     * 估算文本的音频时长（毫秒）
     * 基于经验值：中文平均每个字符约150毫秒
     * 
     * @param text 文本内容
     * @return 估算的音频时长（毫秒）
     */
    public static long estimateAudioDuration(String text) {
        if (!StringUtils.hasText(text)) {
            return 0L;
        }
        
        // 去除标点符号和空格，只计算有效字符
        String cleanText = text.replaceAll("[\\p{Punct}\\s]", "");
        int charCount = cleanText.length();
        
        // 中文字符平均时长约150毫秒，英文单词平均时长约300毫秒
        // 这里简化处理，统一按150毫秒计算
        long estimatedDuration = charCount * 150L;
        
        log.debug("文本: {}, 有效字符数: {}, 估算时长: {}ms", text, charCount, estimatedDuration);
        return estimatedDuration;
    }
}
