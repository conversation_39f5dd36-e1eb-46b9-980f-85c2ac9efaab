package com.wlink.agent.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class JsonFileUtils {

    /**
     * 读取JSON文件内容
     * @param path 文件路径(相对于resources目录)
     * @param clazz 目标类型
     * @return 解析后的对象
     */
    public static <T> T readJsonFile(String path, Class<T> clazz) {
        try {
            ClassPathResource resource = new ClassPathResource(path);
            String jsonContent = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
            return JSON.parseObject(jsonContent, clazz);
        } catch (IOException e) {
            log.error("Read json file failed: {}", path, e);
            throw new RuntimeException("Read json file failed", e);
        }
    }
} 
