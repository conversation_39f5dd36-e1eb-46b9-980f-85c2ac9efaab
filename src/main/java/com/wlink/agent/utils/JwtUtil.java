package com.wlink.agent.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
@RefreshScope
@Component
public class JwtUtil {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);

    @Value("${jwt.secret:rM9$K@pL7#&!sQfThWmZq3t6w9z$C&F)J@NcRfUjXn2r5u8x/A?D*G-KaPdSgVkY}")
    private String secretKeyString;

    @Value("${jwt.expiration:2592000000}") // 默认30(30 * 24 * 60 * 60 * 1000)
    private Long expirationTimeMillis;

    @Value("${jwt.algorithm:HS512}")
    private String algorithm;

    @Value("${jwt.issuer:wlink-system}")
    private String issuer;

    @Value("${jwt.audience:wlink-clients}")
    private String audience;

    private SecretKey secretKey;

    @PostConstruct
    public void init() {
        // 安全性检查和初始
        if (secretKeyString.length() < 64) {
            logger.warn("Security warning: JWT secret key is less than recommended length for HS512");
        }

        byte[] keyBytes = secretKeyString.getBytes(StandardCharsets.UTF_8);
        this.secretKey = Keys.hmacShaKeyFor(keyBytes);
        // 清除明文密钥
        secretKeyString = null;

        logger.info("JwtUtil initialized with algorithm: {}", algorithm);
    }

    private SignatureAlgorithm getSignatureAlgorithm() {
        try {
            return SignatureAlgorithm.valueOf(algorithm);
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid algorithm: {}, falling back to HS512", algorithm);
            return SignatureAlgorithm.HS512;
        }
    }

    // 基本Token生成
    public String generateToken(String userId, String email) {
        return generateToken(userId, email, new HashMap<>());
    }

    // 支持自定义Claims的Token生成
    public String generateToken(String userId, String email, Map<String, Object> additionalClaims) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + expirationTimeMillis);

        Map<String, Object> claims = new HashMap<>(additionalClaims);
        claims.put("userId", userId);
        claims.put("email", email);

        return Jwts.builder()
                .claims(claims) // 新版API使用claims()而不是setClaims()
                .subject(userId)
                .issuedAt(now)
                .notBefore(now)
                .expiration(expirationDate)
                .issuer(issuer)
                .audience().add(audience).and() // 新版API的audience设置方式
                .id(UUID.randomUUID().toString())
                .signWith(secretKey, getSignatureAlgorithm())
                .compact();
    }

    // Token刷新
    public String refreshToken(String token) {
        try {
            // 从原 token 中提取所需信息
            Claims originalClaims = extractAllClaims(token);
            String userId = originalClaims.get("userId", String.class);
            String email = originalClaims.get("email", String.class);

            // 收集其他自定claims (排除标准 JWT claims)
            Map<String, Object> customClaims = new HashMap<>();
            for (Map.Entry<String, Object> entry : originalClaims.entrySet()) {
                String key = entry.getKey();
                // 过滤掉标JWT claims
                if (!key.equals("sub") && !key.equals("iat") && !key.equals("exp") &&
                        !key.equals("jti") && !key.equals("iss") && !key.equals("aud") &&
                        !key.equals("nbf") && !key.equals("userId") && !key.equals("email")) {
                    customClaims.put(key, entry.getValue());
                }
            }

            // 重新生成一个新token
            return generateToken(userId, email, customClaims);
        } catch (Exception e) {
            logger.error("Failed to refresh token: {}", e.getMessage());
            throw new JwtException("Failed to refresh token", e);
        }
    }

    // Claims提取 - 使用新版API
    public Claims extractAllClaims(String token) {
        return Jwts.parser()
                .verifyWith(secretKey)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    // 通用Claim提取
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    // 提取特定Claims
    public String extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", String.class));
    }

    public String extractEmail(String token) {
        return extractClaim(token, claims -> claims.get("email", String.class));
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public String extractTokenId(String token) {
        return extractClaim(token, Claims::getId);
    }

    // Token验证
    public enum TokenValidationResult {
        VALID,
        EXPIRED,
        INVALID_SIGNATURE,
        MALFORMED,
        USER_MISMATCH,
        OTHER_ERROR
    }

    public TokenValidationResult validateToken(String token, String userId) {
        try {
            // 首先验证签名和格
            Claims claims = extractAllClaims(token);

            // 验证用户ID
            String extractedUserId = claims.get("userId", String.class);
            if (!extractedUserId.equals(userId)) {
                logger.warn("Token validation failed: User ID mismatch");
                return TokenValidationResult.USER_MISMATCH;
            }

            // 验证过期时间
            if (claims.getExpiration().before(new Date())) {
                return TokenValidationResult.EXPIRED;
            }

            return TokenValidationResult.VALID;
        } catch (ExpiredJwtException e) {
            logger.info("Token expired: {}", e.getMessage());
            return TokenValidationResult.EXPIRED;
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
            return TokenValidationResult.INVALID_SIGNATURE;
        } catch (MalformedJwtException e) {
            logger.error("Malformed JWT token: {}", e.getMessage());
            return TokenValidationResult.MALFORMED;
        } catch (Exception e) {
            logger.error("Token validation error: {}", e.getMessage());
            return TokenValidationResult.OTHER_ERROR;
        }
    }

    // 简易布尔返回的验证方法（兼容旧版本
    public boolean isTokenValid(String token, String userId) {
        return validateToken(token, userId) == TokenValidationResult.VALID;
    }

     /**
     * 验证 Token 的签名和有效期，不检查用ID
     * @param token JWT Token
     * @return 如果签名有效且未过期则返true，否则返false
     */
    public boolean validateTokenWithoutUserCheck(String token) {
        try {
            extractAllClaims(token); // 尝试解析，如果成功则签名有效且未过期
            return true;
        } catch (ExpiredJwtException e) {
            logger.info("Token expired (checked without user): {}", e.getMessage()); // 可以info debug
            return false;
        } catch (JwtException | IllegalArgumentException e) {
            // 捕获签名错误、格式错误或其他JWT相关异常
            logger.warn("Token validation failed (checked without user): {}", e.getMessage());
            return false;
        }
    }

    // 检查Token是否过期
    public boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    // 用于支持Token撤销的方法（需要配合Redis等外部存储）
    public boolean isTokenRevoked(String token) {
        // 需要集成缓存系统来实现令牌撤销检
        // 这里仅为示例，实际实现需要检查令牌ID是否在黑名单
        String tokenId = extractTokenId(token);
        // return redisTemplate.hasKey("token:blacklist:" + tokenId);
        return false; // 默认未撤销
    }

    // 创建访问令牌与刷新令牌对
    public Map<String, String> createTokenPair(String userId, String email) {
        Map<String, String> tokenPair = new HashMap<>();

        // 创建短期访问令牌 (15分钟)
        Map<String, Object> accessTokenClaims = new HashMap<>();
        accessTokenClaims.put("type", "access");
        accessTokenClaims.put("userId", userId);
        accessTokenClaims.put("email", email);

        String accessToken = Jwts.builder()
                .claims(accessTokenClaims)
                .subject(userId)
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + 900000)) // 15分钟
                .issuer(issuer)
                .id(UUID.randomUUID().toString())
                .signWith(secretKey, getSignatureAlgorithm())
                .compact();

        // 创建长期刷新令牌 (7
        Map<String, Object> refreshTokenClaims = new HashMap<>();
        refreshTokenClaims.put("type", "refresh");
        refreshTokenClaims.put("userId", userId);

        String refreshToken = Jwts.builder()
                .claims(refreshTokenClaims)
                .subject(userId)
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + 604800000)) // 7
                .issuer(issuer)
                .id(UUID.randomUUID().toString())
                .signWith(secretKey, getSignatureAlgorithm())
                .compact();

        tokenPair.put("accessToken", accessToken);
        tokenPair.put("refreshToken", refreshToken);

        return tokenPair;
    }

    // 从刷新令牌创建新的访问令
    public String createAccessTokenFromRefreshToken(String refreshToken) {
        Claims claims = extractAllClaims(refreshToken);

        // 验证这是否是刷新令牌
        String tokenType = claims.get("type", String.class);
        if (!"refresh".equals(tokenType)) {
            throw new JwtException("Token is not a refresh token");
        }

        String userId = claims.get("userId", String.class);

        // 创建新的访问令牌
        Map<String, Object> accessTokenClaims = new HashMap<>();
        accessTokenClaims.put("type", "access");
        accessTokenClaims.put("userId", userId);

        return Jwts.builder()
                .claims(accessTokenClaims)
                .subject(userId)
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + 900000)) // 15分钟
                .issuer(issuer)
                .id(UUID.randomUUID().toString())
                .signWith(secretKey, getSignatureAlgorithm())
                .compact();
    }
}
