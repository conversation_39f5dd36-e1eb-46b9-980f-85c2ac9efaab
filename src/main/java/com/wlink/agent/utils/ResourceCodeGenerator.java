package com.wlink.agent.utils;

import com.wlink.agent.dao.mapper.AiUserResourceMapper;
import com.wlink.agent.enums.UserResourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.UUID;

/**
 * 资源编码生成器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceCodeGenerator {

    private final AiUserResourceMapper userResourceMapper;

    private static final SecureRandom random = new SecureRandom();
    private static final int MAX_RETRIES = 10; // 设置最大重试次数，防止死循环

    /**
     * 生成图片资源编码
     * @return 全局唯一的图片资源编码
     */
    public String generateImageCode() {
        return generateUniqueCode("IMG");
    }

    /**
     * 生成视频资源编码
     * @return 全局唯一的视频资源编码
     */
    public String generateVideoCode() {
        return generateUniqueCode("VID");
    }

    /**
     * 生成音频资源编码
     * @return 全局唯一的音频资源编码
     */
    public String generateAudioCode() {
        return generateUniqueCode("AUD");
    }

    /**
     * 根据资源类型生成编码
     * @param resourceType 资源类型
     * @return 全局唯一的资源编码
     */
    public String generateCodeByType(UserResourceType resourceType) {
        switch (resourceType) {
            case IMAGE:
                return generateImageCode();
            case VIDEO:
                return generateVideoCode();
            case AUDIO:
                return generateAudioCode();
            default:
                throw new IllegalArgumentException("不支持的资源类型: " + resourceType);
        }
    }

    /**
     * 根据资源类型值生成编码
     * @param resourceTypeValue 资源类型值
     * @return 全局唯一的资源编码
     */
    public String generateCodeByTypeValue(Integer resourceTypeValue) {
        UserResourceType resourceType = UserResourceType.fromValue(resourceTypeValue);
        return generateCodeByType(resourceType);
    }

    /**
     * 生成全局唯一的资源编码
     * @param prefix 编码前缀
     * @return 全局唯一的资源编码
     */
    private String generateUniqueCode(String prefix) {
        for (int i = 0; i < MAX_RETRIES; i++) {
            String code = generateRandomCode(prefix);
            if (!isCodeExists(code)) {
                log.info("Generated unique resource code: {}", code);
                return code;
            }
            log.warn("Generated code {} already exists, retrying... ({}/{})", code, i + 1, MAX_RETRIES);
        }
        
        // 如果达到最大重试次数仍未生成唯一编码，使用UUID作为后备方案
        log.error("Failed to generate a unique resource code after {} retries, using UUID fallback.", MAX_RETRIES);
        String fallbackCode = prefix + "-" + UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
        log.info("Generated fallback resource code: {}", fallbackCode);
        return fallbackCode;
    }

    /**
     * 生成随机编码
     * @param prefix 编码前缀
     * @return 随机编码
     */
    private String generateRandomCode(String prefix) {
        // 生成8位随机字符串
        String randomPart = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        return prefix + "-" + randomPart;
    }

    /**
     * 检查编码是否已存在
     * @param code 编码
     * @return 是否存在
     */
    private boolean isCodeExists(String code) {
        try {
            return userResourceMapper.selectByCode(code) != null;
        } catch (Exception e) {
            log.warn("检查编码是否存在时发生异常: {}", e.getMessage());
            // 发生异常时，为了安全起见，认为编码已存在
            return true;
        }
    }
}
