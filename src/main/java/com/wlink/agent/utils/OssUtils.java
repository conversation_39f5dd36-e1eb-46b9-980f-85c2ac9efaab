package com.wlink.agent.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.net.URL;

@Slf4j
@Component
public class OssUtils {

    @Value("${spring.profiles.active}")
    String env;

    @Resource
    private OSS ossClient;

    @Value("${wlink.agent.bucket-name:wlpaas}")
    private String bucketName;
    
    @Value("${wlink.agent.oss-domain:https://wlpaas.oss-cn-shanghai.aliyuncs.com}")
    private String ossDomain;


    /**
     * 上传文件到OSS
     */
    public String uploadFile(File file, String objectName) {
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, file);
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            log.info("File uploaded successfully, ETag: {}", result.getETag());
            return objectName;
        } catch (Exception e) {
            log.error("Failed to upload file to OSS", e);
            throw new RuntimeException("Failed to upload file to OSS: " + e.getMessage());
        }
    }

    /**
     * 上传输入流到OSS
     */
    public String uploadStream(InputStream inputStream, String objectName) {
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            log.info("Stream uploaded successfully, ETag: {}", result.getETag());
            return objectName;
        } catch (Exception e) {
            log.error("Failed to upload stream to OSS", e);
            throw new RuntimeException("Failed to upload stream to OSS: " + e.getMessage());
        }
    }

    /**
     * 上传单个文件到oss
     */
    public String uploadFile(String fileUrl, String targetName) {
        try {
            log.info("上传文件url到oss，fileUrl={},targetName={}", fileUrl, targetName);
            URL url = new URL(fileUrl);
            InputStream inputStream = url.openStream();
            PutObjectRequest request = new PutObjectRequest(bucketName, targetName, inputStream);
            ossClient.putObject(request);
            log.info("上传文件url到oss成功，fileUrl={},targetName={}", fileUrl, targetName);
            return targetName;
        } catch (Exception e) {
            log.error("上传文件流到oss异常，bucketName={},targetName={},错误详情:", bucketName, targetName, e);
            return "";
        }
    }
    
    /**
     * 获取OSS文件的完整访问URL
     * 
     * @param objectName OSS对象名称
     * @return 完整的访问URL
     */
    public String getOssUrl(String objectName) {
        if (objectName == null || objectName.isEmpty()) {
            return "";
        }
        // 确保URL正确拼接，避免重复斜杠
        if (ossDomain.endsWith("/") && objectName.startsWith("/")) {
            return ossDomain + objectName.substring(1);
        } else if (!ossDomain.endsWith("/") && !objectName.startsWith("/")) {
            return ossDomain + "/" + objectName;
        } else {
            return ossDomain + objectName;
        }
    }

    public String getAudioPath(String roomId) {
        return env + "/audio/" + roomId + "/";
    }

    public String getAudioToFacePath(String roomId) {
        return env + "/face_features/" + roomId + "/";
    }

    public String getImagePath(String userName) {
        return env + "/image/" + userName + "/";
    }

    public String getVideoPath(String userName) {
        return env + "/video/" + userName + "/";
    }
}
