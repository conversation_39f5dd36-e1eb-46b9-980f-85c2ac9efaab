package com.wlink.agent.mq;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.wlink.agent.config.AliyunRocketMQProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Slf4j
@Component
public class AliyunOnsConsumerManager implements InitializingBean, DisposableBean {

    @Autowired
    private AliyunRocketMQProperties properties;

    @Autowired
    private ImageGenerationTaskListener imageGenerationTaskListener;
    @Autowired
    private PaymentPointsListener paymentPointsListener;

    private ConsumerBean imageTaskConsumer;
    private ConsumerBean paymentPointsConsumer;

    @Override
    public void afterPropertiesSet() {
        log.info("Initializing Aliyun ONS Consumers...");
        initImageTaskConsumer();
        // 如果有其他消费者，在这里继续调用初始化方法
        initPaymentPointsConsumer();
    }



    private void initImageTaskConsumer() {
        try {
            imageTaskConsumer = new ConsumerBean();

            // 1. 设置基本配置
            // 复用通用配置
            Properties props = buildCommonConsumerProperties();
            imageTaskConsumer.setProperties(props);

            // 2. 设置订阅关系
            Map<Subscription, com.aliyun.openservices.ons.api.MessageListener> subscriptionTable = new HashMap<>();
            Subscription subscription = new Subscription();
            subscription.setTopic(properties.getTopic().getImageTask());
            // 订阅所有 Tag，用 "*"。如果只想订阅部分 Tag，可以用 "TagA || TagB || TagC"
            subscription.setExpression("*");

            // 3. 将订阅和监听器关联起来
            subscriptionTable.put(subscription, imageGenerationTaskListener);

            imageTaskConsumer.setSubscriptionTable(subscriptionTable);

            // 4. 启动消费者
            imageTaskConsumer.start();
            log.info("Image task consumer started successfully. GroupID: {}, Topic: {}",
                    properties.getConsumer().getGroupId(), properties.getTopic().getImageTask());

        } catch (Exception e) {
            log.error("Failed to start image task consumer!", e);
            throw new RuntimeException("Failed to start image task consumer!", e);
        }
    }

    /**
     * 【新增】初始化支付积分消费者
     */
    private void initPaymentPointsConsumer() {
        try {
            paymentPointsConsumer = new ConsumerBean();

            // 复用通用配置
            Properties props = buildCommonConsumerProperties();
            paymentPointsConsumer.setProperties(props);

            // 设置订阅关系
            Map<Subscription, com.aliyun.openservices.ons.api.MessageListener> subscriptionTable = new HashMap<>();
            Subscription subscription = new Subscription();
            // 注意：这里订阅的是支付积分的 Topic
            subscription.setTopic(properties.getTopic().getPaymentPoints());
            // 订阅支付和退款两个 Tag
            subscription.setExpression(MqTag.TAG_PAYMENT_POINTS + " || " + MqTag.TAG_REFUND_POINTS);

            // 绑定到新的监听器
            subscriptionTable.put(subscription, paymentPointsListener);
            paymentPointsConsumer.setSubscriptionTable(subscriptionTable);

            // 启动消费者
            paymentPointsConsumer.start();
            log.info("Payment points consumer started successfully. GroupID: {}, Topic: {}",
                    properties.getConsumer().getGroupId(), properties.getTopic().getPaymentPoints());

        } catch (Exception e) {
            log.error("Failed to start payment points consumer!", e);
            throw new RuntimeException("Failed to start payment points consumer!", e);
        }
    }

    /**
     * 【新增】抽取公共的配置构建逻辑，避免代码重复
     */
    private Properties buildCommonConsumerProperties() {
        Properties props = new Properties();
        props.setProperty(PropertyKeyConst.GROUP_ID, properties.getConsumer().getGroupId());
        props.setProperty(PropertyKeyConst.AccessKey, properties.getAccessKey());
        props.setProperty(PropertyKeyConst.SecretKey, properties.getSecretKey());
        props.setProperty(PropertyKeyConst.NAMESRV_ADDR, properties.getNameSrvAddr());
        props.setProperty(PropertyKeyConst.ConsumeThreadNums, String.valueOf(properties.getConsumer().getConsumeThreadNums()));
        props.setProperty(PropertyKeyConst.MaxReconsumeTimes, String.valueOf(properties.getConsumer().getMaxReconsumeTimes()));
        return props;
    }


    @Override
    public void destroy() {
        log.info("Shutting down Aliyun ONS Consumers...");
        if (imageTaskConsumer != null && imageTaskConsumer.isStarted()) {
            imageTaskConsumer.shutdown();
            log.info("Image task consumer shut down.");
        }
    }
}