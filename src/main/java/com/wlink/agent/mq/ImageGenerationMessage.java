package com.wlink.agent.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片生成任务消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageGenerationMessage {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务类型（tag
     */
    private String taskType;
    
    /**
     * 只有taskId的构造函数（向后兼容
     */
    public ImageGenerationMessage(String taskId) {
        this.taskId = taskId;
        this.taskType = null;
    }
} 
