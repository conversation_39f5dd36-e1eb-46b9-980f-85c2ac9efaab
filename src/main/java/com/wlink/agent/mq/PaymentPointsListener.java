package com.wlink.agent.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.wlink.agent.mq.message.CommonMessage;
import com.wlink.agent.service.UserPointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 支付积分消息消费
 * 用于处理支付成功增加积分和退款扣除积分的消息
 */
@Slf4j
@Component
public class PaymentPointsListener implements MessageListener {

    @Autowired
    private UserPointsService userPointsService;


    @Override
    public Action consume(Message message, ConsumeContext context) {

        try {
            String topic = message.getTopic();
            String tag = message.getTag();
            String messageBody = new String(message.getBody(), "UTF-8");
            // 解析消息
            log.info("收到支付积分消息: tag={}, body={}", tag, messageBody);

            CommonMessage commonMessage = JSON.parseObject(messageBody, CommonMessage.class);
            Map<String, Object> data = JSON.parseObject(commonMessage.getData(), new TypeReference<Map<String, Object>>() {
            });

            String userId = (String) data.get("userId");
            String orderNo = (String) data.get("orderNo");
            // 注意：JSON解析可能将Integer转为Long，需要转换回Integer
            Integer amount = ((Number) data.get("amount")).intValue();

            if (MqTag.TAG_PAYMENT_POINTS.equals(tag)) {
                // 处理支付增加积分
                userPointsService.addPointsAfterPayment(userId, orderNo, amount);
                log.info("支付增加积分处理成功: userId={}, orderNo={}", userId, orderNo);
            } else if (MqTag.TAG_REFUND_POINTS.equals(tag)) {
                // 处理退款扣除积
                userPointsService.deductPointsAfterRefund(userId, orderNo, amount);
                log.info("退款扣除积分处理成 userId={}, orderNo={}", userId, orderNo);
            } else {
                log.warn("未知的支付积分消息类 {}", tag);
            }
            // 消费成功，返回 CommitMessage
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("处理支付积分消息失败: {}", e.getMessage(), e);
            // 消费失败，返回 ReconsumeLater，消息将会在稍后重新投递
            return Action.ReconsumeLater;
        }

    }
}
