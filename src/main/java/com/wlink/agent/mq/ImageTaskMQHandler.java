package com.wlink.agent.mq;

import com.alibaba.cola.exception.BizException;
import com.aliyun.openservices.ons.api.*;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import com.wlink.agent.config.AliyunRocketMQProperties;
import com.wlink.agent.mq.message.CommonMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 图片任务MQ处理器
 * 使用RocketMQ发送图片生成任务消息
 */
@Slf4j
@Component
public class ImageTaskMQHandler implements InitializingBean, DisposableBean {

    /**
     * 任务标签
     */
    private static final String TAG_GENERATE = "TAG_GENERATE";
    private static final String TAG_RETAIN = "TAG_RETAIN";
    private static final String TAG_GENERATE_DOUBAO = "TAG_GENERATE_DOUBAO";
    private static final String TAG_GENERATE_FLUX = "TAG_GENERATE_FLUX";


    // 注入配置类，替代所有 @Value
    @Autowired
    private AliyunRocketMQProperties properties;

    private ProducerBean producer;

    @Override
    public void afterPropertiesSet() {
        // 使用配置类初始化 Producer
        try {
            producer = new ProducerBean();
            Properties props = new Properties();
            props.setProperty(PropertyKeyConst.GROUP_ID, properties.getGroupId());
            props.setProperty(PropertyKeyConst.AccessKey, properties.getAccessKey());
            props.setProperty(PropertyKeyConst.SecretKey, properties.getSecretKey());
            props.setProperty(PropertyKeyConst.NAMESRV_ADDR, properties.getNameSrvAddr());

            producer.setProperties(props);
            producer.start();
            log.info("Aliyun ONS producer initialized successfully");
        } catch (ONSClientException e) {
            log.error("Failed to initialize Aliyun ONS producer", e);
            // 这里可以根据业务需求决定是否要抛出异常中断启动
            throw new RuntimeException("Failed to initialize Aliyun ONS producer", e);
        }
    }

    @Override
    public void destroy() {
        if (producer != null) {
            producer.shutdown();
            log.info("Aliyun ONS producer shut down.");
        }
    }


    /**
     * 发送任务消息
     *
     * @param taskId 任务ID
     * @param tag 消息标签
     */
    public void sendTaskMessage(String taskId, String tag) {
        if (producer == null) {
            log.error("RocketMQ producer is not initialized");
            throw new BizException("RocketMQ producer is not initialized");
        }

        try {
            // 创建消息对象
            ImageGenerationMessage messageBody = new ImageGenerationMessage(taskId, tag);
            String jsonMessage = com.alibaba.fastjson.JSON.toJSONString(messageBody);
            
            Message message = new Message();
            message.setTopic(properties.getTopic().getImageTask());
            message.setTag(mapTaskTypeToTag(tag));
            message.setKey(taskId);
            message.setBody(jsonMessage.getBytes());

            // 发送消息，不等待结果返回
            producer.sendAsync(message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("Send message success: {}", sendResult);
                }

                @Override
                public void onException(OnExceptionContext context) {
                    log.error("Send message failed: {}", context.getException().getMessage());
                }
            });
        } catch (Exception e) {
            log.error("Failed to send message to RocketMQ", e);
            throw new BizException("Failed to send message to RocketMQ: " + e.getMessage());
        }
    }

    /**
     * 将任务类型映射到标签
     */
    private String mapTaskTypeToTag(String taskType) {
        if (taskType == null) {
            return TAG_GENERATE;
        }
        
        switch (taskType) {
            case "GENERATE_DOUBAO":
                return MqTag.TAG_GENERATE_DOUBAO;
            case "GENERATE_FLUX":
                return MqTag.TAG_GENERATE_FLUX;
            case "RETAIN":
                return TAG_RETAIN;
            default:
                return TAG_GENERATE;
        }
    }

    /**
     * 发送生成任务消息
     *
     * @param taskId 任务ID
     */
    public void sendGenerateTaskMessage(String taskId) {
        sendTaskMessage(taskId, TAG_GENERATE);
    }

    /**
     * 发送保留任务消息
     *
     * @param taskId 任务ID
     */
    public void sendRetainTaskMessage(String taskId) {
        sendTaskMessage(taskId, TAG_RETAIN);
    }

    /**
     * 发送延迟任务消息
     *
     * @param taskId 任务ID
     * @param delayLevel 延迟级别
     */
    public void sendDelayTaskMessage(String taskId, int delayLevel,String tag) {
        if (producer == null) {
            log.error("RocketMQ producer is not initialized");
            throw new BizException("RocketMQ producer is not initialized");
        }

        try {
            // 创建消息对象
            ImageGenerationMessage messageBody = new ImageGenerationMessage(taskId);
            String jsonMessage = com.alibaba.fastjson.JSON.toJSONString(messageBody);
            
            Message message = new Message();
            message.setTopic(properties.getTopic().getImageTask());
            message.setTag(StringUtils.isBlank(tag)? TAG_GENERATE : tag);
            message.setKey(taskId);
            message.setBody(jsonMessage.getBytes());
            // 设置延迟级别
            message.setStartDeliverTime(System.currentTimeMillis() + getDelayTimeMillis(delayLevel));

            // 发送消息，不等待结果返回
            producer.sendAsync(message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("Send delay message success: {}, delay level: {}", sendResult, delayLevel);
                }

                @Override
                public void onException(OnExceptionContext context) {
                    log.error("Send delay message failed: {}", context.getException().getMessage());
                }
            });
        } catch (Exception e) {
            log.error("Failed to send delay message to RocketMQ", e);
            throw new BizException("Failed to send delay message to RocketMQ: " + e.getMessage());
        }
    }
    
    /**
     * 根据延迟级别获取延迟时间（毫秒）
     */
    private long getDelayTimeMillis(int delayLevel) {
        // 延迟级别对应的时间（秒）
        switch (delayLevel) {
            case 1: return 1 * 1000L;
            case 2: return 5 * 1000L;
            case 3: return 10 * 1000L;
            case 4: return 30 * 1000L;
            case 5: return 60 * 1000L;
            case 6: return 120 * 1000L;
            case 7: return 180 * 1000L;
            default: return 60 * 1000L; // 默认1分钟
        }
    }
    
    /**
     * 发送支付积分消息
     *
     * @param userId 用户ID
     * @param orderNo 订单号
     * @param amount 金额（分）
     */
    public void sendPaymentPointsMessage(String userId, String orderNo, Integer amount) {
        if (producer == null) {
            log.error("RocketMQ producer is not initialized");
            throw new BizException("RocketMQ producer is not initialized");
        }

        try {
            // 创建消息数据
            Map<String, Object> data = new HashMap<>();
            data.put("userId", userId);
            data.put("orderNo", orderNo);
            data.put("amount", amount);
            String jsonData = com.alibaba.fastjson.JSON.toJSONString(data);
            
            // 创建通用消息体
            CommonMessage messageBody = new CommonMessage("PAYMENT", jsonData);
            String jsonMessage = com.alibaba.fastjson.JSON.toJSONString(messageBody);
            
            // 创建消息对象
            Message message = new Message();
            message.setTopic(properties.getTopic().getPaymentPoints());  // 使用支付积分专用topic
            message.setTag(MqTag.TAG_PAYMENT_POINTS);
            message.setKey(orderNo);
            message.setBody(jsonMessage.getBytes());

            // 发送消息，不等待结果返回
            producer.sendAsync(message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("Send payment points message success: userId={}, orderNo={}", userId, orderNo);
                }

                @Override
                public void onException(OnExceptionContext context) {
                    log.error("Send payment points message failed: {}", context.getException().getMessage());
                }
            });
        } catch (Exception e) {
            log.error("Failed to send payment points message to RocketMQ: userId={}, orderNo={}, error={}", 
                    userId, orderNo, e.getMessage(), e);
            throw new BizException("Failed to send payment points message to RocketMQ: " + e.getMessage());
        }
    }

    /**
     * 发送退款扣除积分消息
     *
     * @param userId 用户ID
     * @param orderNo 订单号
     * @param amount 金额（分）
     */
    public void sendRefundPointsMessage(String userId, String orderNo, Integer amount) {
        if (producer == null) {
            log.error("RocketMQ producer is not initialized");
            throw new BizException("RocketMQ producer is not initialized");
        }

        try {
            // 创建消息数据
            Map<String, Object> data = new HashMap<>();
            data.put("userId", userId);
            data.put("orderNo", orderNo);
            data.put("amount", amount);
            String jsonData = com.alibaba.fastjson.JSON.toJSONString(data);
            
            // 创建通用消息体
            CommonMessage messageBody = new CommonMessage("REFUND", jsonData);
            String jsonMessage = com.alibaba.fastjson.JSON.toJSONString(messageBody);
            
            // 创建消息对象
            Message message = new Message();
            message.setTopic(properties.getTopic().getPaymentPoints());  // 使用支付积分专用topic
            message.setTag(MqTag.TAG_REFUND_POINTS);
            message.setKey(orderNo);
            message.setBody(jsonMessage.getBytes());

            // 发送消息，不等待结果返回
            producer.sendAsync(message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("Send refund points message success: userId={}, orderNo={}", userId, orderNo);
                }

                @Override
                public void onException(OnExceptionContext context) {
                    log.error("Send refund points message failed: {}", context.getException().getMessage());
                }
            });
        } catch (Exception e) {
            log.error("Failed to send refund points message to RocketMQ: userId={}, orderNo={}, error={}", 
                    userId, orderNo, e.getMessage(), e);
            throw new BizException("Failed to send refund points message to RocketMQ: " + e.getMessage());
        }
    }
}