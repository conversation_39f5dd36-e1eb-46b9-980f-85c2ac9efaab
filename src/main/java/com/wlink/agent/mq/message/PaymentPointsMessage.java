package com.wlink.agent.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付积分消息实体
 * 用于在支付成功或退款时发送积分变更消
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentPointsMessage {
    /**
     * 消息类型：PAYMENT-支付增加积分，REFUND-退款扣除积
     */
    private String type;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 订单
     */
    private String orderNo;
    
    /**
     * 金额（分
     */
    private Integer amount;
} 
