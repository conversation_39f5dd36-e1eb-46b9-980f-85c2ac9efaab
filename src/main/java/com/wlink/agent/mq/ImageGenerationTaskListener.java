package com.wlink.agent.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.wlink.agent.service.impl.FluxImageGenerationService;
import com.wlink.agent.service.impl.ReliableImageGenerationService;
import com.wlink.agent.service.impl.StableImageGenerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component // 必须是 Spring Bean，这样才能注入其他服务
public class ImageGenerationTaskListener implements MessageListener {

    @Autowired
    private ReliableImageGenerationService doubaoImageGenerationService;

    @Autowired
    private FluxImageGenerationService fluxImageGenerationService;

    @Autowired
    private StableImageGenerationService stableImageGenerationService;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        try {
            String topic = message.getTopic();
            String tag = message.getTag();
            String body = new String(message.getBody(), "UTF-8");

            log.info("Received message from Aliyun ONS. Topic: {}, Tag: {}, MsgId: {}", topic, tag, message.getMsgID());

            // 解析消息体
            ImageGenerationMessage generationMessage = JSON.parseObject(body, ImageGenerationMessage.class);
            String taskId = generationMessage.getTaskId();

            // 根据 Tag 分发业务逻辑
            if (MqTag.TAG_GENERATE_FLUX.equals(tag)) {
                fluxImageGenerationService.processFluxImageTask(taskId);
                log.info("FLUX图像生成任务处理完成: {}", taskId);
            } else {
                // 使用新的稳定图像生成服务，基于数据库并发控制
                stableImageGenerationService.processImageTask(taskId);
                log.info("图像生成任务处理完成: {}", taskId);
            }

            // 消费成功，返回 CommitMessage
            return Action.CommitMessage;

        } catch (Exception e) {
            log.error("消费图片生成任务失败, MsgId: {}, ReconsumeTimes: {}", message.getMsgID(), message.getReconsumeTimes(), e);
            // 消费失败，返回 ReconsumeLater，消息将会在稍后重新投递
            return Action.ReconsumeLater;
        }
    }
}