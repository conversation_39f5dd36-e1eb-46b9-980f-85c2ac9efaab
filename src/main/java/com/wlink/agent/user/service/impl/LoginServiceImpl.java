package com.wlink.agent.user.service.impl;


import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.constant.WechatConstant;
import com.wlink.agent.dao.mapper.AiInvitationCodesMapper;
import com.wlink.agent.dao.mapper.AiPointTransactionsMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.po.AiInvitationCodesPo;
import com.wlink.agent.dao.po.AiPointTransactionsPo;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.service.ContentService;
import com.wlink.agent.service.SmsService;
import com.wlink.agent.service.WechatService;
import com.wlink.agent.user.model.MobileBindRes;
import com.wlink.agent.user.model.UserLoginRes;
import com.wlink.agent.user.model.WechatLoginRes;
import com.wlink.agent.user.service.LoginService;
import com.wlink.agent.utils.I18nMessageUtils;
import com.wlink.agent.utils.InvitationCodeGenerator;
import com.wlink.agent.utils.JwtUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
@RefreshScope
@Service
public class LoginServiceImpl implements LoginService {

    private final AiUsersMapper aiUsersMapper;
    private final RedissonClient redissonClient;
  //  private final JavaMailSender javaMailSender;
    private final JwtUtil jwtUtil;
    private final AiInvitationCodesMapper aiInvitationCodesMapper;
    private final InvitationCodeGenerator invitationCodeGenerator;
    private final AiPointTransactionsMapper aiPointTransactionsMapper;
    private final WechatService wechatService;
    private final ContentService contentService;
    private final SmsService smsService;
    private final OssUtils ossUtils;
    
    @Value("${spring.mail.username}")
    private String username;
    @Value("${spring.profiles.active}")
    private String env;
    @Value("${send.email:false}")
    private Boolean sendEmail;
    @Value("${user.points.registration.reward:5000}")
    private int registrationRewardPoints;

    // Token in Redis expiration (e.g., 7 days)
    private static final long TOKEN_EXPIRATION_DAYS = 7;
    // Use a key prefix for sets of tokens
    private static final String REDIS_TOKENS_PREFIX = "user:tokens:";
    // Verification code in Redis expiration (e.g., 5 minutes)
    private static final long CODE_EXPIRATION_MINUTES = 5;
    // 发送频率限制时间（例如分钟
    private static final long CODE_FREQUENCY_LIMIT_MINUTES = 1;
    private static final String REDIS_CODE_PREFIX = "user:verify:code:"; // 验证码Redis Key前缀
    private static final String REDIS_CODE_FREQUENCY_PREFIX = "user:verify:frequency:"; // 发送频率限制Key前缀
    // 手机验证码Redis Key前缀
    private static final String REDIS_MOBILE_CODE_PREFIX = "user:verify:mobile:code:";
    private static final String REDIS_MOBILE_FREQUENCY_PREFIX = "user:verify:mobile:frequency:";

    /**
     * oss路径
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    private static final String OSS_PATH = "dify/{env}/user/{userId}/avatar/";

    @Override
    public void sendVerificationCode(String email)  { // 保持 throws Exception 或改throws BizException
        // 仅在生产环境或显式开启邮件发送时执行
        if ("prod".equals(env) || Boolean.TRUE.equals(sendEmail)) {
            String frequencyKey = REDIS_CODE_FREQUENCY_PREFIX + email;
            RBucket<String> frequencyBucket = redissonClient.getBucket(frequencyKey);

            // 发送验证码频繁验证
            if (frequencyBucket.isExists()) {
                // 使用 I18nMessageUtils 获取国际化消
                throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_SEND_FREQUENT.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_SEND_FREQUENT.getMsg()));
            }
            // 设置频率限制标记分钟过期
            frequencyBucket.set(email, CODE_FREQUENCY_LIMIT_MINUTES, TimeUnit.MINUTES);

            // 1. 生成6位随机验证码
            String verificationCode = generateVerificationCode();

            // 2. 将验证码保存到Redis，设置指定分钟过
            String codeKey = REDIS_CODE_PREFIX + email;
            RBucket<String> codeBucket = redissonClient.getBucket(codeKey);
            codeBucket.set(verificationCode, CODE_EXPIRATION_MINUTES, TimeUnit.MINUTES);

            // 3. 发送验证码邮件
            sendVerificationEmail(email, verificationCode);
            log.info("Verification code sent to email: {}", email);
        } else {
            // 非生产环境且未开启邮件发送，可以模拟或跳
            String verificationCode = "123456";
            String codeKey = REDIS_CODE_PREFIX + email;
            RBucket<String> codeBucket = redissonClient.getBucket(codeKey);
            codeBucket.set(verificationCode, CODE_EXPIRATION_MINUTES, TimeUnit.MINUTES);
            log.warn("Email sending is disabled in env '{}' or send.email=false. Verification code for {} is {} (stored in Redis)", env, email, verificationCode);
            // 注意：这里仅存储了验证码，没有实际发送邮件，也没有频率限
        }
    }

    private void sendVerificationEmail(String email, String verificationCode) {
       // MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//        MimeMessageHelper messageHelper;
//        try {
//            messageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
//            messageHelper.setFrom(username);
//            messageHelper.setTo(email);
//            messageHelper.setSubject(I18nMessageUtils.getMessage("verification.code.email.subject"));
//            String text = generateEmailTemplate(verificationCode);
//            messageHelper.setText(text, true);
//            javaMailSender.send(mimeMessage);
//        } catch (MessagingException e) {
//            log.error("Failed to send verification code email to {}", email, e);
//            // 抛出业务异常，使用国际化消息键和ErrorCodeEnum
//            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getMsg()));
//        }
    }

    /**
     * 生成邮件HTML模板
     */
    private String generateEmailTemplate(String code) {
        return String.format(
                "<div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">" +
                        "<h2 style=\"color: #333;\">" + I18nMessageUtils.getMessage("verification.code") + "</h2>" +
                        "<p style=\"font-size: 16px; color: #666;\">" +I18nMessageUtils.getMessage("your.verification.code") + "</p>" +
                        "<div style=\"background-color: #f5f5f5; padding: 15px; margin: 20px 0; text-align: center;\">" +
                        "<span style=\"font-size: 24px; font-weight: bold; color: #333;\">%s</span>" +
                        "</div>" +
                        "<p style=\"font-size: 14px; color: #999;\">" + I18nMessageUtils.getMessage("verification.code.email.content")+ "</p>" +
                        "</div>",
                code
        );
    }

    private String generateVerificationCode() {
        Random random = new Random();
        // 生成6位数字验证码
        return String.format("%06d", random.nextInt(1000000));
    }

    @Override
    @Transactional
    // 确保方法签名包含 HttpServletRequest request
    public UserLoginRes loginOrRegisterByEmailAndCode(String email, String code, HttpServletRequest request)  {
        // 1. 校验验证
        String redisKeyVerifyCode = REDIS_CODE_PREFIX + email;
        RBucket<String> codeBucket = redissonClient.getBucket(redisKeyVerifyCode);
        String storedCode = codeBucket.get();

        if (!StringUtils.hasText(storedCode)) {
            // 抛出业务异常 - 使用 VERIFICATION_CODE_EXPIRED
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getMsg()));
        }
        if (!storedCode.equals(code)) {
            // 抛出业务异常 - 使用 VERIFICATION_CODE_INVALID
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getMsg()));
        }
        // 验证成功后，立即删除验证码，防止重复使用
        codeBucket.delete();
        // 2. 根据邮箱查询用户
        LambdaQueryWrapper<AiUsersPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUsersPo::getEmail, email);
        AiUsersPo user = aiUsersMapper.selectOne(queryWrapper);

        // 3. 如果用户不存在，则注册新用户
        if (user == null) {
            user = new AiUsersPo();
            // Generate userId using KSUID
            user.setUserId(IdUtil.getSnowflakeNextIdStr());
            user.setEmail(email);
            // 生成一个更可读的默认用户名
            user.setNickname(email.split("@")[0] + "_" + RandomStringUtils.randomAlphanumeric(4));
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setAvatar("");
            user.setStatus(0);
            user.setPoints(0); // 初始积分
            user.setDelFlag(0);
            int inserted = aiUsersMapper.insert(user);
            if (inserted <= 0) {
                log.error("Failed to insert new user for email: {}", email);
                // 使用 USER_REGISTRATION_FAILED
                throw new BizException(ErrorCodeEnum.USER_REGISTRATION_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_REGISTRATION_FAILED.getMsg()));
            }
            // 重新查询以获取完整信息，特别是数据库生成的ID（如果需要）
            // user = aiUsersMapper.selectOne(queryWrapper); // 如果后续逻辑需要数据库生成的ID，则取消注释
            log.info("New user registered successfully: {}", email);
        }else if (user.getStatus() == 2) {
            // 用户账户已被禁用
            log.warn("Login attempt for disabled user: {}", email);
            // 使用 USER_ACCOUNT_DISABLED
            throw new BizException(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getMsg()));
        }
        // 4. Generate JWT Token (Reverted: Removed status claim)
        String token = jwtUtil.generateToken(user.getUserId(), user.getEmail());

        // 5. Add the Token to the user's token set in Redis
//        String redisKeyTokens = REDIS_TOKENS_PREFIX + user.getUserId();
//        RSet<String> tokenSet = redissonClient.getSet(redisKeyTokens);
//        tokenSet.add(token);
//        // Set/Reset expiration for the whole set when a new token is added
//        tokenSet.expire(Duration.ofDays(TOKEN_EXPIRATION_DAYS));

        // 6. Update last login time and IP
        AiUsersPo updateUserLoginTime = new AiUsersPo();
        updateUserLoginTime.setId(user.getId());
        updateUserLoginTime.setLastLoginTime(new Date());
        updateUserLoginTime.setUpdateTime(new Date()); // 同时更新 updateTime
        // --- 获取真实客户IP ---
        String requestIp = getClientIpAddr(request); // 调用辅助方法获取IP
        updateUserLoginTime.setLastLoginIp(requestIp); // 设置最后登录IP
        // --- 结束获取 IP ---
        aiUsersMapper.updateById(updateUserLoginTime);
        UserLoginRes userLoginRes = new UserLoginRes();
        userLoginRes.setAuthorization(token);
        userLoginRes.setUserId(user.getUserId());
        userLoginRes.setEmail(user.getEmail());
        userLoginRes.setNickname(user.getNickname());
        userLoginRes.setAvatar(user.getAvatar());
        userLoginRes.setStatus(user.getStatus());
        contentService.updateTagsAsync(user.getUserId());
        log.info("User {} logged in successfully from IP {}", email, requestIp); // 日志中记录获取到的IP
        return userLoginRes;
    }

    /**
     * 获取客户端真实IP地址，考虑反向代理情况
     * @param request HttpServletRequest 对象
     * @return 客户端IP地址
     */
    private String getClientIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 对于通过多个代理的情况，第一个IP为客户端真实IP，多个IP按照','分割
            if(ip.contains(",")){
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 对于 IPv6 本地回环地址 "0:0:0:0:0:0:0:1"，转换为 IPv4 "127.0.0.1"
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }


    /**
     * 激活用户账
     * @param email 用户邮箱
     * @param code  验证
     * @throws BizException 业务异常
     */
    @Override
    @Transactional
    public void activateUser(String email, String code)  {
        // 1. 校验验证
        String redisKeyVerifyCode = REDIS_CODE_PREFIX + email;
        RBucket<String> codeBucket = redissonClient.getBucket(redisKeyVerifyCode);
        String storedCode = codeBucket.get();

        if (!StringUtils.hasText(storedCode)) {
            // 使用 VERIFICATION_CODE_EXPIRED
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getMsg()));
        }
        if (!storedCode.equals(code)) {
            // 使用 VERIFICATION_CODE_INVALID
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getMsg()));
        }

        // 2. 根据邮箱查询用户
        LambdaQueryWrapper<AiUsersPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUsersPo::getEmail, email);
        AiUsersPo user = aiUsersMapper.selectOne(queryWrapper);

        // 3. 检查用户状态并执行激
        if (user == null) {
            // 理论上，激活时用户应该已存在（通过 loginOrRegisterByEmailAndCode 注册
            log.error("Activation attempt for non-existent user: {}", email);
            codeBucket.delete(); // 用户不存在，删除验证
            throw new BizException("USER_NOT_FOUND", I18nMessageUtils.getMessage("user.not.found"));
        } else if (user.getStatus() == 1) {
            // 用户已激
            log.warn("Activation attempt for already activated user: {}", email);
            codeBucket.delete(); // 用户已激活，删除验证
            throw new BizException("USER_ALREADY_ACTIVATED", I18nMessageUtils.getMessage("user.already.activated"));
        } else if (user.getStatus() == 2) {
            // 用户账户已被禁用
            log.warn("Activation attempt for disabled user: {}", email);
            codeBucket.delete(); // 用户禁用，删除验证码
            throw new BizException("USER_ACCOUNT_DISABLED", I18nMessageUtils.getMessage("user.account.disabled"));
        } else if (user.getStatus() == 0) {
            // --- 执行激---
            AiUsersPo updateUser = new AiUsersPo();
            updateUser.setId(user.getId());
            updateUser.setStatus(1); // 更新状态为 1 (已激
            updateUser.setActivationTime(new Date()); // 设置激活时
            updateUser.setUpdateTime(new Date());
            int updated = aiUsersMapper.updateById(updateUser);

            if (updated > 0) {
                log.info("User {} activated successfully", email);
                // 激活成功，删除验证
                codeBucket.delete();
            } else {
                log.error("Failed to update user status for activation: {}", email);
                // 更新失败，不删除验证码，抛出异常 - 使用 USER_ACTIVATION_FAILED
                throw new BizException("USER_ACTIVATION_FAILED", I18nMessageUtils.getMessage("user.activation.failed"));
            }
        } else {
            // 其他未知状
            log.error("User {} has unknown status during activation: {}", email, user.getStatus());
            codeBucket.delete(); // 未知状态，删除验证
            // 使用 USER_STATUS_UNKNOWN
            throw new BizException("USER_STATUS_UNKNOWN", I18nMessageUtils.getMessage("user.status.unknown"));
        }
    }

    @Override
    @Transactional // 确保操作的原子
    public void activateUserWithInvitation(String invitationCode) {
        // 1. 获取当前用户ID (需要根据你的认证机制实
        String currentUserId = UserContext.getUser().getUserId();
        // 2. 查询当前用户信息
        AiUsersPo currentUser = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>().eq(AiUsersPo::getUserId, currentUserId));
        if (currentUser == null) {
            log.warn("Attempt to activate non-existent user with ID: {}", currentUserId);
            // 使用 USER_NOT_EXIST
            throw new BizException(ErrorCodeEnum.USER_NOT_FOUND.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_FOUND.getMsg()));
        }

        // 3. 检查用户状
        if (currentUser.getStatus() != 0) { // 假设 0 是未激活状
            log.warn("User {} is already activated or in an invalid state for activation.", currentUserId);
            // 使用 USER_ALREADY_ACTIVE
            throw new BizException(ErrorCodeEnum.USER_ALREADY_ACTIVATED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ALREADY_ACTIVATED.getMsg()));
        }

        // 4. 查询并验证邀请码
        LambdaQueryWrapper<AiInvitationCodesPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInvitationCodesPo::getCode, invitationCode)
                .eq(AiInvitationCodesPo::getDelFlag, 0); // 确保未被删除
        AiInvitationCodesPo codePo = aiInvitationCodesMapper.selectOne(queryWrapper);

        if (codePo == null) {
            log.warn("Invalid invitation code provided: {}", invitationCode);
            // 使用 INVITATION_CODE_INVALID
            throw new BizException(ErrorCodeEnum.INVITATION_CODE_INVALID.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.INVITATION_CODE_INVALID.getMsg()));
        }
        if (codePo.getStatus() != 1) { // 假设 1 是可用状
            log.warn("Invitation code {} is already used or unavailable.", invitationCode);
            // 使用 INVITATION_CODE_USED_OR_EXPIRED
            throw new BizException(ErrorCodeEnum.INVITATION_CODE_USED_OR_EXPIRED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.INVITATION_CODE_USED_OR_EXPIRED.getMsg()));
        }
        if (codePo.getExpireTime() != null && codePo.getExpireTime().before(new Date())) {
            log.warn("Invitation code {} has expired.", invitationCode);
            // 可选：更新过期邀请码状态为不可
            // codePo.setStatus(SOME_EXPIRED_STATUS);
            // aiInvitationCodesMapper.updateById(codePo);
            throw new BizException(ErrorCodeEnum.INVITATION_CODE_USED_OR_EXPIRED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.INVITATION_CODE_USED_OR_EXPIRED.getMsg()));
        }

        // 5. 执行激活操
        Date now = new Date();

        // 5.1 更新用户状(只更新状态和时间)
        AiUsersPo updateUserStatus = new AiUsersPo();
        updateUserStatus.setId(currentUser.getId());
        updateUserStatus.setStatus(1); // 假设 1 是正激活状
        updateUserStatus.setUpdateTime(now);
        // updateUserStatus.setActivationTime(now); // 如果有激活时间字
        int userUpdateCount = aiUsersMapper.updateById(updateUserStatus); // 先更新状

        // 5.2 更新邀请码状
        codePo.setStatus(0); // 假设 2 是已使用状
        codePo.setUsedUserId(currentUserId); // 记录使用者ID
        codePo.setUsedTime(now);
        codePo.setUpdateTime(now);
        int codeUpdateCount = aiInvitationCodesMapper.updateById(codePo);

        if (userUpdateCount > 0 && codeUpdateCount > 0) {
            log.info("User {} activated successfully using invitation code {}", currentUserId, invitationCode);

            // --- 处理被邀请人积分 ---
            int currentUserPoints = currentUser.getPoints() != null ? currentUser.getPoints() : 0;
            int currentUserNewBalance = currentUserPoints + registrationRewardPoints;
            currentUser.setPoints(currentUserNewBalance); // 更新内存中currentUser对象的积

            handleUserPointsUpdate(currentUser, registrationRewardPoints, 1, // 1-注册奖励
                    "通过邀请码注册获得" + registrationRewardPoints + "积分奖励", invitationCode);
            // --- 结束处理被邀请人积分 ---

            // --- 处理邀请人积分 ---
            String inviterUserId = codePo.getCreateUserId();
            if (StringUtils.hasText(inviterUserId)) {
                AiUsersPo inviterUser = aiUsersMapper.selectById(inviterUserId);
                if (inviterUser != null) {
                    int invitationRewardPoints = 500;
                    int inviterCurrentPoints = inviterUser.getPoints() != null ? inviterUser.getPoints() : 0;
                    int inviterNewBalance = inviterCurrentPoints + invitationRewardPoints;
                    inviterUser.setPoints(inviterNewBalance); // 更新内存中inviterUser对象的积

                    handleUserPointsUpdate(inviterUser, invitationRewardPoints, 2, // 2-邀请奖
                            "成功邀请新用户获得500积分奖励", currentUserId);
                } else {
                    log.warn("Inviter user not found for ID: {}", inviterUserId);
                }
            } else {
                 log.warn("Invitation code {} does not have an associated user ID.", invitationCode);
            }
            // --- 结束处理邀请人积分 ---

            // --- 保留: 5.3 生成并存储新的邀请码 ---
            /**
             *
            List<AiInvitationCodesPo> newCodes = new ArrayList<>();
            for (int i = 0; i < 2; i++) { // 生成两个新码
                AiInvitationCodesPo newCode = new AiInvitationCodesPo();
                newCode.setCode(invitationCodeGenerator.generateUniqueCode()); // 调用生成器生成唯一
                newCode.setCreateUserId(currentUserId); // 创建者是当前激活的用户
                newCode.setStatus(1); // 新码可用
                newCode.setCreateTime(now);
                newCode.setUpdateTime(now);
                newCode.setDelFlag(0);
                // newCode.setExpireTime(null); // 或设置默认有效期
                newCodes.add(newCode);
            }

            // 批量插入新邀请码 (需Mapper 支持)
            // 注意：确保你的Mapper中有 insertBatchSomeColumn 方法或类似的批量插入方法
            int insertCount = aiInvitationCodesMapper.insertBatchSomeColumn(newCodes);
            if (insertCount != newCodes.size()) {
                log.error("Failed to insert all new invitation codes for user {}. Expected {}, inserted {}", currentUserId, newCodes.size(), insertCount);
                // 根据业务决定是否需要回滚事务或仅记录错
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "生成新邀请码失败");
            }
                     log.info("Generated {} new invitation codes for user {}.", insertCount, currentUserId);      */          // --- 结束保留 ---

        } else {
            log.error("Failed to activate user {} or update invitation code {}. UserUpdateCount: {}, CodeUpdateCount: {}", currentUserId, invitationCode, userUpdateCount, codeUpdateCount);
            // 使用 SYSTEM_ERROR 或更具体的错误码
            throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.SYSTEM_ERROR.getMsg()));
        }
    }

    @Override
    public WechatLoginRes loginByWechat(String code, String state, HttpServletRequest request) {
        // 验证状态码，防止CSRF攻击
        if (!wechatService.validateState(state)) {
            log.warn("Invalid state parameter for WeChat login: {}", state);
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.invalid.state"));
        }
        
        // 先从Redis中获取之前保存的登录数据
        JSONObject loginData = wechatService.getLoginData(state);
        if (loginData == null || loginData.isEmpty()) {
            log.error("No login data found in Redis for state: {}", state);
            throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.data.missing"));
        }
        
        String accessToken;
        String openid;
        String unionid = null;
        JSONObject userInfo;
        
        // 优先使用Redis中已保存的Token和用户信
        if (loginData.containsKey("accessToken") && loginData.containsKey("openid") && 
            loginData.containsKey("wechatUserInfo")) {
            
            log.info("Using cached WeChat token and user info for state: {}", state);
            accessToken = loginData.getString("accessToken");
            openid = loginData.getString("openid");
            
            if (loginData.containsKey("unionid")) {
                unionid = loginData.getString("unionid");
            }
            
            userInfo = loginData.getJSONObject("wechatUserInfo");
        } else {
            // 兼容旧逻辑，如果Redis中没有保存完整信息，则调用微信API
            log.warn("Cached WeChat info not complete, calling WeChat API for state: {}", state);
            
            // 获取微信访问令牌
            JSONObject tokenInfo = wechatService.getAccessToken(code, state);
            if (tokenInfo == null) {
                log.error("Failed to get access token from WeChat API with code: {}", code);
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.token.failed"));
            }

            // 从访问令牌响应中提取数据
            accessToken = tokenInfo.getString(WechatConstant.ApiResponse.ACCESS_TOKEN);
            openid = tokenInfo.getString(WechatConstant.ApiResponse.OPENID);
            unionid = tokenInfo.getString(WechatConstant.ApiResponse.UNIONID); // 可能为null

            // 使用访问令牌获取用户信息
            userInfo = wechatService.getUserInfo(accessToken, openid);
            if (userInfo == null) {
                log.error("Failed to get user info from WeChat API with openid: {}", openid);
                throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), I18nMessageUtils.getMessage("wechat.login.userinfo.failed"));
            }
        }

        // 提取用户信息
        String nickname = userInfo.getString(WechatConstant.ApiResponse.NICKNAME);
        String headimgurl = userInfo.getString(WechatConstant.ApiResponse.HEADIMGURL);
        
        // 将微信头像上传到OSS并获取OSS URL
        String ossAvatarUrl = uploadWechatAvatarToOss(headimgurl, openid);
        // 如果上传失败，使用原始微信头像URL
        String finalAvatarUrl = ossAvatarUrl.isEmpty() ? headimgurl : ossAvatarUrl;

        // 查询用户 - 优先使用unionid，unionid为空再使用openid
        AiUsersPo user = null;
        if (StringUtils.hasText(unionid)) {
            // 有unionid时，优先使用unionid查询
            log.info("Searching user by unionid: {}", unionid);
            LambdaQueryWrapper<AiUsersPo> unionidQuery = new LambdaQueryWrapper<>();
            unionidQuery.eq(AiUsersPo::getWechatUnionid, unionid);
            user = aiUsersMapper.selectOne(unionidQuery);
        }
        
        // 如果unionid为空或未查询到用户，则使用openid作为备
        if (user == null && StringUtils.hasText(openid)) {
            log.info("Searching user by openid: {}", openid);
            LambdaQueryWrapper<AiUsersPo> openidQuery = new LambdaQueryWrapper<>();
            openidQuery.eq(AiUsersPo::getWechatOpenid, openid);
            user = aiUsersMapper.selectOne(openidQuery);
        }

        boolean isNewUser = false;
        // 如果用户不存在，则注册新用户
        if (user == null) {
            user = new AiUsersPo();
            // 生成用户ID
            user.setUserId(IdUtil.getSnowflakeNextIdStr());
            user.setWechatOpenid(openid);
            user.setWechatUnionid(unionid);
            user.setWechatNickname(nickname);
            user.setWechatAvatar(finalAvatarUrl); // 保存原始微信头像URL
            user.setWechatBindTime(new Date());

            // 使用微信昵称作为默认昵称
            user.setNickname(nickname);
            user.setAvatar(finalAvatarUrl); // 使用OSS头像URL或原始微信头像URL
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setStatus(0); // 未激活状
            user.setPoints(0); // 初始积分
            user.setDelFlag(0);

            // 获取客户端IP
            String registerIp = getClientIpAddr(request);
            user.setRegisterIp(registerIp);

            int inserted = aiUsersMapper.insert(user);
            if (inserted <= 0) {
                log.error("Failed to insert new user for WeChat openid: {}", openid);
                throw new BizException(ErrorCodeEnum.USER_REGISTRATION_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_REGISTRATION_FAILED.getMsg()));
            }
            
            isNewUser = true;
            log.info("New user registered via WeChat: {}", nickname);
        } else if (user.getStatus() == 2) {
            // 用户账户已被禁用
            log.warn("Login attempt via WeChat for disabled user: {}", user.getUserId());
            throw new BizException(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getMsg()));
        } else {
            // 更新用户的微信相关信息（如有必要
            boolean needUpdate = false;
            AiUsersPo updateUser = new AiUsersPo();
            updateUser.setId(user.getId());
            
            // 检查并更新微信昵称
            if (!nickname.equals(user.getWechatNickname())) {
                updateUser.setWechatNickname(nickname);
                needUpdate = true;
            }
            
            // 检查并更新微信头像
            if (!headimgurl.equals(user.getWechatAvatar())) {
                updateUser.setWechatAvatar(headimgurl);
                needUpdate = true;
            }
            
            // 检查并更新用户头像（使用OSS URL
            if (!finalAvatarUrl.equals(user.getAvatar())) {
                updateUser.setAvatar(finalAvatarUrl);
                needUpdate = true;
            }
            
            // 检查并更新UnionID（如果之前为空且现在有值）
            if (unionid != null && !unionid.equals(user.getWechatUnionid())) {
                updateUser.setWechatUnionid(unionid);
                needUpdate = true;
            }
            
            if (needUpdate) {
                updateUser.setUpdateTime(new Date());
                aiUsersMapper.updateById(updateUser);
                log.info("Updated WeChat info for user: {}", user.getUserId());
            }
        }

        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUserId(), user.getEmail());

        // 更新最后登录时间和IP
        AiUsersPo updateUserLoginTime = new AiUsersPo();
        updateUserLoginTime.setId(user.getId());
        updateUserLoginTime.setLastLoginTime(new Date());
        updateUserLoginTime.setUpdateTime(new Date());
        String requestIp = getClientIpAddr(request);
        updateUserLoginTime.setLastLoginIp(requestIp);
        aiUsersMapper.updateById(updateUserLoginTime);

        // 构造响应对
        WechatLoginRes wechatLoginRes = new WechatLoginRes();
        wechatLoginRes.setUserId(user.getUserId());
        wechatLoginRes.setNickname(user.getNickname());
        wechatLoginRes.setAvatar(user.getAvatar());
        wechatLoginRes.setToken(token);
        wechatLoginRes.setIsNewUser(isNewUser);
        wechatLoginRes.setStatus(user.getStatus());

        log.info("User {} logged in via WeChat from IP {}", user.getUserId(), requestIp);
        return wechatLoginRes;
    }
    
    /**
     * 将微信头像上传到OSS
     * 
     * @param headimgurl 微信头像URL
     * @param openid 用户的微信openid
     * @return OSS URL，上传失败则返回空字符串
     */
    private String uploadWechatAvatarToOss(String headimgurl, String openid) {
        if (headimgurl == null || headimgurl.isEmpty()) {
            return "";
        }

        try {
            // 构建OSS对象名称，使用openid作为唯一标识，避免重
            String fileName = "avatar_" + openid + "_" + System.currentTimeMillis() + ".png";
            String ossPath = OSS_PATH.replace("{env}", env)
                    .replace("{userId}", openid)
                    + fileName;
            // 上传文件到OSS
            String uploadResult = ossUtils.uploadFile(headimgurl, ossPath);
            if (StringUtils.hasText(uploadResult)) {
                 return uploadResult;
            } else {
                log.warn("Failed to upload WeChat avatar to OSS for openid: {}", openid);
                return "";
            }
        } catch (Exception e) {
            log.error("Error uploading WeChat avatar to OSS: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 处理用户积分更新和流水记
     *
     * @param user        需要更新积分的用户对象 (包含最新的积分信息)
     * @param pointsChange 积分变动(正数为增加，负数为减
     * @param transactionType 交易类型 (例如: 1-注册奖励, 2-邀请奖
     * @param description 交易描述
     * @param referenceId 关联ID (可 如邀请码、被邀请人ID
     */
    private void handleUserPointsUpdate(AiUsersPo user, int pointsChange, int transactionType, String description, String referenceId) {
        if (user == null) {
            log.error("Cannot update points for null user.");
            return;
        }
        // 1. 更新用户积分 (注意：调用此方法前，user对象的points应已计算
        AiUsersPo updateUserPoints = new AiUsersPo();
        updateUserPoints.setId(user.getId());
        updateUserPoints.setPoints(user.getPoints()); // 使用传入user对象中已计算好的最新积
        updateUserPoints.setUpdateTime(new Date());
        int updated = aiUsersMapper.updateById(updateUserPoints);

        if (updated > 0) {
            log.info("Successfully updated points for user {}. New balance: {}", user.getUserId(), user.getPoints());

            // 2. 记录积分流水
            AiPointTransactionsPo transaction = new AiPointTransactionsPo();
            transaction.setUserId(user.getUserId());
            transaction.setPoints(pointsChange); // 记录本次变动的积分
            transaction.setBalance(user.getPoints()); // 记录变动后的总余
            transaction.setType(transactionType);
            transaction.setDescription(description);
            if (StringUtils.hasText(referenceId)) {
                transaction.setReferenceId(referenceId);
            }
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            transaction.setDelFlag(0);
            aiPointTransactionsMapper.insert(transaction);
            log.info("Recorded point transaction for user {}: type={}, points={}, balance={}, ref={}",
                    user.getUserId(), transactionType, pointsChange, user.getPoints(), referenceId);
        } else {
            // 积分更新失败，只记录错误日志，不影响主流程（根据业务决定是否抛异常）
            log.error("Failed to update points in database for user {}.", user.getUserId());
            // 可以考虑是否需要补偿逻辑或抛出异
            // throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "Failed to update user points");
        }
    }

    @Override
    public void sendMobileVerificationCode(String mobile) {
        // 仅在生产环境或显式开启短信发送时执行实际发
        // 这里假设我们有个类似的短信开
        boolean sendSmsEnabled = "prod".equals(env);
        
        String frequencyKey = REDIS_MOBILE_FREQUENCY_PREFIX + mobile;
        RBucket<String> frequencyBucket = redissonClient.getBucket(frequencyKey);

        // 发送验证码频繁验证
        if (frequencyBucket.isExists()) {
            // 使用 I18nMessageUtils 获取国际化消
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_SEND_FREQUENT.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_SEND_FREQUENT.getMsg()));
        }
        // 设置频率限制标记分钟过期
        frequencyBucket.set(mobile, CODE_FREQUENCY_LIMIT_MINUTES, TimeUnit.MINUTES);

        // 1. 生成6位随机验证码
        String verificationCode = generateVerificationCode();
        if  (!sendSmsEnabled) {
            verificationCode = "123456";
        }
        // 2. 将验证码保存到Redis，设置指定分钟过
        String codeKey = REDIS_MOBILE_CODE_PREFIX + mobile;
        RBucket<String> codeBucket = redissonClient.getBucket(codeKey);
        codeBucket.set(verificationCode, CODE_EXPIRATION_MINUTES, TimeUnit.MINUTES);

        if (sendSmsEnabled) {
            // 3. 调用短信发送服务发送验证码
            try {
                smsService.sendSms(mobile,  verificationCode);
                log.info("Verification code sent to mobile: {}", mobile);
            } catch (Exception e) {
                log.error("Failed to send verification code SMS to {}", mobile, e);
                throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_SEND_FAILED.getMsg()));
            }
        } else {
            // 非生产环境或未开启短信发送，模拟发
            log.warn("SMS sending is disabled in env '{}'. Verification code for {} is {} (stored in Redis)", env, mobile, verificationCode);
        }
    }

    @Override
    @Transactional
    public MobileBindRes bindMobile(String mobile, String code, HttpServletRequest request) {
        MobileBindRes response = new MobileBindRes();
        
        try {
            // 1. 校验验证
            String redisKeyVerifyCode = REDIS_MOBILE_CODE_PREFIX + mobile;
            RBucket<String> codeBucket = redissonClient.getBucket(redisKeyVerifyCode);
            String storedCode = codeBucket.get();

            if (!StringUtils.hasText(storedCode)) {
                response.setSuccess(false);
                response.setErrorMsg(I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getMsg()));
                return response;
            }
            
            if (!storedCode.equals(code)) {
                response.setSuccess(false);
                response.setErrorMsg(I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getMsg()));
                return response;
            }
            
            // 验证成功后，立即删除验证码，防止重复使用
            codeBucket.delete();
            
            // 2. 获取当前登录用户
            String currentUserId = UserContext.getUser().getUserId();
            if (StringUtils.isEmpty(currentUserId)) {
                response.setSuccess(false);
                response.setErrorMsg(I18nMessageUtils.getMessage("user.not.logged.in"));
                return response;
            }
            
            // 3. 检查手机号是否已被用户绑定
            LambdaQueryWrapper<AiUsersPo> existsQueryWrapper = new LambdaQueryWrapper<>();
            existsQueryWrapper.eq(AiUsersPo::getPhone, mobile);
            AiUsersPo existingUser = aiUsersMapper.selectOne(existsQueryWrapper);
            
            // 4.获取当前用户信息
            LambdaQueryWrapper<AiUsersPo> currentUserQueryWrapper = new LambdaQueryWrapper<>();
            currentUserQueryWrapper.eq(AiUsersPo::getUserId, currentUserId);
            AiUsersPo currentUser = aiUsersMapper.selectOne(currentUserQueryWrapper);
            
            if (currentUser == null) {
                response.setSuccess(false);
                response.setErrorMsg(I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_FOUND.getMsg()));
                return response;
            }
            
            if (existingUser != null && !existingUser.getUserId().equals(currentUserId)) {
                // 手机号已被其他用户绑定，进行用户合并
                log.info("Mobile {} already bound to user {}, merging current user {} into existing user", 
                        mobile, existingUser.getUserId(), currentUserId);
                
                // 更新已有用户的微信信息（从当前用户合并）
                AiUsersPo updateExistingUser = new AiUsersPo();
                updateExistingUser.setId(existingUser.getId());
                
                // 只有当当前用户有微信信息时才更新
                if (StringUtils.hasText(currentUser.getWechatOpenid())) {
                    updateExistingUser.setWechatOpenid(currentUser.getWechatOpenid());
                }
                if (StringUtils.hasText(currentUser.getWechatUnionid())) {
                    updateExistingUser.setWechatUnionid(currentUser.getWechatUnionid());
                }
                if (StringUtils.hasText(currentUser.getWechatNickname())) {
                    updateExistingUser.setWechatNickname(currentUser.getWechatNickname());
                }
                if (StringUtils.hasText(currentUser.getWechatAvatar())) {
                    updateExistingUser.setWechatAvatar(currentUser.getWechatAvatar());
                }
                
                // 如果当前用户有绑定时间，更新到已有用
                if (currentUser.getWechatBindTime() != null) {
                    updateExistingUser.setWechatBindTime(currentUser.getWechatBindTime());
                } else {
                    updateExistingUser.setWechatBindTime(new Date());
                }
                
                updateExistingUser.setUpdateTime(new Date());
                aiUsersMapper.updateById(updateExistingUser);
                
                // 合并积分 - 将当前用户的积分转移到手机号用户
                if (currentUser.getPoints() != null && currentUser.getPoints() > 0) {
                    int pointsToTransfer = currentUser.getPoints();
                    int existingUserPoints = existingUser.getPoints() != null ? existingUser.getPoints() : 0;
                    int newTotalPoints = existingUserPoints + pointsToTransfer;
                    
                    // 更新手机号用户的积分
                    AiUsersPo updatePoints = new AiUsersPo();
                    updatePoints.setId(existingUser.getId());
                    updatePoints.setPoints(newTotalPoints);
                    updatePoints.setUpdateTime(new Date());
                    aiUsersMapper.updateById(updatePoints);
                    
                    // 记录积分合并流水 - 给手机号用户添加积分记录
                    handleUserPointsUpdate(
                        existingUser, // 使用手机号用户，但手动设置新的积分总额
                        pointsToTransfer, // 转移的积
                        3, // 假设3表示账户合并积分转入
                        I18nMessageUtils.getMessage("point.transaction.description.account.merge.in"), 
                        currentUser.getUserId() // 引用当前用户ID
                    );
                    
                    // 记录积分合并流水 - 给当前用户添加扣减记
                    AiUsersPo currentUserWithZeroPoints = new AiUsersPo();
                    currentUserWithZeroPoints.setId(currentUser.getId());
                    currentUserWithZeroPoints.setUserId(currentUser.getUserId());
                    currentUserWithZeroPoints.setPoints(0); // 设置积分
                    
                    handleUserPointsUpdate(
                        currentUserWithZeroPoints,
                        -pointsToTransfer, // 负数表示扣减
                        4, // 假设4表示账户合并积分转出
                        I18nMessageUtils.getMessage("point.transaction.description.account.merge.out"),
                        existingUser.getUserId() // 引用手机号用户ID
                    );
                    
                    log.info("Transferred {} points from user {} to user {}", 
                             pointsToTransfer, currentUser.getUserId(), existingUser.getUserId());
                }
                
                // 使用手机号用户生成新token
                String token = jwtUtil.generateToken(existingUser.getUserId(), existingUser.getEmail());
                
                // 更新最后登录时间和IP
                AiUsersPo updateLoginInfo = new AiUsersPo();
                updateLoginInfo.setId(existingUser.getId());
                updateLoginInfo.setLastLoginTime(new Date());
                updateLoginInfo.setUpdateTime(new Date());
                String requestIp = getClientIpAddr(request);
                updateLoginInfo.setLastLoginIp(requestIp);
                aiUsersMapper.updateById(updateLoginInfo);
                
                // 将当前用户标记为删除状态（软删除）
                aiUsersMapper.deleteById(currentUser.getId());
                
                // 构建成功响应
                response.setSuccess(true);
                response.setMaskedMobile(maskMobile(mobile));
                response.setAuthorization(token); // 返回新token
                response.setUserId(existingUser.getUserId()); // 返回手机号用户ID
                response.setEmail(existingUser.getEmail()); // 添加email
                response.setNickname(existingUser.getNickname()); // 添加nickname
                response.setAvatar(existingUser.getAvatar()); // 添加avatar
                response.setStatus(existingUser.getStatus()); // 添加status
                
                log.info("User accounts merged. Current user {} merged into existing user {}", 
                        currentUserId, existingUser.getUserId());
                
                return response;
            }
            
            // 手机号未被绑定或者绑定的就是当前用户，直接更新当前用户的手机
            AiUsersPo updateUser = new AiUsersPo();
            updateUser.setId(currentUser.getId());
            updateUser.setPhone(mobile);
            updateUser.setUpdateTime(new Date());
            
            int updated = aiUsersMapper.updateById(updateUser);
            
            if (updated > 0) {
                // 绑定成功
                response.setSuccess(true);
                // 手机号部分隐藏处
                response.setMaskedMobile(maskMobile(mobile));
                // 添加完整用户信息
                response.setUserId(currentUser.getUserId());
                response.setEmail(currentUser.getEmail());
                response.setNickname(currentUser.getNickname());
                response.setAvatar(currentUser.getAvatar());
                response.setStatus(currentUser.getStatus());
                // 生成并返回token
                String token = jwtUtil.generateToken(currentUser.getUserId(), currentUser.getEmail());
                response.setAuthorization(token);
                log.info("User {} successfully bound mobile {}", currentUserId, mobile);
            } else {
                response.setSuccess(false);
                response.setErrorMsg(I18nMessageUtils.getMessage("mobile.bind.failed"));
                log.error("Failed to update mobile for user {}", currentUserId);
            }
            
            return response;
        } catch (Exception e) {
            log.error("Error binding mobile for user", e);
            response.setSuccess(false);
            response.setErrorMsg(I18nMessageUtils.getMessage("system.error"));
            return response;
        }
    }
    
    /**
     * 对手机号进行部分隐藏处理，显示前3位和位，中间***代替
     * 例如3800138000 -> 138****8000
     */
    private String maskMobile(String mobile) {
        if (StringUtils.isEmpty(mobile) || mobile.length() < 11) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(mobile.length() - 4);
    }

    @Override
    @Transactional
    public UserLoginRes loginOrRegisterByMobileAndCode(String mobile, String code, HttpServletRequest request) {
        // 1. 校验验证
        String redisKeyVerifyCode = REDIS_MOBILE_CODE_PREFIX + mobile;
        RBucket<String> codeBucket = redissonClient.getBucket(redisKeyVerifyCode);
        String storedCode = codeBucket.get();

        if (!StringUtils.hasText(storedCode)) {
            // 抛出业务异常 - 使用 VERIFICATION_CODE_EXPIRED
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_EXPIRED.getMsg()));
        }
        if (!storedCode.equals(code)) {
            // 抛出业务异常 - 使用 VERIFICATION_CODE_INVALID
            throw new BizException(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.VERIFICATION_CODE_INVALID.getMsg()));
        }
        // 验证成功后，立即删除验证码，防止重复使用
        codeBucket.delete();
        
        // 2. 根据手机号查询用
        LambdaQueryWrapper<AiUsersPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUsersPo::getPhone, mobile);
        AiUsersPo user = aiUsersMapper.selectOne(queryWrapper);

        // 3. 如果用户不存在，则注册新用户
        if (user == null) {
            user = new AiUsersPo();
            // Generate userId using KSUID
            user.setUserId(IdUtil.getSnowflakeNextIdStr());
            user.setPhone(mobile);
            // 生成一个更可读的默认用户名
            user.setNickname("user_" + mobile.substring(mobile.length() - 4) + "_" + RandomStringUtils.randomAlphanumeric(4));
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setAvatar("");
            user.setStatus(0);
            user.setPoints(0); // 初始积分
            user.setDelFlag(0);
            int inserted = aiUsersMapper.insert(user);
            if (inserted <= 0) {
                log.error("Failed to insert new user for mobile: {}", mobile);
                // 使用 USER_REGISTRATION_FAILED
                throw new BizException(ErrorCodeEnum.USER_REGISTRATION_FAILED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_REGISTRATION_FAILED.getMsg()));
            }
            log.info("New user registered successfully with mobile: {}", mobile);
        } else if (user.getStatus() == 2) {
            // 用户账户已被禁用
            log.warn("Login attempt for disabled user with mobile: {}", mobile);
            // 使用 USER_ACCOUNT_DISABLED
            throw new BizException(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.USER_ACCOUNT_DISABLED.getMsg()));
        }
        
        // 4. Generate JWT Token
        String token = jwtUtil.generateToken(user.getUserId(), user.getEmail());

        // 5. Update last login time and IP
        AiUsersPo updateUserLoginTime = new AiUsersPo();
        updateUserLoginTime.setId(user.getId());
        updateUserLoginTime.setLastLoginTime(new Date());
        updateUserLoginTime.setUpdateTime(new Date()); // 同时更新 updateTime
        // --- 获取真实客户IP ---
        String requestIp = getClientIpAddr(request); // 调用辅助方法获取IP
        updateUserLoginTime.setLastLoginIp(requestIp); // 设置最后登录IP
        // --- 结束获取 IP ---
        aiUsersMapper.updateById(updateUserLoginTime);
        
        UserLoginRes userLoginRes = new UserLoginRes();
        userLoginRes.setAuthorization(token);
        userLoginRes.setUserId(user.getUserId());
        userLoginRes.setEmail(user.getEmail());
        userLoginRes.setNickname(user.getNickname());
        userLoginRes.setAvatar(user.getAvatar());
        userLoginRes.setStatus(user.getStatus());
        userLoginRes.setMobile(maskMobile(mobile)); // 设置掩码后的手机
        
        contentService.updateTagsAsync(user.getUserId());
        log.info("User with mobile {} logged in successfully from IP {}", mobile, requestIp);
        return userLoginRes;
    }
}
