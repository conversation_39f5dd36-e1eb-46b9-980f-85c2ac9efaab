package com.wlink.agent.user.service;

import com.wlink.agent.user.model.UserProfileRes;
import com.wlink.agent.user.model.UserProfileUpdateReq;
import com.wlink.agent.user.model.InvitationCodeRes; // 导入
import com.wlink.agent.user.model.PointsHistoryRes; // 导入
import com.wlink.agent.common.dto.PageRes; // 导入分页 DTO
import java.util.List;

/**
 * 用户资料服务接口
 */
public interface UserService {

    /**
     * 获取当前登录用户的详细信
     * @return 用户资料响应 DTO
     */
    UserProfileRes getCurrentUserProfile();

    /**
     * 更新当前登录用户的个人资
     * @param req 包含更新信息的用户资料请DTO
     */
    void updateUserProfile(UserProfileUpdateReq req);

    /**
     * 获取当前用户的邀请码列表
     * @return 邀请码列表
     */
    List<InvitationCodeRes> getCurrentUserInvitationCodes(); // 新增方法

    /**
     * 分页查询当前用户的积分账
     * @param pageNum 当前页码
     * @param pageSize 每页数量
     * @param type 交易类型（可选）
     * @param referenceId 关联ID（可选）
     * @return 分页的积分账单结
     */
    PageRes<PointsHistoryRes> getCurrentUserPointsHistory(int pageNum, int pageSize, Integer type, String referenceId); // 修改方法签名
}
