package com.wlink.agent.user.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wlink.agent.common.dto.PageRes;
import com.wlink.agent.dao.mapper.AiInvitationCodesMapper;
import com.wlink.agent.dao.mapper.AiPointTransactionsMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper; // 新增导入
import com.wlink.agent.dao.po.AiInvitationCodesPo;
import com.wlink.agent.dao.po.AiPointTransactionsPo;
import com.wlink.agent.dao.po.AiUsersPo; // 新增导入
import com.wlink.agent.exception.ErrorCodeEnum; // 新增导入
import com.wlink.agent.model.TextModerationResult;
import com.wlink.agent.model.dto.SimpleUserInfo; // 新增导入
import com.wlink.agent.service.ContentModerationService;
import com.wlink.agent.user.model.InvitationCodeRes;
import com.wlink.agent.user.model.PointsHistoryRes;
import com.wlink.agent.user.model.UserProfileRes;
import com.wlink.agent.user.model.UserProfileUpdateReq;
import com.wlink.agent.user.service.UserService;
import com.wlink.agent.utils.I18nMessageUtils; // 新增导入
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext; // 新增导入
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils; // 新增导入
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // 新增导入
import org.springframework.util.StringUtils; // 新增导入

import java.util.Collections;
import java.util.Date; // 新增导入
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    // 注入 AiUsersMapper 用于数据库操
    private final AiUsersMapper aiUsersMapper; // 修改注入
    private final AiInvitationCodesMapper aiInvitationCodesMapper;
    private final AiPointTransactionsMapper pointTransactionsMapper;
    private final ContentModerationService contentModerationService;


    //平台地址
    @Value("${platform.url:https://dev.neodomain.cn}")
    private String platformUrl;

    @Override
    public UserProfileRes getCurrentUserProfile() {
        // 1. 获取当前登录用户的标
        SimpleUserInfo currentUser = UserContext.getUser();
        String userId = currentUser.getUserId();
        log.info("Fetching profile for current user: {}", userId);

        // 2. 根据用户标识从数据库查询用户信息
        AiUsersPo user = aiUsersMapper.selectOne(
                new LambdaQueryWrapper<AiUsersPo>()
                        .eq(AiUsersPo::getUserId, userId) // 或使email/userId，取决于 UserContext 提供的信
                        .eq(AiUsersPo::getDelFlag, 0) // 确保用户未被删除
        );
        if (user == null) {
            log.error("User not found in database: {}", userId);
            // 使用国际化消
            throw new BizException(ErrorCodeEnum.USER_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_FOUND.getMsg()));
        }
        // 3. AiUsersPo 转换UserProfileRes DTO
        UserProfileRes res = new UserProfileRes();
        BeanUtils.copyProperties(user, res); // 使用 BeanUtils 简化转
        // 可以根据需要设置其他字段，例如头像 URL 拼接前缀
        res.setAvatar(MediaUrlPrefixUtil.getMediaUrl(user.getAvatar()));
        log.info("User profile fetched successfully for: {}", userId);
        return res;
    }

    @Override
    @Transactional // 添加事务注解
    public void updateUserProfile(UserProfileUpdateReq req) {
        // 1. 获取当前登录用户的标
        SimpleUserInfo currentUser = UserContext.getUser();
        String userId = currentUser.getUserId();
        log.info("Updating profile for current user: {} with data: {}", userId, JSON.toJSONString(req));
        if  (StringUtils.hasText(req.getNickname())) {
            TextModerationResult textModerationResult = contentModerationService.moderateText(req.getNickname(),"nickname_detection");
            if (textModerationResult.hasViolation()) {
                log.error("Text moderation failed for nickname: {}", req.getNickname());
                // 使用国际化消
                //用户昵称包含敏感
                throw new BizException("用户昵称包含敏感词，请修改后重试");
            }
        }
        // 2. 根据用户标识从数据库查询用户实体
        AiUsersPo user = aiUsersMapper.selectOne(
                new LambdaQueryWrapper<AiUsersPo>()
                        .eq(AiUsersPo::getUserId, userId) // 或使email/userId
                        .eq(AiUsersPo::getDelFlag, 0)
        );

        if (user == null) {
            log.error("User not found in database for update: {}", userId);
            // 使用国际化消
            throw new BizException(ErrorCodeEnum.USER_NOT_FOUND.getCode(),
                    I18nMessageUtils.getMessage(ErrorCodeEnum.USER_NOT_FOUND.getMsg()));
        }

        AiUsersPo userToUpdate = new AiUsersPo(); // 创建一个用于更新的对象，避免修改原始查询结
        userToUpdate.setId(user.getId()); // 必须设置 ID

        userToUpdate.setNickname(req.getNickname());
        userToUpdate.setAvatar(req.getAvatarUrl());
        userToUpdate.setRegion(req.getRegion());
        userToUpdate.setJob(req.getJob());
        userToUpdate.setAge(req.getAge());
        userToUpdate.setGender(req.getGender());
        userToUpdate.setUpdateTime(new Date()); // 更新修改时间
        aiUsersMapper.updateById(userToUpdate);

    }

    @Override
    public List<InvitationCodeRes> getCurrentUserInvitationCodes() {
        SimpleUserInfo currentUser = UserContext.getUser();
        String currentUserId = currentUser.getUserId();
        log.info("Fetching invitation codes for user ID: {}", currentUserId);

        // 1. 使用 Mapper 查询该用户的邀请码实体列表
        List<AiInvitationCodesPo> aiInvitationCodesPos = aiInvitationCodesMapper.selectList(new LambdaQueryWrapper<AiInvitationCodesPo>()
                .eq(AiInvitationCodesPo::getCreateUserId, currentUserId)
                .eq(AiInvitationCodesPo::getDelFlag, 0)
        );
        // 2. 将实体列表映射为 DTO 列表
        if (aiInvitationCodesPos == null || aiInvitationCodesPos.isEmpty()) {
            return Collections.emptyList(); // 如果没有查到，返回空列表
        }
        return aiInvitationCodesPos.stream()
                .map(this::mapInvitationCodeToDto) // 调用转换方法
                .collect(Collectors.toList());
    }

    @Override
    public PageRes<PointsHistoryRes> getCurrentUserPointsHistory(int pageNum, int pageSize, Integer type, String referenceId) {
        SimpleUserInfo currentUser = UserContext.getUser();
        String currentUserId = currentUser.getUserId();
        log.info("Fetching points history for user ID: {} - Page: {}, Size: {}, Type: {}, ReferenceId: {}", 
                 currentUserId, pageNum, pageSize, type, referenceId);
                 
        // 1. 创建 MyBatis-Plus 分页对象 (Page 构造函数的页码通常1 开
        IPage<AiPointTransactionsPo> pageRequest = new Page<>(pageNum, pageSize);
        
        // 2. 构建查询条件
        LambdaQueryWrapper<AiPointTransactionsPo> queryWrapper = new LambdaQueryWrapper<AiPointTransactionsPo>()
                .eq(AiPointTransactionsPo::getUserId, currentUserId)
                .eq(AiPointTransactionsPo::getDelFlag, 0);
                
        // 3. 添加可选的查询条件
        if (type != null) {
            queryWrapper.eq(AiPointTransactionsPo::getType, type);
        }
        if (StringUtils.hasText(referenceId)) {
            queryWrapper.eq(AiPointTransactionsPo::getReferenceId, referenceId);
        }
        
        // 4. 添加排序条件
        queryWrapper.orderByDesc(AiPointTransactionsPo::getCreateTime);
        
        IPage<AiPointTransactionsPo> aiPointTransactionsPoIPage = pointTransactionsMapper.selectPage(pageRequest, queryWrapper);

        // 5. 将查询结IPage<Entity> 转换PageRes<DTO>
        List<PointsHistoryRes> dtoList = aiPointTransactionsPoIPage.getRecords().stream() // IPage 获取记录列表
                .map(this::mapPointsHistoryToDto) // 调用转换方法
                .collect(Collectors.toList());
        // 6. 使用 PageRes 的静态工厂方法创建响应对
        return PageRes.of(
                (int) aiPointTransactionsPoIPage.getCurrent(), // 当前页码 (来自 IPage)
                (int) aiPointTransactionsPoIPage.getSize(),    // 每页数量 (来自 IPage)
                aiPointTransactionsPoIPage.getTotal(),         // 总记录数 (来自 IPage)
                dtoList                         // 当前页数DTO 列表
        );
    }

    private InvitationCodeRes mapInvitationCodeToDto(AiInvitationCodesPo entity) {
        if (entity == null) return null;
        String usedUserName = "";
        if (StringUtils.hasText(entity.getUsedUserId())) {
            AiUsersPo aiUsersPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                    .eq(AiUsersPo::getUserId, entity.getUsedUserId())
                    .eq(AiUsersPo::getDelFlag, 0)
            );
            usedUserName = aiUsersPo.getEmail();
        }
        return new InvitationCodeRes(
                entity.getCode(), // 假设实体getCode() 方法
                entity.getCreateTime(), // 假设实体getCreationTime() 方法
                entity.getStatus(), // 假设实体getStatus() 方法
                usedUserName,
                platformUrl + "/inviteActivate?inviteCode=" + entity.getCode()
        );
    }

    /**
     * PointsHistory 实体映射PointsHistoryRes DTO
     *
     * @param entity 积分历史实体
     * @return 积分历史 DTO
     */
    private PointsHistoryRes mapPointsHistoryToDto(AiPointTransactionsPo entity) {
        if (entity == null) return null;
        return new PointsHistoryRes(
                entity.getId(), // 假设实体getId() 方法
                entity.getDescription(), // 假设实体getDescription() 方法
                entity.getPoints(), // 假设实体getPointsChange() 方法
                entity.getBalance(), // 假设实体getBalanceAfterChange() 方法
                entity.getCreateTime(), // 假设实体getTimestamp() 方法
                entity.getReferenceId() // 假设实体getRelatedBusinessId() 方法
        );
    }

}
