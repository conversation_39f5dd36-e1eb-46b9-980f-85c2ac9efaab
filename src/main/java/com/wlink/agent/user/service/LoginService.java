package com.wlink.agent.user.service;

import jakarta.servlet.http.HttpServletRequest;

import com.wlink.agent.user.model.UserLoginRes;
import com.wlink.agent.user.model.WechatLoginRes;
import com.wlink.agent.user.model.MobileBindRes;

/**
 * 用户登录服务接口
 */
public interface LoginService {
    /**
     * 发送邮箱验证码
     * @param email 邮箱地址
     */
    void sendVerificationCode(String email) ; // BizException
    
    /**
     * 发送手机验证码
     * @param mobile 手机
     */
    void sendMobileVerificationCode(String mobile);

    /**
     * 通过邮箱和验证码登录或注
     * @param email 邮箱地址
     * @param code 验证
     * @param request HTTP请求
     * @return 登录响应信息
     */
    UserLoginRes loginOrRegisterByEmailAndCode(String email, String code, HttpServletRequest request); // UserLoginRes

    /**
     * 通过手机号和验证码登录或注册
     * @param mobile 手机
     * @param code 验证
     * @param request HTTP请求
     * @return 登录响应信息
     */
    UserLoginRes loginOrRegisterByMobileAndCode(String mobile, String code, HttpServletRequest request);

    /**
     * 绑定手机
     * @param mobile 手机
     * @param code 验证
     * @param request HTTP请求
     * @return 绑定结果响应
     */
    MobileBindRes bindMobile(String mobile, String code, HttpServletRequest request);

    /**
     * 激活用户账
     * @param email 用户邮箱
     * @param code 验证
     */
    void activateUser(String email, String code) ; // BizException

    /**
     * 使用邀请码激活用
     * @param invitationCode 邀请码
     */
    void activateUserWithInvitation(String invitationCode);

    /**
     * 通过微信登录
     * @param code 微信授权
     * @param state 状态码，用于防止CSRF攻击
     * @param request HTTP请求
     * @return 登录响应信息
     */
    WechatLoginRes loginByWechat(String code, String state, HttpServletRequest request);
}
