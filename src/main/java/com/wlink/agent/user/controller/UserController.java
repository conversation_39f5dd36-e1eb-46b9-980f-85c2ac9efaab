package com.wlink.agent.user.controller;

// --- 导入必要的类 ---
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.wlink.agent.common.dto.PageRes;
import com.wlink.agent.user.model.InvitationCodeRes;
import com.wlink.agent.user.model.PointsHistoryRes;
import com.wlink.agent.user.model.UserProfileRes;
import com.wlink.agent.user.model.UserProfileUpdateReq;
import com.wlink.agent.user.model.SendMobileCodeReq;
import com.wlink.agent.user.model.MobileBindReq;
import com.wlink.agent.user.model.MobileBindRes;
import com.wlink.agent.user.service.UserService;
import com.wlink.agent.user.service.LoginService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.util.List;

@Slf4j
@Tag(name = "用户信息接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserController {

    private final UserService userService;
    private final LoginService loginService;

    /**
     * 获取当前登录用户的详细信息
     * @return 包含用户信息的响应
     */
    @GetMapping("/profile")
    @Operation(summary = "查询当前用户信息")
    public SingleResponse<UserProfileRes> getUserProfile() {
        log.info("Request to get current user profile");
        // 假设 userService.getCurrentUserProfile() 获取当前用户信息
        UserProfileRes userProfile = userService.getCurrentUserProfile();
        return SingleResponse.of(userProfile);
    }

    /**
     * 修改当前登录用户的个人资料
     * @param req 包含要更新的用户信息的请求体
     * @return 标准成功/失败响应
     */
    @PutMapping("/profile")
    @Operation(summary = "修改当前用户个人资料")
    public Response updateUserProfile(@Valid @RequestBody UserProfileUpdateReq req) {
        log.info("Request to update current user profile");
        // 假设 userService.updateUserProfile() 处理更新逻辑
        userService.updateUserProfile(req);
        return Response.buildSuccess();
        // 可以在service层添加异常处理并返回FailureResponse
    }

    /**
     * 获取当前用户的邀请码列表
     * @return 包含邀请码列表的响应
     */
    @GetMapping("/invitation-codes")
    @Operation(summary = "查询当前用户的邀请码列表")
    public MultiResponse<InvitationCodeRes> getUserInvitationCodes() {
        log.info("Request to get current user invitation codes");
        List<InvitationCodeRes> codes = userService.getCurrentUserInvitationCodes();
        return MultiResponse.of(codes); // 使用 MultiResponse 包装列表
    }

    /**
     * 分页查询当前用户的积分账单
     * @param pageNum 页码，从 1 开始
     * @param pageSize 每页数量
     * @param type 交易类型（可选）
     * @param referenceId 关联ID（可选）
     * @return 分页的积分账单响应
     */
    @GetMapping("/points-history")
    @Operation(summary = "分页查询当前用户的积分账单")
    public PageResponse<PointsHistoryRes> getUserPointsHistory(
            @Parameter(description = "页码 (从1开始)", required = true, example = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") @Min(1) int pageNum,
            @Parameter(description = "每页数量", required = true, example = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") @Min(1) int pageSize,
            @Parameter(description = "交易类型 (1-注册奖励，2-邀请奖励，3-会话消耗，4-系统赠送等)", required = false)
            @RequestParam(value = "type", required = false) Integer type,
            @Parameter(description = "关联ID (如会话ID、邀请码等)", required = false)
            @RequestParam(value = "referenceId", required = false) String referenceId) {

        log.info("Request to get current user points history - Page: {}, Size: {}, Type: {}, ReferenceId: {}", 
                 pageNum, pageSize, type, referenceId);
        PageRes<PointsHistoryRes> pageResult = userService.getCurrentUserPointsHistory(pageNum, pageSize, type, referenceId);
        // 将自定义的PageRes转换为COLA的PageResponse
        return PageResponse.of(pageResult.getList(), (int) pageResult.getTotal(), pageResult.getPageSize(), pageResult.getPageNum());
    }

    /**
     * 绑定手机号
     *
     * @param req 包含手机号和验证码的请求体
     * @param request HTTP请求对象
     * @return 绑定结果响应
     */
    @PostMapping("/mobile/bind")
    @Operation(summary = "绑定手机号")
    public SingleResponse<MobileBindRes> bindMobile(@Valid @RequestBody MobileBindReq req, HttpServletRequest request) {
        log.info("Request to bind mobile number: {}", req.getMobile());
        MobileBindRes result = loginService.bindMobile(req.getMobile(), req.getCode(), request);
        return SingleResponse.of(result);
    }
}
