package com.wlink.agent.user.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.user.model.MobileBindReq;
import com.wlink.agent.user.model.MobileBindRes;
import com.wlink.agent.user.model.MobileLoginReq;
import com.wlink.agent.user.model.SendCodeReq;
import com.wlink.agent.user.model.SendMobileCodeReq;
import com.wlink.agent.user.model.UserLoginReq;
import com.wlink.agent.user.model.UserLoginRes;
import com.wlink.agent.user.service.LoginService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

@Slf4j
@Tag(name = "用户认证接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/login/") // /agent/v1/user
public class LoginController { // <<<--- 类名已修

    private final LoginService loginService;

    /**
     * 发送邮箱验证码接口
     *
     * @param req 包含邮箱的请求体
     * @return 标准成功/失败响应
     */
    @PostMapping("/send/verification/code")
    @Operation(summary = "发送邮箱验证码")
    public Response sendVerificationCode(@Valid @RequestBody SendCodeReq req) {
        log.info("Send verification code request for email: {}", req.getEmail());
        loginService.sendVerificationCode(req.getEmail());
        // 成功时返COLA 的成Response
        return Response.buildSuccess();
    }

    /**
     * 用户邮箱和验证码登录/注册接口
     * - 新用户注册后状态为未激活，需调用 /activate 接口激
     * - 已激活用户登录成功返Token
     * - 未激禁用用户登录会报
     *
     * @param req 包含邮箱和验证码的请求体
     * @return 成功时返回包含JWT token的响应，失败或需激活时返回错误信息
     */
    @PostMapping("/email")
    @Operation(summary = "邮箱验证码登注册")
    public SingleResponse<UserLoginRes> loginOrRegisterByEmailAndCode(@Valid @RequestBody UserLoginReq req, HttpServletRequest request) {
        log.info("Login/Register request for email: {}", req.getEmail());
        UserLoginRes res = loginService.loginOrRegisterByEmailAndCode(req.getEmail(), req.getVerifyCode(), request);
        return SingleResponse.of(res);
    }

    /**
     * 用户手机号和验证码登注册接口
     * - 新用户注册后状态为未激活，需调用 /activate 接口激
     * - 已激活用户登录成功返Token
     * - 未激禁用用户登录会报
     *
     * @param req 包含手机号和验证码的请求
     * @return 成功时返回包含JWT token的响应，失败或需激活时返回错误信息
     */
    @PostMapping("/mobile")
    @Operation(summary = "手机验证码登注册")
    public SingleResponse<UserLoginRes> loginOrRegisterByMobileAndCode(@Valid @RequestBody MobileLoginReq req, HttpServletRequest request) {
        log.info("Login/Register request for mobile: {}", req.getMobile());
        UserLoginRes res = loginService.loginOrRegisterByMobileAndCode(req.getMobile(), req.getVerifyCode(), request);
        return SingleResponse.of(res);
    }

    /**
     * 使用邀请码激活当前用户账户接
     *
     * @param invitationCode 邀请码
     * @return 标准成功/失败响应
     */
    @PostMapping("/activate/{invitationCode}") // 修改端点，使用路径参
@Operation(summary = "Operation")
    public Response activateUser(@PathVariable @NotBlank(message = "邀请码不能为空") String invitationCode) { // 修改参数为路径变
        log.info("Activate user request with invitation code: {}", invitationCode);
        // 调用 LoginService 的方法处理激活逻辑 (假设 userService 能获取当前用户信
        loginService.activateUserWithInvitation(invitationCode);
        return Response.buildSuccess();
    }

    /**
     * 发送手机验证码
     *
     * @param req 包含手机号的请求
     * @return 标准成功/失败响应
     */
    @PostMapping("/mobile/send-code")
    @Operation(summary = "发送手机验证码")
    public Response sendMobileVerificationCode(@Valid @RequestBody SendMobileCodeReq req) {
        log.info("Request to send mobile verification code to: {}", req.getMobile());
        loginService.sendMobileVerificationCode(req.getMobile());
        return Response.buildSuccess();
    }



}
