package com.wlink.agent.user.controller;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.constant.WechatConstant;
import com.wlink.agent.service.WechatService;


import com.wlink.agent.user.model.WechatLoginCheckRes;
import com.wlink.agent.user.model.WechatLoginReq;
import com.wlink.agent.user.model.WechatLoginRes;
import com.wlink.agent.user.model.WechatQrCodeRes;
import com.wlink.agent.user.service.LoginService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * 微信登录控制
 */
@Slf4j
@Tag(name = "微信登录接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/wechat")
public class WechatLoginController {

    private final WechatService wechatService;
    private final LoginService loginService;

    /**
     * 获取微信扫码登录的二维码URL
     * 前端需要根据返回的URL生成二维码图
     *
     * @return 包含二维码URL和state的响应对
     */
    @GetMapping("/login/qrcode")
    @Operation(summary = "获取微信扫码登录二维码URL")
    public SingleResponse<WechatQrCodeRes> getLoginQrCode() {
        log.info("Generate WeChat login QR code URL");
        WechatQrCodeRes qrCodeInfo = wechatService.generateLoginQrCodeUrl();
        return SingleResponse.of(qrCodeInfo);
    }

    /**
     * 检查微信扫码登录状
     * 前端需要轮询此接口获取最新状
     * 当状态为CONFIRMED时自动完成登
     *
     * @param state 状态码
     * @param request HTTP请求对象
     * @return 登录状态和相关数据（包括登录成功的凭证
     */
    @GetMapping("/login/check")
    @Operation(summary = "检查微信扫码登录状态并自动完成登录")
    public SingleResponse<WechatLoginCheckRes> checkLoginStatus(@RequestParam String state, HttpServletRequest request) {
        log.info("Check WeChat login status for state: {}", state);
        WechatLoginCheckRes response = wechatService.checkLoginStatusAndAutoLogin(state, request);
        return SingleResponse.of(response);
    }

    /**
     * 处理微信授权回调
     * 此接口由微信服务器调用，不应由前端直接调
     *
     * @param code 授权
     * @param state 状态码
     * @return 成功响应
     */
    @GetMapping("/callback")
    @Operation(summary = "处理微信授权回调")
    public Response handleCallback(@RequestParam String code, @RequestParam String state) {
        log.info("WeChat authorization callback with code: {}, state: {}", code, state);
        
        // 验证state参数
        if (!wechatService.validateState(state)) {
            log.warn("Invalid state parameter: {}", state);
            return Response.buildFailure("INVALID_STATE", "无效的state参数");
        }
        
        try {
            // 获取访问令牌
            JSONObject tokenInfo = wechatService.getAccessToken(code, state);
            if (tokenInfo == null) {
                log.error("Failed to get access token for state: {}", state);
                wechatService.setLoginStatus(state, WechatConstant.LoginStatus.CANCELED, null);
                return Response.buildFailure("GET_TOKEN_FAILED", "获取访问令牌失败");
            }
            
            String accessToken = tokenInfo.getString(WechatConstant.ApiResponse.ACCESS_TOKEN);
            String openid = tokenInfo.getString(WechatConstant.ApiResponse.OPENID);
            
            // 获取用户信息
            JSONObject userInfo = wechatService.getUserInfo(accessToken, openid);
            if (userInfo == null) {
                log.error("Failed to get user info for openid: {}", openid);
                wechatService.setLoginStatus(state, WechatConstant.LoginStatus.CANCELED, null);
                return Response.buildFailure("GET_USERINFO_FAILED", "获取用户信息失败");
            }
            
            // 合并token信息和用户信
            JSONObject loginData = new JSONObject();
            loginData.putAll(tokenInfo);
            loginData.putAll(userInfo);
            
            // 保存授权码、访问令牌和用户信息到Redis
            loginData.put("code", code);
            loginData.put("accessToken", accessToken);
            loginData.put("openid", openid);
            loginData.put("wechatTokenInfo", tokenInfo);
            loginData.put("wechatUserInfo", userInfo);
            
            // 设置为已确认状态并保存数据到Redis
            wechatService.setLoginStatus(state, WechatConstant.LoginStatus.CONFIRMED, loginData);
            
            return Response.buildSuccess();
        } catch (Exception e) {
            log.error("Exception occurred during WeChat callback handling", e);
            wechatService.setLoginStatus(state, WechatConstant.LoginStatus.CANCELED, null);
            return Response.buildFailure("CALLBACK_ERROR", "处理微信回调时发生错误");
        }
    }

    /**
     * 通过微信授权码登
     * 前端在收到回调并确认状态为CONFIRMED后调用此接口完成登录
     * 注意：此接口现在已可选，因为/login/check接口会自动完成登
     *
     * @param req 微信登录请求
     * @param request HTTP请求
     * @return 登录响应
     */
    @PostMapping("/login")
    @Operation(summary = "微信扫码登录(已可选，由check接口自动完成)")
    public SingleResponse<WechatLoginRes> wechatLogin(@Valid @RequestBody WechatLoginReq req, HttpServletRequest request) {
        log.info("WeChat login with state: {}", req.getState());
        
        WechatLoginRes res = loginService.loginByWechat(req.getCode(), req.getState(), request);
        return SingleResponse.of(res);
    }



} 
