package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 微信登录检查响应数据模型，兼容用户登录响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "微信登录检查响应数据模型，兼容用户登录响应格式")
public class WechatLoginCheckRes {
    /**
     * 微信登录状
     * 对应 WechatConstant.LoginStatus 中的
     * 0-未扫码，1-已扫码未确认-已确认登录，3-已取消，4-已过
     */
    @Schema()
    private int wxStatus;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息", example = "授权失败")
    private String errorMsg;

    /**
     * 是否登录成功
     * 只有在状态为已确认登2)且成功完成登录流程时才为true
     */
    @Schema(description = "是否登录成功", example = "true")
    private boolean loginSuccess;

    /**
     * 认证令牌 (JWT Token)
     */
    @Schema(description = "认证令牌 (JWT Token)", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String authorization;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1234567890")
    private String userId;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 用户
     */
    @Schema(description = "用户, example = ", example = "John Doe")
    private String nickname;

    /**
     * 用户头像URL
     */
    @Schema(description = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 用户状态：0-未激活，1-正常-禁用
     */
    @Schema(description = "用户状, notes = ", example = "1")
    private Integer status;
    
    /**
     * 是否已绑定手机号
     */
    @Schema(description = "是否已绑定手机号", example = "false")
    private boolean isMobileBound;
} 
