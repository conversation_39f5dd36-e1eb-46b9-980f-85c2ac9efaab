package com.wlink.agent.user.model; // 注意包路径可能需要调

import io.swagger.v3.oas.annotations.media.Schema; // 添加 Swagger 注解
import io.swagger.v3.oas.annotations.media.Schema; // 添加 Swagger 注解
import lombok.AllArgsConstructor; // 方便创建实例
import lombok.Data;
import lombok.NoArgsConstructor; // 需要无参构

@Data
@NoArgsConstructor // COLA 反序列化可能需
@AllArgsConstructor
@Schema(description = "用户登录响应") // Swagger 注解
public class UserLoginRes {

    @Schema(description = "认证令牌 (JWT Token)", example = "eyJhbGciOiJIUzUxMiJ9...") // Swagger 注解
    private String authorization;

    @Schema(description = "用户ID", example = "1234567890") // Swagger 注解
    private String userId;

    @Schema(description = "用户邮箱", example = "<EMAIL>") // Swagger 注解
    private String email;
    
    @Schema(description = "用户手机掩码处理)", example = "138****8000")
    private String mobile;

    @Schema(description = "用户, example = ", example = "John Doe")
    private String nickname;

    @Schema(description = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    //状
    @Schema(description = "用户状状态：0-未激活，1-正常-禁用 ", example = "1")
    private Integer status;

}
