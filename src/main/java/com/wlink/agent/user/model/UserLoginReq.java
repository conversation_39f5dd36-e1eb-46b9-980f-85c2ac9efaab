package com.wlink.agent.user.model; // 注意包路径可能需要调

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

import io.swagger.v3.oas.annotations.media.Schema; // 添加 Swagger 注解
import io.swagger.v3.oas.annotations.media.Schema; // 添加 Swagger 注解
import lombok.Data;

@Data
@Schema(description = "用户登录请求") // Swagger 注解
public class UserLoginReq {

    @Schema(description = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$", message = "邮箱格式不正确")
    private String email;
    //验证
    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;
}
