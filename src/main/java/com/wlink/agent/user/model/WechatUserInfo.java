package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信用户信息数据模型
 */
@Data
@Schema(description = "微信用户信息")
public class WechatUserInfo {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "u123456789")
    private String userId;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    /**
     * 用户头像URL
     */
    @Schema(description = "用户头像URL", example = "https://thirdwx.qlogo.cn/mmopen/xxx")
    private String avatar;

    /**
     * 访问令牌
     */
    @Schema(description = "JWT访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    /**
     * 是否为新用户（首次登录）
     */
    @Schema(description = "是否为新用户", example = "true")
    private Boolean isNewUser;

    /**
     * 用户状态：0-未激活，1-正常-禁用
     */
    @Schema(description = "用户状, notes = ", example = "1")
    private Integer status;
} 
