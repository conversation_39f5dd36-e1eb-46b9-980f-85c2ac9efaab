package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime; // 建议使用 Java 8 时间 API
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户邀请码信息")
public class InvitationCodeRes {

    @Schema(description = "邀请码")
    private String code;

    @Schema(description = "创建时间")
    private Date createTime; // 使用 LocalDateTime

    @Schema(description = "状")
    private Integer status;

    //使用用户
    @Schema()
    private String email;

    //分享链接
    @Schema(description = "分享链接")
    private String shareLink;


}
