package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 支付订单响应DTO
 */
@Data
@Schema(description = "支付订单响应")
public class PayOrderRes {

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题")
    private String subject;

    /**
     * 订单金额（单位：元）
     */
    @Schema(description = "订单金额（单位：元）")
    private Double amount;

    /**
     * 支付方式-微信支付
     */
    @Schema(description = "支付方式-微信支付")
    private Integer payType;

    /**
     * 微信支付参数 - 预支付ID
     */
    @Schema(description = "微信支付参数 - 预支付ID")
    private String prepayId;

    /**
     * 微信支付参数 - 二维码链
     */
    @Schema()
    private String codeUrl;

    /**
     * 微信支付参数 - H5支付URL（用于移动端唤起微信支付
     */
    @Schema(description = "微信支付参数 - H5支付URL")
    private String mwebUrl;

    /**
     * 微信支付参数 - 小程序支付参
     */
    @Schema(description = "微信支付参数 - 小程序支付参?")
    private WxPayParams wxPayParams;

    /**
     * 订单状态：0-未支付，1-支付中，2-已支付，3-已取消，4-已退
     */
    @Schema()
    private Integer status;

    /**
     * 微信小程序支付参
     */
    @Data
    @Schema(description = "微信小程序支付参数")
    public static class WxPayParams {
        
        /**
         * 小程序appId
         */
        @Schema(description = "小程序appId")
        private String appId;
        
        /**
         * 时间
         */
        @Schema()
        private String timeStamp;
        
        /**
         * 随机字符
         */
        @Schema()
        private String nonceStr;
        
        /**
         * 订单详情扩展字符
         */
        @Schema()
        private String packageValue;
        
        /**
         * 签名方式
         */
        @Schema(description = "签名方式")
        private String signType;
        
        /**
         * 签名
         */
        @Schema(description = "签名")
        private String paySign;
    }
} 
