package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 绑定手机号请求模
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "绑定手机号请求")
public class MobileBindReq {
    
    /**
     * 手机
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机, required = true, example = ", required = true, example = "13800138000")
    private String mobile;
    
    /**
     * 验证
     */
    @NotBlank(message = "验证码不能为空")
    @Size(min = 6, max = 6, message = "验证码长度必须为6位")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确")
    @Schema(description = "验证, required = true, example = ", required = true, example = "123456")
    private String code;
} 
