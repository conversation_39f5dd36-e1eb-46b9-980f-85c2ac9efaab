package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 微信登录请求数据模型
 */
@Data
@Schema(description = "微信登录请求")
public class WechatLoginReq {

    /**
     * 微信授权
     */
    @NotBlank(message = "微信授权码不能为")
    @Schema(description = "微信授权, required = true, example = ", required = true, example = "081nt1Ga1j70W64nv8Ga1uV2mB2nt1G8")
    private String code;

    /**
     * 状态码，用于防止CSRF攻击
     */
    @NotBlank(message = "状态码不能为空")
    @Schema(description = "状态码", required = true, example = "a1b2c3d4e5f6g7h8")
    private String state;
} 
