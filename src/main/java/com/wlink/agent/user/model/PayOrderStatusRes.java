package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 支付订单状态响应DTO（用于轮询支付状态）
 */
@Data
@Schema(description  = "支付订单状态响应DTO（用于轮询支付状态）")
public class PayOrderStatusRes {

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 订单金额（单位：元）
     */
    @Schema(description = "订单金额（单位：元）")
    private Double amount;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题")
    private String subject;

    /**
     * 订单状态：0-未支付，1-支付中，2-已支付，3-已取消，4-已退
     */
    @Schema()
    private Integer status;

    /**
     * 订单状态描
     */
    @Schema()
    private String statusDesc;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 支付时间（支付成功时有值）
     */
    @Schema(description = "支付时间（支付成功时有值）")
    private Date payTime;
} 
