package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 发送手机验证码请求模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "发送手机验证码请求")
public class SendMobileCodeReq {
    
    /**
     * 手机
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机, required = true, example = ", required = true, example = "13800138000")
    private String mobile;
} 
