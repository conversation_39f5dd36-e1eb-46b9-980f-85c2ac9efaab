package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.Size;


@Data // Lombok 注解，自动生getter, setter, toString, equals, hashCode
@NoArgsConstructor // Lombok 注解，生成无参构造函
@AllArgsConstructor // Lombok 注解，生成全参构造函
@Schema(description = "用户个人资料响应 DTO") // Swagger 注解，用API 文档
public class UserProfileRes {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "用户头像 URL")
    private String avatar;

    @Schema(description = "用户状状态：0-未激活，1-正常-禁用 ")
    private Integer status;

    //地区
    @Schema(description = "用户地区", example = "上海")
    @Size(max = 50, message = "地区长度不能超过50个字")
    private String region;


    @Schema(description = "用户职业")
    @Size(max = 50, message = "职业长度不能超过50个字")
    private String job;


    @Schema(description = "用户年龄")
    @Size(max = 50, message = "年龄长度不能超过50个字")
    private String age;

    @Schema(description = "用户性别")
    @Size(max = 50, message = "性别长度不能超过50个字")
    private String gender;

    //积分
    @Schema(description = "用户积分", example = "1000")
    private Integer points;





}
