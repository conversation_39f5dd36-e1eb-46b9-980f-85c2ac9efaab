package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建订单请求DTO
 */
@Data
@Schema(description = "创建订单请求")
public class CreateOrderReq {

    /**
     * 商品标题
     */
    @NotBlank(message = "商品标题不能为空")
    @Schema(description = "商品标题", required = true)
    private String subject;

    /**
     * 商品描述
     */
    @Schema(description = "商品描述", example = "会员服务1个月")
    private String body;

    /**
     * 订单金额（单位：元）
     */
    @NotNull(message = "订单金额不能为空")
    @Min(value = 0, message = "订单金额必须大于等于0")
    @Schema(description = "订单金额（单位：元）", required = true, example = "9.9")
    private Double amount;

    /**
     * 支付方式-微信支付
     */
    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式-微信支付", required = true, example = "1")
    private Integer payType;
} 
