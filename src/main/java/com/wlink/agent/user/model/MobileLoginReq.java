package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 手机号验证码登录请求数据模型
 */
@Data
@Schema(description = "手机号验证码登录请求")
public class MobileLoginReq {

    /**
     * 手机
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机, required = true, example = ", required = true, example = "13800138000")
    private String mobile;

    /**
     * 验证
     */
    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证, required = true, example = ", required = true, example = "123456")
    private String verifyCode;
} 
