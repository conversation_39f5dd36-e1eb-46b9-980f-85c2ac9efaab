package com.wlink.agent.user.model; // 注意包路径可能需要调

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

import io.swagger.v3.oas.annotations.media.Schema; // 添加 Swagger 注解
import io.swagger.v3.oas.annotations.media.Schema; // 添加 Swagger 注解
import lombok.Data;

@Data
@Schema(description = "发送邮箱验证码请求") // Swagger 注解
public class SendCodeReq {

    @Schema(description = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$", message = "邮箱格式不正确")
    private String email;
}
