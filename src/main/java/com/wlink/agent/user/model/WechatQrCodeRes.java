package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 微信扫码登录二维码响应数据模
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "微信扫码登录二维码响应数据")
public class WechatQrCodeRes {
    /**
     * 微信扫码登录的二维码URL
     */
    @Schema(description = "微信扫码登录的二维码URL, example = ", example = "https://wx.qq.com/cgi-bin/mmwebwx-bin/qrcode?ticket=TICKET")
    private String qrCodeUrl;

    /**
     * 状态码，用于防止CSRF攻击和关联登录流
     */
    @Schema(description = "状态码，用于防止CSRF攻击和关联登录流, example = ", example = "f7a8b9c0d1e2")
    private String state;
} 
