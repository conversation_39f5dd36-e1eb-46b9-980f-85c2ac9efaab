package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 绑定手机号响应模
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "绑定手机号响应")
public class MobileBindRes {
    
    /**
     * 绑定是否成功
     */
    @Schema(description = "绑定是否成功", example = "true")
    private boolean success;
    
    /**
     * 手机号（部分隐藏，如38****8000
     */
    @Schema(description = "手机号（部分隐藏, example = ", example = "138****8000")
    private String maskedMobile;
    
    /**
     * 错误消息，成功时为null或空
     */
    @Schema(description = "错误消息")
    private String errorMsg;
    
    /**
     * 用户令牌，合并账户时返回
     */
    @Schema(description = "用户令牌(合并账户时返", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String Authorization;
    
    /**
     * 用户ID，合并账户时返回
     */
    @Schema(description = "用户ID(合并账户时返", example = "1234567890")
    private String userId;
    
    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;
    
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称", example = "John Doe")
    private String nickname;
    
    /**
     * 用户头像URL
     */
    @Schema(description = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;
    
    /**
     * 用户状态：0-未激活，1-正常-禁用
     */
    @Schema(description = "用户状状态：0-未激活，1-正常-禁用", example = "1")
    private Integer status;
} 
