package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户积分账单记录")
public class PointsHistoryRes {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "变更描述")
    private String description;

    @Schema(description = "积分变更值（正数表示增加，负数表示减少）")
    private Integer pointsChange;

    @Schema(description = "变更后的余额（可选）")
    private Integer balanceAfterChange;

    @Schema(description = "变更时间")
    private Date timestamp;

    @Schema()
    private String relatedBusinessId;
}
