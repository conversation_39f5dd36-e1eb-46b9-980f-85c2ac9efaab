package com.wlink.agent.user.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户个人资料更新请求 DTO")
public class UserProfileUpdateReq {

    @Schema(description = "新的用户昵称")
    @Size(max = 50, message = "昵称长度不能超过50个字") // 添加校验示例
    private String nickname;

    @Schema(description = "新的用户头像 URL", example = "http://example.com/new_avatar.png")
    @Size(max = 255, message = "头像URL长度不能超过255个字") // 添加校验示例
    private String avatarUrl;

    //地区
    @Schema(description = "用户地区", example = "上海")
    @Size(max = 50, message = "地区长度不能超过50个字")
    private String region;


    @Schema(description = "用户职业")
    @Size(max = 50, message = "职业长度不能超过50个字")
    private String job;


    @Schema(description = "用户年龄")
    @Size(max = 50, message = "年龄长度不能超过50个字")
    private String age;

    @Schema(description = "用户性别")
    @Size(max = 50, message = "性别长度不能超过50个字")
    private String gender;



}
