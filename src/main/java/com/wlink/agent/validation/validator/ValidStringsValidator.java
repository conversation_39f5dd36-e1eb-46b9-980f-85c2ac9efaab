package com.wlink.agent.validation.validator;

import com.wlink.agent.validation.annotation.ValidStrings;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;

/**
 * 验证集合中的字符串元素均为有效值的验证器实
 */
public class ValidStringsValidator implements ConstraintValidator<ValidStrings, Collection<String>> {

    @Override
    public void initialize(ValidStrings constraintAnnotation) {
        // 初始化，不需要特殊操
    }

    @Override
    public boolean isValid(Collection<String> values, ConstraintValidatorContext context) {
        if (values == null) {
            return true; // 为null的情况由@NotEmpty或@NotNull处理
        }
        
        // 验证集合中的每个元素不为null且不为空字符
        for (String value : values) {
            if (value == null || value.trim().isEmpty()) {
                return false;
            }
        }
        
        return true;
    }
} 
