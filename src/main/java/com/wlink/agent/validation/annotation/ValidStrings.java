package com.wlink.agent.validation.annotation;

import com.wlink.agent.validation.validator.ValidStringsValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证集合中的字符串元素均为有效值（非空字符串且非null
 */
@Documented
@Constraint(validatedBy = ValidStringsValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidStrings {
    
    String message() default "集合中不能包含空字符串或null";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
} 
