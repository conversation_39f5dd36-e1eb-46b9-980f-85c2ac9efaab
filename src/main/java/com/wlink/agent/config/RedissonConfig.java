package com.wlink.agent.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/22 15:21
 */
@Slf4j
@Configuration
public class RedissonConfig {


    @Bean
    public RedissonClient redissonClient(@Value("${spring.redis.host}") String host,
                                         @Value("${spring.redis.port}") String port,
                                         @Value("${spring.redis.password:}") String password,
                                         @Value("${spring.redis.database:0}") int database) {
        log.info("Redis host: {}, port: {}, password: {}, database: {}", host, port, password, database);
        Config config = new Config();

        // 使用JSON序列化
        config.setCodec(new JsonJacksonCodec());

        SingleServerConfig serverConfig = config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setDatabase(database);  // 设置database

        if (StringUtils.hasText(password)) {
            serverConfig.setPassword(password);
        }

        return Redisson.create(config);
    }
}
