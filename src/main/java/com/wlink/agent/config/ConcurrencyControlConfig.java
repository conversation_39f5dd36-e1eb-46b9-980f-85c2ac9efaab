package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 并发控制配置
 * 可通过配置文件动态调整并发参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.concurrency")
public class ConcurrencyControlConfig {

    /**
     * 最大并发任务数
     */
    private int maxConcurrentTasks = 5;

    /**
     * 任务超时时间（分钟）
     */
    private int taskTimeoutMinutes = 10;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 重试延迟基础时间（秒）
     */
    private int retryDelaySeconds = 30;

    /**
     * 是否启用数据库并发控制
     * true: 使用数据库并发控制
     * false: 使用原有的Redis信号量
     */
    private boolean enableDatabaseConcurrencyControl = true;

    /**
     * 任务调度相关配置
     */
    public static class Scheduler {
        /**
         * 主调度器执行间隔（秒）
         */
        private int mainSchedulerIntervalSeconds = 30;

        /**
         * 高优先级调度器执行间隔（秒）
         */
        private int highPrioritySchedulerIntervalSeconds = 10;

        /**
         * 统计任务执行间隔（分钟）
         */
        private int statisticsIntervalMinutes = 5;

        /**
         * 每次调度处理的最大任务数
         */
        private int maxTasksPerSchedule = 10;

        // Getters and Setters
        public int getMainSchedulerIntervalSeconds() { return mainSchedulerIntervalSeconds; }
        public void setMainSchedulerIntervalSeconds(int mainSchedulerIntervalSeconds) {
            this.mainSchedulerIntervalSeconds = mainSchedulerIntervalSeconds;
        }

        public int getHighPrioritySchedulerIntervalSeconds() { return highPrioritySchedulerIntervalSeconds; }
        public void setHighPrioritySchedulerIntervalSeconds(int highPrioritySchedulerIntervalSeconds) {
            this.highPrioritySchedulerIntervalSeconds = highPrioritySchedulerIntervalSeconds;
        }

        public int getStatisticsIntervalMinutes() { return statisticsIntervalMinutes; }
        public void setStatisticsIntervalMinutes(int statisticsIntervalMinutes) {
            this.statisticsIntervalMinutes = statisticsIntervalMinutes;
        }

        public int getMaxTasksPerSchedule() { return maxTasksPerSchedule; }
        public void setMaxTasksPerSchedule(int maxTasksPerSchedule) {
            this.maxTasksPerSchedule = maxTasksPerSchedule;
        }
    }

    private Scheduler scheduler = new Scheduler();

    public Scheduler getScheduler() { return scheduler; }
    public void setScheduler(Scheduler scheduler) { this.scheduler = scheduler; }
}
