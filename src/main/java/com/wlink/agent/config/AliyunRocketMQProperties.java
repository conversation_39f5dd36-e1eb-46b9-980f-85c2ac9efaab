package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "rocketmq")
public class AliyunRocketMQProperties {

    private String accessKey;
    private String secretKey;
    private String nameSrvAddr;
    private String groupId;

    // 主题配置
    private Topic topic = new Topic();

    // 消费者配置
    private Consumer consumer = new Consumer();

    @Data
    public static class Topic {
        private String imageTask;
        private String paymentPoints;
        private String videoTask;
    }

    @Data
    public static class Consumer {
        private String groupId; // 消费者组ID (CID)
        private int consumeThreadNums = 20; // 消费线程数
        private int maxReconsumeTimes = 16; // 最大重试消费次数
    }
}