package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云短信配置类
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "aliyun.sms")
public class AliSmsConfig {
    
    /**
     * 访问密钥ID
     */
    private String accessKeyId = "LTAI5tMcHkKdzqwYUEkPxzHu";
    
    /**
     * 访问密钥Secret
     */
    private String accessKeySecret = "******************************";
    
    /**
     * 短信签名名称
     */
    private String signName = "上海智灵新境科技";
    
    /**
     * 地域ID
     */
    private String regionId = "cn-hangzhou";
    
    /**
     * 验证码短信模板编码
     */
    private String verificationTemplateCode = "SMS_318630139";

    /**
     * 通知短信模板编码
     */
    private String notificationTemplateCode;
} 