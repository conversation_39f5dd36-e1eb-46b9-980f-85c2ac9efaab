package com.wlink.agent.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class OSSConfig {
    @Value("${wlink.agent.accessKey:LTAI5tJWLvYv6LQvoHxMhujg}")
    private String accessKey;
    
    @Value("${wlink.agent.secret:******************************}")
    private String accessKeySecret;
    
    @Value("${wlink.agent.endpoint:https://oss-accelerate.aliyuncs.com}")
    private String endpoint;
    
    @Bean
    public OSS ossClient() {
        log.info("Initializing OSS client with endpoint: {}", endpoint);
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKey, accessKeySecret);
        log.info("OSS client initialized successfully");
        return ossClient;
    }
}
