//package com.wlink.agent.config;
//
//import io.swagger.v3.oas.annotations.OpenAPIDefinition;
//import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
//import io.swagger.v3.oas.annotations.info.Info;
//import io.swagger.v3.oas.annotations.security.SecurityScheme;
//import io.swagger.v3.oas.annotations.servers.Server;
//import org.springframework.context.annotation.Configuration;
//
///**
// * OpenAPI配置类
// */
//@Configuration
//@OpenAPIDefinition(
//    info = @Info(title = "运营后台 API", version = "v1", description = "运营后台管理系统API接口文档"),
//    servers = {@Server(url = "/", description = "Default Server URL")}
//)
//@SecurityScheme(
//    name = "bearerAuth",
//    type = SecuritySchemeType.HTTP,
//    bearerFormat = "JWT",
//    scheme = "bearer"
//)
//public class OpenAPIConfig {
//}