package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "app.auth") // 定义配置前缀
public class AuthProperties {

    /**
     * 获取 Token 的请求头名称
     */
    private String tokenHeader = "accessToken";

    /**
     * 不需要 Token 校验的路径白名单 (Ant 风格)
     */
    private List<String> tokenWhitelist = new ArrayList<>();

    /**
     * 需要进行 IP 校验的路径 (Ant 风格)
     */
    private List<String> ipWhitelistPaths = new ArrayList<>();

    /**
     * 允许访问 IP 校验路径的 IP 地址列表
     */
    private List<String> allowedIps = new ArrayList<>();

    /**
     * Redis 中存储 Token 的 Key 前缀
     */
    private String redisTokenPrefix = "user:tokens:"; // Default value

}