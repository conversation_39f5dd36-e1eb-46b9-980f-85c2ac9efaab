package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云内容安全配置类
 * 用于管理图片和文本检测服务的相关配置
 * 
 * <AUTHOR> Assistant
 * @since 2024-12-19
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.content-security")
public class AliyunContentSecurityConfig {

    /**
     * 阿里云AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * 内容安全服务端点
     * 默认为华东1（杭州）
     */
    private String endpoint = "green-cip-vpc.cn-shanghai.aliyuncs.com";

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 默认图片检测服务类型
     * baselineCheck: 基线检测
     * tonalityImprove: 色调优化检测
     */
    private String defaultService = "baselineCheck";
    
    /**
     * 默认文本检测服务类型
     * chat_detection: 聊天文本检测
     * comment_detection: 评论文本检测
     */
    private String defaultTextService = "comment_detection";

    /**
     * 是否启用内容安全检测
     */
    private Boolean enabled = true;
} 