package com.wlink.agent.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: WebClient 配置，仅保留超时设置
 * @date 2025/4/18 11:40
 */
@Configuration
public class WebClientConfig {

    @Bean
    public WebClient webClient() {
        // 创建 HttpClient 并配置超时
        HttpClient httpClient = HttpClient.create()
                // 使用 tcpConfiguration 来设置连接超时和读写超时处理
                .tcpConfiguration(tcp -> tcp
                        .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000) // 连接超时 30秒
                        .doOnConnected(connection -> {
                            connection.addHandlerLast(new ReadTimeoutHandler(600, TimeUnit.SECONDS)); // 读取超时 600秒
                            connection.addHandlerLast(new WriteTimeoutHandler(600, TimeUnit.SECONDS)); // 写入超时 600秒
                        })
                )
                .responseTimeout(Duration.ofSeconds(600)); // 响应超时 600秒

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }
}
