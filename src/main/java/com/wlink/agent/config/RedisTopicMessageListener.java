package com.wlink.agent.config;


import com.google.common.cache.Cache;
import com.wlink.agent.manager.SseEmitterManager;
import com.wlink.agent.model.dto.NotificationMessage;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/22 19:02
 */
@Slf4j
@Configuration
public class RedisTopicMessageListener {

    @Resource
    RedissonClient redissonClient;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private SseEmitterManager sseEmitterManager;

    private final Cache<String, Boolean> messageProcessCache;

    // 使用消息ID作为锁的key，为每个消息ID创建一个锁
    private final Map<String, Lock> messageLocks = new ConcurrentHashMap<>();
    
    private static final String SSE_NOTIFICATION_TOPIC = "sse_operation_notification_topic";

    @Autowired
    public RedisTopicMessageListener(Cache<String, Boolean> messageProcessCache) {
        this.messageProcessCache = messageProcessCache;
    }


    @Bean
    public void onMessage() {
        log.info("RedisTopicMessageListener init");
        setupSseNotificationListener();
        log.info("All Redis topic listeners registered.");
    }

    private void setupSseNotificationListener() {
        RTopic topic = redissonClient.getTopic(SSE_NOTIFICATION_TOPIC);
        topic.addListener(NotificationMessage.class, (channel, notification) -> {
            String conversationId = notification.getConversationId();
            log.info("Received SSE notification via channel {} for conversationId: {}, eventPage: {}",
                    channel, conversationId, notification.getEventPage());

            // Find the emitter managed by *this* instance
            SseEmitter emitter = sseEmitterManager.get(conversationId);
            if (emitter != null) {
                try {
                    Thread.sleep(1000);
                    log.info("Found local emitter for conversationId: {}. Sending notification.", conversationId);
                    emitter.send(SseEmitter.event()
                            .name("eventPage")
                            .data(notification, MediaType.APPLICATION_JSON));
                } catch (IOException e) {
                    log.error("Failed to send SSE notification to conversationId: {}. Removing emitter. Error: {}", conversationId, e.getMessage());
                    // Remove emitter if send fails, as the connection is likely broken
                    sseEmitterManager.remove(conversationId, "send failed");
                } catch (Exception e) {
                    log.error("Unexpected error sending SSE notification to conversationId: {}. Error: {}", conversationId, e.getMessage(), e);
                }
            } else {
                 log.error("No local emitter found for conversationId: {}. Notification ignored by this instance.", conversationId);
            }
        });
        log.info("Subscribed to Redisson topic for SSE notifications: {}", SSE_NOTIFICATION_TOPIC);
    }
}
