package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信开放平台配置属性
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wechat")
public class WechatProperties {

    /**
     * 微信开放平台应用ID
     */
    private String appId;

    /**
     * 微信开放平台应用密钥
     */
    private String appSecret;


    /**
     * 公众号appId
     */
    private String mpAppId;

    /**
     * 公众号密钥
     */
    private String mpAppSecret;

    /**
     * 微信登录回调URL
     */
    private String redirectUrl;

    /**
     * 授权作用域，默认snsapi_login表示获取用户信息
     */
    private String scope = "snsapi_login";

    /**
     * 微信登录二维码过期时间（秒），默认120秒
     */
    private Integer qrCodeExpiresIn = 120;

    /**
     * 用于生成state参数的密钥，防止CSRF攻击
     */
    private String stateKey = "wechat_state_key";
    
    /**
     * 微信登录相关的Redis键前缀
     */
    private String redisPrefix = "wechat:login:";
    
    /**
     * Redis中存储微信登录状态的过期时间（秒）
     */
    private Integer redisExpire = 300;
    
    /**
     * jsapi_ticket缓存时间（秒），默认7000秒，比微信官方的7200秒少一些，确保安全
     */
    private Integer jsapiTicketExpireSeconds = 7000;
} 