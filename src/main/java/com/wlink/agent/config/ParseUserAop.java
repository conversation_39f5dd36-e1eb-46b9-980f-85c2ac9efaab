//package com.wlink.agent.config;
//
//import com.alibaba.cola.exception.BizException;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.serializer.SerializerFeature;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//
//import com.wlink.agent.exception.ErrorCodeEnum;
//import com.wlink.agent.model.dto.SimpleUserInfo;
//import com.wlink.agent.utils.I18nMessageUtils;
//import com.wlink.agent.utils.UserContext;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.redisson.api.RedissonClient;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StopWatch;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import org.springframework.web.multipart.MultipartFile;
//
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import java.util.Arrays;
//import java.util.Enumeration;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//
///**
// * 解析访问controller层的用户信息
// *
// * <AUTHOR>
// */
//@Slf4j
//@Component
//@Aspect
//@Order(3)
//public class ParseUserAop {
//
//    private static final List<String> HEADERS = Lists.newArrayList("authorization", "username", "userAuthStatus");
//    private static final int LIMIT_BODY_LENGTH = 2000;
//
//    private static final String LOG_FMT = "Aop Controller Log [%s %s] user[%s %s] header[%s] req[%s] resp[%s] exception[%s] time[%sms]";
//
//    @Resource
//    private RedissonClient redisClient;
//
//    @Resource
//    private UserInfoFeign userInfoFeign;
//
//    /**
//     * 定义切点Pointcut controller包路
//     */
//    @Pointcut("execution(* com.wlink.agent.controller..*(..)))")
//    public void controllerReq() {
//
//    }
//
//    /**
//     * 获取请求controller方法（IgnoreRequestUser注解修饰的除外）的用户信
//     */
//    @Around("controllerReq() && !@annotation( com.wlink.agent.annotation.IgnoreRequestUser)")
//    public Object parseToken(ProceedingJoinPoint point) throws Throwable {
//        Object result = null;
//        String exceptionMsg = "";
//        StopWatch clock = new StopWatch();
//        clock.start();
//        try {
//            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
//            // 获取 header里的token
//            final String token = request.getHeader("authorization");
//            if (StringUtils.isBlank(token)) {
//                throw new BizException(ErrorCodeEnum.LOGIN_FAIL.getCode(),I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
//            }
//
//            TokenValidRequest validRequest = new TokenValidRequest();
//            validRequest.setToken(token);
//            ResultWrapper<UserBaseInfoDTO> resultWrapper = userInfoFeign.getInfoByToken(validRequest);
//            if (!resultWrapper.success()) {
//                throw new BizException(ErrorCodeEnum.LOGIN_FAIL.getCode(),I18nMessageUtils.getMessage(ErrorCodeEnum.LOGIN_FAIL.getMsg()));
//            }
//
//            UserBaseInfoDTO userBaseInfoDTO = resultWrapper.getData();
//            SimpleUserInfo simpleUserInfo = new SimpleUserInfo();
//            simpleUserInfo.setUserType(userBaseInfoDTO.getUserType());
//            simpleUserInfo.setActiveStatus(userBaseInfoDTO.getActiveStatus());
//            simpleUserInfo.setParentUsername(userBaseInfoDTO.getParentUsername());
//            simpleUserInfo.setNickName(userBaseInfoDTO.getNickName());
//            simpleUserInfo.setUsername(userBaseInfoDTO.getUsername());
//            if (UserTypeEnum.MAIN.getCode() == simpleUserInfo.getUserType()) {
//                simpleUserInfo.setParentUsername(simpleUserInfo.getUsername());
//            }
//
//            //写入登录信息
//            UserContext.setUser(simpleUserInfo);
//            result = point.proceed();
//            return result;
//        } catch (Throwable throwable) {
//            log.warn("请求执行异常, ", throwable);
//            exceptionMsg = throwable.getMessage();
//            throw throwable;
//        } finally {
//            clock.stop();
//            printLog(point, clock.getTotalTimeMillis(), result, exceptionMsg);
//            UserContext.clearUserInfo();
//        }
//    }
//
//    private void printLog(ProceedingJoinPoint joinPoint, long time, Object result, String exceptionMsg) {
//        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
//        String className = joinPoint.getTarget()
//                .getClass()
//                .getName();
//        String methodName = signature.getName();
//        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
//        String method = "";
//        String url = "";
//        String requestHeader = "";
//        String req = "";
//        String res = "";
//        Object[] args = joinPoint.getArgs();
//        String userName = "";
//        String nickName = "";
//        try {
//            if (null != args && args.length != 0) {
//                List<Object> argList = Arrays.stream(args)
//                        .filter((arg) ->
//                                    {
//                                        return !(arg instanceof MultipartFile) && !(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse);
//                                    })
//                        .collect(Collectors.toList());
//                req = JSON.toJSONString(argList, SerializerFeature.IgnoreErrorGetter);
//            }
//            if (null != result) {
//                res = JSON.toJSONString(result);
//            }
//            // 构造请求header
//            Enumeration<String> headerNames = request.getHeaderNames();
//            Map<String, String> headerMap = Maps.newHashMap();
//            while (headerNames.hasMoreElements()) {
//                String key = headerNames.nextElement();
//                String value = request.getHeader(key);
//                if (HEADERS.contains(key)) {
//                    headerMap.put(key, value);
//                }
//            }
//            method = request.getMethod();
//            url = request.getRequestURL()
//                    .toString();
//            requestHeader = headerMap.isEmpty() ? StringUtils.EMPTY : headerMap.toString();
//            res = res.length() < LIMIT_BODY_LENGTH ? res.replaceAll("\n|\r", "") : StringUtils.EMPTY;
//
//            SimpleUserInfo simpleUserInfo = UserContext.getUser();
//            userName = Optional.ofNullable(simpleUserInfo)
//                    .map(SimpleUserInfo::getUsername)
//                    .orElse("");
//            nickName = Optional.ofNullable(simpleUserInfo)
//                    .map(SimpleUserInfo::getNickName)
//                    .orElse("");
//        } catch (Exception ex) {
//            log.error("printLog exception", ex);
//        } finally {
//            // 打印 log
//            log.info(String.format(LOG_FMT, method, url, nickName, userName, requestHeader, req, res, exceptionMsg, time));
//        }
//    }
//
//}
