package com.wlink.agent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步事件配置类
 * 使用Java 17的特性优化配置
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncEventConfig {
    
    /**
     * 配置异步任务执行器
     * 使用Java 17的var关键字和增强的配置
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        var executor = new ThreadPoolTaskExecutor();
        
        // 使用Java 17的文本块和格式化字符串
        var configInfo = """
                配置异步任务执行器:
                - 核心线程数: %d
                - 最大线程数: %d
                - 队列容量: %d
                - 线程名前缀: %s
                """.formatted(5, 10, 200, "async-event-");
        
        log.info(configInfo);
        
        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数
        executor.setMaxPoolSize(10);
        // 队列容量
        executor.setQueueCapacity(200);
        // 线程名前缀
        executor.setThreadNamePrefix("async-event-");
        
        // 拒绝策略 - 使用调用者运行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        return executor;
    }
    
    /**
     * 配置事件专用的执行器
     * 使用Java 17的record类型作为配置参数（如果需要的话）
     */
    @Bean("eventExecutor")
    public Executor eventExecutor() {
        // 使用Java 17的var和switch表达式
        var poolSize = switch (getEnvironment()) {
            case "dev" -> new PoolConfig(2, 5, 50);
            case "test" -> new PoolConfig(3, 8, 100);
            case "prod" -> new PoolConfig(5, 15, 300);
            default -> new PoolConfig(3, 8, 100);
        };
        
        var executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(poolSize.core());
        executor.setMaxPoolSize(poolSize.max());
        executor.setQueueCapacity(poolSize.queue());
        executor.setThreadNamePrefix("event-handler-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        
        log.info("事件执行器配置完成: 核心={}, 最大={}, 队列={}", 
                poolSize.core(), poolSize.max(), poolSize.queue());
        
        return executor;
    }
    
    /**
     * 获取当前环境
     */
    private String getEnvironment() {
        // 这里可以从配置文件或环境变量中获取
        return System.getProperty("spring.profiles.active", "dev");
    }
    
    /**
     * 线程池配置记录类 - 使用Java 17的record特性
     */
    public record PoolConfig(int core, int max, int queue) {
        
        /**
         * 紧凑构造器 - Java 17的record特性
         */
        public PoolConfig {
            if (core <= 0 || max <= 0 || queue <= 0) {
                throw new IllegalArgumentException("线程池参数必须大于0");
            }
            if (core > max) {
                throw new IllegalArgumentException("核心线程数不能大于最大线程数");
            }
        }
        
        /**
         * 获取配置描述
         */
        public String getDescription() {
            return "PoolConfig[core=%d, max=%d, queue=%d]".formatted(core, max, queue);
        }
    }
}
