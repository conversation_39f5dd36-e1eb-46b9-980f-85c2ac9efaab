package com.wlink.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * 微信支付属性配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {

    /**
     * 微信支付应用ID
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户密钥
     */
    private String mchKey;

    /**
     * 服务商模式下的子商户号
     */
    private String subMchId;

    /**
     * 支付回调地址
     */
    private String notifyUrl;

    /**
     * 证书路径
     */
    private String pubKeyPath;
    
    /**
     * API V3密钥
     */
    private String apiV3Key;
    
    /**
     * 私钥文件路径
     */
    private String privateKeyPath;
    
    /**
     * 私钥证书路径
     */
    private String privateCertPath;
    
    /**
     * 证书序列号
     */
    private String certSerialNo;

    /**
     * 微信支付类型： 0-普通模式，1-服务商模式
     */
    private Integer payType = 0;
    
    /**
     * 小程序appId
     */
    private String miniappAppId;
    
    /**
     * 小程序密钥
     */
    private String miniappSecret;
} 