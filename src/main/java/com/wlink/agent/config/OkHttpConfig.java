package com.wlink.agent.config;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * OkHttpClient 配置类
 */
@Configuration
public class OkHttpConfig {

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)    // 连接超时时间：10秒
                .readTimeout(30, TimeUnit.SECONDS)       // 读取超时时间：30秒
                .writeTimeout(30, TimeUnit.SECONDS)      // 写入超时时间：30秒
                .retryOnConnectionFailure(true)          // 连接失败时重试
                .build();
    }
}
