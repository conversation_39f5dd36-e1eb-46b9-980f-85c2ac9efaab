package com.wlink.agent.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/18 14:42
 */

@Configuration
public class CacheConfig {

    @Bean
    public Cache<String, Boolean> messageProcessCache() {
        return CacheBuilder.newBuilder()
                .expireAfterWrite(5, TimeUnit.SECONDS)  // 5秒后自动过期
                .maximumSize(1000)  // 最多缓存1000条消息ID
                .build();
    }
}