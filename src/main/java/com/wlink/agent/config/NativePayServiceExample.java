package com.wlink.agent.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.CloseOrderRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByIdRequest;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
import org.springframework.data.repository.init.ResourceReader;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Scanner;

/** NativePayService使用示例 */
public class NativePayServiceExample {

  /** 商户号 */
  public static String merchantId = "1716346678";

  /** 商户API私钥路径 */
  public static String privateKeyPath = "D:\\wx\\apiclient_key.pem";


  public static String publicKeyPath = "D:\\wx\\pub_key.pem";

  /** 商户证书序列号 */
  public static String merchantSerialNumber = "396FD0A30FD789BEE560B050FB69F234D97EB36E";

  /** 商户APIV3密钥 */
  public static String apiV3Key = "35f1d04629ad483fafa09866558d809c";

  public static NativePayService service;

  public static void main(String[] args) {

    String content = readResourceFileAsString("cert/apiclient_key.pem");

    String publicKey = readResourceFileAsString("cert/pub_key.pem");
    // 初始化商户配置
    Config config =
            new RSAPublicKeyConfig.Builder()
                    .merchantId(merchantId)
                    .privateKeyFromPath(privateKeyPath)
                    .publicKeyFromPath(publicKeyPath)
                    .publicKeyId("PUB_KEY_ID_0117163466782025050700451859000800")
                    .merchantSerialNumber(merchantSerialNumber)
                    .apiV3Key(apiV3Key)
                    .build();

    // 初始化服务
    service = new NativePayService.Builder().config(config).build();
    // ... 调用接口
    try {
      closeOrder();
    } catch (HttpException e) { // 发送HTTP请求失败
      // 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
    } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
      // 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
    } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
      // 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
    }
  }

  /** 关闭订单 */
  public static void closeOrder() {

    CloseOrderRequest request = new CloseOrderRequest();
    // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
    // 调用接口
    service.closeOrder(request);
  }

  /** Native支付预下单 */
  public static PrepayResponse prepay() {
    PrepayRequest request = new PrepayRequest();
    // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
    // 调用接口
    return service.prepay(request);
  }

  /** 微信支付订单号查询订单 */
  public static Transaction queryOrderById() {

    QueryOrderByIdRequest request = new QueryOrderByIdRequest();
    // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
    // 调用接口
    return service.queryOrderById(request);
  }

  /** 商户订单号查询订单 */
  public static Transaction queryOrderByOutTradeNo() {

    QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
    // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
    // 调用接口
    return service.queryOrderByOutTradeNo(request);
  }


    public static String readResourceFileAsString(String resourcePath) {
      ClassLoader classLoader = NativePayServiceExample.class.getClassLoader();
      try (InputStream inputStream = classLoader.getResourceAsStream(resourcePath)) {
        if (inputStream == null) {
          throw new IllegalArgumentException("资源文件未找到: " + resourcePath);
        }
        Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name());
        scanner.useDelimiter("\\A"); // 读取整个流
        return scanner.hasNext() ? scanner.next() : "";
      } catch (Exception e) {
        throw new RuntimeException("读取资源文件失败: " + resourcePath, e);
      }
    }

}