package com.wlink.agent.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * 音频合成服务配置
 */
@Configuration
public class AudioMergeConfig {

    /**
     * 音频合成服务选择配置
     * 
     * 可以通过配置文件选择使用哪种音频合成服务：
     * - simple: SimpleAudioMergeServiceImpl (推荐，无数据库依赖)
     * - redis: AudioMergeRedisServiceImpl (需要Redis)
     * - database: AudioMergeServiceImpl (原始版本，可能有数据库锁问题)
     * 
     * 配置示例：
     * audio:
     *   merge:
     *     service-type: simple
     */
    
    @Configuration
    @ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "simple", matchIfMissing = true)
    public static class SimpleAudioMergeConfig {
        // SimpleAudioMergeServiceImpl 会被自动注册为 "simpleAudioMergeService"
    }
    
    @Configuration
    @ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "redis")
    public static class RedisAudioMergeConfig {
        // AudioMergeRedisServiceImpl 会被自动注册为 "audioMergeRedisService"
    }
    
    @Configuration
    @ConditionalOnProperty(name = "audio.merge.service-type", havingValue = "database")
    public static class DatabaseAudioMergeConfig {
        // AudioMergeServiceImpl 会被自动注册为默认的 AudioMergeService
    }
}
