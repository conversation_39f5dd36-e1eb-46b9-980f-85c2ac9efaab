//package com.wlink.agent.config;
//
//import com.github.binarywang.wxpay.config.WxPayConfig;
//import com.github.binarywang.wxpay.service.WxPayService;
//import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 微信支付配置
// */
//@Slf4j
//@Configuration
//@AllArgsConstructor
//@ConditionalOnClass(WxPayService.class)
//@EnableConfigurationProperties(WxPayProperties.class)
//public class WxPayConfiguration {
//
//    private final WxPayProperties properties;
//
//    @Bean
//    @ConditionalOnMissingBean
//    public WxPayService wxPayService() {
//        log.info("开始初始化微信支付配置");
//
//        if (StringUtils.isBlank(properties.getMchId()) || StringUtils.isBlank(properties.getMchKey())
//                || StringUtils.isBlank(properties.getAppId())) {
//            log.error("缺少微信支付配置，请检查配置文件");
//            throw new IllegalArgumentException("微信支付配置异常: appid=" + properties.getAppId()
//                    + ", mchId=" + properties.getMchId() + ", mchKey=" + properties.getMchKey());
//        }
//
//        WxPayConfig payConfig = new WxPayConfig();
//        payConfig.setAppId(StringUtils.trimToNull(properties.getAppId()));
//        payConfig.setMchId(StringUtils.trimToNull(properties.getMchId()));
//        payConfig.setMchKey(StringUtils.trimToNull(properties.getMchKey()));
//        payConfig.setSubMchId(StringUtils.trimToNull(properties.getSubMchId()));
//        payConfig.setKeyPath(StringUtils.trimToNull(properties.getKeyPath()));
//        payConfig.setNotifyUrl(StringUtils.trimToNull(properties.getNotifyUrl()));
//        payConfig.setApiV3Key(StringUtils.trimToNull(properties.getApiV3Key()));
//        payConfig.setPrivateKeyPath(StringUtils.trimToNull(properties.getPrivateKeyPath()));
//        payConfig.setPrivateCertPath(StringUtils.trimToNull(properties.getPrivateCertPath()));
//        payConfig.setCertSerialNo(StringUtils.trimToNull(properties.getCertSerialNo()));
//
//        // 可以指定是否使用沙箱环境
//        payConfig.setUseSandboxEnv(false);
//
//        WxPayService wxPayService = new WxPayServiceImpl();
//        wxPayService.setConfig(payConfig);
//
//        log.info("微信支付配置初始化完成");
//        return wxPayService;
//    }
//}