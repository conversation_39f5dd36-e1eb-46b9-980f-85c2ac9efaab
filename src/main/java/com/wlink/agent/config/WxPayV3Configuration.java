package com.wlink.agent.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.cipher.SignatureResult;
import com.wechat.pay.java.core.http.DefaultHttpClientBuilder;
import com.wechat.pay.java.core.http.HttpClient;
import com.wechat.pay.java.core.util.PemUtil;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.h5.H5Service;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.refund.RefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.FileCopyUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Scanner;

/**
 * 微信支付V3配置
 */
@Slf4j
@Configuration
public class WxPayV3Configuration {

    @Resource
    private WxPayProperties properties;
    
    @Autowired
    private ResourceLoader resourceLoader;

    /**
     * 配置微信支付V3 Config
     */
    @Bean
    public Config wxPayV3Config() {
        log.info("初始化微信支付V3 Config");

        String mchId = properties.getMchId();
        String originalPrivateKeyPath = properties.getPrivateKeyPath();
        String certSerialNo = properties.getCertSerialNo();
        String apiV3Key = properties.getApiV3Key();

        String privateKey = readResourceFileAsString("cert/apiclient_key.pem");

        String publicKey = readResourceFileAsString("cert/pub_key.pem");
      try{
            // 使用RSAConfig，不自动下载平台证书
          return new RSAPublicKeyConfig.Builder()
                  .merchantId(mchId)
                  .privateKey(privateKey)
                  .publicKey(publicKey)
                  .publicKeyId("PUB_KEY_ID_0117163466782025050700451859000800")
                  .merchantSerialNumber(certSerialNo)
                  .apiV3Key(apiV3Key)
                  .build();
        } catch (Exception e) {
            log.error("初始化微信支付V3 Config失败", e);
            throw new RuntimeException("初始化微信支付V3配置失败", e);
        }
    }

    /**
     * 配置HTTP客户端
     */
    @Bean
    public HttpClient wxPayV3HttpClient(Config wxPayV3Config) {
        log.info("初始化微信支付V3 HttpClient");

        return new DefaultHttpClientBuilder()
                .config(wxPayV3Config)
                // 启用双域名自动切换容灾
                .enableRetryMultiDomain()
                .disableRetryOnConnectionFailure()
                .build();
    }

    /**
     * Native支付服务
     */
    @Bean
    public NativePayService nativePayService(HttpClient wxPayV3HttpClient) {
        return new NativePayService.Builder()
                .httpClient(wxPayV3HttpClient)
                .build();
    }

    /**
     * JSAPI支付服务
     */
    @Bean
    public JsapiService jsapiService(HttpClient wxPayV3HttpClient) {
        return new JsapiService.Builder()
                .httpClient(wxPayV3HttpClient)
                .build();
    }

    /**
     * H5支付服务
     */
    @Bean
    public H5Service h5Service(HttpClient wxPayV3HttpClient) {
        return new H5Service.Builder()
                .httpClient(wxPayV3HttpClient)
                .build();
    }

    /**
     * APP支付服务
     */
    @Bean
    public AppService appService(HttpClient wxPayV3HttpClient) {
        return new AppService.Builder()
                .httpClient(wxPayV3HttpClient)
                .build();
    }

    /**
     * 退款服务
     */
    @Bean
    public RefundService refundService(HttpClient wxPayV3HttpClient) {
        return new RefundService.Builder()
                .httpClient(wxPayV3HttpClient)
                .build();
    }

    /**
     * 提供签名服务
     */
    @Bean
    public com.wechat.pay.java.core.cipher.Signer wxPayV3Signer(Config wxPayV3Config) {
        return new com.wechat.pay.java.core.cipher.Signer() {
            @Override
            public SignatureResult sign(String message) {
                try {
                    // 获取配置中的私钥路径
                    String originalPrivateKeyPath = properties.getPrivateKeyPath();
                    if (originalPrivateKeyPath == null || originalPrivateKeyPath.trim().isEmpty()) {
                        log.error("微信支付V3签名错误：商户私钥路径 (privateKeyPath) 未配置或为空。");
                        throw new IllegalStateException("微信支付V3签名错误：商户私钥路径 (privateKeyPath) 未配置或为空。");
                    }

                    // 使用Spring ResourceLoader加载私钥文件
                    org.springframework.core.io.Resource resource;
                    if (originalPrivateKeyPath.startsWith("classpath:")) {
                        resource = resourceLoader.getResource(originalPrivateKeyPath);
                    } else {
                        resource = resourceLoader.getResource("file:" + originalPrivateKeyPath);
                    }
                    
                    if (!resource.exists()) {
                        log.error("微信支付V3签名错误：商户私钥文件未找到，路径: {}", originalPrivateKeyPath);
                        throw new IllegalArgumentException("微信支付V3签名错误：商户私钥文件未找到，路径: " + originalPrivateKeyPath);
                    }
                    
                    // 将资源转为临时文件
                    File tempFile = resource.getFile();
                    
                    // 加载商户私钥
                    PrivateKey privateKey = PemUtil.loadPrivateKeyFromPath(tempFile.getAbsolutePath());
                    
                    // 使用SHA256withRSA算法进行签名
                    Signature sign = Signature.getInstance("SHA256withRSA");
                    sign.initSign(privateKey);
                    sign.update(message.getBytes(StandardCharsets.UTF_8));
                    byte[] signatureBytes = sign.sign();
                    
                    // 使用Base64编码签名结果并返回
                    String signature = Base64.getEncoder().encodeToString(signatureBytes);
                    return new SignatureResult("RSA-SHA256", signature);
                } catch (Exception e) {
                    throw new RuntimeException("签名失败", e);
                }
            }
            
            @Override
            public String getAlgorithm() {
                // 返回签名算法名称，RSA-SHA256是微信支付V3使用的算法
                return "RSA-SHA256";
            }
        };
    }

    public String readResourceFileAsString(String resourcePath) {
        ClassLoader classLoader = NativePayServiceExample.class.getClassLoader();
        try (InputStream inputStream = classLoader.getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IllegalArgumentException("资源文件未找到: " + resourcePath);
            }
            Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name());
            scanner.useDelimiter("\\A"); // 读取整个流
            return scanner.hasNext() ? scanner.next() : "";
        } catch (Exception e) {
            throw new RuntimeException("读取资源文件失败: " + resourcePath, e);
        }
    }

} 