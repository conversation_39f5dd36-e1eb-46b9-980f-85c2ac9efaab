package com.wlink.agent.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.doubao.DoubaoImageGenerationRequest;
import com.wlink.agent.client.model.volcengine.ImageGenerateRes;
import com.wlink.agent.client.model.volcengine.VolcengineCharacterRetentionRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageEnhanceRequest;
import com.wlink.agent.client.model.volcengine.VolcengineImageRequest;
import com.wlink.agent.client.model.volcengine.VolcengineInpaintingEditRequest;
import com.wlink.agent.client.model.volcengine.VolcengineSeedEditRequest;
import com.wlink.agent.dao.mapper.AiImageApiRecordMapper;
import com.wlink.agent.dao.po.AiImageApiRecordPo;
import com.wlink.agent.dao.po.VendorAccountPo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * 火山引擎图像API调用记录切面
 * 专门针对VolcengineImageApiClient的调用进行记
 */
@Slf4j
@Aspect
@Component
public class AiImageApiAspect {

    @Resource
    private AiImageApiRecordMapper aiImageApiRecordMapper;
    
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 定义切点 - 火山引擎图像API调用客户端中的方
     */
    @Pointcut("execution(* com.wlink.agent.client.VolcengineImageApiClient.generateImage*(..)) || " +
              "execution(* com.wlink.agent.client.VolcengineImageApiClient.enhanceImage(..)) || " +
              "execution(* com.wlink.agent.client.VolcengineImageApiClient.seedEditImageWithCredentials(..)) || " +
              "execution(* com.wlink.agent.client.VolcengineImageApiClient.retainCharacterImage*(..)) || " +
              "execution(* com.wlink.agent.client.VolcengineImageApiClient.paintingEditImage(..)) || " +
              "execution(* com.wlink.agent.client.DoubaoImageApiClient.generateImage(..))"  )
    public void volcengineImageApiPointcut() {}

    /**
     * 环绕通知 - 记录API调用信息
     */
    @Around("volcengineImageApiPointcut()")
    public Object recordVolcengineImageApiCall(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getName();
        
        // 构建记录对象
        AiImageApiRecordPo.AiImageApiRecordPoBuilder builder = AiImageApiRecordPo.builder()
                .apiMethod(methodName)
                .createTime(new Date())
                .updateTime(new Date());
        
        // 设置API类型
        setApiType(builder, methodName);
        
        // 处理请求参数，包括是否使用自定义账号
        processRequestParameters(builder, joinPoint);
        
        Object result = null;
        try {
            // 执行原方
            result = joinPoint.proceed();
            
            // 记录成功响应
            long endTime = System.currentTimeMillis();
            builder.status(1)
                   .costTime((int)(endTime - startTime));
            
            // 处理响应结果
            if (result instanceof ImageGenerateRes) {
                ImageGenerateRes imageResult = (ImageGenerateRes) result;
                builder.resultImageUrl(imageResult.getImageUrl())
                       .statusCode(imageResult.getCode())
                       .statusMessage(imageResult.getMessage());
                
                try {
                    builder.responseData(objectMapper.writeValueAsString(imageResult));
                } catch (Exception e) {
                    log.error("Failed to serialize response data", e);
                    builder.responseData("Error serializing response: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            // 记录失败响应
            long endTime = System.currentTimeMillis();
            builder.status(0)
                   .errorMsg(e.getMessage())
                   .costTime((int)(endTime - startTime));
            
            // 继续抛出异常
            throw e;
        } finally {
            // 保存记录
            try {
                AiImageApiRecordPo record = builder.build();
                aiImageApiRecordMapper.insert(record);
            } catch (Exception e) {
                log.error("Failed to save Volcengine image API call record", e);
            }
        }
        
        return result;
    }
    
    /**
     * 设置API类型
     */
    private void setApiType(AiImageApiRecordPo.AiImageApiRecordPoBuilder builder, String methodName) {
        if (methodName.contains("generateImage")) {
            builder.apiType(1); // 生成图片
        } else if (methodName.contains("enhanceImage")) {
            builder.apiType(2); // 增强图片
        } else if (methodName.contains("seedEditImage")) {
            builder.apiType(3); // 种子编辑
        } else if (methodName.contains("retainCharacterImage")) {
            builder.apiType(4); // 保留角色形象
        } else if (methodName.contains("paintingEditImage")) {
            builder.apiType(5); // 图像内部编辑
        } else {
            builder.apiType(99); // 其他类型
        }
    }
    
    /**
     * 处理请求参数
     */
    private void processRequestParameters(AiImageApiRecordPo.AiImageApiRecordPoBuilder builder, ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                // 检查是否使用自定义账号
                boolean hasCustomAccount = false;
                Long accountId = null;
                
                // 检查参数中是否有VendorAccountPo
                for (Object arg : args) {
                    if (arg instanceof VendorAccountPo) {
                        VendorAccountPo account = (VendorAccountPo) arg;
                        hasCustomAccount = true;
                        accountId = account.getId();
                        break;
                    }
                }
                
                builder.useCustomAccount(hasCustomAccount ? 1 : 0)
                       .accountId(accountId);
                
                // 序列化第一个参数为请求参数
                if (args[0] != null) {
                    // 针对不同类型的请求对象进行处
                    if (args[0] instanceof VolcengineImageRequest || 
                        args[0] instanceof VolcengineImageEnhanceRequest ||
                        args[0] instanceof VolcengineSeedEditRequest ||
                        args[0] instanceof VolcengineCharacterRetentionRequest ||
                        args[0] instanceof VolcengineInpaintingEditRequest ||
                        args[0] instanceof DoubaoImageGenerationRequest) {
                        builder.requestParams(objectMapper.writeValueAsString(args[0]));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to process request parameters", e);
            builder.requestParams("Error processing parameters: " + e.getMessage());
        }
    }
} 
