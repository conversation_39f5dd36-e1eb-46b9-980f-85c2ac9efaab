package com.wlink.agent.aspect;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.annotation.LogRequest;
import com.wlink.agent.dao.po.RequestLog;

import com.wlink.agent.service.RequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求日志切面
 * 用于记录带有@LogRequest注解的方法的请求和响
 */
@Aspect
@Component
@Slf4j
public class RequestLogAspect {

    @Resource
    private RequestLogService requestLogService;

    /**
     * 定义切入点，匹配带有@LogRequest注解的方
     */
    @Pointcut("@annotation(com.wlink.agent.annotation.LogRequest)")
    public void logRequestPointcut() {
    }

    /**
     * 环绕通知，记录请求和响应信息
     *
     * @param joinPoint 切入
     * @return 方法执行结果
     * @throws Throwable 执行异常
     */
    @Around("logRequestPointcut()")
    public Object logRequestAndResponse(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }

        HttpServletRequest request = attributes.getRequest();
        
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取注解
        LogRequest logRequestAnnotation = method.getAnnotation(LogRequest.class);
        if (logRequestAnnotation == null) {
            return joinPoint.proceed();
        }
        
        // 创建请求日志对象
        RequestLog requestLog = new RequestLog();
        requestLog.setRequestTime(new Date());
        requestLog.setMethodName(method.getName());
        requestLog.setRequestUri(request.getRequestURI());
        requestLog.setHttpMethod(request.getMethod());
        
        // 设置描述信息
        requestLog.setDescription(logRequestAnnotation.description());
        
        // 获取会话ID
        String conversationId = extractConversationId(joinPoint, request);
        requestLog.setConversationId(conversationId);
        
        // 获取请求参数
        requestLog.setRequestParams(getRequestParams(request));
        
        // 获取请求
        if (logRequestAnnotation.logRequestBody()) {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                try {
                    requestLog.setRequestBody(JSON.toJSONString(args));
                } catch (Exception e) {
                    log.error("Failed to serialize request body", e);
                    requestLog.setRequestBody("Failed to serialize: " + e.getMessage());
                }
            }
        }
        
        // 记录开始时
        long startTime = System.currentTimeMillis();
        
        // 执行目标方法
        Object result;
        try {
            result = joinPoint.proceed();
            // 请求成功
            requestLog.setStatus(true);
        } catch (Throwable e) {
            // 请求失败，记录错误信
            requestLog.setStatus(false);
            requestLog.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            // 计算处理时间
            long endTime = System.currentTimeMillis();
            requestLog.setProcessingTime(endTime - startTime);
            requestLog.setResponseTime(new Date());
        }
        
        // 记录响应结果
        if (logRequestAnnotation.logResponseBody() && result != null) {
            try {
                requestLog.setResponseBody(JSON.toJSONString(result));
            } catch (Exception e) {
                log.error("Failed to serialize response", e);
                requestLog.setResponseBody("Failed to serialize: " + e.getMessage());
            }
        }
        
        // 保存请求日志
        try {
            requestLogService.save(requestLog);
        } catch (Exception e) {
            log.error("Failed to save request log", e);
        }
        
        return result;
    }
    
    /**
     * 从请求中提取会话ID
     *
     * @param joinPoint 切入
     * @param request   HTTP请求
     * @return 会话ID
     */
    private String extractConversationId(ProceedingJoinPoint joinPoint, HttpServletRequest request) {
        // 首先尝试从URL参数中获
        String conversationId = request.getParameter("conversationId");
        
        // 如果URL参数中没有，尝试从路径变量中获取
        if (conversationId == null || conversationId.isEmpty()) {
            String uri = request.getRequestURI();
            if (uri.contains("/conversation/")) {
                String[] parts = uri.split("/");
                for (int i = 0; i < parts.length; i++) {
                    if ("conversation".equals(parts[i]) && i + 1 < parts.length) {
                        conversationId = parts[i + 1];
                        break;
                    }
                }
            }
            
            // 检查其他路径模式，/save/story/{conversationId}/...
            if ((conversationId == null || conversationId.isEmpty()) && uri.contains("/save/")) {
                String[] parts = uri.split("/");
                for (int i = 0; i < parts.length; i++) {
                    if (i >= 2 && "save".equals(parts[i-1]) && 
                        (parts[i].equals("story") || 
                         parts[i].equals("scene") || 
                         parts[i].equals("role") || 
                         parts[i].equals("shot") || 
                         parts[i].equals("design") || 
                         parts[i].equals("narration") || 
                         parts[i].equals("visual")) && 
                        i + 1 < parts.length) {
                        conversationId = parts[i + 1];
                        break;
                    }
                }
            }
        }
        
        // 如果还是没有，尝试从方法参数中获
        if ((conversationId == null || conversationId.isEmpty()) && joinPoint.getArgs() != null) {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] parameterNames = signature.getParameterNames();
            Object[] args = joinPoint.getArgs();
            
            for (int i = 0; i < parameterNames.length; i++) {
                if ("conversationId".equals(parameterNames[i]) && i < args.length && args[i] != null) {
                    conversationId = args[i].toString();
                    break;
                }
            }
        }
        
        return conversationId;
    }
    
    /**
     * 获取请求参数
     *
     * @param request HTTP请求
     * @return 请求参数的JSON字符
     */
    private String getRequestParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            params.put(paramName, request.getParameter(paramName));
        }
        return JSON.toJSONString(params);
    }
} 
