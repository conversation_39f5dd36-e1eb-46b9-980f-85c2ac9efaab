//package com.wlink.agent.aspect;
//
//import com.alibaba.fastjson.JSON;
//
//import com.wlink.agent.dao.mapper.AgentApiCallRecordMapper;
//import com.wlink.agent.dao.po.AgentApiCallRecordPo;
//import com.wlink.agent.model.dto.SimpleUserInfo;
//import com.wlink.agent.utils.UserContext;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.springframework.stereotype.Component;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import org.springframework.web.multipart.MultipartFile;
//
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//@Slf4j
//@Aspect
//@Component
//public class ApiCallRecordAspect {
//
//    @Resource
//    private AgentApiCallRecordMapper apiCallRecordMapper;
//
//    @Pointcut("execution(* com.wlink.agent.controller.AgentOpenAPIController.*(..))")
//    public void apiCallRecord() {}
//
//    @Around("apiCallRecord()")
//    public Object around(ProceedingJoinPoint point) throws Throwable {
//        long startTime = System.currentTimeMillis();
//        String apiName = point.getSignature().getName();
//        String apiPath = getApiPath();
//
//        // 过滤和处理请求参
//        Object[] args = point.getArgs();
//        List<Object> filteredArgs = new ArrayList<>();
//        if (args != null) {
//            for (Object arg : args) {
//                if (isSerializableArg(arg)) {
//                    filteredArgs.add(arg);
//                }
//            }
//        }
//        String requestParams = JSON.toJSONString(filteredArgs);
//
//        String ip = getIpAddress();
//        String userId = getUserId();
//
//        Object result = null;
//        Integer status = 1;
//        String errorMsg = null;
//        String responseData = null;
//
//        try {
//            result = point.proceed();
//            // 过滤和处理响应结
//            if (isSerializableArg(result)) {
//                responseData = JSON.toJSONString(result);
//            }
//            return result;
//        } catch (Throwable e) {
//            status = 0;
//            errorMsg = e.getMessage();
//            throw e;
//        } finally {
//            long endTime = System.currentTimeMillis();
//            int costTime = (int)(endTime - startTime);
//
//            // 保存调用记录
//            AgentApiCallRecordPo record = AgentApiCallRecordPo.builder()
//                .apiName(apiName)
//                .apiPath(apiPath)
//                .requestParams(requestParams)
//                .responseData(responseData)
//                .status(status)
//                .errorMsg(errorMsg)
//                .costTime(costTime)
//                .ip(ip)
//                .userId(userId)
//                .createTime(new Date())
//                .updateTime(new Date())
//                .build();
//
//            try {
//                apiCallRecordMapper.insert(record);
//            } catch (Exception e) {
//                log.error("Failed to save api call record", e);
//            }
//        }
//    }
//
//    /**
//     * 判断参数是否可以序列
//     */
//    private boolean isSerializableArg(Object arg) {
//        if (arg == null) {
//            return true;
//        }
//        // 排除不能序列化的类型
//        return !(arg instanceof HttpServletRequest)
//            && !(arg instanceof HttpServletResponse)
//            && !(arg instanceof MultipartFile)
//            && !(arg.getClass().getName().contains("ContentCachingRequestWrapper"))
//            && !(arg.getClass().getName().contains("ContentCachingResponseWrapper"));
//    }
//
//    private String getIpAddress() {
//        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        if (attributes != null) {
//            HttpServletRequest request = attributes.getRequest();
//            String ip = request.getHeader("X-Real-IP");
//            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
//                ip = request.getHeader("X-Forwarded-For");
//            }
//            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
//                ip = request.getHeader("Proxy-Client-IP");
//            }
//            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
//                ip = request.getHeader("WL-Proxy-Client-IP");
//            }
//            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
//                ip = request.getRemoteAddr();
//            }
//            return ip;
//        }
//        return null;
//    }
//
//    private String getUserId() {
//        try {
//            SimpleUserInfo userInfo = UserContext.getUser();
//            return userInfo != null ? userInfo.getUsername() : null;
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    private String getApiPath() {
//        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        if (attributes != null) {
//            HttpServletRequest request = attributes.getRequest();
//            return request.getRequestURI();
//        }
//        return null;
//    }
//}
