package com.wlink.agent.aspect;

import com.alibaba.cola.exception.BizException;
import com.wlink.agent.annotation.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
public class DistributedLockAspect {

    @Resource
    private RedissonClient redissonClient;

    private final ExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint point, DistributedLock distributedLock) throws Throwable {
        String lockKey = getLockKey(point, distributedLock);
        RLock lock = redissonClient.getLock(lockKey);
        
        log.debug("Trying to acquire lock: {}", lockKey);
        boolean acquired = false;
        
        try {
            acquired = lock.tryLock(
                distributedLock.waitTime(),
                distributedLock.leaseTime(),
                distributedLock.timeUnit()
            );
            
            if (acquired) {
                log.debug("Lock acquired: {}", lockKey);
                return point.proceed();
            } else {
                log.warn("Failed to acquire lock: {}", lockKey);
                throw new BizException(distributedLock.failMessage());
            }
        } finally {
            if (acquired) {
                lock.unlock();
                log.debug("Lock released: {}", lockKey);
            }
        }
    }

    private String getLockKey(ProceedingJoinPoint point, DistributedLock distributedLock) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Object[] args = point.getArgs();
        String[] paramNames = discoverer.getParameterNames(method);
        
        EvaluationContext context = new StandardEvaluationContext();
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        
        Expression expression = parser.parseExpression(distributedLock.key());
        String key = expression.getValue(context, String.class);
        return distributedLock.prefix() + key;
    }
} 
