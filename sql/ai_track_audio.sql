-- 音轨音频关联表
CREATE TABLE `ai_track_audio` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `track_id` bigint(20) NOT NULL COMMENT '音轨ID',
  `audio_name` varchar(255) NOT NULL COMMENT '音频名称',
  `audio_url` varchar(1000) NOT NULL COMMENT '音频地址',
  `start_play_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '音频开始播放时间（毫秒）',
  `end_play_time` bigint(20) DEFAULT NULL COMMENT '音频结束播放时间（毫秒）',
  `volume` int(11) NOT NULL DEFAULT '100' COMMENT '音频音量（0-100）',
  `duration` bigint(20) DEFAULT NULL COMMENT '音频时长（毫秒）',
  `file_size` bigint(20) DEFAULT NULL COMMENT '音频文件大小（字节）',
  `audio_format` varchar(50) DEFAULT NULL COMMENT '音频格式（mp3、wav、ogg等）',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '音频排序序号',
  `muted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否静音（0-否，1-是）',
  `fade_in_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '淡入时间（毫秒）',
  `fade_out_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '淡出时间（毫秒）',
  `audio_source` tinyint(4) NOT NULL DEFAULT '1' COMMENT '音频来源（1-上传，2-AI生成，3-素材库）',
  `description` varchar(1000) DEFAULT NULL COMMENT '音频描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_track_id` (`track_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_audio_source` (`audio_source`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_track_sort` (`track_id`, `sort_order`),
  KEY `idx_start_play_time` (`start_play_time`),
  KEY `idx_end_play_time` (`end_play_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='音轨音频关联表';

-- 添加索引说明
-- idx_track_id: 用于查询音轨下的音频
-- idx_sort_order: 用于音频排序
-- idx_audio_source: 用于按音频来源查询
-- idx_del_flag: 用于过滤已删除的记录
-- idx_track_sort: 复合索引，用于查询音轨下按顺序排列的音频
-- idx_start_play_time: 用于按开始播放时间查询
-- idx_end_play_time: 用于按结束播放时间查询
