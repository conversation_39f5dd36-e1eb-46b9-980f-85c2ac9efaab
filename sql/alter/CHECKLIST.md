# 视频分享功能部署检查清单

## 部署前检查

### 1. 代码准备
- [ ] 代码已提交到版本控制系统
- [ ] 代码已通过编译测试
- [ ] 单元测试已通过
- [ ] 代码审查已完成

### 2. 数据库准备
- [ ] 已在测试环境验证ALTER语句
- [ ] 已执行 `test_alter_statements.sql` 验证脚本
- [ ] 已准备数据库备份计划
- [ ] 已确认数据库用户权限（需要ALTER权限）

### 3. 环境准备
- [ ] 已通知相关团队成员
- [ ] 已选择合适的维护窗口
- [ ] 已准备回滚方案

## 部署执行

### 1. 数据库备份
```bash
# 执行前备份
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```
- [ ] 数据库备份已完成
- [ ] 备份文件已验证完整性

### 2. 执行ALTER语句
```bash
# 执行ALTER脚本
mysql -u username -p database_name < sql/alter/add_video_share_fields.sql
```
- [ ] ALTER语句执行成功
- [ ] 无错误信息输出
- [ ] 执行时间记录：_______

### 3. 验证数据库变更
```sql
-- 检查表结构
DESC ai_video_render_export;

-- 检查索引
SHOW INDEX FROM ai_video_render_export;

-- 检查现有数据
SELECT COUNT(*) as total_records, 
       SUM(CASE WHEN share_status = 0 THEN 1 ELSE 0 END) as unshared,
       SUM(CASE WHEN share_status = 1 THEN 1 ELSE 0 END) as shared
FROM ai_video_render_export;
```
- [ ] 新字段已添加：share_status, share_code, share_time
- [ ] 新索引已创建：idx_share_code, idx_share_status_time
- [ ] 现有数据share_status默认为0
- [ ] 现有数据share_code和share_time为NULL

### 4. 应用部署
- [ ] 应用服务已停止
- [ ] 新版本代码已部署
- [ ] 应用服务已启动
- [ ] 应用启动日志无错误

## 部署后验证

### 1. 功能测试
- [ ] 分享视频功能测试通过
- [ ] 取消分享功能测试通过
- [ ] 分页查询已分享视频功能测试通过
- [ ] 根据分享码查询视频功能测试通过

### 2. 接口测试
```bash
# 测试分享视频
curl -X PUT "http://localhost:8080/agent/video-render/share/1" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"share": true}'

# 测试取消分享
curl -X PUT "http://localhost:8080/agent/video-render/share/1" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"share": false}'

# 测试查询已分享视频
curl -X POST "http://localhost:8080/agent/video-render/shared/list" \
     -H "Content-Type: application/json" \
     -d '{"pageNum":1,"pageSize":10}'

# 测试根据分享码查询
curl -X GET "http://localhost:8080/agent/video-render/shared/ABC12345"
```
- [ ] 分享接口返回分享码
- [ ] 取消分享接口返回成功
- [ ] 分页查询接口返回正确数据
- [ ] 分享码查询接口返回视频详情

### 3. 数据验证
```sql
-- 验证分享功能数据写入
SELECT * FROM ai_video_render_export WHERE share_status = 1 LIMIT 5;

-- 验证索引使用情况
EXPLAIN SELECT * FROM ai_video_render_export WHERE share_code = 'test';
EXPLAIN SELECT * FROM ai_video_render_export WHERE share_status = 1 ORDER BY share_time DESC;
```
- [ ] 分享操作正确更新数据库
- [ ] 索引被正确使用（type=const或ref）

### 4. 性能监控
- [ ] 数据库连接池正常
- [ ] 查询响应时间正常
- [ ] 应用内存使用正常
- [ ] CPU使用率正常

## 问题处理

### 如果出现问题
1. [ ] 立即停止相关操作
2. [ ] 记录错误信息和现象
3. [ ] 评估是否需要回滚
4. [ ] 如需回滚，执行回滚脚本：
   ```bash
   mysql -u username -p database_name < sql/alter/rollback_video_share_fields.sql
   ```
5. [ ] 通知相关团队成员
6. [ ] 分析问题原因并制定修复方案

## 完成确认

### 最终检查
- [ ] 所有功能测试通过
- [ ] 性能指标正常
- [ ] 无用户投诉或错误报告
- [ ] 监控指标正常

### 文档更新
- [ ] API文档已更新
- [ ] 数据库文档已更新
- [ ] 部署记录已归档

---

**部署负责人**: _______________  
**部署时间**: _______________  
**验证完成时间**: _______________  
**签字确认**: _______________
