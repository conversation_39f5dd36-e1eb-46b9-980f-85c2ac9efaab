-- =====================================================
-- 回滚视频分享功能相关字段
-- 执行时间: 如需回滚时执行
-- 说明: 删除 ai_video_render_export 表中的分享相关字段和索引
-- 警告: 执行此脚本将永久删除分享相关数据，请谨慎操作！
-- =====================================================

-- 1. 删除分享状态和时间的复合索引
ALTER TABLE `ai_video_render_export` 
DROP KEY `idx_share_status_time`;

-- 2. 删除分享码唯一索引
ALTER TABLE `ai_video_render_export` 
DROP KEY `idx_share_code`;

-- 3. 删除分享时间字段
ALTER TABLE `ai_video_render_export` 
DROP COLUMN `share_time`;

-- 4. 删除分享码字段
ALTER TABLE `ai_video_render_export` 
DROP COLUMN `share_code`;

-- 5. 删除分享状态字段
ALTER TABLE `ai_video_render_export` 
DROP COLUMN `share_status`;

-- =====================================================
-- 验证语句（可选执行，用于验证字段是否删除成功）
-- =====================================================

-- 查看表结构
-- DESC `ai_video_render_export`;

-- 查看索引
-- SHOW INDEX FROM `ai_video_render_export`;
