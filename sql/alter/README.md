# 视频分享功能数据库变更指南

## 概述

本目录包含为视频渲染导出表 `ai_video_render_export` 添加分享功能所需的数据库变更脚本。

## 文件说明

- `add_video_share_fields.sql` - 添加分享功能字段的ALTER语句
- `rollback_video_share_fields.sql` - 回滚脚本（删除添加的字段）
- `README.md` - 本说明文件

## 变更内容

### 新增字段

1. **share_status** - 分享状态
   - 类型: `tinyint(1)`
   - 默认值: `0`
   - 说明: 0-未分享，1-已分享

2. **share_code** - 分享码
   - 类型: `varchar(32)`
   - 默认值: `NULL`
   - 说明: 8位随机字符串，用于公开访问

3. **share_time** - 分享时间
   - 类型: `datetime`
   - 默认值: `NULL`
   - 说明: 记录分享的时间

### 新增索引

1. **idx_share_code** - 分享码唯一索引
   - 类型: UNIQUE KEY
   - 用途: 确保分享码唯一性，快速根据分享码查询

2. **idx_share_status_time** - 分享状态和时间复合索引
   - 类型: KEY
   - 用途: 优化分页查询已分享视频的性能

## 执行步骤

### 1. 备份数据库（重要！）

```sql
-- 备份整个数据库
mysqldump -u username -p database_name > backup_before_share_feature.sql

-- 或者只备份相关表
mysqldump -u username -p database_name ai_video_render_export > backup_ai_video_render_export.sql
```

### 2. 执行变更脚本

```bash
# 连接到数据库
mysql -u username -p database_name

# 执行ALTER语句
source sql/alter/add_video_share_fields.sql;
```

### 3. 验证变更

```sql
-- 查看表结构
DESC ai_video_render_export;

-- 查看索引
SHOW INDEX FROM ai_video_render_export;

-- 验证新字段默认值
SELECT share_status, share_code, share_time FROM ai_video_render_export LIMIT 5;
```

### 4. 测试应用

1. 重启应用服务
2. 测试分享功能接口
3. 验证数据库记录更新

## 回滚步骤（如需要）

⚠️ **警告**: 回滚操作将永久删除所有分享相关数据！

```bash
# 执行回滚脚本
mysql -u username -p database_name < sql/alter/rollback_video_share_fields.sql
```

## 影响评估

### 对现有数据的影响
- ✅ 不会影响现有记录
- ✅ 新字段有默认值，兼容现有代码
- ✅ 索引添加不会锁表太久（取决于数据量）

### 对应用的影响
- ✅ 向后兼容，现有功能不受影响
- ✅ 新增字段在实体类中已定义默认值
- ✅ 查询性能可能略有提升（新增索引）

### 性能影响
- 添加索引时可能短暂影响表的写入性能
- 建议在业务低峰期执行
- 对于大表（>100万记录），建议分批执行或使用在线DDL工具

## 监控建议

执行变更后，建议监控以下指标：

1. **应用日志** - 检查是否有相关错误
2. **数据库性能** - 监控查询响应时间
3. **磁盘空间** - 新增字段和索引会占用额外空间
4. **分享功能** - 测试分享和查询功能是否正常

## 联系信息

如有问题，请联系开发团队。
