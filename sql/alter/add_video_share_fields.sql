-- =====================================================
-- 为视频渲染导出表添加分享功能相关字段
-- 执行时间: 2024-01-01
-- 说明: 为 ai_video_render_export 表添加分享状态、分享码、分享时间字段
-- =====================================================

-- 1. 添加分享状态字段
ALTER TABLE `ai_video_render_export` 
ADD COLUMN `share_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分享状态(0-未分享,1-已分享)' AFTER `complete_time`;

-- 2. 添加分享码字段
ALTER TABLE `ai_video_render_export` 
ADD COLUMN `share_code` varchar(32) DEFAULT NULL COMMENT '分享码' AFTER `share_status`;

-- 3. 添加分享时间字段
ALTER TABLE `ai_video_render_export` 
ADD COLUMN `share_time` datetime DEFAULT NULL COMMENT '分享时间' AFTER `share_code`;

-- 4. 添加分享码唯一索引（用于快速查询和确保唯一性）
ALTER TABLE `ai_video_render_export` 
ADD UNIQUE KEY `idx_share_code` (`share_code`);

-- 5. 添加分享状态和时间的复合索引（用于分页查询已分享视频）
ALTER TABLE `ai_video_render_export` 
ADD KEY `idx_share_status_time` (`share_status`, `share_time`);

-- =====================================================
-- 验证语句（可选执行，用于验证字段是否添加成功）
-- =====================================================

-- 查看表结构
-- DESC `ai_video_render_export`;

-- 查看索引
-- SHOW INDEX FROM `ai_video_render_export`;

-- 查看新增字段的默认值
-- SELECT share_status, share_code, share_time FROM `ai_video_render_export` LIMIT 1;
