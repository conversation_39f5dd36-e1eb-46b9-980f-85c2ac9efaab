-- =====================================================
-- 测试ALTER语句的正确性
-- 说明: 在测试环境中验证ALTER语句是否能正确执行
-- =====================================================

-- 1. 创建测试表（模拟现有表结构）
DROP TABLE IF EXISTS `test_ai_video_render_export`;

CREATE TABLE `test_ai_video_render_export` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `canvas_id` bigint(20) NOT NULL COMMENT '画布ID',
  `resolution` varchar(32) NOT NULL COMMENT '分辨率',
  `ratio` varchar(16) NOT NULL COMMENT '比例',
  `show_subtitle` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示字幕(0-不显示,1-显示)',
  `render_task_id` varchar(128) DEFAULT NULL COMMENT '渲染任务ID (Python渲染服务返回的任务ID)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '渲染状态(0-排队中,1-渲染中,2-已完成,3-失败)',
  `video_url` varchar(512) DEFAULT NULL COMMENT '渲染后的视频地址',
  `canvas_data_json` longtext COMMENT '画布数据JSON，发送给Python渲染接口',
  `error_message` varchar(1024) DEFAULT NULL COMMENT '错误信息',
  `query_fail_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询失败次数',
  `start_time` datetime DEFAULT NULL COMMENT '渲染开始时间',
  `complete_time` datetime DEFAULT NULL COMMENT '渲染完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_canvas_id` (`canvas_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_canvas` (`user_id`, `canvas_id`),
  KEY `idx_render_task_id` (`render_task_id`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试视频渲染导出记录表';

-- 2. 插入测试数据
INSERT INTO `test_ai_video_render_export` 
(`user_id`, `canvas_id`, `resolution`, `ratio`, `status`, `video_url`) 
VALUES 
('user1', 1, '1080p', '16:9', 2, 'http://example.com/video1.mp4'),
('user2', 2, '720p', '16:9', 2, 'http://example.com/video2.mp4');

-- 3. 执行ALTER语句（与实际脚本相同）
ALTER TABLE `test_ai_video_render_export` 
ADD COLUMN `share_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分享状态(0-未分享,1-已分享)' AFTER `complete_time`;

ALTER TABLE `test_ai_video_render_export` 
ADD COLUMN `share_code` varchar(32) DEFAULT NULL COMMENT '分享码' AFTER `share_status`;

ALTER TABLE `test_ai_video_render_export` 
ADD COLUMN `share_time` datetime DEFAULT NULL COMMENT '分享时间' AFTER `share_code`;

ALTER TABLE `test_ai_video_render_export` 
ADD UNIQUE KEY `idx_share_code` (`share_code`);

ALTER TABLE `test_ai_video_render_export` 
ADD KEY `idx_share_status_time` (`share_status`, `share_time`);

-- 4. 验证结果
SELECT '=== 表结构验证 ===' as info;
DESC `test_ai_video_render_export`;

SELECT '=== 索引验证 ===' as info;
SHOW INDEX FROM `test_ai_video_render_export`;

SELECT '=== 数据验证 ===' as info;
SELECT id, user_id, share_status, share_code, share_time FROM `test_ai_video_render_export`;

-- 5. 测试插入带分享信息的数据
INSERT INTO `test_ai_video_render_export` 
(`user_id`, `canvas_id`, `resolution`, `ratio`, `status`, `video_url`, `share_status`, `share_code`, `share_time`) 
VALUES 
('user3', 3, '1080p', '16:9', 2, 'http://example.com/video3.mp4', 1, 'ABC12345', NOW());

SELECT '=== 插入测试验证 ===' as info;
SELECT id, user_id, share_status, share_code, share_time FROM `test_ai_video_render_export` WHERE user_id = 'user3';

-- 6. 清理测试表
DROP TABLE IF EXISTS `test_ai_video_render_export`;

SELECT '=== 测试完成 ===' as info;
