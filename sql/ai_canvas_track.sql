-- 画布音轨关联表
CREATE TABLE `ai_canvas_track` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `canvas_id` bigint(20) NOT NULL COMMENT '画布ID',
  `track_name` varchar(255) NOT NULL COMMENT '音轨名称',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '音轨开始时间（毫秒）',
  `end_time` bigint(20) DEFAULT NULL COMMENT '音轨结束时间（毫秒）',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '音轨排序序号',
  `track_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '音轨类型（1-背景音乐，2-音效，3-旁白，4-对话）',
  `volume` int(11) NOT NULL DEFAULT '100' COMMENT '音轨音量（0-100）',
  `muted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否静音（0-否，1-是）',
  `locked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否锁定（0-否，1-是）',
  `description` varchar(1000) DEFAULT NULL COMMENT '音轨描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_canvas_id` (`canvas_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_track_type` (`track_type`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_canvas_sort` (`canvas_id`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='画布音轨关联表';

-- 添加索引说明
-- idx_canvas_id: 用于查询画布下的音轨
-- idx_sort_order: 用于音轨排序
-- idx_track_type: 用于按音轨类型查询
-- idx_del_flag: 用于过滤已删除的记录
-- idx_canvas_sort: 复合索引，用于查询画布下按顺序排列的音轨
